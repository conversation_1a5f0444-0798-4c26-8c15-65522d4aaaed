#!/usr/bin/env python3
"""
Script to clean up all profile analytics data from PostgreSQL.
Deletes data in reverse order of dependencies to respect foreign key constraints.
"""

import psycopg2
import sys

# Database connection string
DB_URI = "****************************************************************"

def connect_db():
    """Establish database connection"""
    try:
        conn = psycopg2.connect(DB_URI)
        return conn
    except Exception as e:
        print(f"Error connecting to database: {e}")
        sys.exit(1)

def cleanup_tables(conn):
    """Clean up all tables in reverse order of dependencies"""
    cur = conn.cursor()
    
    # List of tables to clean up in correct order
    tables = [
        # First delete dependent tables
        "profile_analytics.contact_detail",
        "profile_analytics.profile_stats_current",
        "profile_analytics.audience_device",
        "profile_analytics.audience_income",
        "profile_analytics.audience_interest",
        "profile_analytics.audience_ethnicity",
        "profile_analytics.audience_gender_age",
        "profile_analytics.audience_language",
        "profile_analytics.audience_location",
        "profile_analytics.content",
        "profile_analytics.profile_hashtag",
        "profile_analytics.profile_interest",
        "profile_analytics.profile_brand_affinity",
        "profile_analytics.pricing",
        "profile_analytics.reputation_history",
        # Finally delete the main profile table
        "profile_analytics.profile"
    ]
    
    for table in tables:
        try:
            print(f"Cleaning up table {table}...")
            cur.execute(f"DELETE FROM {table}")
            cur.execute(f"SELECT COUNT(*) FROM {table}")
            count = cur.fetchone()[0]
            print(f"Remaining records in {table}: {count}")
            conn.commit()
        except Exception as e:
            print(f"Error cleaning up table {table}: {e}")
            conn.rollback()
    
    # Optionally clean up master data if needed
    master_tables = [
        "api_provider.master_interest",
        "api_provider.master_brand",
        "api_provider.master_work_platform"
    ]
    
    cleanup_master = input("Do you want to clean up master data tables as well? (y/n): ").lower()
    if cleanup_master == 'y':
        for table in master_tables:
            try:
                print(f"Cleaning up master table {table}...")
                cur.execute(f"DELETE FROM {table}")
                cur.execute(f"SELECT COUNT(*) FROM {table}")
                count = cur.fetchone()[0]
                print(f"Remaining records in {table}: {count}")
                conn.commit()
            except Exception as e:
                print(f"Error cleaning up master table {table}: {e}")
                conn.rollback()
    
    cur.close()

def main():
    """Main function to orchestrate the cleanup process"""
    print("Starting cleanup of profile analytics data...")
    
    # Confirm before proceeding
    confirm = input("This will delete ALL profile analytics data. Are you sure? (y/n): ").lower()
    if confirm != 'y':
        print("Cleanup cancelled")
        return
    
    # Connect to database
    conn = connect_db()
    print("Connected to database")
    
    # Perform cleanup
    cleanup_tables(conn)
    
    # Close connection
    conn.close()
    print("Cleanup completed successfully")

if __name__ == "__main__":
    main()
