#!/usr/bin/env python3
"""
Script to bulk insert profile analytics data into PostgreSQL
following foreign key dependencies and constraints.

This script now uses the BulkInsertService for better maintainability.
"""

import sys
from app.services.bulk_insert_service import BulkInsertService

# Database connection string from MCP config
DB_URI = "*******************************************************/postgres"

def main():
    """Main function to orchestrate the data insertion process"""
    try:
        # Get limit from command line arguments
        limit = None
        if len(sys.argv) > 1:
            try:
                limit = int(sys.argv[1])
                print(f"Using limit: {limit}")
            except ValueError:
                print("Invalid limit argument, using default")
        
        # Create service instance
        service = BulkInsertService(db_uri=DB_URI)
        
        # Perform bulk insert
        stats = service.bulk_insert_profiles(limit=limit)
        
        if stats['failed'] > 0:
            print(f"\nWARNING: {stats['failed']} profiles failed to process.")
            print("Errors encountered:")
            for error in stats['errors'][:10]:  # Show first 10 errors
                print(f"  - {error}")
            if len(stats['errors']) > 10:
                print(f"  ... and {len(stats['errors']) - 10} more errors")
        
        print("\nBulk insert completed successfully!")
        
    except Exception as e:
        print(f"Fatal error in bulk insert: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
