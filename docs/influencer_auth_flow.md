# Influencer Authentication Flow Analysis

This document analyzes the full flow of influencer authentication as implemented in `app/api/api_v1/endpoints/auth_influencer.py`.

---

## 1. Registration (`/register`)

- **Input:** `InfluencerRegisterRequest` (identifier, identifier_type)
- **Flow:**
  1. Checks for OTP cooldown using Redis key (`otp_cooldown_key`).
  2. Validates if the user already exists (via cache-aside pattern).
  3. If not exists, creates a new user with status "requested" and `is_active=False`.
  4. Sends OTP to email or mobile (does not persist OTP in DB at this stage).
  5. Returns a response indicating OTP sent.

---

## 2. OTP Verification (`/verify-otp`)

- **Input:** `VerifyOTPRequest` (identifier, identifier_type, otp, role_uuid)
- **Flow:**
  1. Validates identifier type (must be email or mobile).
  2. Fetches user by identifier (email/mobile) to get `user_id`.
  3. Verifies <PERSON><PERSON> using the OTP manager.
  4. Updates failed attempts in UserOTP if needed.
  5. If <PERSON><PERSON> is valid:
     - Opens a DB transaction.
     - Fetches the user (session-bound).
     - Marks user as verified (`is_email_verified` or `is_phone_verified`), sets status to ACTIVE, and `is_active=True`.
     - Looks up the auth method ID and assigns the influencer role.
     - Saves the role to Redis for future authentication.
     - Records the auth method selection.
     - Creates a user session and mints tokens.
     - Sends a welcome email if email is present.
  6. Returns authentication tokens.

---

## 3. Resend OTP (`/resend-otp`)

- **Input:** `ResendOTPRequest` (identifier, identifier_type)
- **Flow:**
  1. Checks if user exists.
  2. Validates email domain if identifier is email.
  3. Checks for OTP cooldown (enforces 30s wait).
  4. Sends/resends OTP using the OTP manager.
  5. Returns a response indicating OTP resent.

---

## 4. Login (`/login`)

- **Input:** `LoginRequest` (identifier, identifier_type)
- **Flow:**
  1. Checks for OTP cooldown.
  2. Fetches user by identifier (cache-aside).
  3. Validates user is active and verified for the login method.
  4. Validates email domain if identifier is email.
  5. Sends OTP for login and persists it in DB.
  6. Returns a response indicating OTP sent.

---

## 5. Login OTP Verification (`/login/verify-otp`)

- **Input:** `LoginVerifyOTPRequest` (identifier, identifier_type, otp)
- **Flow:**
  1. Validates identifier type.
  2. Fetches user by identifier.
  3. Verifies OTP using the OTP manager.
  4. Updates failed attempts in UserOTP if needed.
  5. If OTP is valid:
     - Opens a DB transaction.
     - Fetches the user (session-bound).
     - Validates user is active.
     - Checks user role from Redis (must be "influencer").
     - Updates last login timestamp.
     - Looks up auth method ID for OTP.
     - Creates user auth method record if not exists.
     - Creates user session and mints tokens.
     - Logs successful login.
     - Clears OTP from Redis.
  6. Returns authentication tokens.

---

## Key Points

- **Cache-aside pattern** is used for all user lookups (email/mobile).
- **OTP management** is handled via a dedicated OTP manager, with Redis for cooldown and failed attempts.
- **Session-bound user objects** are always used for DB updates to avoid detached instance errors.
- **Role assignment** and **auth method tracking** are handled at registration/verification.
- **Welcome emails** are sent asynchronously after successful registration.
- **All flows use proper logging with trace IDs** (from context, not headers).
- **Redis keys** follow the `CreatorVerse:feature:key` pattern.
- **No .env usage**; config comes from `APP_CONFIG`.

---

## Error Handling

- All endpoints use FastAPI's `HTTPException` for error responses.
- Cooldown and validation errors return appropriate status codes and messages.
- All DB and Redis operations are wrapped in try/except with logging.

---

## Security

- OTPs are rate-limited and have cooldowns.
- Only verified and active users can log in.
- Role checks prevent cross-endpoint misuse.

---

## Extensibility

- The flow is modular and can be extended for new user types or auth methods.
- All cache and DB operations are reusable and follow project conventions.

