# Brand Authentication Flow Analysis

This document provides a comprehensive analysis of the brand authentication and account management flow in the CreatorVerse backend. The brand authentication system is implemented in `auth_brands.py` and integrates with organization and role management systems.

## Table of Contents
- [Registration Flow](#registration-flow)
- [OTP Verification Flow](#otp-verification-flow)
- [Resending OTP Flow](#resending-otp-flow)
- [Login Flow](#login-flow)
- [Login OTP Verification Flow](#login-otp-verification-flow)
- [Brand Role Assignment](#brand-role-assignment)
- [Organization Integration](#organization-integration)
- [Caching Strategy](#caching-strategy)
- [Security Considerations](#security-considerations)

## Registration Flow

### Endpoint: `/register`
This endpoint handles the initial brand registration with these steps:

1. **Input Validation**:
   - Accepts `BrandRegisterRequest` with identifier (email/mobile) and identifier type
   - Only email is fully supported for brand registration

2. **OTP Cooldown Check**:
   - Uses Redis key pattern `CreatorVerse:otp:cooldown:{identifier}` 
   - Enforces 30-second cooldown between OTP requests
   - Returns 429 error if cooldown is still active

3. **User Existence Check**:
   - Calls `validate_and_fetch_existing_user()` to check if email/mobile already exists
   - Uses cache-aside pattern with Redis and bloom filter for efficiency

4. **User Creation**:
   - If user doesn't exist, creates a new User record with status "requested" and `is_active=False`
   - Uses transaction with `session.begin()` to ensure atomicity

5. **OTP Generation and Delivery**:
   - Calls `send_user_otp()` to generate and send OTP
   - Sets `user_role="brand"` to use brand-specific email templates
   - Does not persist OTP in database at this stage (done during verification)

6. **Response**:
   - Returns success message with OTP details
   - Does not expose actual OTP in response

### Key Code Path:
```python
cooldown_key = RedisKeys.otp_cooldown_key(data.email)
cooldown_exists = await redis_client.exists(cooldown_key)

user_exists = await validate_and_fetch_existing_user(identifier, identifier_type, db_conn, redis_client)

if not user_exists:
    async with db_conn.get_db() as session:
        async with session.begin():
            new_user = await create_user(session, data)
            session.add(new_user)
            await session.commit()

await send_user_otp(identifier, identifier_type, redis_client, user_role="brand")
```

## OTP Verification Flow

### Endpoint: `/verify-otp`
This endpoint verifies the OTP and activates the brand user account:

1. **Input Validation**:
   - Accepts `VerifyOTPRequest` with identifier, identifier type, OTP, and role_uuid
   - For brands, enforces email-only verification (rejects mobile)

2. **User Lookup**:
   - Uses cache-aside pattern to fetch user by email
   - Extracts user_id for OTP verification record

3. **OTP Verification**:
   - Uses OTP manager to verify the code
   - Updates UserOTP record with failed attempts if needed
   - Uses separate database session for OTP record updates

4. **User Activation**:
   - Opens a transaction
   - Fetches session-bound user to avoid detached entity issues
   - Marks user as verified (`is_email_verified = True`)
   - Updates status to "active" and sets `is_active = True`

5. **Organization Setup**:
   - Calls `ensure_org_and_membership()` to:
     - Check if organization exists for user's email domain
     - Create organization if none exists
     - Add user as organization member
     - Set appropriate role based on whether org was newly created

6. **Role Assignment**:
   - Assigns the brand role from `role_uuid` parameter using `UserRoleModel`
   - Also saves role to Redis for quick access during authentication
   - Role is "brand-admin" if the user created a new organization, otherwise "brand"

7. **Auth Method Recording**:
   - Creates `UserAuthMethod` record to track authentication method

8. **Session/Token Creation**:
   - Creates user session with `create_user_session()`
   - Generates access and refresh tokens

9. **Welcome Email**:
   - Sends welcome email asynchronously
   - Uses brand-specific email template

10. **Response**:
    - Returns tokens, organization info, and brand data
    - Sets `is_first` flag based on whether organization was newly created
    - Includes list of organization brands with user relationship status

### Key Code Path:
```python
async with db_conn.get_db() as session:
    async with session.begin():
        # Fetch user in transaction
        stmt = select(User).where(func.lower(User.email) == identifier.lower())
        result = await session.scalars(stmt)
        session_user = result.one_or_none()
        
        # Activate user
        session_user.is_email_verified = True
        session_user.status = UserStatus.get_status_name(UserStatus.ACTIVE.value)
        session_user.is_active = True
        
        # Assign role
        session.add(UserRoleModel(user_id=session_user.id, role_id=data.role_uuid))
        
        # Ensure organization
        org, membership, org_created = await ensure_org_and_membership(
            session=session,
            redis_client=redis_client,
            user=session_user
        )
        
        # Determine role based on org creation
        role = "brand-admin" if org_created else "brand"
        await save_user_role_to_redis(str(session_user.id), role, redis_client)
```

## Resending OTP Flow

### Endpoint: `/resend-otp`
Handles resending OTP with cooldown enforcement:

1. **User Verification**:
   - Checks if user exists with the provided identifier
   - Returns 404 if not found

2. **Email Validation**:
   - For email identifiers, validates domain using `verify_email_exists_optimized()`

3. **Cooldown Check**:
   - Checks if OTP is still in cooldown period (30s)
   - Returns 429 error if too many requests

4. **OTP Generation and Delivery**:
   - Regenerates and sends OTP via email
   - Updates Redis OTP data

5. **Response**:
   - Returns success message without exposing OTP

## Login Flow

### Endpoint: `/login`
Initiates login process for existing brand users:

1. **Cooldown Check**:
   - Enforces OTP cooldown period

2. **User Verification**:
   - Verifies user exists and is active
   - Returns 404 if not found or 403 if not active

3. **OTP Generation**:
   - Generates and sends login OTP
   - Persists OTP in the database (unlike registration)

4. **Response**:
   - Returns success message indicating OTP delivery

## Login OTP Verification Flow

### Endpoint: `/login/verify-otp`
Verifies login OTP and issues authentication tokens:

1. **Input Processing**:
   - Accepts identifier, identifier type, and OTP

2. **User Retrieval**:
   - Fetches user information from cache or database

3. **OTP Verification**:
   - Validates OTP using OTP manager
   - Updates failed attempts count

4. **Role Verification**:
   - Checks if user has appropriate brand role
   - Ensures user isn't trying to use influencer endpoint

5. **User Session**:
   - Updates last login timestamp
   - Creates session and tokens

6. **Organization Data**:
   - Fetches user's organization and related data
   - Gets list of organization's brands with relationship status

7. **Response**:
   - Returns auth tokens, org info, and brand relationships
   - `is_first` flag indicates if user is organization owner

## Brand Role Assignment

Brand roles are determined in these key ways:

1. **During Registration**:
   - Role is assigned from `role_uuid` parameter in `/verify-otp` request
   - When a new organization is created, user gets "brand-admin" role
   - Otherwise, user gets "brand" role

2. **Role Hierarchy**:
   ```
   brand-admin > brand-owner > brand
   ```

3. **Persistence**:
   - Roles are stored in two places:
     - Database: `UserRoleModel` table with role_id from `MasterRole`
     - Redis: For fast access during authentication

4. **Verification**:
   - During login, system ensures user has brand-related role
   - Prevents brand users from using influencer endpoints and vice versa

## Organization Integration

Brand users are always tied to organizations:

1. **Organization Creation**:
   - If no organization exists for user's email domain, one is created
   - First user from a domain becomes organization admin

2. **Brand-Organization Relationship**:
   - All brands belong to an organization
   - A user must have organization membership to create/join brands

3. **Multi-Level Authentication**:
   - User authentication is separate from organization membership
   - Organization membership is separate from brand membership
   - Each level has its own roles and permissions

## Caching Strategy

The brand authentication flow uses Redis extensively:

1. **User Cache**:
   - Users cached by email and ID
   - Cache invalidated during session changes

2. **OTP Cache**:
   - OTP data stored with TTL
   - Failed attempts tracked to prevent brute force

3. **Role Cache**:
   - Roles cached for quick access
   - Used during authentication flow

4. **Organization Cache**:
   - Organization data cached for performance
   - Brand relationships cached with user status

5. **Brand Cache**:
   - Brand data cached with user relationship status
   - Updated during membership changes

## Security Considerations

The brand authentication system employs multiple security measures:

1. **OTP Security**:
   - Rate limiting via cooldown periods
   - Failed attempt tracking
   - OTP expiration (600 seconds)

2. **Session Management**:
   - JWT with configurable expiration
   - Refresh token mechanism
   - Sessions tied to specific users

3. **Role Enforcement**:
   - Strict role checking during authentication
   - Separate endpoints for different user types

4. **Transaction Safety**:
   - Critical operations use database transactions
   - Cache updates synchronized with database updates

5. **Domain Validation**:
   - Email domains validated for brand accounts
   - Organization-domain relationship enforced
