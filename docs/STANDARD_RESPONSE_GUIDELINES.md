# StandardResponse Guidelines for CreatorVerse

This document provides guidelines on how to properly use the `StandardResponse` class along with the associated `StandardResponseModel` in the CreatorVerse Discovery and Profile Analytics service. It includes details on the recent fixes and best practices.

## Table of Contents

- [Overview](#overview)
- [Implementation](#implementation)
- [Issues Fixed](#issues-fixed)
- [Best Practices](#best-practices)
- [Examples](#examples)

## Overview

The `StandardResponse` system provides a consistent way to format API responses across the CreatorVerse Discovery and Profile Analytics service. It consists of:

1. **StandardResponseModel** - A Pydantic model for response validation in FastAPI
2. **StandardResponse** - A utility class for creating JSONResponses with standardized structure
3. **PaginatedResponseModel** - A specialized model for paginated responses
4. **Helper functions** - Additional utilities for specific response patterns

## Implementation

### Response Structure

All API responses follow this standard format:

```json
{
  "status": "success|error",
  "message": "Human-readable message",
  "data": {
    // Response payload - varies by endpoint
  }
}
```

For paginated responses:

```json
{
  "status": "success",
  "message": "Human-readable message",
  "data": {
    "items": [
      // Array of result items
    ],
    "meta": {
      "total_count": 100,
      "page": 1,
      "page_size": 20,
      "total_pages": 5,
      "has_next": true,
      "has_previous": false
      // Additional metadata as needed
    }
  }
}
```

### Key Components

#### StandardResponseModel

```python
class StandardResponseModel(BaseModel, Generic[T]):
    """Pydantic model for standard API responses"""
    status: str = Field(..., description="Response status (success/error)")
    message: str = Field(..., description="Response message")
    data: Optional[T] = Field(None, description="Response payload data")
```

This Pydantic model is used with FastAPI's `response_model` parameter to validate and document responses.

#### StandardResponse

```python
class StandardResponse:
    @staticmethod
    def success(
        data: Any = None, 
        message: str = "Success", 
        status_code: int = 200
    ) -> JSONResponse:
        return JSONResponse(
            status_code=status_code,
            content={
                "status": "success",
                "message": message,
                "data": data
            }
        )

    @staticmethod
    def error(
        message: str = "Error occurred", 
        status_code: int = 400, 
        data: Optional[Any] = None,
        error_code: Optional[str] = None,
        details: Optional[Any] = None
    ) -> JSONResponse:
        error_data = data if data is not None else {}
        if isinstance(error_data, dict):
            if error_code:
                error_data["error_code"] = error_code
            if details:
                error_data["details"] = details
        
        return JSONResponse(
            status_code=status_code,
            content={
                "status": "error",
                "message": message,
                "data": error_data
            }
        )
```

This utility class creates FastAPI `JSONResponse` objects with the standardized structure.

## Issues Fixed

We encountered and resolved the following issues with the StandardResponse implementation:

1. **Invalid Response Model Usage**

   **Issue**: `StandardResponse` was incorrectly used as a response model in FastAPI route decorators, causing validation errors.
   
   **Fix**: Updated all route decorators to use `StandardResponseModel` for response validation instead:
   
   ```python
   # Before (incorrect)
   @router.get("/endpoint", response_model=StandardResponse)
   
   # After (correct)
   @router.get("/endpoint", response_model=StandardResponseModel[dict])
   ```

2. **Missing Type Parameters**

   **Issue**: `StandardResponseModel` was used without type parameters, causing validation issues.
   
   **Fix**: Added appropriate generic type parameters for each endpoint:
   
   ```python
   @router.get("/endpoint", response_model=StandardResponseModel[dict])
   @router.get("/profiles", response_model=StandardResponseModel[List[ProfileSchema]])
   ```

3. **Exception Handler Issues**

   **Issue**: Exception handlers were trying to call a non-existent `.dict()` method on `JSONResponse` objects.
   
   **Fix**: Updated exception handlers to directly return `StandardResponse.error()` which already produces a `JSONResponse`:
   
   ```python
   # Before (incorrect)
   return JSONResponse(
       status_code=exc.status_code,
       content=StandardResponse.error(
           message=exc.message,
           error_code=exc.error_code
       ).dict()
   )
   
   # After (correct)
   return StandardResponse.error(
       message=exc.message,
       status_code=exc.status_code,
       error_code=exc.error_code
   )
   ```

4. **Missing Error Parameters**

   **Issue**: The `error_code` and `details` parameters were not properly handled in `StandardResponse.error()`.
   
   **Fix**: Updated the `StandardResponse.error()` method to handle these parameters correctly:
   
   ```python
   @staticmethod
   def error(
       message: str = "Error occurred", 
       status_code: int = 400, 
       data: Optional[Any] = None,
       error_code: Optional[str] = None,
       details: Optional[Any] = None
   ) -> JSONResponse:
       error_data = data if data is not None else {}
       if isinstance(error_data, dict):
           if error_code:
               error_data["error_code"] = error_code
           if details:
               error_data["details"] = details
       
       return JSONResponse(
           status_code=status_code,
           content={
               "status": "error",
               "message": message,
               "data": error_data
           }
       )
   ```

## Best Practices

1. **Route Response Models**

   Always use `StandardResponseModel` with appropriate type parameters in route decorators:

   ```python
   @router.get("/endpoint", response_model=StandardResponseModel[dict])
   @router.post("/profiles", response_model=StandardResponseModel[ProfileResponse])
   ```

2. **Route Handlers**

   Return `StandardResponse.success()` or `StandardResponse.error()` from route handlers:

   ```python
   async def get_data():
       try:
           data = await fetch_data()
           return StandardResponse.success(
               data=data,
               message="Data retrieved successfully"
           )
       except Exception as e:
           return StandardResponse.error(
               message=str(e),
               status_code=500
           )
   ```

3. **Exception Handlers**

   Use `StandardResponse.error()` directly in exception handlers:

   ```python
   async def exception_handler(request: Request, exc: CustomException):
       return StandardResponse.error(
           message=exc.message,
           status_code=exc.status_code,
           error_code=exc.error_code
       )
   ```

4. **Paginated Responses**

   Use `PaginatedResponse.paginated_success()` for paginated data:

   ```python
   return PaginatedResponse.paginated_success(
       data=items,
       total_count=total,
       page=page,
       page_size=page_size,
       message="Items retrieved successfully"
   )
   ```

5. **Error Codes**

   Use consistent error codes for specific error types:

   ```python
   return StandardResponse.error(
       message="Validation failed",
       status_code=400,
       error_code="VALIDATION_ERROR",
       details=validation_errors
   )
   ```

## Examples

### Basic Success Response

```python
@router.get("/health", response_model=StandardResponseModel[dict])
async def health_check():
    return StandardResponse.success(
        data={"status": "healthy"},
        message="Service is healthy"
    )
```

### Error Response

```python
@router.get("/resource/{id}", response_model=StandardResponseModel[dict])
async def get_resource(id: str):
    try:
        resource = await find_resource(id)
        if not resource:
            return StandardResponse.error(
                message=f"Resource {id} not found",
                status_code=404,
                error_code="RESOURCE_NOT_FOUND"
            )
        return StandardResponse.success(
            data=resource,
            message="Resource retrieved successfully"
        )
    except Exception as e:
        return StandardResponse.error(
            message=str(e),
            status_code=500,
            error_code="INTERNAL_ERROR"
        )
```

### Paginated Response

```python
@router.get("/items", response_model=StandardResponseModel[dict])
async def get_items(page: int = 1, page_size: int = 20):
    items, total = await fetch_paginated_items(page, page_size)
    return PaginatedResponse.paginated_success(
        data=items,
        total_count=total,
        page=page,
        page_size=page_size,
        message=f"Retrieved {len(items)} items"
    )
```

---

This document serves as a guide for maintaining consistent API responses across the CreatorVerse Discovery and Profile Analytics service. Following these guidelines will ensure a uniform experience for API consumers and simplify error handling.
