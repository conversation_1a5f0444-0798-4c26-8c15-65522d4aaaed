# Redis Client Guide

## Overview

The `RedisClient` class is a singleton wrapper around the `redis.asyncio` library that provides a consistent interface for Redis operations in the CreatorVerse backend. It follows the project's coding standards and integrates with the custom logger and async patterns.

## Architecture

### Singleton Pattern
The `RedisClient` uses a singleton pattern to ensure only one Redis connection pool is maintained throughout the application lifecycle.

```python
from app.core.config import get_locobuzz_redis

# This will always return the same instance
redis_client = get_locobuzz_redis()
```

### Connection Management
- **Automatic connection pooling** via `redis.asyncio`
- **Health checks** every 30 seconds
- **Configurable timeouts** for socket operations
- **Graceful error handling** with logging

## Configuration

The Redis client is configured through `appsettings.json`:

```json
{
  "redis_host": "localhost"
}
```

The client automatically constructs the Redis URL as `redis://{redis_host}:6379`.

## Usage Patterns

### 1. Standard Service Pattern

**✅ Recommended Pattern:**
```python
from app.core.config import get_locobuzz_redis
from app.core.config import APP_CONFIG

class MyService:
    def __init__(self, redis_client=get_locobuzz_redis()):
        self.redis_client = redis_client
        self.logger = APP_CONFIG.logger
    
    async def some_operation(self):
        # Use redis operations here
        result = await self.redis_client.get("some:key")
        return result
```

**❌ Avoid:**
```python
# Don't import redis directly
import redis.asyncio as redis

# Don't use type hints for redis_client parameter
def __init__(self, redis_client: redis.Redis):
```

### 2. Cache-Aside Pattern

The Redis client is designed to work with the cache-aside pattern:

```python
async def get_user_cache_aside(self, user_id: str):
    """Get user with cache-aside pattern"""
    cache_key = f"CreatorVerse:user:{user_id}"
    
    # Try cache first
    cached_data = await self.redis_client.get(cache_key)
    if cached_data:
        return json.loads(cached_data)
    
    # Cache miss - get from database
    user = await self._get_user_from_db(user_id)
    if user:
        # Store in cache with TTL
        await self.redis_client.setex(
            cache_key, 
            3600,  # 1 hour TTL
            json.dumps(user.dict())
        )
    
    return user
```

### 3. Redis Key Naming Convention

**Always follow the pattern:** `CreatorVerse:feature:key`

```python
# ✅ Good examples
user_key = "CreatorVerse:user:12345"
session_key = "CreatorVerse:session:abc123"
rbac_key = "CreatorVerse:rbac:permissions"
otp_key = "CreatorVerse:otp:user:12345"

# ❌ Bad examples  
user_key = "user:12345"          # Missing CreatorVerse prefix
cache_key = "cache_user_data"    # Wrong format
temp_key = "temp:data"           # Missing feature context
```

## Available Operations

### Basic Key-Value Operations

```python
# Set a value
await redis_client.set("CreatorVerse:user:123", "user_data")

# Set with expiration (TTL in seconds)
await redis_client.setex("CreatorVerse:session:abc", 3600, "session_data")

# Get a value
value = await redis_client.get("CreatorVerse:user:123")

# Delete a key
success = await redis_client.delete("CreatorVerse:user:123")
```

### Hash Operations

```python
# Set hash field
await redis_client.hset("CreatorVerse:user:123", "name", "John Doe")

# Get hash field
name = await redis_client.hget("CreatorVerse:user:123", "name")

# Set multiple hash fields
mapping = {"name": "John", "email": "<EMAIL>"}
await redis_client.hmset("CreatorVerse:user:123", mapping)

# Get all hash fields
user_data = await redis_client.hgetall("CreatorVerse:user:123")

# Get multiple hash fields
fields = await redis_client.hmget("CreatorVerse:user:123", ["name", "email"])

# Check if hash field exists
exists = await redis_client.hexists("CreatorVerse:user:123", "name")

# Delete hash fields
await redis_client.hdel("CreatorVerse:user:123", "name", "email")
```

### Set Operations

```python
# Add members to a set
await redis_client.sadd("CreatorVerse:user:123:permissions", "read", "write")

# Get all set members
permissions = await redis_client.smembers("CreatorVerse:user:123:permissions")
```

### Expiration

```python
# Set TTL on existing key (time in seconds)
await redis_client.expire("CreatorVerse:user:123", 3600)
```

### Pipeline Operations

For bulk operations, use pipeline for better performance:

```python
# Get pipeline
pipe = redis_client.pipeline()

# Queue operations (note: these are queued, not executed yet)
await pipe.hset("CreatorVerse:rbac:roles", "admin", "1")
await pipe.hset("CreatorVerse:rbac:roles", "user", "2")
await pipe.expire("CreatorVerse:rbac:roles", 7200)

# Execute all operations atomically
await pipe.execute()
```

## Error Handling

The Redis client automatically handles errors and logs them. All methods return sensible defaults on error:

- `get()` operations return `None` on error
- `set()` operations return `False` on error
- `delete()` operations return `False` on error

```python
# Always check return values
result = await redis_client.get("CreatorVerse:user:123")
if result is None:
    # Either key doesn't exist or there was an error
    # Check logs for error details
    pass

success = await redis_client.set("CreatorVerse:user:123", "data")
if not success:
    # Set operation failed - check logs
    pass
```

## Integration Examples

### 1. RBAC Service Integration

```python
class RBACService:
    def __init__(self, redis_client=get_locobuzz_redis(), db_conn=get_database()):
        self.redis_client = redis_client
        self.db_conn = db_conn
    
    async def cache_user_permissions(self, user_id: str, role_id: str):
        """Cache user permissions using pipeline for efficiency"""
        pipe = self.redis_client.pipeline()
        
        # Get permissions for role from database
        permissions = await self._get_role_permissions_from_db(role_id)
        
        # Cache using pipeline
        key = f"CreatorVerse:rbac:user:{user_id}:permissions"
        await pipe.delete(key)  # Clear existing
        
        if permissions:
            await pipe.sadd(key, *permissions)
            await pipe.expire(key, 3600)  # 1 hour TTL
        
        await pipe.execute()
```

### 2. OTP Manager Integration

```python
class OTPManager:
    def __init__(self, redis_client=get_locobuzz_redis()):
        self.redis_client = redis_client
    
    async def store_otp(self, user_id: str, otp_code: str, ttl: int = 300):
        """Store OTP with TTL"""
        key = f"CreatorVerse:otp:user:{user_id}"
        
        # Store with 5-minute default TTL
        success = await self.redis_client.setex(key, ttl, otp_code)
        
        if success:
            APP_CONFIG.logger.info(f"OTP stored for user {user_id}")
        else:
            APP_CONFIG.logger.error(f"Failed to store OTP for user {user_id}")
        
        return success
    
    async def verify_otp(self, user_id: str, provided_otp: str) -> bool:
        """Verify OTP and delete if correct"""
        key = f"CreatorVerse:otp:user:{user_id}"
        
        stored_otp = await self.redis_client.get(key)
        if not stored_otp:
            return False
        
        if stored_otp == provided_otp:
            # Delete OTP after successful verification
            await self.redis_client.delete(key)
            return True
        
        return False
```

### 3. Session Management

```python
class SessionManager:
    def __init__(self, redis_client=get_locobuzz_redis()):
        self.redis_client = redis_client
    
    async def create_session(self, user_id: str, session_data: dict, ttl: int = 86400):
        """Create user session with 24-hour default TTL"""
        session_id = str(uuid.uuid4())
        key = f"CreatorVerse:session:{session_id}"
        
        # Store session data as JSON
        success = await self.redis_client.setex(
            key, 
            ttl, 
            json.dumps(session_data)
        )
        
        if success:
            # Also store user->session mapping
            user_key = f"CreatorVerse:user:{user_id}:session"
            await self.redis_client.setex(user_key, ttl, session_id)
        
        return session_id if success else None
```

## Best Practices

### 1. Always Use the Singleton
```python
# ✅ Use the configured singleton
redis_client = get_locobuzz_redis()

# ❌ Don't create new instances
redis_client = RedisClient("redis://localhost:6379")
```

### 2. Follow TTL Guidelines
```python
# ✅ Always set appropriate TTLs
await redis_client.setex("CreatorVerse:temp:data", 300, value)  # 5 minutes

# ❌ Avoid keys without TTL unless absolutely necessary
await redis_client.set("CreatorVerse:permanent:data", value)  # No TTL
```

### 3. Use Pipeline for Bulk Operations
```python
# ✅ Use pipeline for multiple operations
pipe = redis_client.pipeline()
for i in range(100):
    await pipe.set(f"CreatorVerse:bulk:{i}", f"value_{i}")
await pipe.execute()

# ❌ Don't use individual operations for bulk data
for i in range(100):
    await redis_client.set(f"CreatorVerse:bulk:{i}", f"value_{i}")
```

### 4. Handle Errors Gracefully
```python
# ✅ Always check return values
result = await redis_client.get(key)
if result is None:
    # Fallback to database or handle appropriately
    result = await self.get_from_database(key)

# ❌ Don't assume operations always succeed
result = await redis_client.get(key)
return json.loads(result)  # Could fail if result is None
```

### 5. Use Appropriate Data Structures
```python
# ✅ Use sets for collections
await redis_client.sadd("CreatorVerse:user:123:roles", "admin", "user")

# ✅ Use hashes for structured data
await redis_client.hmset("CreatorVerse:user:123", {
    "name": "John",
    "email": "<EMAIL>"
})

# ❌ Don't store JSON in strings when hash is more appropriate
user_data = json.dumps({"name": "John", "email": "<EMAIL>"})
await redis_client.set("CreatorVerse:user:123", user_data)
```

## Connection Lifecycle

### Initialization
The Redis client is automatically initialized during application startup in `main.py`:

```python
# In main.py lifespan
redis_client = get_locobuzz_redis()
await redis_client.initialize()
```

### Shutdown
The connection is properly closed during application shutdown:

```python
# In main.py lifespan shutdown
await redis_client.close()
```

## Monitoring and Debugging

### Logging
All Redis operations are automatically logged with trace IDs:

```python
# Logs will include:
# - Operation type (get, set, etc.)
# - Key names
# - Success/failure status
# - Error details if applicable
# - Trace ID for request correlation
```

### Health Checks
The Redis client includes automatic health checking:

- Connection verification on startup
- Periodic health checks every 30 seconds
- Automatic error reporting through logs

## Migration Notes

When migrating from direct Redis usage to this wrapper:

1. Replace direct `redis.asyncio` imports with `get_locobuzz_redis()`
2. Update key naming to follow `CreatorVerse:feature:key` pattern
3. Add proper error handling for all operations
4. Use appropriate data structures (hashes, sets) instead of JSON strings
5. Implement TTLs for all temporary data

## Common Pitfalls

1. **Forgetting TTLs**: Always set expiration for temporary data
2. **Wrong key naming**: Use the standardized naming convention
3. **Not checking return values**: Always handle potential failures
4. **Inefficient bulk operations**: Use pipeline for multiple operations
5. **Creating multiple instances**: Always use the singleton pattern
6. **Missing error handling**: Redis operations can fail silently

This Redis client provides a robust, consistent interface for all Redis operations in the CreatorVerse backend while following the project's architectural patterns and coding standards.
