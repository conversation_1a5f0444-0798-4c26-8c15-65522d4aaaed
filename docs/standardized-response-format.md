# Standardized Response Format

## Overview

All API endpoints in the CreatorVerse backend now use a standardized response format:

```json
{
    "data": any_data_or_null,
    "message": "Response message"
}
```

## Implementation

### For New Endpoints

Use the `StandardResponse` class from `app.utilities.response_handler`:

```python
from app.utilities.response_handler import StandardResponse

@router.get("/users/{user_id}")
async def get_user(user_id: int):
    # Your business logic here
    user_data = {"id": user_id, "name": "<PERSON>"}
    
    return StandardResponse.success(
        data=user_data,
        message="User retrieved successfully"
    )

@router.post("/users")
async def create_user(user_data: dict):
    # Your business logic here
    created_user = {"id": 123, "name": "New User"}
    
    return StandardResponse.created(
        data=created_user,
        message="User created successfully"
    )
```

### Available Methods

- `StandardResponse.success(data, message, status_code=200)` - Success responses
- `StandardResponse.created(data, message)` - 201 responses
- `StandardResponse.error(message, data, status_code=400)` - Error responses  
- `StandardResponse.not_found(message, data)` - 404 responses
- `StandardResponse.unauthorized(message, data)` - 401 responses
- `StandardResponse.forbidden(message, data)` - 403 responses
- `StandardResponse.internal_server_error(message, data)` - 500 responses

### Exception Handling

All exceptions are automatically handled by the global exception handlers and return the standardized format:

- `CreatorVerseError` - Custom application exceptions
- `HTTPException` - FastAPI HTTP exceptions
- `RequestValidationError` - Validation errors
- `Exception` - General unhandled exceptions

### Migration Notes

- Legacy functions `create_success_response` and `create_error_response` are still available for backward compatibility
- All new endpoints should use `StandardResponse` class
- Existing endpoints can be gradually migrated to use the new format

### Example Responses

#### Success Response
```json
{
    "data": {"id": 1, "name": "John Doe"},
    "message": "User retrieved successfully"
}
```

#### Error Response
```json
{
    "data": null,
    "message": "User not found"
}
```

#### Validation Error Response
```json
{
    "data": [{"field": "email", "message": "Invalid email format"}],
    "message": "Validation error"
}
```
