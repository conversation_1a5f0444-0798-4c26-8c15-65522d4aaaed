# Comprehensive Guide to CreatorVerse Async Logger

This guide explains how to leverage the async logger module in the CreatorVerse microservice architecture for structured, contextual, and trace-aware logging.

## Table of Contents
1. [Introduction](#introduction)
2. [Logger Components](#logger-components)
3. [Trace ID System](#trace-id-system)
4. [Basic Usage](#basic-usage)
5. [Advanced Features](#advanced-features)
6. [Logging Best Practices](#logging-best-practices)
7. [Structured Logging Patterns](#structured-logging-patterns)
8. [Performance Considerations](#performance-considerations)

## Introduction

The async logger module (`app/core_helper/async_logger.py`) provides a comprehensive logging solution with:

- Async and sync logging methods
- Structured logging with JSON formatting
- Trace ID propagation throughout request lifecycles
- Performance metrics tracking
- FastAPI and Uvicorn integration

## Logger Components

### Key Classes and Functions

1. **FastAPILogger**: Main logger class with synchronous and asynchronous methods
2. **JsonFormatter**: Formats logs as JSON for structured logging
3. **StructuredFormatter**: Formats logs in a standardized text pattern
4. **with_trace_id**: Decorator to ensure trace ID propagation
5. **log_execution_time**: Decorator to track performance

## Trace ID System

### Trace ID Generation and Propagation

The trace ID system uses context variables to maintain a trace ID across asynchronous contexts, ensuring all logs from a single request chain are connected.

```python
trace_id_var = contextvars.ContextVar("trace_id", default=None)
```

### Working with Trace IDs

#### 1. Manual Trace ID Management

```python
# Set a trace ID explicitly
token = set_trace_id("my-custom-trace-id")

# Log with the trace ID
logger.info("This log will have the trace ID")

# Reset when done
reset_trace_id(token)
```

#### 2. Using the with_trace_id Decorator

```python
@with_trace_id
async def handle_request():
    # Automatically inherits trace ID from context
    # or generates a new one if none exists
    logger.info("Will have trace ID automatically")
    
    # All logs in this function and any functions it calls
    # will have the same trace ID
    result = await process_data()
    return result
```

#### 3. Structured Trace ID Generation

```python
# Generate a structured trace ID with service prefix and environment
trace_id = generate_structured_trace_id(
    prefix="USER-SVC", 
    environment="prod",
    request_info="user-123"
)
```

## Basic Usage

### Creating a Logger Instance

```python
from app.core_helper.async_logger import create_logger, LogType, LogLevel
import sys

# Basic logger setup
logger = create_logger(
    service_name="user-service",
    sys_module=sys,
    log_level=LogLevel.INFO,
    log_type=LogType.JSON,
    trace_id_prefix="USER",
    environment="dev"
)
```

### Synchronous Logging

```python
# Basic logging levels
logger.debug("Debug information, not visible in production")
logger.info("Standard information message")
logger.warning("Warning about potential issues")
logger.error("Error information", exc_info=True)  # Includes traceback
logger.critical("Critical system failure")

# With additional context
logger.info("User registered", extra={"user_id": 123, "email": "<EMAIL>"})
```

### Asynchronous Logging

```python
# Async versions of standard methods
await logger.adebug("Debug message in async context")
await logger.ainfo("Information in async context")
await logger.awarning("Warning in async context")
await logger.aerror("Error in async context", exc_info=True)
await logger.acritical("Critical issue in async context")
```

## Advanced Features

### FastAPI Integration

```python
from fastapi import FastAPI
from app.core_helper.async_logger import setup_fastapi_logging
import sys

app = FastAPI()
logger = setup_fastapi_logging(
    app, 
    "api-service", 
    sys,
    log_level="INFO",
    log_type="json"
)

# Now all requests will be automatically logged with trace IDs
```

### Performance Tracking

```python
@log_execution_time(logger, "database_operation")
async def fetch_user_data(user_id: int):
    # Function execution time will be logged
    # with operation name "database_operation"
    await db.get_user(user_id)
    return user

# Manual performance logging
start_time = time.time()
result = process_data()
duration = time.time() - start_time
logger.log_performance("data_processing", duration, items_processed=len(result))
```

### Structured Error Logging

```python
try:
    # Some operation that might fail
    process_data()
except Exception as e:
    # Log with contextual information
    logger.log_error_with_context(e, {
        "operation": "data_processing",
        "user_id": user_id,
        "data_source": source
    })
```

### HTTP Request Logging

```python
# Manually log HTTP requests
logger.log_request(
    method="GET",
    url="/api/users/123",
    status_code=200,
    duration=0.045,
    user_id=123,
    client_ip="***********"
)
```

## Logging Best Practices

### 1. Consistent Log Levels

- **DEBUG**: Detailed information for debugging purposes
- **INFO**: General information about application operation
- **WARNING**: Issues that might cause problems or unexpected behavior
- **ERROR**: Errors that prevented an operation from completing
- **CRITICAL**: Critical failures requiring immediate attention

### 2. Contextual Information

Always include relevant context in logs:

```python
# Good
logger.info(
    "User checkout completed", 
    extra={
        "user_id": 123, 
        "order_id": "ORD-456",
        "amount": 99.99,
        "currency": "USD",
        "payment_method": "credit_card"
    }
)

# Avoid
logger.info("Checkout completed")  # Missing context
```

### 3. Using Trace IDs Effectively

- Ensure all related operations share the same trace ID
- Use the `with_trace_id` decorator on entry point functions
- Include request-specific information in trace ID generation

## Structured Logging Patterns

### 1. Operation Start/End Pattern

```python
@with_trace_id
async def process_payment(payment_data):
    logger.info("Payment processing started", extra={"payment_id": payment_data.id})
    
    # Processing logic
    
    logger.info("Payment processing completed", extra={
        "payment_id": payment_data.id,
        "status": "success",
        "amount": payment_data.amount
    })
```

### 2. State Change Pattern

```python
logger.info("User state changed", extra={
    "user_id": user.id,
    "previous_state": previous_state,
    "new_state": new_state,
    "reason": reason
})
```

### 3. Event-Based Pattern

```python
logger.info("Event occurred", extra={
    "event_type": "user_login",
    "user_id": user.id,
    "ip_address": request.client.host,
    "user_agent": request.headers.get("user-agent")
})
```

## Performance Considerations

### 1. Async vs Sync Logging

- Use async logging methods (`ainfo`, `adebug`, etc.) in async contexts
- Use standard methods (`info`, `debug`, etc.) in synchronous code

### 2. Log Level Filtering

- Only log what's necessary for the current environment
- Use DEBUG sparingly in production
- Configure different log levels for different environments

### 3. Batch Logging for High-Volume Operations

```python
# Instead of logging each item:
async def process_batch(items):
    for item in items:
        await process_item(item)
        # DON'T do this in tight loops
        # await logger.ainfo(f"Processed item {item.id}")
    
    # DO log summary information
    await logger.ainfo(
        "Batch processing completed", 
        extra={
            "items_count": len(items),
            "successful_items": successful_count,
            "failed_items": failed_count
        }
    )
```

---

By following these patterns and practices, you can create a consistent, traceable, and informative logging system throughout your CreatorVerse microservices.
