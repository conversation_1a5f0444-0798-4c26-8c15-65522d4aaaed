# CreatorVerse Async Logger: Practical Examples

This document provides practical examples of using the async logger module specifically within the CreatorVerse microservices architecture, focusing on real-world use cases and code patterns.

## Key Logging Scenarios in CreatorVerse

## 1. API Endpoint Logging

### Request Entry and Exit Points

```python
from app.core_helper.async_logger import with_trace_id
from app.core.config import APP_CONFIG

@router.get("/users/{user_id}")
@with_trace_id
async def get_user_endpoint(user_id: int, db_conn = Depends(get_database)):
    """Get user details endpoint."""
    # Log request received with trace ID
    APP_CONFIG.logger.info(f"Received request for user", extra={
        "user_id": user_id,
        "endpoint": "/users/{user_id}",
        "method": "GET"
    })
    
    try:
        # Process the request
        user = await user_service.get_user(db_conn, user_id)
        
        # Log successful response
        APP_CONFIG.logger.info(f"Request completed successfully", extra={
            "user_id": user_id,
            "status_code": 200
        })
        
        return user
    except UserNotFoundException as e:
        # Log not found error
        APP_CONFIG.logger.warning(f"User not found", extra={
            "user_id": user_id,
            "error": str(e)
        })
        raise HTTPException(status_code=404, detail="User not found")
    except Exception as e:
        # Log unexpected error
        APP_CONFIG.logger.error("Error processing request", extra={
            "user_id": user_id,
            "error": str(e),
            "error_type": type(e).__name__
        }, exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")
```

## 2. Database Operations Logging

### Tracking Database Queries with Performance Metrics

```python
from app.core_helper.async_logger import with_trace_id, log_execution_time
from app.core.config import APP_CONFIG
import time

class UserRepository:
    @with_trace_id
    @log_execution_time(APP_CONFIG.logger, "db_query_user_by_email")
    async def get_user_by_email(self, db_conn, email: str):
        """Get user by email with performance tracking."""
        try:
            # The decorator will log the execution time automatically
            return await db_conn.query(User).filter(User.email == email).first()
        except Exception as e:
            APP_CONFIG.logger.error("Database error when fetching user by email", extra={
                "email": email,
                "error": str(e)
            })
            raise
```

## 3. Redis Cache Operations

### Cache-Aside Pattern with Logging

```python
from app.core_helper.async_logger import with_trace_id
from app.core.config import APP_CONFIG
from app.core.redis_keys import RedisKeys
import json

class UserService:
    @with_trace_id
    async def get_user_by_id_cached(self, db_conn, redis_client, user_id: int):
        """Get user by ID with cache-aside pattern."""
        # Create the cache key following CreatorVerse pattern
        cache_key = RedisKeys.get_user_cache_key(user_id)
        
        # Try to get from cache first
        try:
            cached_data = await redis_client.get(cache_key)
            if cached_data:
                APP_CONFIG.logger.info("Cache hit for user", extra={
                    "user_id": user_id,
                    "cache_key": cache_key
                })
                return json.loads(cached_data)
                
        except Exception as e:
            # Log cache error but don't fail the request
            APP_CONFIG.logger.warning("Redis error when accessing cache", extra={
                "user_id": user_id,
                "cache_key": cache_key,
                "error": str(e)
            })
        
        # Cache miss, get from database
        try:
            APP_CONFIG.logger.info("Cache miss for user", extra={
                "user_id": user_id,
                "cache_key": cache_key
            })
            
            user = await db_conn.query(User).filter(User.id == user_id).first()
            
            if user:
                # Cache the result
                try:
                    user_data = user.dict()
                    await redis_client.set(
                        cache_key,
                        json.dumps(user_data),
                        expire=3600  # 1 hour
                    )
                    APP_CONFIG.logger.info("User data cached", extra={
                        "user_id": user_id,
                        "cache_key": cache_key,
                        "ttl": 3600
                    })
                except Exception as e:
                    APP_CONFIG.logger.warning("Failed to cache user data", extra={
                        "user_id": user_id,
                        "error": str(e)
                    })
                    
            return user
        except Exception as e:
            APP_CONFIG.logger.error("Database error when fetching user", extra={
                "user_id": user_id,
                "error": str(e)
            })
            raise
```

## 4. Authentication Flows

### Logging User Authentication Events

```python
from app.core_helper.async_logger import with_trace_id
from app.core.config import APP_CONFIG

class AuthService:
    @with_trace_id
    async def authenticate_user(self, db_conn, email: str, password: str):
        """Authenticate a user and log the attempt."""
        APP_CONFIG.logger.info("Authentication attempt", extra={
            "email": email,
            # Never log passwords, even hashed ones
            "attempt_time": datetime.now().isoformat()
        })
        
        try:
            user = await self.get_user_by_email(db_conn, email)
            
            if not user or not self.verify_password(password, user.password_hash):
                APP_CONFIG.logger.warning("Failed authentication attempt", extra={
                    "email": email,
                    "reason": "Invalid credentials"
                })
                return None
                
            if not user.is_active:
                APP_CONFIG.logger.warning("Authentication attempt for inactive user", extra={
                    "user_id": user.id,
                    "email": email
                })
                return None
                
            # Successful login
            APP_CONFIG.logger.info("User authenticated successfully", extra={
                "user_id": user.id,
                "email": email
            })
            
            return user
            
        except Exception as e:
            APP_CONFIG.logger.error("Error during authentication", extra={
                "email": email,
                "error": str(e)
            })
            raise
```

## 5. Influencer and Brand Registration

### Logging Registration Process

```python
from app.core_helper.async_logger import with_trace_id
from app.core.config import APP_CONFIG

class RegistrationService:
    @with_trace_id
    async def register_influencer(self, db_conn, redis_client, registration_data):
        """Register a new influencer with detailed logging."""
        # Log the registration attempt
        APP_CONFIG.logger.info("Influencer registration attempt", extra={
            "email": registration_data.email,
            "platform": registration_data.platform
        })
        
        try:
            # Check if email already exists using bloom filter
            bloom_manager = get_bloom_filter_manager(redis_client)
            
            if bloom_manager.check_email_exists(registration_data.email):
                APP_CONFIG.logger.info("Email might already exist in system", extra={
                    "email": registration_data.email,
                    "check_type": "bloom_filter"
                })
                
                # Double check in database for certainty
                existing_user = await self.get_user_by_email(db_conn, registration_data.email)
                if existing_user:
                    APP_CONFIG.logger.warning("Registration attempt with existing email", extra={
                        "email": registration_data.email,
                        "existing_user_id": existing_user.id
                    })
                    return {"success": False, "error": "Email already registered"}
            
            # Create the new user
            new_user = await self.create_influencer_user(db_conn, registration_data)
            
            # Add to bloom filter
            bloom_manager.add_email(registration_data.email)
            
            # Log successful registration
            APP_CONFIG.logger.info("Influencer registered successfully", extra={
                "user_id": new_user.id,
                "email": new_user.email,
                "influencer_id": new_user.influencer_id
            })
            
            return {"success": True, "user_id": new_user.id}
            
        except Exception as e:
            APP_CONFIG.logger.error("Error during influencer registration", extra={
                "email": registration_data.email,
                "error": str(e)
            }, exc_info=True)
            raise
```

## 6. Bloom Filter Integration

### Logging Bloom Filter Operations

```python
from app.core_helper.async_logger import with_trace_id
from app.core.config import APP_CONFIG

@with_trace_id
async def initialize_email_bloom_filter(db_conn, redis_client):
    """Initialize and populate the email bloom filter."""
    APP_CONFIG.logger.info("Starting email bloom filter initialization")
    
    try:
        bloom_manager = get_bloom_filter_manager(redis_client)
        stats = bloom_manager.get_email_filter_stats()
        
        if stats.get("element_count", 0) > 0:
            APP_CONFIG.logger.info("Email bloom filter already populated", extra={
                "element_count": stats["element_count"],
                "filter_capacity": stats["capacity"],
                "false_positive_probability": stats["current_false_positive_probability"]
            })
            return True
            
        # Populate from database
        result = bloom_manager.populate_email_filter_from_database(db_conn)
        
        APP_CONFIG.logger.info("Email bloom filter populated", extra={
            "status": result["status"],
            "emails_added": result["emails_added"],
            "element_count": result["element_count"]
        })
        
        return True
    except Exception as e:
        APP_CONFIG.logger.error("Error initializing email bloom filter", extra={
            "error": str(e)
        }, exc_info=True)
        return False
```

## 7. Service Startup Logging

### Application Lifecycle Events

```python
from app.core_helper.async_logger import with_trace_id
from app.core.config import APP_CONFIG

@app.on_event("startup")
@with_trace_id
async def startup_event():
    """Log service startup with component initialization status."""
    APP_CONFIG.logger.info("Starting CreatorVerse user microservice", extra={
        "version": APP_CONFIG.version,
        "environment": APP_CONFIG.environment
    })
    
    # Initialize components
    components = {
        "database": False,
        "redis": False,
        "bloom_filters": False,
        "email_service": False
    }
    
    try:
        # Database initialization
        APP_CONFIG.logger.info("Initializing database connection")
        db_conn = get_database()
        await db_conn.initialize()
        components["database"] = True
        
        # Redis initialization
        APP_CONFIG.logger.info("Initializing Redis connection")
        redis_client = get_locobuzz_redis()
        await redis_client.initialize()
        components["redis"] = True
        
        # Bloom filters
        APP_CONFIG.logger.info("Initializing bloom filters")
        await initialize_email_bloom_filter(db_conn, redis_client)
        components["bloom_filters"] = True
        
        # Email service
        APP_CONFIG.logger.info("Initializing email service")
        await initialize_email_templates()
        components["email_service"] = True
        
        APP_CONFIG.logger.info("Service startup complete", extra={
            "component_status": components
        })
        
    except Exception as e:
        APP_CONFIG.logger.critical("Service startup failed", extra={
            "error": str(e),
            "component_status": components
        }, exc_info=True)
        raise
```

## 8. Background Tasks with Logging

### Logging in Background Task Execution

```python
from fastapi.background import BackgroundTasks
from app.core_helper.async_logger import with_trace_id, get_trace_id, set_trace_id
from app.core.config import APP_CONFIG

@router.post("/notifications/send")
@with_trace_id
async def send_notification_endpoint(
    notification_data: NotificationCreate,
    background_tasks: BackgroundTasks,
    db_conn = Depends(get_database)
):
    """API endpoint that schedules a notification in a background task."""
    # Get the current trace ID to propagate to background task
    current_trace_id = get_trace_id()
    
    APP_CONFIG.logger.info("Scheduling notification delivery", extra={
        "notification_type": notification_data.type,
        "recipient_count": len(notification_data.recipients)
    })
    
    # Schedule background task with the same trace ID
    background_tasks.add_task(
        process_notification_with_trace,
        notification_data,
        db_conn,
        current_trace_id
    )
    
    return {"status": "notification scheduled"}

# Helper function to maintain trace ID in background task
async def process_notification_with_trace(
    notification_data: NotificationCreate,
    db_conn,
    trace_id: str
):
    """Process notification in background with same trace ID as the request."""
    # Set the trace ID from the originating request
    token = set_trace_id(trace_id)
    
    try:
        APP_CONFIG.logger.info("Background task: processing notification", extra={
            "notification_type": notification_data.type,
            "recipient_count": len(notification_data.recipients)
        })
        
        # Process notification
        # ...
        
        APP_CONFIG.logger.info("Notification processing complete", extra={
            "notification_type": notification_data.type,
            "status": "sent"
        })
    except Exception as e:
        APP_CONFIG.logger.error("Error processing notification", extra={
            "notification_type": notification_data.type,
            "error": str(e)
        }, exc_info=True)
    finally:
        # Reset the trace ID context
        reset_trace_id(token)
```

## Best Practices Summary

1. **Always Use `with_trace_id` Decorator**: Apply this decorator to API endpoints and key service functions to ensure trace ID propagation.

2. **Propagate Trace IDs to Background Tasks**: When using FastAPI background tasks, explicitly pass and set the trace ID.

3. **Structured Extra Data**: Use the `extra` parameter to include structured data with every log.

4. **Appropriate Log Levels**: Use the correct log level based on the severity and importance of the event.

5. **Performance Tracking**: Use the `log_execution_time` decorator on database and external service calls.

6. **Error Context**: Always include relevant context when logging errors to aid debugging.

7. **Sensitive Information**: Never log sensitive data like passwords, tokens, or personal information.

8. **Application Lifecycle Events**: Log important application lifecycle events (startup, shutdown, etc.) with component status.

9. **Cache Operations**: Log cache hits, misses, and errors with relevant keys and operations.

10. **Business Events**: Log important business events with detailed context to aid in auditing and analytics.
