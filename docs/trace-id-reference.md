# Trace ID Reference Guide for CreatorVerse

This reference sheet provides a concise overview of the trace ID system in the CreatorVerse microservices architecture, focusing on implementation patterns and best practices.

## What is a Trace ID?

A trace ID is a unique identifier that tracks the flow of a request through multiple services, functions, and asynchronous operations. In CreatorVerse, trace IDs follow a structured format:

```
[PREFIX]-[ENV]-[TIMESTAMP]-[SEQUENCE]-[REQUEST_INFO]-[RANDOM]
```

Example: `USER-dev-************-0042-8f3d2a-a7b3c9d2`

## Core Functions

| Function | Description | Example |
|----------|-------------|---------|
| `get_trace_id()` | Get current trace ID or generate new one | `trace_id = get_trace_id()` |
| `set_trace_id(id)` | Set trace ID explicitly | `token = set_trace_id("my-trace-id")` |
| `reset_trace_id(token)` | Reset trace ID | `reset_trace_id(token)` |
| `@with_trace_id` | Decorator to propagate or generate trace ID | `@with_trace_id` |
| `generate_structured_trace_id()` | Generate a new structured trace ID | `generate_structured_trace_id(prefix="API")` |

## Implementation Patterns

### 1. API Endpoint Tracing

```python
from app.core_helper.async_logger import with_trace_id
from app.core.config import APP_CONFIG

@router.get("/api/resource/{id}")
@with_trace_id
async def get_resource(id: str):
    # The trace ID is automatically set and available in logs
    APP_CONFIG.logger.info(f"Processing request for resource", extra={"resource_id": id})
    return {"id": id}
```

### 2. Service-to-Service Communication

```python
from app.core_helper.async_logger import get_trace_id
import httpx

async def call_external_service(data):
    # Get current trace ID to include in the request
    current_trace_id = get_trace_id()
    
    # Pass the trace ID in headers
    headers = {"X-Trace-ID": current_trace_id}
    
    async with httpx.AsyncClient() as client:
        response = await client.post(
            "https://other-service/api/endpoint", 
            json=data,
            headers=headers
        )
    
    return response.json()
```

### 3. Background Tasks with Trace ID

```python
from fastapi.background import BackgroundTasks
from app.core_helper.async_logger import get_trace_id, set_trace_id, reset_trace_id

@router.post("/tasks")
@with_trace_id
async def create_task(task_data: TaskData, background_tasks: BackgroundTasks):
    # Get current trace ID
    current_trace_id = get_trace_id()
    
    # Add background task with trace ID
    background_tasks.add_task(
        process_task_with_trace,
        task_data,
        current_trace_id
    )
    
    return {"status": "scheduled"}

async def process_task_with_trace(task_data: TaskData, trace_id: str):
    # Set trace ID in the background task
    token = set_trace_id(trace_id)
    
    try:
        # All logs will have the same trace ID as the original request
        APP_CONFIG.logger.info("Processing background task")
        # Process task...
    finally:
        # Reset trace ID
        reset_trace_id(token)
```

### 4. Database Transaction Tracing

```python
from app.core_helper.async_logger import with_trace_id
from sqlalchemy.ext.asyncio import AsyncSession

@with_trace_id
async def create_user_transaction(db: AsyncSession, user_data: UserCreate):
    # Trace ID is propagated through the database transaction
    APP_CONFIG.logger.info("Starting user creation transaction")
    
    async with db.begin():
        # Create user
        db_user = User(**user_data.dict())
        db.add(db_user)
        
        # Create profile
        profile = UserProfile(user_id=db_user.id)
        db.add(profile)
        
    APP_CONFIG.logger.info("User creation transaction completed")
    return db_user
```

### 5. Scheduled Tasks with Trace ID

```python
from app.core_helper.async_logger import with_trace_id, generate_structured_trace_id
from apscheduler.schedulers.asyncio import AsyncIOScheduler

@with_trace_id
async def scheduled_cleanup_task():
    # Generate custom trace ID for scheduled task with descriptive prefix
    trace_id = generate_structured_trace_id(
        prefix="CRON",
        request_info="cleanup"
    )
    token = set_trace_id(trace_id)
    
    try:
        APP_CONFIG.logger.info("Starting scheduled cleanup task")
        # Perform cleanup operations
    finally:
        reset_trace_id(token)

# Add to scheduler
scheduler = AsyncIOScheduler()
scheduler.add_job(scheduled_cleanup_task, 'interval', hours=24)
```

### 6. Error Handling with Trace ID

```python
from app.core_helper.async_logger import with_trace_id
from fastapi import HTTPException

@router.post("/api/resource")
@with_trace_id
async def create_resource(data: ResourceCreate):
    try:
        # Process resource creation
        resource = await resource_service.create(data)
        return resource
    except ValidationError as e:
        # Log error with trace ID
        APP_CONFIG.logger.warning("Validation error", extra={
            "error": str(e),
            "data": data.dict()
        })
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        # Log unexpected error with trace ID
        APP_CONFIG.logger.error("Unexpected error creating resource", extra={
            "error": str(e),
            "error_type": type(e).__name__
        }, exc_info=True)
        
        # Include trace ID in the error response for easier debugging
        trace_id = get_trace_id()
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error. Reference: {trace_id}"
        )
```

### 7. Worker Tasks with Trace ID

```python
import dramatiq
from app.core_helper.async_logger import generate_structured_trace_id, set_trace_id, reset_trace_id

@dramatiq.actor
def process_large_job(job_id: str, params: dict):
    # Generate a new trace ID for this worker job
    trace_id = generate_structured_trace_id(
        prefix="WORKER", 
        request_info=f"job-{job_id}"
    )
    token = set_trace_id(trace_id)
    
    try:
        APP_CONFIG.logger.info("Worker started processing job", extra={
            "job_id": job_id,
            "params": params
        })
        
        # Process the job
        
        APP_CONFIG.logger.info("Worker completed job", extra={
            "job_id": job_id,
            "status": "completed"
        })
    except Exception as e:
        APP_CONFIG.logger.error("Worker job failed", extra={
            "job_id": job_id,
            "error": str(e)
        }, exc_info=True)
    finally:
        reset_trace_id(token)
```

## Trace ID Best Practices

1. **Use the `@with_trace_id` Decorator**: Apply this to all API endpoints and key service functions.

2. **Explicit Trace ID Management**: For background tasks, workers, or scheduled jobs, explicitly pass and set the trace ID.

3. **Include in Error Responses**: Include the trace ID in error responses to help with debugging.

4. **Structured Generation**: Use the `generate_structured_trace_id` function with meaningful prefixes and request information.

5. **Service Communication**: Pass the trace ID in headers when making requests to other services.

6. **Reset After Use**: Always reset the trace ID context when done with explicit management.

7. **Descriptive Prefixes**: Use service-specific prefixes (e.g., "USER", "AUTH", "API").

8. **Include in Logs**: The trace ID is automatically included in logs when using the CreatorVerse logger.

## Analyzing Logs by Trace ID

To find all logs related to a specific request:

```bash
# Using grep on log files
grep "trace_id=USER-dev-************-0042" /path/to/logs/*.log

# Using jq with JSON logs
cat /path/to/logs/app.log | jq 'select(.trace_id == "USER-dev-************-0042")'

# In log aggregation tools like Elasticsearch
trace_id: USER-dev-************-0042
```

---

Following these patterns ensures complete traceability across your CreatorVerse microservices ecosystem, making debugging and monitoring much more effective.
