"""
Pydantic models for Phyllo-compatible API responses.
These models mimic the structure of Phyllo's API responses.
"""

from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any, Union
from datetime import datetime
from enum import Enum

# Enums
class PlatformType(str, Enum):
    INSTAGRAM = "INSTAGRAM"
    YOUTUBE = "YOUTUBE"
    TIKTOK = "TIKTOK"
    TWITTER = "TWITTER"
    TWITCH = "TWITCH"

class GenderType(str, Enum):
    MALE = "MALE"
    FEMALE = "FEMALE"

class AccountStatus(str, Enum):
    CONNECTED = "CONNECTED"
    DISCONNECTED = "DISCONNECTED"
    ERROR = "ERROR"

class ContentType(str, Enum):
    VIDEO = "VIDEO"
    IMAGE = "IMAGE"
    REEL = "REEL"
    STORY = "STORY"

# Base models
class PhylloWorkPlatform(BaseModel):
    id: str
    name: str
    logo_url: Optional[str] = None

class PhylloProfile(BaseModel):
    id: str
    work_platform: PhylloWorkPlatform
    external_id: str
    platform_username: str
    url: Optional[str] = None
    image_url: Optional[str] = None
    full_name: Optional[str] = None
    introduction: Optional[str] = None
    content_count: Optional[int] = None
    is_verified: Optional[bool] = False
    platform_account_type: Optional[str] = None
    gender: Optional[GenderType] = None
    age_group: Optional[str] = None
    language: Optional[str] = None
    follower_count: Optional[int] = None
    subscriber_count: Optional[int] = None
    average_likes: Optional[int] = None
    average_comments: Optional[int] = None
    average_views: Optional[int] = None
    average_reels_views: Optional[int] = None
    engagement_rate: Optional[float] = None
    credibility_score: Optional[float] = None
    created_at: datetime
    updated_at: datetime

class PhylloAccount(BaseModel):
    id: str
    user_id: str
    work_platform: PhylloWorkPlatform
    status: AccountStatus
    connected_at: datetime
    profile: Optional[PhylloProfile] = None

class PhylloUser(BaseModel):
    id: str
    name: str
    email: str
    status: str = "active"
    created_at: datetime
    updated_at: datetime

# Content models
class ContentEngagement(BaseModel):
    like_count: Optional[int] = None
    comment_count: Optional[int] = None
    view_count: Optional[int] = None
    play_count: Optional[int] = None
    save_count: Optional[int] = None

class PhylloContent(BaseModel):
    id: str
    profile_id: str
    type: ContentType
    url: Optional[str] = None
    thumbnail_url: Optional[str] = None
    title: Optional[str] = None
    description: Optional[str] = None
    posted_at: Optional[datetime] = None
    engagement: Optional[ContentEngagement] = None
    is_sponsored: Optional[bool] = False

# Analytics models
class HashtagData(BaseModel):
    name: str
    value: float

class InterestData(BaseModel):
    name: str

class BrandMention(BaseModel):
    name: str
    value: Optional[float] = None

class ReputationHistory(BaseModel):
    month: str
    follower_count: Optional[int] = None
    subscriber_count: Optional[int] = None
    following_count: Optional[int] = None
    average_likes: Optional[int] = None
    average_views: Optional[int] = None
    average_comments: Optional[int] = None
    total_views: Optional[int] = None
    total_likes: Optional[int] = None

class AudienceGenderAge(BaseModel):
    gender: GenderType
    age_range: str
    value: float

class AudienceLanguage(BaseModel):
    language_code: str
    value: float

class AudienceLocation(BaseModel):
    location_name: str
    value: float

class AudienceInterest(BaseModel):
    interest_name: str
    value: float

class AudienceIncome(BaseModel):
    income_range: str
    value: float

class AudienceDevice(BaseModel):
    device_type: str
    value: float

class AudienceAnalytics(BaseModel):
    gender_age: Optional[List[AudienceGenderAge]] = []
    languages: Optional[List[AudienceLanguage]] = []
    locations: Optional[List[AudienceLocation]] = []
    interests: Optional[List[AudienceInterest]] = []
    income: Optional[List[AudienceIncome]] = []
    devices: Optional[List[AudienceDevice]] = []

class PricingData(BaseModel):
    post_type: str
    currency: str = "USD"
    price_min: Optional[int] = None
    price_max: Optional[int] = None

class ContactDetail(BaseModel):
    type: str
    value: str

class ProfileAnalytics(BaseModel):
    profile: PhylloProfile
    top_hashtags: Optional[List[HashtagData]] = []
    top_interests: Optional[List[InterestData]] = []
    top_mentions: Optional[List[BrandMention]] = []
    top_contents: Optional[List[PhylloContent]] = []
    recent_contents: Optional[List[PhylloContent]] = []
    reputation_history: Optional[List[ReputationHistory]] = []
    audience: Optional[AudienceAnalytics] = None
    pricing: Optional[List[PricingData]] = []
    contact_details: Optional[List[ContactDetail]] = []

# Response models
class PhylloResponse(BaseModel):
    success: bool = True
    message: Optional[str] = None
    data: Optional[Any] = None
    error: Optional[str] = None

class PaginatedResponse(BaseModel):
    success: bool = True
    data: List[Any]
    pagination: Dict[str, Any] = {
        "page": 1,
        "limit": 10,
        "total": 0,
        "has_next": False,
        "has_prev": False
    }

# Request models
class CreateUserRequest(BaseModel):
    name: str
    email: str
    external_id: Optional[str] = None

class ConnectAccountRequest(BaseModel):
    user_id: str
    platform: PlatformType
    access_token: str
    refresh_token: Optional[str] = None

class ProfileSearchRequest(BaseModel):
    platform: Optional[PlatformType] = None
    username: Optional[str] = None
    follower_count_min: Optional[int] = None
    follower_count_max: Optional[int] = None
    engagement_rate_min: Optional[float] = None
    engagement_rate_max: Optional[float] = None
    interests: Optional[List[str]] = []
    location: Optional[str] = None
    gender: Optional[GenderType] = None
    age_group: Optional[str] = None
    verified_only: Optional[bool] = False
