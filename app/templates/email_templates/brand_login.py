"""
Email template for brand login OTP verification.
"""


def get_brand_login_otp_email_template(otp: str, email: str) -> str:
    """
    Generate HTML email template for brand login OTP verification.

    Args:
        otp: One-time password
        email: Email address

    Returns:
        str: HTML email content
    """
    return f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>CreatorVerse Business - Login Verification</title>
        <style>
            body {{
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                line-height: 1.6;
                color: #333;
                max-width: 600px;
                margin: 0 auto;
                padding: 20px;
                background-color: #f8fafc;
            }}
            .container {{
                background: white;
                padding: 30px;
                border-radius: 12px;
                box-shadow: 0 4px 20px rgba(0,0,0,0.08);
                border-top: 4px solid #dc2626;
            }}
            .header {{
                text-align: center;
                margin-bottom: 30px;
            }}
            .logo {{
                font-size: 28px;
                font-weight: bold;
                color: #7c3aed;
                margin-bottom: 10px;
            }}
            .business-badge {{
                display: inline-block;
                background: linear-gradient(135deg, #7c3aed, #a855f7);
                color: white;
                padding: 8px 16px;
                border-radius: 20px;
                font-size: 12px;
                font-weight: 600;
                text-transform: uppercase;
                letter-spacing: 1px;
                margin-bottom: 15px;
            }}
            .login-banner {{
                background: linear-gradient(135deg, #dc2626, #ef4444);
                color: white;
                padding: 20px;
                border-radius: 10px;
                text-align: center;
                margin-bottom: 25px;
            }}
            .otp-box {{
                background: #fef2f2;
                border: 3px solid #dc2626;
                border-radius: 10px;
                padding: 25px;
                text-align: center;
                margin: 25px 0;
            }}
            .otp-code {{
                font-size: 36px;
                font-weight: bold;
                color: #dc2626;
                letter-spacing: 6px;
                margin: 15px 0;
                font-family: 'Courier New', monospace;
            }}
            .security-alert {{
                background: #fef3c7;
                border: 2px solid #f59e0b;
                border-radius: 10px;
                padding: 20px;
                margin: 25px 0;
                color: #92400e;
            }}
            .business-security {{
                background: #f0f9ff;
                border-left: 4px solid #0ea5e9;
                padding: 20px;
                margin: 25px 0;
                border-radius: 0 8px 8px 0;
            }}
            .quick-actions {{
                background: #f3f4f6;
                padding: 20px;
                border-radius: 8px;
                margin: 20px 0;
            }}
            .footer {{
                margin-top: 35px;
                padding-top: 25px;
                border-top: 2px solid #e5e7eb;
                text-align: center;
                color: #6b7280;
                font-size: 14px;
            }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <div class="logo">CreatorVerse</div>
                <div class="business-badge">Business Account</div>
                <div class="login-banner">
                    <h1 style="margin: 0; font-size: 22px;">🔐 Business Account Login Verification</h1>
                    <p style="margin: 10px 0 0 0; opacity: 0.95;">Secure access to your brand dashboard</p>
                </div>
            </div>

            <p>Dear Business Partner,</p>

            <p>Someone is attempting to access your CreatorVerse Business account. To ensure the security of your brand's data and campaigns, please use the verification code below to complete your login:</p>

            <div class="otp-box">
                <div style="font-size: 18px; margin-bottom: 15px; color: #dc2626;">🔑 Business Login Code</div>
                <div class="otp-code">{otp}</div>
                <div style="font-size: 14px; color: #6b7280; margin-top: 15px;">⏰ This code expires in 5 minutes</div>
            </div>

            <div class="business-security">
                <h3 style="margin-top: 0; color: #0ea5e9;">🛡️ Enhanced Business Security</h3>
                <p style="margin-bottom: 15px;">Your business account includes advanced security features:</p>
                <ul style="margin: 15px 0; padding-left: 20px;">
                    <li>🔐 Two-factor authentication for all logins</li>
                    <li>📊 Login activity monitoring and alerts</li>
                    <li>🏢 Team member access controls</li>
                    <li>💼 Brand asset protection protocols</li>
                </ul>
            </div>

            <div class="security-alert">
                <h3 style="margin-top: 0;">⚠️ Didn't attempt to log in?</h3>
                <p style="margin-bottom: 15px;"><strong>If you didn't try to access your business account:</strong></p>
                <ul style="margin: 15px 0; padding-left: 20px;">
                    <li>Ignore this email - your account remains secure</li>
                    <li>Consider updating your password immediately</li>
                    <li>Review your team member access permissions</li>
                    <li>Contact our business security team if this occurs repeatedly</li>
                </ul>
            </div>

            <div class="quick-actions">
                <h4 style="margin-top: 0; color: #7c3aed;">🚀 Quick Access After Login</h4>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-top: 15px;">
                    <div>📈 Campaign Dashboard</div>
                    <div>👥 Creator Discovery</div>
                    <div>💰 Budget Management</div>
                    <div>📊 Performance Analytics</div>
                </div>
            </div>

            <p><strong>Business Security Reminder:</strong> Always log out from shared devices and ensure your team members use strong, unique passwords for their accounts.</p>

            <div class="footer">
                <p><strong>CreatorVerse Business Security Team</strong></p>
                <p>© 2025 CreatorVerse. All rights reserved.</p>
                <p>This is an automated security message for business account verification.</p>
                <p>Business Security: <strong><EMAIL></strong> | Emergency: <strong>+1-800-SECURE-BIZ</strong></p>
            </div>
        </div>
    </body>
    </html>
    """
