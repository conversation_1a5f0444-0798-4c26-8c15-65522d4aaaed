"""
Email template for influencer login OTP verification.
"""


def get_influencer_login_otp_email_template(otp: str, email: str) -> str:
    """
    Generate HTML email template for influencer login OTP verification.

    Args:
        otp: One-time password
        email: Email address

    Returns:
        str: HTML email content
    """
    return f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>CreatorVerse - Login Verification</title>
        <style>
            body {{
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                line-height: 1.6;
                color: #333;
                max-width: 600px;
                margin: 0 auto;
                padding: 20px;
                background-color: #f9f9f9;
            }}
            .container {{
                background: white;
                padding: 30px;
                border-radius: 10px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }}
            .header {{
                text-align: center;
                margin-bottom: 30px;
            }}
            .logo {{
                font-size: 28px;
                font-weight: bold;
                color: #6366f1;
                margin-bottom: 10px;
            }}
            .login-banner {{
                background: linear-gradient(135deg, #f59e0b, #ef4444);
                color: white;
                padding: 15px;
                border-radius: 8px;
                text-align: center;
                margin-bottom: 20px;
            }}
            .otp-box {{
                background: #fef7ed;
                border: 2px solid #f59e0b;
                border-radius: 8px;
                padding: 20px;
                text-align: center;
                margin: 20px 0;
            }}
            .otp-code {{
                font-size: 32px;
                font-weight: bold;
                color: #f59e0b;
                letter-spacing: 4px;
                margin: 10px 0;
            }}
            .security-info {{
                background: #fef2f2;
                border-left: 4px solid #ef4444;
                padding: 15px;
                margin: 20px 0;
                color: #991b1b;
            }}
            .warning {{
                background: #fef3cd;
                border: 1px solid #ffeaa7;
                border-radius: 5px;
                padding: 15px;
                margin: 20px 0;
                color: #856404;
            }}
            .footer {{
                margin-top: 30px;
                padding-top: 20px;
                border-top: 1px solid #eee;
                text-align: center;
                color: #666;
                font-size: 14px;
            }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <div class="logo">CreatorVerse</div>
                <div class="login-banner">
                    <h1 style="margin: 0; font-size: 20px;">🔐 Login Verification Required</h1>
                </div>
            </div>

            <p>Hi {email},</p>

            <p>Someone is trying to log in to your CreatorVerse account. To ensure the security of your account, please use the verification code below to complete your login:</p>

            <div class="otp-box">
                <div style="font-size: 16px; margin-bottom: 10px;">🔑 Your Login Code</div>
                <div class="otp-code">{otp}</div>
                <div style="font-size: 14px; color: #666; margin-top: 10px;">⏰ This code expires in 5 minutes</div>
            </div>

            <div class="security-info">
                <h3 style="margin-top: 0;">🚨 Didn't try to log in?</h3>
                <p style="margin-bottom: 0;">If you didn't attempt to log in, please:</p>
                <ul style="margin: 10px 0;">
                    <li>Ignore this email - your account remains secure</li>
                    <li>Consider changing your password if you're concerned</li>
                    <li>Contact our support team if this happens frequently</li>
                </ul>
            </div>

            <div class="warning">
                <strong>🔒 Security Reminder:</strong> Never share this code with anyone. CreatorVerse support will never ask for your verification codes.
            </div>

            <p><strong>Account Security Tip:</strong> Always log out from shared devices and use strong, unique passwords.</p>

            <div class="footer">
                <p>© 2025 CreatorVerse. All rights reserved.</p>
                <p>This is an automated security message, please do not reply to this email.</p>
                <p>Questions? Contact <NAME_EMAIL></p>
            </div>
        </div>
    </body>
    </html>
    """
