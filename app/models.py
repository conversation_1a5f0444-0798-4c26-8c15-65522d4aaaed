from pydantic import BaseModel
from typing import Optional, List


class PhylloUser(BaseModel):
    """Model representing a user in the Phyllo system."""
    id: str
    name: str
    email: str
    platform: Optional[str] = None
    status: str = "active"
    
    
class PhylloAccount(BaseModel):
    """Model representing a connected platform account in the Phyllo system."""
    id: str
    user_id: str
    platform_id: str
    status: str
    connected_at: str
    
    
class PhylloData(BaseModel):
    """Model representing data from a platform in the Phyllo system."""
    id: str
    account_id: str
    data_type: str
    content: dict
    created_at: str
