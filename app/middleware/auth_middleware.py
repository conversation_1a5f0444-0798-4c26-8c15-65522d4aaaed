"""
Authentication middleware for CreatorVerse Backend.

This middleware provides JWT token validation and injects RBAC services into request state
for permission-based route protection.
"""
from typing import Callable

from fastapi import <PERSON><PERSON><PERSON>, Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

from app.core.config import get_database, get_locobuzz_redis
from app.core.logger import get_logger
from app.services.rbac_service import RBACService

logger = get_logger()


class AuthMiddleware(BaseHTTPMiddleware):
    """
    Middleware that injects authentication and authorization services into request state.
    """
    
    def __init__(self, app: FastAPI):
        super().__init__(app)
        self.app = app
        
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        Process request and inject authentication services.
        
        Args:
            request: FastAPI request object
            call_next: Next middleware/route handler
            
        Returns:
            Response from the next handler
        """
        try:
            # Get database and Redis connections
            db_conn = get_database()
            redis_client = get_locobuzz_redis()
            
            # Initialize RBAC service
            rbac_service = RBACService(redis_client, db_conn)
            
            # Inject services into request state for use by dependencies
            request.state.db_conn = db_conn
            request.state.redis_client = redis_client
            request.state.rbac_service = rbac_service
            
            # Process request
            response = await call_next(request)
            return response
            
        except Exception as e:
            logger.error("Error in auth middleware", extra={"error": str(e)})
            # Continue without injecting services (dependencies will handle gracefully)
            response = await call_next(request)
            return response


def setup_auth_middleware(app: FastAPI) -> None:
    """
    Set up authentication middleware for the FastAPI app.
    
    Args:
        app: FastAPI application instance
    """
    app.add_middleware(AuthMiddleware)
    logger.info("Authentication middleware added to application")
