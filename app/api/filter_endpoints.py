"""
Filter System API Endpoints for CreatorVerse Profile Analytics.

This module provides comprehensive REST API endpoints for:
- Retrieving filter configurations
- Managing filter groups and definitions  
- Saved filter sets operations
- Filter usage analytics
- System health and metadata
"""

from typing import List, Dict, Any, Optional
from uuid import UUID
from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException, Query, status
from fastapi.responses import JSONResponse
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.filter_models import PlatformEnum, OptionForTypeEnum
from app.schemas.filter_schemas import (
    FilterGroupSchema, FilterDefinitionSchema, LocationHierarchySchema,
    SavedFilterSetSchema, FilterMetadataSchema, GetFiltersRequest,
    SaveFilterSetRequest, ApplyFiltersRequest, FilterUsageStatsSchema,
    FilteredCreatorsResponse
)
from app.services.filter_service import FilterService
from app.core.exceptions import CreatorVerseError
from app.api.dependencies import get_db_session, get_current_user_optional


# Initialize router and service
router = APIRouter(prefix="/api/v1/filters", tags=["Filter System"])
filter_service = FilterService()


@router.get("/", response_model=List[FilterGroupSchema])
async def get_filters(
    channel: PlatformEnum = Query(..., description="Platform/channel"),
    option_for: OptionForTypeEnum = Query(..., description="Creator or audience filters"),
    include_inactive: bool = Query(False, description="Include inactive filters"),
    group_ids: Optional[List[UUID]] = Query(None, description="Specific group IDs"),
    db: AsyncSession = Depends(get_db_session)
) -> List[FilterGroupSchema]:
    """
    Get available filters for a specific platform and target.
    
    This endpoint returns the complete filter configuration that the frontend
    needs to render the filter UI components.
    """
    try:
        request = GetFiltersRequest(
            channel=channel,
            option_for=option_for,
            include_inactive=include_inactive,
            group_ids=group_ids
        )
        
        filter_groups = await filter_service.get_filter_groups(
            channel=request.channel,
            option_for=request.option_for,
            include_inactive=request.include_inactive
        )
        
        # Filter by specific group IDs if provided
        if request.group_ids:
            filter_groups = [
                group for group in filter_groups 
                if group.id in request.group_ids
            ]
        
        return filter_groups
        
    except CreatorVerseError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve filters: {str(e)}"
        )


@router.get("/groups", response_model=List[FilterGroupSchema])
async def get_filter_groups(
    channel: Optional[PlatformEnum] = Query(None, description="Filter by platform"),
    option_for: Optional[OptionForTypeEnum] = Query(None, description="Filter by target type"),
    include_inactive: bool = Query(False, description="Include inactive groups"),
    db: AsyncSession = Depends(get_db_session)
) -> List[FilterGroupSchema]:
    """
    Get filter groups with optional filtering.
    
    Useful for administrative interfaces and filter management.
    """
    try:
        if channel and option_for:
            return await filter_service.get_filter_groups(
                channel=channel,
                option_for=option_for,
                include_inactive=include_inactive
            )
        
        # If no specific channel/option_for, get all groups
        all_groups = []
        for ch in PlatformEnum:
            for opt in OptionForTypeEnum:
                try:
                    groups = await filter_service.get_filter_groups(
                        channel=ch,
                        option_for=opt,
                        include_inactive=include_inactive
                    )
                    all_groups.extend(groups)
                except:
                    # Skip if combination doesn't exist
                    continue
        
        # Apply filters if specified
        if channel:
            all_groups = [g for g in all_groups if g.channel == channel]
        if option_for:
            all_groups = [g for g in all_groups if g.option_for == option_for]
        
        return all_groups
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve filter groups: {str(e)}"
        )


@router.get("/groups/{group_id}", response_model=FilterGroupSchema)
async def get_filter_group(
    group_id: UUID,
    db: AsyncSession = Depends(get_db_session)
) -> FilterGroupSchema:
    """Get a specific filter group by ID."""
    try:
        # This would need to be implemented in the service
        # For now, get all groups and filter by ID
        all_groups = []
        for ch in PlatformEnum:
            for opt in OptionForTypeEnum:
                try:
                    groups = await filter_service.get_filter_groups(
                        channel=ch,
                        option_for=opt,
                        include_inactive=True
                    )
                    all_groups.extend(groups)
                except:
                    continue
        
        group = next((g for g in all_groups if g.id == group_id), None)
        if not group:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Filter group with ID {group_id} not found"
            )
        
        return group
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve filter group: {str(e)}"
        )


@router.get("/locations", response_model=List[LocationHierarchySchema])
async def get_locations(
    parent_id: Optional[UUID] = Query(None, description="Parent location ID"),
    tier: Optional[str] = Query(None, description="Tier classification"),
    level: Optional[int] = Query(None, description="Hierarchy level"),
    db: AsyncSession = Depends(get_db_session)
) -> List[LocationHierarchySchema]:
    """
    Get location hierarchy data.
    
    Used for location-based filters with hierarchical structure.
    """
    try:
        return await filter_service.get_location_hierarchy(
            parent_id=parent_id,
            tier=tier,
            level=level
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve locations: {str(e)}"
        )


@router.post("/saved-sets", response_model=SavedFilterSetSchema)
async def save_filter_set(
    request: SaveFilterSetRequest,
    user_id: Optional[UUID] = Depends(get_current_user_optional),
    db: AsyncSession = Depends(get_db_session)
) -> SavedFilterSetSchema:
    """
    Save a filter set for reuse and sharing.
    
    Allows users to save their filter combinations for later use
    or share them with others.
    """
    try:
        return await filter_service.save_filter_set(request, user_id)
        
    except CreatorVerseError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to save filter set: {str(e)}"
        )


@router.get("/saved-sets", response_model=List[SavedFilterSetSchema])
async def get_saved_filter_sets(
    channel: Optional[PlatformEnum] = Query(None, description="Filter by platform"),
    option_for: Optional[OptionForTypeEnum] = Query(None, description="Filter by target type"),
    include_shared: bool = Query(True, description="Include publicly shared sets"),
    user_id: Optional[UUID] = Depends(get_current_user_optional),
    db: AsyncSession = Depends(get_db_session)
) -> List[SavedFilterSetSchema]:
    """
    Get saved filter sets for the current user or public shared sets.
    """
    try:
        return await filter_service.get_saved_filter_sets(
            user_id=user_id,
            channel=channel,
            option_for=option_for,
            include_shared=include_shared
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve saved filter sets: {str(e)}"
        )


@router.get("/saved-sets/{share_code}", response_model=SavedFilterSetSchema)
async def get_shared_filter_set(
    share_code: str,
    db: AsyncSession = Depends(get_db_session)
) -> SavedFilterSetSchema:
    """Get a shared filter set by its share code."""
    try:
        # This would need to be implemented in the service
        filter_sets = await filter_service.get_saved_filter_sets(include_shared=True)
        
        filter_set = next((fs for fs in filter_sets if fs.share_code == share_code), None)
        if not filter_set:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Shared filter set with code '{share_code}' not found"
            )
        
        return filter_set
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve shared filter set: {str(e)}"
        )


@router.post("/apply", response_model=FilteredCreatorsResponse)
async def apply_filters(
    request: ApplyFiltersRequest,
    user_id: Optional[UUID] = Depends(get_current_user_optional),
    db: AsyncSession = Depends(get_db_session)
) -> FilteredCreatorsResponse:
    """
    Apply filters to search for creators or analyze audiences.
    
    This is the main endpoint that integrates with external APIs
    (like Phyllo) to fetch and filter creator/audience data.
    """
    try:
        # Log filter usage for analytics
        start_time = datetime.utcnow()
        
        # TODO: Implement actual filter application logic
        # This would involve:
        # 1. Mapping UI filters to API parameters
        # 2. Calling external APIs (Phyllo, etc.)
        # 3. Applying filters to the data
        # 4. Returning paginated results
        
        # For now, return a mock response
        mock_response = FilteredCreatorsResponse(
            creators=[],
            total_count=0,
            page=request.page,
            page_size=request.page_size,
            total_pages=0,
            applied_filters=request.filters,
            execution_time_ms=int((datetime.utcnow() - start_time).total_seconds() * 1000)
        )
        
        # Log filter usage
        for filter_name, filter_value in request.filters.items():
            try:
                # This would need proper filter ID mapping
                await filter_service.log_filter_usage(
                    filter_definition_id=UUID("00000000-0000-0000-0000-000000000000"),  # Mock ID
                    filter_value=filter_value,
                    result_count=mock_response.total_count,
                    execution_time_ms=mock_response.execution_time_ms,
                    user_id=user_id
                )
            except:
                # Don't fail the request if logging fails
                pass
        
        return mock_response
        
    except CreatorVerseError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to apply filters: {str(e)}"
        )


@router.get("/metadata", response_model=FilterMetadataSchema)
async def get_filter_metadata(
    db: AsyncSession = Depends(get_db_session)
) -> FilterMetadataSchema:
    """
    Get filter system metadata and statistics.
    
    Useful for administrative dashboards and system monitoring.
    """
    try:
        return await filter_service.get_filter_metadata()
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve filter metadata: {str(e)}"
        )


@router.get("/analytics/usage", response_model=List[FilterUsageStatsSchema])
async def get_filter_usage_analytics(
    days: int = Query(30, ge=1, le=365, description="Number of days to analyze"),
    channel: Optional[PlatformEnum] = Query(None, description="Filter by platform"),
    limit: int = Query(20, ge=1, le=100, description="Number of results"),
    db: AsyncSession = Depends(get_db_session)
) -> List[FilterUsageStatsSchema]:
    """
    Get filter usage analytics.
    
    Returns statistics about which filters are most used,
    performance metrics, etc.
    """
    try:
        # TODO: Implement actual analytics query
        # This would query the filter_usage_logs table and aggregate data
        
        # For now, return mock data
        return []
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve filter analytics: {str(e)}"
        )


# Administrative endpoints (would typically require admin authentication)

@router.put("/filters/{filter_id}/status")
async def toggle_filter_status(
    filter_id: UUID,
    is_active: bool,
    db: AsyncSession = Depends(get_db_session)
) -> Dict[str, Any]:
    """
    Enable or disable a specific filter.
    
    Administrative endpoint for filter management.
    """
    try:
        success = await filter_service.toggle_filter_status(filter_id, is_active)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Filter with ID {filter_id} not found"
            )
        
        return {
            "success": True,
            "message": f"Filter {'enabled' if is_active else 'disabled'} successfully",
            "filter_id": str(filter_id),
            "is_active": is_active
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to toggle filter status: {str(e)}"
        )


@router.put("/groups/{group_id}/status")
async def toggle_group_status(
    group_id: UUID,
    is_active: bool,
    db: AsyncSession = Depends(get_db_session)
) -> Dict[str, Any]:
    """
    Enable or disable a filter group and all its filters.
    
    Administrative endpoint for group management.
    """
    try:
        success = await filter_service.toggle_group_status(group_id, is_active)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Filter group with ID {group_id} not found"
            )
        
        return {
            "success": True,
            "message": f"Filter group {'enabled' if is_active else 'disabled'} successfully",
            "group_id": str(group_id),
            "is_active": is_active
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to toggle group status: {str(e)}"
        )


@router.get("/health")
async def filter_system_health_check(
    db: AsyncSession = Depends(get_db_session)
) -> Dict[str, Any]:
    """
    Perform a health check of the filter system.
    
    Returns system status, statistics, and any issues.
    """
    try:
        health_data = {
            "status": "healthy",
            "timestamp": datetime.utcnow().isoformat(),
            "components": {}
        }
        
        # Check filter metadata
        try:
            metadata = await filter_service.get_filter_metadata()
            health_data["components"]["metadata"] = {
                "status": "healthy",
                "total_groups": metadata.total_groups,
                "active_groups": metadata.active_groups,
                "total_filters": metadata.total_filters,
                "active_filters": metadata.active_filters
            }
        except Exception as e:
            health_data["components"]["metadata"] = {
                "status": "error",
                "error": str(e)
            }
            health_data["status"] = "degraded"
        
        # Check cache connectivity
        try:
            await filter_service.redis.ping()
            health_data["components"]["cache"] = {"status": "healthy"}
        except Exception as e:
            health_data["components"]["cache"] = {
                "status": "error",
                "error": str(e)
            }
            health_data["status"] = "degraded"
        
        return health_data
        
    except Exception as e:
        return {
            "status": "error",
            "timestamp": datetime.utcnow().isoformat(),
            "error": str(e)
        }


@router.get("/test/sample-data")
async def generate_sample_filter_response(
    channel: PlatformEnum = Query(PlatformEnum.instagram, description="Platform"),
    option_for: OptionForTypeEnum = Query(OptionForTypeEnum.creator, description="Target type")
) -> Dict[str, Any]:
    """
    Generate sample filter response for frontend testing.
    
    This endpoint returns the expected filter structure that the frontend
    should receive, useful for development and testing.
    """
    try:
        # Get actual filters if available
        try:
            actual_filters = await filter_service.get_filter_groups(channel, option_for)
            if actual_filters:
                return {
                    "source": "database",
                    "channel": channel.value,
                    "option_for": option_for.value,
                    "filter_groups": [group.model_dump() for group in actual_filters]
                }
        except:
            pass
        
        # Return sample data structure
        sample_data = {
            "source": "sample",
            "channel": channel.value,
            "option_for": option_for.value,
            "filter_groups": [
                {
                    "id": "550e8400-e29b-41d4-a716-446655440000",
                    "optionName": "Demography & Identity",
                    "optionFor": option_for.value,
                    "channel": channel.value,
                    "sort_order": 1,
                    "filters": [
                        {
                            "id": "550e8400-e29b-41d4-a716-446655440001",
                            "name": "Gender",
                            "type": "radio_button",
                            "icon": "gender-icon",
                            "minmax": False,
                            "enterValue": False,
                            "searchBox": False,
                            "placeholder": "Select Gender",
                            "options": [
                                {"label": "Male", "value": "male", "description": ""},
                                {"label": "Female", "value": "female", "description": ""},
                                {"label": "Other", "value": "other", "description": ""}
                            ],
                            "db_field": "gender",
                            "api_field": "creator_gender",
                            "sort_order": 1
                        },
                        {
                            "id": "550e8400-e29b-41d4-a716-446655440002",
                            "name": "Age",
                            "type": "checkbox",
                            "icon": "age-icon",
                            "minmax": True,
                            "enterValue": False,
                            "searchBox": False,
                            "placeholder": "Select Age",
                            "options": [
                                {"label": "Teen", "value": "13-19", "description": "13-19"},
                                {"label": "Young Adult", "value": "20-35", "description": "20-35"},
                                {"label": "Adult", "value": "36-55", "description": "36-55"},
                                {"label": "Senior", "value": "56+", "description": "56+"}
                            ],
                            "db_field": "age_group",
                            "api_field": "creator_age",
                            "sort_order": 2
                        }
                    ]
                },
                {
                    "id": "550e8400-e29b-41d4-a716-446655440003",
                    "optionName": "Performance Metrics",
                    "optionFor": option_for.value,
                    "channel": channel.value,
                    "sort_order": 2,
                    "filters": [
                        {
                            "id": "550e8400-e29b-41d4-a716-446655440004",
                            "name": "Follower Count",
                            "type": "checkbox",
                            "icon": "followers-icon",
                            "minmax": True,
                            "enterValue": False,
                            "searchBox": False,
                            "placeholder": "Select Follower Range",
                            "options": [
                                {"label": "Nano", "value": "1k-10k", "description": "1K-10K"},
                                {"label": "Micro", "value": "10k-50k", "description": "10K-50K"},
                                {"label": "Mid", "value": "50k-500k", "description": "50K-500K"},
                                {"label": "Macro", "value": "500k-1m", "description": "500K-1M"},
                                {"label": "Mega", "value": "1m+", "description": "1M+"}
                            ],
                            "db_field": "follower_count",
                            "api_field": "follower_count",
                            "sort_order": 1
                        }
                    ]
                }
            ]
        }
        
        return sample_data
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate sample data: {str(e)}"
        )


# Include the router in the main application
def get_filter_router() -> APIRouter:
    """Get the filter system router for inclusion in the main app."""
    return router
