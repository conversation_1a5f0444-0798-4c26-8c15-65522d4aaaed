"""
Common logout endpoint for all user types (influencers, brands)
"""
from fastapi import <PERSON><PERSON>out<PERSON>, Depends, HTTPException, status
from pydantic import BaseModel

from app.api.dependencies import get_locobuzz_redis, get_database
from app.api.deps import get_current_user
from app.core.logger import get_logger
from app.core_helper.async_logger import with_trace_id
from app.services.session_service import generate_auth_tokens, revoke_user_session, validate_refresh_token
from app.utilities.response_handler import StandardResponse

router = APIRouter()
logger = get_logger()


class LogoutRequest(BaseModel):
    """Schema for logout request"""
    session_id: str


class RefreshTokenRequest(BaseModel):
    """Schema for refresh token request"""
    refresh_token: str


@router.post("/logout", status_code=status.HTTP_200_OK)
@with_trace_id
async def logout_endpoint(
        data: LogoutRequest,
        redis_client=Depends(get_locobuzz_redis),
        db_conn=Depends(get_database),
        current_user=Depends(get_current_user)
):
    """
    Logout endpoint for all user types.
    Invalidates the user session in both Redis and database.
    
    Flow:
    1. Extract user ID from current_user dependency
    2. Revoke the session using session service
    3. Return success response
    """
    try:
        # Extract user_id from current_user dependency
        user_id = current_user.get("sub")

        if not user_id:
            logger.warning(
                "Logout attempted without valid user ID",
                extra={"session_id": data.session_id}
            )
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authentication credentials"
            )

        # Revoke the session using the cache-aside pattern service
        success, message = await revoke_user_session(
            db_conn=db_conn,
            redis_client=redis_client,
            user_id=user_id,
            session_id=data.session_id
        )

        if not success:
            logger.warning(
                "Failed to logout user",
                extra={"user_id": str(user_id), "session_id": data.session_id, "reason": message}
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to logout: {message}"
            )

        logger.info(
            "User logged out successfully",
            extra={"user_id": str(user_id), "session_id": data.session_id}
        )

        return StandardResponse.success(
            data=None,
            message="Logged out successfully"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Error during logout",
            extra={"error": str(e), "session_id": data.session_id}
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred during logout"
        )


@router.post("/refresh-token", status_code=status.HTTP_200_OK)
@with_trace_id
async def refresh_token_endpoint(
        data: RefreshTokenRequest,
        redis_client=Depends(get_locobuzz_redis),
        db_conn=Depends(get_database)
):
    """
    Refresh token endpoint for all user types.
    Validates the refresh token and issues new access tokens.
    
    Flow:
    1. Validate the refresh token
    2. If valid, generate new access token (and optionally a new refresh token)
    3. Return the new tokens
    """
    try:
        # Validate the refresh token and get user info
        token_valid, user_id, session_id = await validate_refresh_token(
            db_conn=db_conn,
            redis_client=redis_client,
            refresh_token=data.refresh_token
        )
        if not token_valid or not user_id:
            logger.warning(
                "Invalid refresh token attempt",
                extra={"token_fragment": data.refresh_token[:10] + "..."})
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid or expired refresh token"
            )

        # Generate new tokens
        tokens = await generate_auth_tokens(
            db_conn=db_conn,
            redis_client=redis_client,
            user_id=user_id,
            session_id=session_id,
            old_refresh_token=data.refresh_token,
            refresh=True
        )

        logger.info(
            "Token refresh successful",
            extra={"user_id": str(user_id), "session_id": session_id}
        )

        return StandardResponse.success(
            data=tokens,
            message="Tokens refreshed successfully"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Error during token refresh",
            extra={"error": str(e)}
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred during token refresh"
        )
