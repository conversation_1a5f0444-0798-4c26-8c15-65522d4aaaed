"""
Health check endpoints for CreatorVerse Discovery & Analytics API
"""
from fastapi import APIRouter, Depends
from typing import Dict, Any

from app.utilities.response_handler import StandardResponse, StandardResponseModel
from app.utilities.startup_tasks import get_service_health
from app.core.config import APP_CONFIG

router = APIRouter()


@router.get("/", response_model=StandardResponseModel[dict])
async def health_check():
    """Basic health check endpoint"""
    return StandardResponse.success(
        data={
            "status": "healthy",
            "service": "CreatorVerse Discovery & Analytics",
            "environment": APP_CONFIG.environ
        },
        message="Service is healthy"
    )


@router.get("/detailed", response_model=StandardResponseModel[dict])
async def detailed_health_check():
    """Detailed health check with component status"""
    health_status = await get_service_health()
    
    status_code = 200 if health_status["overall"] else 503
    message = "All systems operational" if health_status["overall"] else "Some systems degraded"
    
    return StandardResponse.success(
        data={
            "status": "healthy" if health_status["overall"] else "degraded",
            "service": "CreatorVerse Discovery & Analytics",
            "environment": APP_CONFIG.environ,
            "components": health_status,
            "timestamp": APP_CONFIG.initialize_logger().info("Health check performed")
        },
        message=message
    )


@router.get("/metrics")
async def service_metrics():
    """Get basic service metrics"""
    try:
        from app.core.config import get_discovery_redis
        redis_client = get_discovery_redis()
        
        # Get metrics from Redis
        metrics = await redis_client.hgetall("discovery:metrics")
        
        if not metrics:
            metrics = {
                "total_requests": "0",
                "cache_hits": "0",
                "external_api_calls": "0"
            }
        
        # Convert to integers
        parsed_metrics = {key: int(value) for key, value in metrics.items()}
        
        return StandardResponse.success(
            data={
                "service_metrics": parsed_metrics,
                "service": "CreatorVerse Discovery & Analytics",
                "environment": APP_CONFIG.environ
            },
            message="Metrics retrieved successfully"
        )
        
    except Exception as e:
        return StandardResponse.error(
            message=f"Failed to retrieve metrics: {str(e)}",
            error_code="METRICS_ERROR"
        )
