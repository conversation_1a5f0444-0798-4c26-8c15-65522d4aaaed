"""
OAuth endpoints for CreatorVerse Backend.
"""
from fastapi import APIRout<PERSON>, Depends, HTTPException, Query, Request, status
from fastapi.responses import RedirectResponse
from typing import Optional

from app.api.dependencies import get_database, get_locobuzz_redis, get_rbac_service_dependency
from app.core.logger import get_logger
from app.core_helper.async_logger import with_trace_id
from app.oauth.oauth_flow_manager import get_oauth_flow_manager
from app.oauth.oauth_handlers import get_oauth_handler
from app.oauth.oauth_service import get_oauth_service
from app.schemas.oauth_schemas import OAuthInitiateRequest, OAuthInitiateResponse

router = APIRouter()
logger = get_logger()


@router.post("/initiate", response_model=OAuthInitiateResponse)
@with_trace_id
async def initiate_oauth_endpoint(
    request: Request,
    data: OAuthInitiateRequest,
    redis_client=Depends(get_locobuzz_redis)
):
    """
    Initiate OAuth flow for a provider
    
    Args:
        request: FastAPI Request
        data: OAuth initiation request with provider and role_uuid
        redis_client: Redis client
        
    Returns:
        OAuthInitiateResponse: Auth URL and state token
    """
    try:
        # Get OAuth flow manager
        flow_manager = get_oauth_flow_manager(redis_client)
        
        # Generate state and store data
        state = await flow_manager.generate_state()
        await flow_manager.store_state(state, data.provider, data.role_uuid)
        
        # Generate authorization URL
        auth_url = await flow_manager.get_provider_auth_url(data.provider, state)
        
        return OAuthInitiateResponse(auth_url=auth_url, state=state)
    except ValueError as e:
        logger.error(f"OAuth initiation error: {e}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error in OAuth initiation: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal server error")


@router.get("/callback")
@with_trace_id
async def oauth_callback_endpoint(
    request: Request,
    code: str = Query(..., description="Authorization code from OAuth provider"),
    state: str = Query(..., description="State token for CSRF protection"),
    db_conn=Depends(get_database),
    redis_client=Depends(get_locobuzz_redis),
    rbac_service=Depends(get_rbac_service_dependency)
):
    """
    OAuth callback endpoint to handle authentication
    
    Args:
        request: FastAPI Request
        code: Authorization code from provider
        state: State token to verify CSRF protection
        db_conn: Database connection
        redis_client: Redis client
        
    Returns:
        Redirect or token response
    """
    try:
        # Get flow manager and recover state
        flow_manager = get_oauth_flow_manager(redis_client)
        state_data = await flow_manager.get_and_delete_state(state)
        
        if not state_data:
            logger.warning("Invalid or expired OAuth state", extra={"state": state[:8] + "..."})
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, 
                detail="Invalid or expired OAuth state"
            )
        
        provider = state_data.provider
        role_uuid = state_data.role_uuid
        
        logger.info("Processing OAuth callback", extra={
            "provider": provider,
            "role_uuid": role_uuid,
            "user_ip": request.client.host if request.client else "unknown"
        })
        
        # Get appropriate handler for the provider
        handler = get_oauth_handler(provider)
        
        # Exchange code for tokens
        redirect_uri = str(request.url.replace(query=None, path=str(request.url.path)))
        provider_tokens = await handler.exchange_code_for_tokens(code, redirect_uri)
        
        logger.debug("Successfully exchanged code for tokens", extra={
            "provider": provider,
            "has_refresh_token": provider_tokens.refresh_token is not None
        })
        
        # Get user profile
        provider_profile = await handler.get_user_profile(provider_tokens)
        
        logger.info("Retrieved user profile from provider", extra={
            "provider": provider,
            "email": provider_profile.email,
            "provider_user_id": provider_profile.provider_id,
            "is_email_verified": provider_profile.is_email_verified
        })
        
        # Create or update user
        oauth_service = get_oauth_service(redis_client)
        auth_response, redirect_to, is_new = await oauth_service.create_or_update_user_from_oauth(
            db_conn=db_conn,
            redis_client=redis_client,
            provider_tokens=provider_tokens,
            provider_profile=provider_profile,
            role_uuid=role_uuid,
            provider=provider,
            rbac_service=rbac_service
        )
        
        logger.info("OAuth flow completed successfully", extra={
            "provider": provider,
            "email": provider_profile.email,
            "is_new_user": is_new,
            "needs_redirect": redirect_to is not None
        })
        
        # For web clients, redirect to frontend with token
        # Check if there's a configured frontend URL, otherwise use a default
        frontend_base_url = getattr(request.url, 'scheme', 'http') + "://" + getattr(request.url, 'netloc', 'localhost:3000')
        
        # Use environment variable or default frontend URL
        import os
        frontend_url = os.getenv('FRONTEND_URL', f'{frontend_base_url}')
        
        # Redirect to auth success page based on user type and state
        if redirect_to:
            # Custom redirect (e.g., YouTube channel selection)
            redirect_url = f"{frontend_url}{redirect_to}"
            logger.info("Redirecting user for additional setup", extra={
                "redirect_url": redirect_url
            })
            return RedirectResponse(url=redirect_url)
        
        # Default success redirect with tokens - use /auth/success as expected by frontend
        final_redirect_url = (
            f"{frontend_url}/auth/success?access_token={auth_response.access_token}"
            f"&refresh_token={auth_response.refresh_token}"
            f"&is_new={is_new}"
        )
        
        logger.info("Redirecting to frontend auth success with tokens", extra={
            "frontend_url": frontend_url,
            "is_new_user": is_new,
            "redirect_path": "/auth/success"
        })
        
        return RedirectResponse(url=final_redirect_url)
        
    except ValueError as e:
        logger.error("OAuth callback validation error", extra={
            "error": str(e),
            "provider": getattr(state_data, 'provider', 'unknown') if 'state_data' in locals() else 'unknown',
            "user_ip": request.client.host if request.client else "unknown"
        })
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.error("Unexpected error in OAuth callback", extra={
            "error": str(e),
            "error_type": type(e).__name__,
            "provider": getattr(state_data, 'provider', 'unknown') if 'state_data' in locals() else 'unknown',
            "user_ip": request.client.host if request.client else "unknown"
        }, exc_info=True)
        
        # If we have user context, attempt cleanup
        if 'provider_profile' in locals() and 'state_data' in locals():
            try:
                from app.utilities.oauth_utils import check_oauth_account_exists, cleanup_failed_oauth_attempt
                
                # Check if OAuth account was partially created
                existing_user_id = await check_oauth_account_exists(
                    db_conn=db_conn,
                    provider=state_data.provider,
                    provider_user_id=provider_profile.provider_id
                )
                
                if existing_user_id:
                    await cleanup_failed_oauth_attempt(
                        db_conn=db_conn,
                        redis_client=redis_client,
                        user_id=existing_user_id,
                        provider=state_data.provider
                    )
                    logger.info("Cleaned up partial OAuth setup", extra={
                        "user_id": str(existing_user_id),
                        "provider": state_data.provider
                    })
            except Exception as cleanup_err:
                logger.error("Failed to cleanup partial OAuth setup", extra={
                    "cleanup_error": str(cleanup_err)
                })
        
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal server error")
