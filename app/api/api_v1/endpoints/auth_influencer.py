"""
Authentication endpoints for influencers in CreatorVerse Backend.
"""
from datetime import datetime, UTC

from fastapi import APIRouter, Depends, HTTPException, Request, status
from sqlalchemy import select, func


from app.api.dependencies import get_database, get_locobuzz_redis, get_rbac_service_dependency
from app.core.enums import UserStatus
from app.core.logger import get_logger
from app.core.redis_keys import RedisKeys
from app.core_helper.async_logger import with_trace_id
from app.models.user_models import User, UserRoleModel, UserAuthMethod
from app.schemas.auth import (
    InfluencerRegisterRequest,
    VerifyOTPRequest,
    ResendOTPRequest,
    LoginRequest,
    LoginVerifyOTPRequest,
    OtpResponse,
    AuthTokenResponse
)
from app.services.email_validation_service import verify_email_exists_optimized
from app.services.rbac_utils import get_auth_method_id_by_name_cache_aside
from app.services.session_service import create_user_session
from app.services.user_service import get_user_by_email_cache_aside, get_user_by_mobile_cache_aside
from app.utilities.bloom_filter import bloom_register
from app.utilities.otp_manager import get_optimized_otp_manager
from app.utilities.user_utils import validate_and_fetch_existing_user, send_user_otp, create_user, upsert_user_otp_entry

router = APIRouter()
logger = get_logger()


@router.post("/register", response_model=OtpResponse)
@bloom_register(field="identifier", add_on_success=False)  # ✅ FIXED: Use identifier instead of email
@with_trace_id
async def register_influencer_endpoint(
        data: InfluencerRegisterRequest,
        db_conn=Depends(get_database),
        redis_client=Depends(get_locobuzz_redis)
):
    """
    Register a new influencer with email or mobile and send OTP for verification.
    
    Flow:
    1. Check if user already exists
    2. Create user with status "requested" and is_active=False
    3. Generate and send OTP via email or SMS
    4. Return success response with OTP details
    """
    identifier = data.identifier
    identifier_type = data.identifier_type

    # First check for cooldown to avoid unnecessary processing
    cooldown_key = RedisKeys.otp_cooldown_key(identifier) if identifier_type == "email" else RedisKeys.otp_cooldown_key(
        f"mobile:{identifier}")
    cooldown_exists = await redis_client.exists(cooldown_key)

    if cooldown_exists:
        logger.warning(
            "OTP request blocked due to cooldown",
            extra={"identifier": identifier, "type": identifier_type}
        )
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail="Please wait 30 seconds before requesting another verification code"
        )
    # Validate email domain if email is provided and if already entry is created then return the existing user
    user_exists = await validate_and_fetch_existing_user(identifier, identifier_type, db_conn, redis_client)

    # Create new user with status requested
    if not user_exists:
        try:
            async with db_conn.get_db() as session:
                async with session.begin():
                    new_user = await create_user(session, data)
                    session.add(new_user)

                    # Commit to get the user ID
                    await session.commit()

                    logger.info(
                        f"New {identifier_type} user created",
                        extra={
                            "user_id": str(new_user.id),
                            "identifier": identifier,
                            "source": data.register_source_name
                        }
                    )
        except Exception as e:
            logger.error(
                "Error creating new user",
                extra={
                    "identifier": identifier,
                    "error": str(e)
                }
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create user account")

    # For registration, no need to persist OTP in database since we haven't created the user yet
    # or we need to activate it - this will be done during verification
    await send_user_otp(identifier, identifier_type, redis_client)

    return OtpResponse(
        success=True,
        message=f"Verification code sent to your {identifier_type}",
        **{identifier_type: identifier}
    )


@router.post("/verify-otp", response_model=AuthTokenResponse)
@bloom_register(field="identifier", add_on_success=True)  # ✅ FIXED: Use identifier and add to bloom filter on success
@with_trace_id
async def verify_influencer_otp_endpoint(
        request: Request,
        data: VerifyOTPRequest,
        db_conn=Depends(get_database),
        redis_client=Depends(get_locobuzz_redis),
        rbac_service=Depends(get_rbac_service_dependency)
):
    # 1) Validate identifier_type
    identifier = data.identifier
    id_type = data.identifier_type.lower()
    if id_type not in ("email", "mobile"):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid identifier_type: {data.identifier_type}",
        )

    # 2) First fetch user to get user_id - needed for OTP DB entry
    user_id = None
    if id_type == "email":
        user_dict = await get_user_by_email_cache_aside(db_conn, redis_client, identifier)
    else:
        user_dict = await get_user_by_mobile_cache_aside(db_conn, redis_client, identifier)

    if user_dict and user_dict.get("id"):
        user_id = user_dict.get("id")

    # 3) Verify OTP
    otp_manager = get_optimized_otp_manager(redis_client)
    if id_type == "email":
        is_valid, message = await otp_manager.verify_otp(identifier, data.otp)
    else:
        is_valid, message = await otp_manager.verify_mobile_otp(identifier, data.otp)

    # Get the number of failed attempts from redis for UserOTP update
    failed_attempts = 0
    otp_key = RedisKeys.otp_key(identifier) if id_type == "email" else RedisKeys.otp_mobile_key(identifier)
    otp_data = await redis_client.hgetall(otp_key)
    if otp_data and "failed_attempts" in otp_data:
        failed_attempts = int(otp_data.get("failed_attempts", 0))

    # Update UserOTP record if user_id is available
    if user_id:
        try:
            # Handle db operations in a separate session since we don't want to mix it
            # with the primary transaction below
            async with db_conn.get_db() as otp_session:
                await upsert_user_otp_entry(
                    db_session=otp_session,
                    user_id=str(user_id),
                    channel=id_type,
                    new_entry=False,
                    current_failed_attempts=failed_attempts
                )
                await otp_session.commit()
        except Exception as e:
            # Log error but continue with the flow
            logger.error(
                "Failed to update UserOTP entry",
                extra={"identifier": identifier, "user_id": user_id, "error": str(e)}
            )

    if not is_valid:
        logger.warning(
            "OTP verification failed",
            extra={"identifier": identifier, "type": id_type, "reason": message},
        )
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=message)

    try:
        # 3) Open one session & transaction
        async with db_conn.get_db() as session:
            async with session.begin():
                # 4) Fetch the user in one atomic call
                if id_type == "email":
                    # build the stmt
                    stmt = select(User).where(func.lower(User.email) == identifier.lower())
                else:
                    stmt = select(User).where(User.phone_number == identifier)

                    # execute as “scalars”, then pull the User
                result = await session.scalars(stmt)
                session_user = result.one_or_none()
                if session_user is None:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail="User not found"
                    )

                # 5) Mark verified & activate
                if id_type == "email":
                    session_user.is_email_verified = True
                else:
                    session_user.is_phone_verified = True

                session_user.status = UserStatus.get_status_name(UserStatus.ACTIVE.value)
                session_user.is_active = True

                # 6) Lookup auth‐method ID
                auth_key = "email_otp" if id_type == "email" else "phone_otp"
                auth_method_id = await get_auth_method_id_by_name_cache_aside(
                    db_conn, redis_client, auth_key
                )

                # 7) Assign influencer role need to get the role id from MasterRole / we get it in the api
                # Check if user already has this role to avoid duplicate key error
                existing_role_stmt = select(UserRoleModel).where(
                    UserRoleModel.user_id == session_user.id,
                    UserRoleModel.role_id == data.role_uuid
                )
                existing_role_result = await session.execute(existing_role_stmt)
                existing_role = existing_role_result.scalar_one_or_none()
                
                if not existing_role:
                    session.add(
                        UserRoleModel(user_id=session_user.id, role_id=data.role_uuid)
                    )
                    logger.info(
                        "Assigned influencer role to user",
                        extra={"user_id": str(session_user.id), "role_uuid": str(data.role_uuid)}
                    )
                else:
                    logger.info(
                        "User already has influencer role assigned",
                        extra={"user_id": str(session_user.id), "role_uuid": str(data.role_uuid)}
                    )

                # Save role to Redis for future authentication
                from app.utilities.user_utils import save_user_role_to_redis
                await save_user_role_to_redis(str(session_user.id), "influencer", redis_client)

                # 8) Record the auth selection
                # Check if user already has this auth method to avoid duplicate key error
                existing_auth_stmt = select(UserAuthMethod).where(
                    UserAuthMethod.user_id == session_user.id,
                    UserAuthMethod.auth_method_id == auth_method_id
                )
                existing_auth_result = await session.execute(existing_auth_stmt)
                existing_auth = existing_auth_result.scalar_one_or_none()
                
                if not existing_auth:
                    auth_method = UserAuthMethod(
                        user_id=session_user.id,
                        auth_method_id=auth_method_id,
                        enabled_at=datetime.now(UTC),
                        is_enabled=True,
                    )
                    session.add(auth_method)
                    logger.debug(
                        "Added auth method for user",
                        extra={"user_id": str(session_user.id), "auth_method": auth_key}
                    )
                else:
                    # Update existing auth method to ensure it's enabled
                    existing_auth.is_enabled = True
                    existing_auth.enabled_at = datetime.now(UTC)
                    logger.debug(
                        "Updated existing auth method for user",
                        extra={"user_id": str(session_user.id), "auth_method": auth_key}
                    )
                
                # Inject RBAC service into request for permission-enabled tokens
                if rbac_service:
                    request.rbac_service = rbac_service
                
                # 9)  mint tokens & persist session
                token_data = await create_user_session(
                    session_user=session_user,
                    db_session=session,
                    redis_client=redis_client,
                    request=request,
                )

                # Send welcome email if email is available
                if id_type == "email" and session_user.email:
                    from app.services.email_service import get_email_service
                    email_service = get_email_service()
                    # Send welcome email asynchronously - we don't wait for it
                    # so that the user flow isn't affected if there's an email issue
                    await email_service.send_welcome_email(session_user.email, user_role="influencer")
                    logger.info(
                        "Welcome email sent to influencer",
                        extra={"user_id": str(session_user.id), "email": session_user.email}
                    )

        # 10) Build and return response
        logger.info(
            "User activated after OTP verification",
            extra={
                "user_id": str(session_user.id),
                "identifier": identifier,
                "session_id": token_data.get("session_id", "unknown"),
            },
        )

        return AuthTokenResponse(
            access_token=token_data["access_token"],
            refresh_token=token_data["refresh_token"],
            token_type=token_data["token_type"],
            expires_in=int(token_data["expires_in"])
        )

    except HTTPException:
        # pass through 4xx
        raise
    except Exception as e:
        logger.error(
            "Error during user activation",
            extra={"identifier": identifier, "error": str(e)},
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to activate user account",
        )


@router.post("/resend-otp", response_model=OtpResponse)
@with_trace_id
async def resend_influencer_otp_endpoint(
        data: ResendOTPRequest,
        db_conn=Depends(get_database),
        redis_client=Depends(get_locobuzz_redis)
):
    """
    Resend OTP for influencer verification with cooldown enforcement.
    
    Flow:
    1. Check if user exists
    2. Check cooldown period (30s)
    3. Generate and send new OTP via email or SMS
    4. Return success response with OTP details
    """
    identifier = data.identifier
    identifier_type = data.identifier_type

    # Check if user exists
    if identifier_type == "email":
        user_dict = await get_user_by_email_cache_aside(db_conn, redis_client, identifier)
    else:  # mobile
        user_dict = await get_user_by_mobile_cache_aside(db_conn, redis_client, identifier)

    if not user_dict:
        logger.warning(
            "Resend OTP attempt for non-existent user",
            extra={
                "identifier": identifier,
                "type": identifier_type
            }
        )
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"No user found with this {identifier_type}"
        )

    # Validate email domain if email is provided
    if identifier_type == "email":
        try:
            is_valid, reason = await verify_email_exists_optimized(identifier, redis_client)
            if not is_valid:
                logger.warning(
                    "Invalid email domain provided",
                    extra={"identifier": identifier, "reason": reason}
                )
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Email validation failed: {reason}"
                )
        except HTTPException:
            raise
        except Exception as e:
            logger.error(
                "Email validation error",
                extra={"identifier": identifier, "error": str(e)}
            )
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email validation failed"
            )

    # Check if OTP is in cooldown period
    otp_manager = get_optimized_otp_manager(redis_client)

    if identifier_type == "email":
        has_otp, remaining_seconds, message = await otp_manager.check_existing_otp_status_pipeline(identifier)
    else:  # mobile
        has_otp, remaining_seconds, message = await otp_manager.check_mobile_otp_status_pipeline(identifier)

    # If it has valid OTP with more than 30 seconds remaining, enforce cooldown
    if has_otp and remaining_seconds > 570:  # 600 - 30 = 570 (10 min TTL - 30s cooldown)
        cooldown_remaining = remaining_seconds - 570
        logger.info(
            "OTP resend attempt during cooldown period",
            extra={
                "identifier": identifier,
                "cooldown_remaining": cooldown_remaining
            }
        )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Please wait {cooldown_remaining} seconds before requesting a new OTP"
        )  # Send new OTP - this is for resending OTP, so we're using the same OTP type (login/registration)
    # as the original OTP request (which is determined by the endpoint)
    # For simplicity and since it's a resend for registration, setting is_login=False
    if identifier_type == "email":
        otp = await otp_manager.create_otp_optimized(identifier, user_role="influencer", is_login=False)
    else:  # mobile
        otp = await otp_manager.create_mobile_otp_optimized(identifier, user_role="influencer", is_login=False)

    if not otp:
        logger.error(
            "Failed to create OTP during resend",
            extra={
                "identifier": identifier,
                "type": identifier_type
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to send verification {identifier_type}"
        )

    return OtpResponse(
        success=True,
        message=f"New verification code sent to your {identifier_type}",
        **{identifier_type: identifier}
    )


@router.post("/login", response_model=OtpResponse)
@with_trace_id
async def login_endpoint(
        data: LoginRequest,
        db_conn=Depends(get_database),
        redis_client=Depends(get_locobuzz_redis)
):
    """
    Common login endpoint for both influencers and brands.
    Sends OTP to the provided email or mobile number.
    
    Flow:
    1. Validate the identifier (email or mobile)
    2. Check if user exists and is active
    3. Check cooldown period for OTP requests
    4. Generate and send OTP
    5. Return success response
    """
    identifier = data.identifier
    identifier_type = data.identifier_type

    # Check for cooldown to avoid unnecessary processing
    cooldown_key = RedisKeys.otp_cooldown_key(identifier) if identifier_type == "email" else RedisKeys.otp_cooldown_key(
        f"mobile:{identifier}")
    cooldown_exists = await redis_client.exists(cooldown_key)

    if cooldown_exists:
        logger.warning(
            "Login OTP request blocked due to cooldown",
            extra={"identifier": identifier, "type": identifier_type}
        )
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail="Please wait 30 seconds before requesting another verification code"
        )

    # Check if user exists using cache-aside pattern
    if identifier_type == "email":
        user_dict = await get_user_by_email_cache_aside(db_conn, redis_client, identifier)
    else:  # mobile
        user_dict = await get_user_by_mobile_cache_aside(db_conn, redis_client, identifier)

    if not user_dict:
        logger.warning(
            "Login attempt for non-existent user",
            extra={"identifier": identifier, "type": identifier_type}
        )
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"No account found with this {identifier_type}"
        )

    # Check if user is active
    if user_dict.get("is_active") != "True":
        logger.warning(
            "Login attempt for inactive user",
            extra={"identifier": identifier, "user_id": user_dict.get("id")}
        )
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Account is not active. Please contact support."
        )

    # Check if user is verified for the login method
    if identifier_type == "email" and user_dict.get("is_email_verified") != "True":
        logger.warning(
            "Login attempt with unverified email",
            extra={"identifier": identifier, "user_id": user_dict.get("id")}
        )
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Email address is not verified. Please verify your email first."
        )
    elif identifier_type == "mobile" and user_dict.get("is_mobile_verified") != "True":
        logger.warning(
            "Login attempt with unverified mobile",
            extra={"identifier": identifier, "user_id": user_dict.get("id")}
        )
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Mobile number is not verified. Please verify your mobile first."
        )

    # Validate email domain if email is provided
    if identifier_type == "email":
        try:
            is_valid, reason = await verify_email_exists_optimized(identifier, redis_client)
            if not is_valid:
                logger.warning(
                    "Invalid email domain for login",
                    extra={"identifier": identifier, "reason": reason}
                )
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Email validation failed: {reason}"
                )
        except HTTPException:
            raise
        except Exception as e:
            logger.error(
                "Email validation error during login",
                extra={"identifier": identifier, "error": str(e)}
            )
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email validation failed"
            )

    # Determine user role for email template
    user_role = "influencer"  # Default assumption, could be enhanced with role detection    # Send OTP and persist it in database
    await send_user_otp(
        identifier,
        identifier_type,
        redis_client,
        user_role,
        is_login=True,
        db_conn=db_conn
    )

    logger.info(
        "Login OTP sent successfully",
        extra={"identifier": identifier, "type": identifier_type, "user_id": user_dict.get("id")}
    )

    return OtpResponse(
        success=True,
        message=f"Verification code sent to your {identifier_type}",
        **{identifier_type: identifier}
    )


@router.post("/login/verify-otp", response_model=AuthTokenResponse)
@with_trace_id
async def login_verify_otp_endpoint(
        request: Request,
        data: LoginVerifyOTPRequest,
        db_conn=Depends(get_database),
        redis_client=Depends(get_locobuzz_redis),
        rbac_service=Depends(get_rbac_service_dependency)
):
    """
    Verify OTP for login and return authentication tokens.
    Common endpoint for both influencers and brands.
    
    Flow:
    1. Validate OTP
    2. Fetch user and verify status
    3. Update last login timestamp
    4. Create user session and generate tokens
    5. Return authentication response
    """
    identifier = data.identifier
    identifier_type = data.identifier_type.lower()

    if identifier_type not in ("email", "mobile"):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid identifier_type: {identifier_type}",
        )

    # Fetch user information first for user_id
    if identifier_type == "email":
        user_dict = await get_user_by_email_cache_aside(db_conn, redis_client, identifier)
    else:
        user_dict = await get_user_by_mobile_cache_aside(db_conn, redis_client, identifier)

    # Get user_id for UserOTP table
    user_id = user_dict.get("id") if user_dict else None

    # Verify OTP
    otp_manager = get_optimized_otp_manager(redis_client)
    if identifier_type == "email":
        is_valid, message = await otp_manager.verify_otp(identifier, data.otp)
    else:
        is_valid, message = await otp_manager.verify_mobile_otp(identifier, data.otp)

    # Get the number of failed attempts from redis for UserOTP update
    failed_attempts = 0
    otp_key = RedisKeys.otp_key(identifier) if identifier_type == "email" else RedisKeys.otp_mobile_key(identifier)
    otp_data = await redis_client.hgetall(otp_key)
    if otp_data and "failed_attempts" in otp_data:
        failed_attempts = int(otp_data.get("failed_attempts", 0))

    # Update UserOTP record if user_id is available
    if user_id:
        try:
            # Handle db operations in a separate session since we don't want to mix it
            # with the primary transaction below
            async with db_conn.get_db() as otp_session:
                await upsert_user_otp_entry(
                    db_session=otp_session,
                    user_id=str(user_id),
                    channel=identifier_type,
                    new_entry=False,
                    current_failed_attempts=failed_attempts
                )
                await otp_session.commit()
        except Exception as e:
            # Log error but continue with the flow
            logger.error(
                "Failed to update UserOTP entry",
                extra={"identifier": identifier, "user_id": user_id, "error": str(e)}
            )

    if not is_valid:
        logger.warning(
            "Login OTP verification failed",
            extra={"identifier": identifier, "type": identifier_type, "reason": message},
        )
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=message)

    try:
        # Fetch user and create session in a transaction
        async with db_conn.get_db() as session:
            async with session.begin():
                # Get user within the transaction to avoid detached instance issue
                if identifier_type == "email":
                    stmt = select(User).where(func.lower(User.email) == func.lower(identifier))
                else:
                    stmt = select(User).where(User.phone_number == identifier)

                result = await session.execute(stmt)
                user = result.scalar_one_or_none()

                if not user:
                    logger.warning(
                        "User not found during login OTP verification",
                        extra={"identifier": identifier, "type": identifier_type}
                    )
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail=f"No user found with this {identifier_type}"
                    )

                if not user.is_active:
                    logger.warning(
                        "Inactive user attempted login",
                        extra={"identifier": identifier, "user_id": str(user.id)}
                    )
                    raise HTTPException(
                        status_code=status.HTTP_403_FORBIDDEN,
                        detail="User account is not active"
                    )
                    
                # Check if the user has the correct role
                from app.utilities.user_utils import get_user_role_from_redis
                user_role = await get_user_role_from_redis(str(user.id), redis_client)
                if user_role and user_role != "influencer":
                    logger.warning(
                        "Login attempt with wrong endpoint",
                        extra={"identifier": identifier, "user_id": str(user.id), 
                               "expected_role": "influencer", "actual_role": user_role}
                    )
                    raise HTTPException(
                        status_code=status.HTTP_403_FORBIDDEN,
                        detail="Please use the correct login for your account type"
                    )

                # Update last login timestamp
                user.last_login_at = datetime.now(UTC)

                # Get auth method ID for OTP
                auth_method_id = await get_auth_method_id_by_name_cache_aside(
                    db_conn, redis_client, "email_otp" if identifier_type == "email" else "phone_otp"
                )

                # Create user auth method record if doesn't exist
                stmt = select(UserAuthMethod).where(
                    UserAuthMethod.user_id == user.id,
                    UserAuthMethod.auth_method_id == auth_method_id
                )
                existing_auth = await session.execute(stmt)
                if not existing_auth.scalar_one_or_none():
                    user_auth = UserAuthMethod(
                        user_id=user.id,
                        auth_method_id=auth_method_id
                    )
                    session.add(user_auth)
                
                # Inject RBAC service into request for permission-enabled tokens
                if rbac_service:
                    request.rbac_service = rbac_service
                
                # Create user session and get tokens
                # Pass the session-bound user instance to create_user_session
                token_data = await create_user_session(
                    session_user=user,
                    db_session=session,
                    redis_client=redis_client,
                    request=request
                )

                logger.info(
                    "User logged in successfully",
                    extra={
                        "user_id": str(user.id),
                        "identifier": identifier,
                        "session_id": token_data.get("session_id", "unknown"),
                    }
                )

        otp_key = RedisKeys.otp_key(str(data.email))
        # Clear OTP from Redis after successful verification
        await redis_client.delete(otp_key)
        return AuthTokenResponse(
            access_token=token_data["access_token"],
            refresh_token=token_data["refresh_token"],
            token_type=token_data["token_type"],
            expires_in=int(token_data["expires_in"])
        )
    except HTTPException:
        # pass through HTTP exceptions
        raise
    except Exception as e:
        logger.error(
            "Error during login OTP verification",
            extra={"identifier": identifier, "error": str(e)},
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to complete login process"
        )
