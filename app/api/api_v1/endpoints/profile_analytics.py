"""
Profile Analytics API endpoints for CreatorVerse Discovery
"""
from fastapi import APIRouter, HTTPException, Query, Path, Depends
from typing import Optional

from app.services.profile_analytics_service import ProfileAnalyticsService
from app.schemas.profile_analytics_schemas import (
    ProfileAnalyticsRequest, BasicProfileAnalyticsResponse,
    AudienceDemographicsResponse, AudienceInsightsResponse,
    SponsoredContentResponse, SimilarCreatorsResponse,
    DateRangeEnum
)
from app.utilities.response_handler import StandardResponse, StandardResponseModel
from app.core.exceptions import ProfileNotFoundError, DiscoveryError, ExternalAPIError
from app.core_helper.async_logger import with_trace_id

router = APIRouter()
analytics_service = ProfileAnalyticsService()


@router.get("/{profile_id}", response_model=StandardResponseModel[BasicProfileAnalyticsResponse])
@with_trace_id
async def get_profile_analytics(
    profile_id: str = Path(..., description="Profile ID"),
    include_audience: bool = Query(True, description="Include audience demographics"),
    include_content_analysis: bool = Query(True, description="Include content analysis"),
    refresh_external: bool = Query(False, description="Force refresh from external API"),
    date_range: Optional[DateRangeEnum] = Query(None, description="Date range for analytics")
):
    """
    Get basic profile analytics including performance metrics and content analysis
    
    This endpoint provides:
    - Performance overview metrics (followers, engagement, growth)
    - Historical reputation data
    - Top performing content
    - Content performance analysis
    - Hashtag and mention analysis
    """
    try:
        request = ProfileAnalyticsRequest(
            include_audience=include_audience,
            include_content_analysis=include_content_analysis,
            refresh_external=refresh_external,
            date_range=date_range
        )
        
        analytics_data = await analytics_service.get_basic_profile_analytics(profile_id, request)
        
        return StandardResponse.success(
            data=analytics_data.model_dump(mode='json') if analytics_data else None,
            message=f"Profile analytics retrieved for {profile_id}"
        )
        
    except ProfileNotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except ExternalAPIError as e:
        raise HTTPException(status_code=502, detail=f"External API error: {str(e)}")
    except DiscoveryError as e:
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.get("/{profile_id}/audience", response_model=StandardResponseModel[AudienceDemographicsResponse])
@with_trace_id
async def get_audience_demographics(
    profile_id: str = Path(..., description="Profile ID"),
    refresh_external: bool = Query(False, description="Force refresh from external API"),
    date_range: Optional[DateRangeEnum] = Query(None, description="Date range for analytics")
):
    """
    Get detailed audience demographics
    
    This endpoint provides:
    - Geographic distribution (countries, states, cities)
    - Age group breakdown
    - Gender distribution
    - Language preferences
    - Audience credibility score
    """
    try:
        request = ProfileAnalyticsRequest(
            include_audience=True,
            include_content_analysis=False,
            refresh_external=refresh_external,
            date_range=date_range
        )
        
        demographics_data = await analytics_service.get_audience_demographics(profile_id, request)
        
        return StandardResponse.success(
            data=demographics_data.model_dump(mode='json') if demographics_data else None,
            message=f"Audience demographics retrieved for {profile_id}"
        )
        
    except ProfileNotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except ExternalAPIError as e:
        raise HTTPException(status_code=502, detail=f"External API error: {str(e)}")
    except DiscoveryError as e:
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.get("/{profile_id}/audience-insights", response_model=StandardResponseModel[AudienceInsightsResponse])
@with_trace_id
async def get_audience_insights(
    profile_id: str = Path(..., description="Profile ID"),
    refresh_external: bool = Query(False, description="Force refresh from external API"),
    date_range: Optional[DateRangeEnum] = Query(None, description="Date range for analytics")
):
    """
    Get audience behavioral insights
    
    This endpoint provides:
    - Detailed interest analysis
    - Brand affinity data
    - Follower type distribution
    - Engagement behavior patterns
    """
    try:
        request = ProfileAnalyticsRequest(
            include_audience=True,
            include_content_analysis=False,
            refresh_external=refresh_external,
            date_range=date_range
        )
        
        insights_data = await analytics_service.get_audience_insights(profile_id, request)
        
        return StandardResponse.success(
            data=insights_data.model_dump(mode='json') if insights_data else None,
            message=f"Audience insights retrieved for {profile_id}"
        )
        
    except ProfileNotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except ExternalAPIError as e:
        raise HTTPException(status_code=502, detail=f"External API error: {str(e)}")
    except DiscoveryError as e:
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.get("/{profile_id}/sponsored-content", response_model=StandardResponseModel[SponsoredContentResponse])
@with_trace_id
async def get_sponsored_content_analysis(
    profile_id: str = Path(..., description="Profile ID"),
    refresh_external: bool = Query(False, description="Force refresh from external API"),
    date_range: Optional[DateRangeEnum] = Query(None, description="Date range for analytics")
):
    """
    Get sponsored content analysis
    
    This endpoint provides:
    - Sponsored content performance metrics
    - Pricing recommendations
    - Pricing factor explanations
    - Commercial content insights
    """
    try:
        request = ProfileAnalyticsRequest(
            include_audience=False,
            include_content_analysis=True,
            refresh_external=refresh_external,
            date_range=date_range
        )
        
        sponsored_data = await analytics_service.get_sponsored_content_analysis(profile_id, request)
        
        return StandardResponse.success(
            data=sponsored_data.model_dump(mode='json') if sponsored_data else None,
            message=f"Sponsored content analysis retrieved for {profile_id}"
        )
        
    except ProfileNotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except ExternalAPIError as e:
        raise HTTPException(status_code=502, detail=f"External API error: {str(e)}")
    except DiscoveryError as e:
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.get("/{profile_id}/similar-creators", response_model=StandardResponseModel[SimilarCreatorsResponse])
@with_trace_id
async def get_similar_creators(
    profile_id: str = Path(..., description="Profile ID"),
    limit: int = Query(10, ge=1, le=50, description="Number of similar creators to return")
):
    """
    Get similar creators recommendations
    
    This endpoint provides:
    - Similar creator profiles based on follower count, engagement rate, and category
    - Similarity scores for each recommendation
    - Basic metrics for comparison
    """
    try:
        similar_data = await analytics_service.get_similar_creators(profile_id, limit)
        
        return StandardResponse.success(
            data=similar_data.model_dump(mode='json') if similar_data else None,
            message=f"Similar creators retrieved for {profile_id}"
        )
        
    except ProfileNotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except DiscoveryError as e:
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.get("/{profile_id}/health", response_model=StandardResponseModel[dict])
@with_trace_id
async def get_profile_analytics_health(
    profile_id: str = Path(..., description="Profile ID")
):
    """
    Get profile analytics health status
    
    This endpoint provides:
    - Data freshness information
    - Cache status
    - Last update timestamps
    - Data quality indicators
    """
    try:
        # This is a simple health check endpoint
        # In a real implementation, you might check data freshness, cache status, etc.
        
        return StandardResponse.success(
            data={
                "profile_id": profile_id,
                "status": "healthy",
                "endpoints_available": [
                    "basic_analytics",
                    "audience_demographics", 
                    "audience_insights",
                    "sponsored_content",
                    "similar_creators"
                ],
                "last_checked": "2024-01-01T00:00:00Z"
            },
            message=f"Profile analytics health check for {profile_id}"
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Health check failed: {str(e)}")
