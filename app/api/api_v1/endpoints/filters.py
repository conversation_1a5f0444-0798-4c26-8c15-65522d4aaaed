"""
Filter Management API endpoints
"""
from fastapi import APIRouter, HTTPException, Query, Body, Path
from typing import Optional, List

from app.services.filter_service import FilterService
from app.schemas.filter_schemas import (
    SavedFilterSetCreate, SavedFilterSetUpdate, SavedFilterSetResponse, FilterMetadata
)
from app.utilities.response_handler import StandardResponse, StandardResponseModel
from app.core.exceptions import FilterValidationError, DiscoveryError

router = APIRouter()
filter_service = FilterService()


@router.post("/saved", response_model=StandardResponseModel[dict])
async def create_saved_filter_set(
    filter_data: SavedFilterSetCreate = Body(...),
    user_id: str = Query(..., description="User ID for ownership")
):
    """
    Create a new saved filter set
    
    Allows brands to save their filter criteria for future reuse.
    Filter sets can be marked as public to share with other users.
    """
    try:
        saved_filter = await filter_service.create_saved_filter_set(user_id, filter_data)
        
        return StandardResponse.success(
            data=saved_filter.dict(),
            message=f"Filter set '{filter_data.name}' created successfully"
        )
        
    except FilterValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except DiscoveryError as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/saved", response_model=StandardResponseModel[dict])
async def get_saved_filter_sets(
    user_id: str = Query(..., description="User ID for ownership"),
    include_public: bool = Query(True, description="Include public filter sets"),
    favorites_only: bool = Query(False, description="Show only favorite filter sets"),
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(50, ge=1, le=100, description="Results per page")
):
    """
    Get saved filter sets for a user
    
    Returns the user's saved filter sets, optionally including public sets
    from other users. Results can be filtered to show only favorites.
    """
    try:
        filter_sets = await filter_service.get_saved_filter_sets(
            user_id=user_id,
            include_public=include_public,
            favorites_only=favorites_only,
            page=page,
            page_size=page_size
        )
        
        return StandardResponse.success(
            data={
                "filter_sets": [fs.dict() for fs in filter_sets],
                "total_count": len(filter_sets),
                "page": page,
                "page_size": page_size
            },
            message=f"Retrieved {len(filter_sets)} saved filter sets"
        )
        
    except DiscoveryError as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/saved/{filter_set_id}", response_model=StandardResponseModel[dict])
async def get_saved_filter_set(
    filter_set_id: str = Path(..., description="Filter set ID"),
    user_id: Optional[str] = Query(None, description="User ID for access control")
):
    """
    Get a specific saved filter set
    
    Returns the details of a saved filter set. Public filter sets can be
    accessed by any user, while private sets require ownership.
    """
    try:
        filter_set = await filter_service.get_saved_filter_set(filter_set_id, user_id)
        
        return StandardResponse.success(
            data=filter_set.dict(),
            message="Filter set retrieved successfully"
        )
        
    except FilterValidationError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except DiscoveryError as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/saved/{filter_set_id}", response_model=StandardResponseModel[dict])
async def update_saved_filter_set(
    filter_set_id: str = Path(..., description="Filter set ID"),
    update_data: SavedFilterSetUpdate = Body(...),
    user_id: str = Query(..., description="User ID for ownership verification")
):
    """
    Update a saved filter set
    
    Allows users to modify their saved filter sets. Only the owner
    can update a filter set.
    """
    try:
        updated_filter = await filter_service.update_saved_filter_set(
            filter_set_id, user_id, update_data
        )
        
        return StandardResponse.success(
            data=updated_filter.dict(),
            message="Filter set updated successfully"
        )
        
    except FilterValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except DiscoveryError as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/saved/{filter_set_id}", response_model=StandardResponseModel[dict])
async def delete_saved_filter_set(
    filter_set_id: str = Path(..., description="Filter set ID"),
    user_id: str = Query(..., description="User ID for ownership verification")
):
    """
    Delete a saved filter set
    
    Permanently removes a saved filter set. Only the owner can delete
    their filter sets.
    """
    try:
        success = await filter_service.delete_saved_filter_set(filter_set_id, user_id)
        
        return StandardResponse.success(
            data={"deleted": success},
            message="Filter set deleted successfully"
        )
        
    except FilterValidationError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except DiscoveryError as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/metadata", response_model=StandardResponseModel[dict])
async def get_filter_metadata():
    """
    Get filter metadata for frontend
    
    Returns the complete filter schema including all available filter
    categories, types, and options. This is used by the frontend to
    dynamically render the filter interface.
    """
    try:
        metadata = await filter_service.get_filter_metadata()
        
        return StandardResponse.success(
            data=metadata.dict(),
            message="Filter metadata retrieved successfully"
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get filter metadata: {str(e)}")


@router.post("/saved/{filter_set_id}/clone", response_model=StandardResponseModel[dict])
async def clone_filter_set(
    filter_set_id: str = Path(..., description="Source filter set ID"),
    user_id: str = Query(..., description="User ID for new filter set ownership"),
    new_name: str = Body(..., embed=True, description="Name for the cloned filter set")
):
    """
    Clone an existing filter set
    
    Creates a copy of an existing filter set (public or owned) for the
    requesting user. Useful for creating variations of existing filters.
    """
    try:
        cloned_filter = await filter_service.clone_filter_set(
            filter_set_id, user_id, new_name
        )
        
        return StandardResponse.success(
            data=cloned_filter.dict(),
            message=f"Filter set cloned successfully as '{new_name}'"
        )
        
    except FilterValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except DiscoveryError as e:
        raise HTTPException(status_code=500, detail=str(e))
