from typing import Any, Dict
import asyncio

from fastapi import APIRouter, Depends, status
from sqlalchemy import select
from app.core.redis_keys import RedisConfig, RedisKeys
from app.core.exceptions import CreatorVerseError
from app.core.logger import get_logger
from app.models.user_models import Master<PERSON><PERSON>
from app.api.dependencies import get_locobuzz_redis, get_database
from app.utilities.response_handler import StandardResponse
from app.core_helper.async_logger import with_trace_id

common_router = APIRouter()

@common_router.get(
    "/master/roles",
    status_code=status.HTTP_200_OK
)
@with_trace_id
async def get_master_roles(
    redis_client=Depends(get_locobuzz_redis),
    db_conn=Depends(get_database)
):
    """
    Get master roles from Redis cache.
    If not found or if Red<PERSON> is unavailable, fetch from database and attempt to cache.
    Uses resilient cache-aside pattern with automatic fallback to database.
    """
    logger = get_logger()
    
    # Try to get roles from Redis with retry logic
    roles = None
    redis_available = True
    
    try:
        # Try to get data from Redis cache
        logger.info("Attempting to fetch roles from Redis cache")
        roles = await redis_client.hgetall(RedisKeys.rbac_roles())
        
    except Exception as e:
        # Redis unavailable, log the error and continue with database fallback
        logger.error(f"Redis error while fetching roles: {str(e)}", extra={"exception": str(e)})
        redis_available = False    # If Redis failed or returned no data, fetch from database
    if roles is None or not roles:
        try:
            logger.info("Fetching roles from database")
            # Fetch from database as fallback
            async with db_conn.get_db() as session:
                # Execute the select query
                query = select(MasterRole)
                result = await session.execute(query)
                # Fetch all rows from the result
                roles_query = result.scalars().all()
                roles = {str(role.id): role.role_name for role in roles_query}
                
                # Try to cache the roles in Redis if it's available
                if roles and redis_available:
                    logger.info(f"Caching {len(roles)} roles in Redis")
                    try:
                        # Use fire-and-forget approach for caching to not block the response
                        asyncio.create_task(cache_roles(redis_client, roles))
                    except Exception as cache_error:
                        # Non-critical error, just log it
                        logger.warning(f"Failed to queue role caching task: {str(cache_error)}")
                
        except Exception as db_error:
            logger.error(f"Database error while fetching roles: {str(db_error)}", 
                        extra={"exception": str(db_error)})
            raise CreatorVerseError(
                message="Unable to retrieve roles data from the database.",
                status_code=500
            )
    
    return StandardResponse.success(
        data=roles,
        message="Master roles retrieved successfully"
    )

@with_trace_id
async def cache_roles(redis_client, roles: Dict[str, str]) -> None:
    """
    Helper function to cache roles in Redis asynchronously.
    Uses the cache-aside pattern with automatic trace ID propagation.
    """
    logger = get_logger()
    try:
        # Set the hash with all role entries
        await redis_client.hmset(RedisKeys.rbac_roles(), roles)
        # Set expiration on the key
        await redis_client.expire(RedisKeys.rbac_roles(), RedisConfig.RBAC_TTL)
        logger.info("Successfully cached roles in Redis")
    except Exception as e:
        logger.error(f"Failed to cache roles in Redis: {str(e)}", 
                    extra={"exception": str(e)})
        # This is non-critical, so we just log the error and continue