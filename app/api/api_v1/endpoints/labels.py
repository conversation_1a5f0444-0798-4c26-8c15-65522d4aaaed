"""
Label management endpoints for CreatorVerse Backend.
Handles global labels, brand-specific label usage, and label assignments.
"""
from uuid import UUID
from typing import Optional, List

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.exc import IntegrityError

from app.api.dependencies import get_database, get_locobuzz_redis
from app.api.deps import get_current_user
from app.core.logger import get_logger
from app.core_helper.async_logger import with_trace_id
from app.models.user_models import User
from app.schemas.label_schemas import (
    CreateLabelRequest, UpdateLabelRequest, AssignLabelsToEntryRequest,
    BulkAssignLabelsRequest, LabelFilters, BrandLabelFilters,
    GlobalLabelResponse, BrandLabelResponse, LabelsListResponse,
    BrandLabelsListResponse, LabelAutocompleteResponse,
    GenericSuccessResponse, BulkOperationResponse,
    LabelAssignmentResponse
)
from app.services.label_service import (
    create_global_label, get_global_labels, update_global_label,
    delete_global_label, get_brand_labels, get_label_autocomplete,
    assign_labels_to_entry, remove_labels_from_entry, get_entry_labels
)

router = APIRouter()
logger = get_logger()


def get_uuid(id_obj) -> UUID:
    """Convert any ID object to a UUID safely"""
    return UUID(str(id_obj))


def get_current_user_id(current_user) -> UUID:
    """Extract user ID from current user"""
    if isinstance(current_user, dict):
        return UUID(current_user.get("sub"))
    return UUID(str(current_user.id))


# ──────────────────────────────────────────────────────────────────────────────
# GLOBAL LABEL MANAGEMENT ENDPOINTS
# ──────────────────────────────────────────────────────────────────────────────

@router.post("/labels", response_model=GlobalLabelResponse)
@with_trace_id
async def create_label_endpoint(
    data: CreateLabelRequest,
    db_conn=Depends(get_database),
    redis_client=Depends(get_locobuzz_redis),
    current_user: User = Depends(get_current_user)
):
    """
    Create a new global label.
    
    Global labels are shared across all brands in the system.
    """
    user_id = get_current_user_id(current_user)
    
    try:
        async with db_conn.get_db() as session:
            async with session.begin():
                new_label = await create_global_label(
                    session=session,
                    redis_client=redis_client,
                    label_data=data
                )
                
                logger.info(
                    f"Global label created successfully",
                    extra={
                        "user_id": str(user_id),
                        "label_id": str(new_label.id),
                        "label_name": data.name
                    }
                )
                
                return {
                    "id": str(new_label.id),
                    "name": new_label.name,
                    "description": new_label.description,
                    "color": new_label.color,
                    "usage_count": new_label.usage_count,
                    "created_at": new_label.created_at.isoformat(),
                    "updated_at": new_label.updated_at.isoformat()
                }
                
    except IntegrityError:
        logger.warning(
            f"Label name already exists",
            extra={"user_id": str(user_id), "name": data.name}
        )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="A label with this name already exists"
        )
    except Exception as e:
        logger.error(
            "Error creating global label",
            extra={"user_id": str(user_id), "error": str(e)}
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create label"
        )


@router.get("/labels", response_model=LabelsListResponse)
@with_trace_id
async def get_labels_endpoint(
    search: Optional[str] = Query(None, description="Search in name and description"),
    color: Optional[str] = Query(None, description="Filter by color"),
    min_usage: Optional[int] = Query(None, description="Minimum usage count"),
    max_usage: Optional[int] = Query(None, description="Maximum usage count"),
    db_conn=Depends(get_database),
    redis_client=Depends(get_locobuzz_redis),
    current_user: User = Depends(get_current_user)
):
    """
    Get all global labels with optional filtering.
    """
    user_id = get_current_user_id(current_user)
    
    try:
        # Create filters
        filters = LabelFilters(
            search=search,
            color=color,
            min_usage=min_usage,
            max_usage=max_usage
        ) if any([search, color, min_usage, max_usage]) else None
        
        labels = await get_global_labels(
            db_conn=db_conn,
            redis_client=redis_client,
            filters=filters
        )
        
        logger.info(
            f"Retrieved {len(labels)} global labels",
            extra={"user_id": str(user_id)}
        )
        
        return {
            "labels": labels,
            "total": len(labels)
        }
        
    except Exception as e:
        logger.error(
            "Error retrieving global labels",
            extra={"user_id": str(user_id), "error": str(e)}
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve labels"
        )


@router.put("/labels/{label_id}", response_model=GlobalLabelResponse)
@with_trace_id
async def update_label_endpoint(
    label_id: UUID,
    data: UpdateLabelRequest,
    db_conn=Depends(get_database),
    redis_client=Depends(get_locobuzz_redis),
    current_user: User = Depends(get_current_user)
):
    """
    Update a global label.
    """
    user_id = get_current_user_id(current_user)
    
    try:
        async with db_conn.get_db() as session:
            async with session.begin():
                updated_label = await update_global_label(
                    session=session,
                    redis_client=redis_client,
                    label_id=label_id,
                    updates=data
                )
                
                if not updated_label:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail="Label not found"
                    )
                
                logger.info(
                    f"Global label updated successfully",
                    extra={
                        "user_id": str(user_id),
                        "label_id": str(label_id)
                    }
                )
                
                return {
                    "id": str(updated_label.id),
                    "name": updated_label.name,
                    "description": updated_label.description,
                    "color": updated_label.color,
                    "usage_count": updated_label.usage_count,
                    "created_at": updated_label.created_at.isoformat(),
                    "updated_at": updated_label.updated_at.isoformat()
                }
                
    except HTTPException:
        raise
    except IntegrityError:
        logger.warning(
            f"Label name already exists",
            extra={"user_id": str(user_id), "label_id": str(label_id)}
        )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="A label with this name already exists"
        )
    except Exception as e:
        logger.error(
            "Error updating global label",
            extra={"user_id": str(user_id), "label_id": str(label_id), "error": str(e)}
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update label"
        )


@router.delete("/labels/{label_id}", response_model=GenericSuccessResponse)
@with_trace_id
async def delete_label_endpoint(
    label_id: UUID,
    db_conn=Depends(get_database),
    redis_client=Depends(get_locobuzz_redis),
    current_user: User = Depends(get_current_user)
):
    """
    Delete a global label.
    
    This will also remove all assignments of this label.
    """
    user_id = get_current_user_id(current_user)
    
    try:
        async with db_conn.get_db() as session:
            async with session.begin():
                deleted = await delete_global_label(
                    session=session,
                    redis_client=redis_client,
                    label_id=label_id
                )
                
                if not deleted:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail="Label not found"
                    )
                
                logger.info(
                    f"Global label deleted successfully",
                    extra={
                        "user_id": str(user_id),
                        "label_id": str(label_id)
                    }
                )
                
                return {
                    "success": True,
                    "message": "Label deleted successfully"
                }
                
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Error deleting global label",
            extra={"user_id": str(user_id), "label_id": str(label_id), "error": str(e)}
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete label"
        )


# ──────────────────────────────────────────────────────────────────────────────
# BRAND LABEL ENDPOINTS
# ──────────────────────────────────────────────────────────────────────────────

@router.get("/brands/{brand_id}/labels", response_model=BrandLabelsListResponse)
@with_trace_id
async def get_brand_labels_endpoint(
    brand_id: UUID,
    search: Optional[str] = Query(None, description="Search in name and description"),
    color: Optional[str] = Query(None, description="Filter by color"),
    min_usage: Optional[int] = Query(None, description="Minimum usage count"),
    max_usage: Optional[int] = Query(None, description="Maximum usage count"),
    created_by: Optional[UUID] = Query(None, description="Filter by creator"),
    db_conn=Depends(get_database),
    redis_client=Depends(get_locobuzz_redis),
    current_user: User = Depends(get_current_user)
):
    """
    Get labels used by a specific brand.
    """
    user_id = get_current_user_id(current_user)
    
    try:
        # Create filters
        filters = BrandLabelFilters(
            search=search,
            color=color,
            min_usage=min_usage,
            max_usage=max_usage,
            created_by=created_by
        ) if any([search, color, min_usage, max_usage, created_by]) else None
        
        labels = await get_brand_labels(
            db_conn=db_conn,
            redis_client=redis_client,
            brand_id=brand_id,
            filters=filters
        )
        
        logger.info(
            f"Retrieved {len(labels)} brand labels for brand {brand_id}",
            extra={"user_id": str(user_id), "brand_id": str(brand_id)}
        )
        
        return {
            "labels": labels,
            "total": len(labels)
        }
        
    except Exception as e:
        logger.error(
            "Error retrieving brand labels",
            extra={"user_id": str(user_id), "brand_id": str(brand_id), "error": str(e)}
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve brand labels"
        )


@router.get("/brands/{brand_id}/labels/autocomplete", response_model=LabelAutocompleteResponse)
@with_trace_id
async def get_label_autocomplete_endpoint(
    brand_id: UUID,
    q: Optional[str] = Query(None, description="Search query"),
    limit: int = Query(10, description="Maximum number of suggestions"),
    db_conn=Depends(get_database),
    redis_client=Depends(get_locobuzz_redis),
    current_user: User = Depends(get_current_user)
):
    """
    Get label autocomplete suggestions for a brand.
    
    Returns labels ordered by brand usage, then global usage.
    """
    user_id = get_current_user_id(current_user)
    
    try:
        suggestions = await get_label_autocomplete(
            db_conn=db_conn,
            redis_client=redis_client,
            brand_id=brand_id,
            query=q or "",
            limit=limit
        )
        
        logger.info(
            f"Retrieved {len(suggestions)} label suggestions for brand {brand_id}",
            extra={"user_id": str(user_id), "brand_id": str(brand_id), "query": q}
        )
        
        return {
            "suggestions": suggestions,
            "total": len(suggestions)
        }
        
    except Exception as e:
        logger.error(
            "Error retrieving label autocomplete",
            extra={"user_id": str(user_id), "brand_id": str(brand_id), "error": str(e)}
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve label suggestions"
        )


# ──────────────────────────────────────────────────────────────────────────────
# LABEL ASSIGNMENT ENDPOINTS
# ──────────────────────────────────────────────────────────────────────────────

@router.post("/entries/{entry_id}/labels", response_model=GenericSuccessResponse)
@with_trace_id
async def assign_labels_to_entry_endpoint(
    entry_id: UUID,
    data: AssignLabelsToEntryRequest,
    db_conn=Depends(get_database),
    redis_client=Depends(get_locobuzz_redis),
    current_user: User = Depends(get_current_user)
):
    """
    Assign labels to an influencer entry.
    """
    user_id = get_current_user_id(current_user)
    
    try:
        async with db_conn.get_db() as session:
            async with session.begin():
                success, errors = await assign_labels_to_entry(
                    session=session,
                    redis_client=redis_client,
                    entry_id=entry_id,
                    user_id=user_id,
                    label_ids=data.label_ids
                )
                
                if not success:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"Assignment failed: {'; '.join(errors)}"
                    )
                
                logger.info(
                    f"Labels assigned to entry successfully",
                    extra={
                        "user_id": str(user_id),
                        "entry_id": str(entry_id),
                        "label_count": len(data.label_ids)
                    }
                )
                
                return {
                    "success": True,
                    "message": f"Successfully assigned {len(data.label_ids)} labels"
                }
                
    except ValueError as e:
        logger.warning(
            f"Invalid request for assigning labels: {str(e)}",
            extra={"user_id": str(user_id), "entry_id": str(entry_id)}
        )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Error assigning labels to entry",
            extra={"user_id": str(user_id), "entry_id": str(entry_id), "error": str(e)}
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to assign labels"
        )


@router.delete("/entries/{entry_id}/labels", response_model=GenericSuccessResponse)
@with_trace_id
async def remove_labels_from_entry_endpoint(
    entry_id: UUID,
    label_ids: List[UUID] = Query(..., description="Label IDs to remove"),
    db_conn=Depends(get_database),
    redis_client=Depends(get_locobuzz_redis),
    current_user: User = Depends(get_current_user)
):
    """
    Remove labels from an influencer entry.
    """
    user_id = get_current_user_id(current_user)
    
    try:
        async with db_conn.get_db() as session:
            async with session.begin():
                success, errors = await remove_labels_from_entry(
                    session=session,
                    redis_client=redis_client,
                    entry_id=entry_id,
                    user_id=user_id,
                    label_ids=label_ids
                )
                
                if not success:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"Removal failed: {'; '.join(errors)}"
                    )
                
                logger.info(
                    f"Labels removed from entry successfully",
                    extra={
                        "user_id": str(user_id),
                        "entry_id": str(entry_id),
                        "label_count": len(label_ids)
                    }
                )
                
                return {
                    "success": True,
                    "message": f"Successfully removed {len(label_ids)} labels"
                }
                
    except ValueError as e:
        logger.warning(
            f"Invalid request for removing labels: {str(e)}",
            extra={"user_id": str(user_id), "entry_id": str(entry_id)}
        )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Error removing labels from entry",
            extra={"user_id": str(user_id), "entry_id": str(entry_id), "error": str(e)}
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to remove labels"
        )


@router.get("/entries/{entry_id}/labels")
@with_trace_id
async def get_entry_labels_endpoint(
    entry_id: UUID,
    db_conn=Depends(get_database),
    current_user: User = Depends(get_current_user)
):
    """
    Get all labels assigned to an entry.
    """
    user_id = get_current_user_id(current_user)
    
    try:
        labels = await get_entry_labels(
            db_conn=db_conn,
            entry_id=entry_id
        )
        
        logger.info(
            f"Retrieved {len(labels)} labels for entry {entry_id}",
            extra={"user_id": str(user_id), "entry_id": str(entry_id)}
        )
        
        return {
            "labels": labels,
            "total": len(labels)
        }
        
    except Exception as e:
        logger.error(
            "Error retrieving entry labels",
            extra={"user_id": str(user_id), "entry_id": str(entry_id), "error": str(e)}
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve entry labels"
        )


# ──────────────────────────────────────────────────────────────────────────────
# BULK OPERATIONS
# ──────────────────────────────────────────────────────────────────────────────

@router.post("/entries/labels/bulk-assign", response_model=BulkOperationResponse)
@with_trace_id
async def bulk_assign_labels_endpoint(
    data: BulkAssignLabelsRequest,
    db_conn=Depends(get_database),
    redis_client=Depends(get_locobuzz_redis),
    current_user: User = Depends(get_current_user)
):
    """
    Bulk assign labels to multiple entries.
    """
    user_id = get_current_user_id(current_user)
    
    try:
        successful = 0
        failed = 0
        errors = []
        
        async with db_conn.get_db() as session:
            async with session.begin():
                for entry_id in data.entry_ids:
                    try:
                        success, entry_errors = await assign_labels_to_entry(
                            session=session,
                            redis_client=redis_client,
                            entry_id=entry_id,
                            user_id=user_id,
                            label_ids=data.label_ids
                        )
                        
                        if success:
                            successful += 1
                        else:
                            failed += 1
                            errors.extend([f"Entry {entry_id}: {error}" for error in entry_errors])
                            
                    except Exception as e:
                        failed += 1
                        errors.append(f"Entry {entry_id}: {str(e)}")
        
        logger.info(
            f"Bulk label assignment completed: {successful} successful, {failed} failed",
            extra={
                "user_id": str(user_id),
                "total_entries": len(data.entry_ids),
                "successful": successful,
                "failed": failed
            }
        )
        
        return {
            "success": successful > 0,
            "total_processed": len(data.entry_ids),
            "successful": successful,
            "failed": failed,
            "errors": errors,
            "message": f"Processed {len(data.entry_ids)} entries: {successful} successful, {failed} failed"
        }
        
    except Exception as e:
        logger.error(
            "Error in bulk label assignment",
            extra={"user_id": str(user_id), "error": str(e)}
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to perform bulk assignment"
        )
