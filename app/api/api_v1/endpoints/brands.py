"""
Brand management endpoints for CreatorVerse Backend.
Handles brand creation, member management, and other brand-related operations.
"""
from datetime import datetime, UTC
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy import select
from sqlalchemy.exc import IntegrityError

from app.api.dependencies import get_database, get_locobuzz_redis
from app.api.deps import get_current_user
from app.core.logger import get_logger
from app.core_helper.async_logger import with_trace_id
from app.models.user_models import (
    User, Organization, BrandMembership, Brand, BrandMembershipStatus,
    OrgMemberRole
)
from app.schemas.brand_schemas import (
    BrandMembershipApprovalRequest,
    BrandPendingRequestsResponse
)
from app.schemas.brand_schemas import (
    CreateBrandRequest,
    BrandResponse,
    BrandListResponse,
    BrandMemberListResponse,
    GenericSuccessResponse
)
from app.utilities.brand_utils import (
    create_brand,
    request_join_brand,
    approve_join_request,
    deactivate_brand_member,
    get_brand_by_id,
    list_brand_members,
    list_brands_with_user_status,
    get_user_brand_status,
    _evict_member_caches
)
from app.utilities.brand_utils import (
    list_all_pending_requests_for_user
)
from app.utilities.organization_utils import get_user_organization_membership
from app.utilities.response_handler import StandardResponse

router = APIRouter()
logger = get_logger()


def get_uuid(id_obj) -> UUID:
    """Convert any ID object to a UUID safely"""
    return UUID(str(id_obj))


@router.post("", response_model=BrandResponse)
@with_trace_id
async def create_brand_endpoint(
        data: CreateBrandRequest,
        db_conn=Depends(get_database),
        redis_client=Depends(get_locobuzz_redis),
        current_user: User = Depends(get_current_user)
):
    """
    Create a new brand for the user's organization.
    
    The user must be an active member of the organization with admin or owner role.
    """
    try:
        # Get user's current organization
        if isinstance(current_user, dict):
            user_id = current_user.get("sub")

        org_membership = await get_user_organization_membership(
            db_conn=db_conn,
            redis_client=redis_client,
            user_id=UUID(str(user_id))
        )

        if not org_membership:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="You are not a member of any organization"
            )

        # Check if user has permission to create a brand (must be org admin or owner)
        if org_membership.get("role") not in ["admin", "owner", "member"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You don't have permission to create a brand in this organization"
            )

        async with db_conn.get_db() as session:
            async with session.begin():
                # Get organization
                org_query = select(Organization).where(
                    Organization.id == UUID(org_membership["organization_id"]),
                    Organization.is_active == True
                )
                result = await session.execute(org_query)
                org = result.scalar_one_or_none()

                if not org:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail="Organization not found"
                    )

                # Create brand
                brand, membership = await create_brand(
                    session=session,
                    redis_client=redis_client,
                    org=org,
                    creator_id=user_id,
                    name=data.name,
                    description=data.description,
                    logo_url=data.logo_url
                )

                logger.info(
                    f"Brand created successfully",
                    extra={
                        "user_id": str(user_id),
                        "organization_id": str(org.id),
                        "brand_id": str(brand.id),
                    }
                )

                # Format response
                response_data = {
                    "id": str(brand.id),
                    "name": brand.name,
                    "description": brand.description,
                    "logo_url": brand.logo_url,
                    "website_url": brand.website_url,
                    "contact_email": brand.contact_email,
                    "total_members": 1,
                    # Just the creator initially                    "created_at": brand.created_at.isoformat() if getattr(brand, "created_at", None) else None,
                    "updated_at": brand.updated_at.isoformat() if getattr(brand, "updated_at", None) else None,
                    "is_active": brand.is_active,
                    "user_relationship": {
                        "status": "active",
                        "role": membership.role
                    }
                }

                return response_data

    except HTTPException:
        raise
    except IntegrityError as e:
        # Handle unique constraint violations (e.g. brand name already exists)
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Brand with this name already exists in the organization"
        )
    except Exception as e:
        logger.error(
            "Error creating brand",
            extra={
                "user_id": str(user_id),
                "error": str(e)
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create brand"
        )


@router.post("/{brand_id}/join", response_model=GenericSuccessResponse)
@with_trace_id
async def join_brand_request_endpoint(
        brand_id: UUID,
        db_conn=Depends(get_database),
        redis_client=Depends(get_locobuzz_redis),
        current_user: User = Depends(get_current_user)
):
    """
    Request to join a brand.
    Creates a pending membership that requires approval from a brand admin.
    """
    if isinstance(current_user, dict):
        user_id = current_user.get("sub")
    try:
        # Get brand info
        brand_data = await get_brand_by_id(
            db_conn=db_conn,
            redis_client=redis_client,
            brand_id=brand_id
        )

        if not brand_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Brand not found"
            )

        if not brand_data.get("is_active", False):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="This brand is not currently accepting members"
            )

        async with db_conn.get_db() as session:
            async with session.begin():
                # Get brand object for relationship
                brand_query = select(Brand).where(Brand.id == brand_id)
                result = await session.execute(brand_query)
                brand = result.scalar_one_or_none()

                if not brand:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail="Brand not found"
                    )
                # Check if user already has a membership with this brand
                stmt = select(BrandMembership).where(
                    BrandMembership.brand_id == brand_id,
                    BrandMembership.user_id == user_id
                )
                existing_membership = await session.execute(stmt)
                existing_membership = existing_membership.scalar_one_or_none()

                if existing_membership:
                    # Check membership status
                    if existing_membership.status == BrandMembershipStatus.active:
                        logger.info(
                            "User is already an active member of this brand",
                            extra={
                                "user_id": str(user_id),
                                "brand_id": str(brand_id)
                            }
                        )
                        return {
                            "success": True,
                            "message": "You are already an active member of this brand"
                        }
                    elif existing_membership.status == BrandMembershipStatus.pending:
                        logger.info(
                            "User already has a pending join request for this brand",
                            extra={
                                "user_id": str(user_id),
                                "brand_id": str(brand_id)
                            }
                        )
                        return {
                            "success": True,
                            "message": "Your request is already pending approval"
                        }
                    elif existing_membership.status == BrandMembershipStatus.rejected:
                        # Update the membership to pending again
                        existing_membership.status = BrandMembershipStatus.pending
                        existing_membership.is_active = True
                        existing_membership.updated_at = datetime.now(UTC)
                        await _evict_member_caches(redis_client, brand_id, user_id)
                        logger.info(
                            "Rejected user membership changed to pending",
                            extra={
                                "user_id": str(user_id),
                                "brand_id": str(brand_id)
                            }
                        )
                        message = "Your request to join has been submitted and is awaiting approval"
                        return {
                            "success": True,
                            "message": message
                        }

                # Create new join request
                membership = await request_join_brand(
                    session=session,
                    redis_client=redis_client,
                    brand=brand,
                    user_id=user_id
                )

                logger.info(
                    "Brand join request submitted",
                    extra={
                        "user_id": str(user_id),
                        "brand_id": str(brand_id),
                        "status": membership.status.value
                    }
                )

                # Set message for new request
                message = "Your request to join has been submitted and is awaiting approval"

                return {
                    "success": True,
                    "message": message
                }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Error joining brand",
            extra={
                "user_id": str(user_id),
                "brand_id": str(brand_id),
                "error": str(e)
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to process brand join request"
        )


@router.get("/{brand_id}/members", response_model=BrandMemberListResponse)
@with_trace_id
async def list_brand_members_endpoint(
        brand_id: UUID,
        include_pending: bool = False,
        db_conn=Depends(get_database),
        redis_client=Depends(get_locobuzz_redis),
        current_user: User = Depends(get_current_user)
):
    """
    List all members of a brand.
    Includes pending requests if include_pending=true and user is admin/owner.
    """
    try:
        # Check if current user is a member of this brand
        user_brand_status = await get_user_brand_status(
            db_conn=db_conn,
            redis_client=redis_client,
            brand_id=brand_id,
            user_id=UUID(str(current_user.id))
        )

        if not user_brand_status or user_brand_status.get("status") != "active":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You are not a member of this brand"
            )

        # Only admins and owners can see pending members
        if include_pending and user_brand_status.get("role") not in ["brand_admin", "brand_owner"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Only brand admins and owners can view pending requests"
            )

        # Get members
        members = await list_brand_members(
            db_conn=db_conn,
            redis_client=redis_client,
            brand_id=brand_id,
            include_pending=include_pending
        )

        return {
            "members": members,
            "total": len(members)
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Error listing brand members",
            extra={
                "user_id": str(current_user.id),
                "brand_id": str(brand_id),
                "error": str(e)
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to list brand members"
        )


@router.get("", response_model=BrandListResponse)
@with_trace_id
async def list_user_brands_endpoint(
        db_conn=Depends(get_database),
        redis_client=Depends(get_locobuzz_redis),
        current_user: User = Depends(get_current_user)
):
    """
    List all brands for the current user's organization,
    with relationship status to the current user.
    """
    try:
        # Get user's current organization
        if isinstance(current_user, dict):
            user_id = current_user.get("sub")

        org_membership = await get_user_organization_membership(
            db_conn=db_conn,
            redis_client=redis_client,
            user_id=UUID(str(user_id))
        )

        if not org_membership:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="You are not a member of any organization"
            )

        org_id = org_membership.get("organization_id")

        # Get brands with user relationship
        brands = await list_brands_with_user_status(
            db_conn=db_conn,
            redis_client=redis_client,
            org_id=UUID(org_id),
            user_id=UUID(str(user_id))
        )

        return {
            "brands": brands,
            "total": len(brands)
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Error listing user brands",
            extra={
                "user_id": str(user_id),
                "error": str(e)
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to list brands"
        )


@router.get(
    "/pending-requests/all",
    status_code=status.HTTP_200_OK,
    response_model=BrandPendingRequestsResponse
)
@with_trace_id
async def get_all_pending_requests_endpoint(
        current_user=Depends(get_current_user),
        db_conn=Depends(get_database),
        redis_client=Depends(get_locobuzz_redis)
):
    """
    Get all pending brand join requests that current user can view.
    
    For organization owners: returns all pending requests across all brands in their organization.
    For brand owners/admins: returns only pending requests for brands they manage.
    
    Returns aggregated view of pending requests grouped by brand, with count summaries.
    """
    logger = get_logger()
    user_id = UUID(current_user.get("sub"))
    # Get user's organization membership
    org_membership = await get_user_organization_membership(
        db_conn=db_conn,
        redis_client=redis_client,
        user_id=user_id
    )

    if not org_membership:
        logger.warning(f"User {user_id} doesn't have an active organization membership")
        return StandardResponse.forbidden(
            message="You don't have an active organization membership"
        )

    # Get organization ID from membership
    org_id = UUID(org_membership.get("organization_id"))

    # Get pending requests based on user's role
    try:
        pending_requests = await list_all_pending_requests_for_user(
            db_conn=db_conn,
            redis_client=redis_client,
            user_id=user_id,
            org_id=org_id
        )

        logger.info(
            f"Retrieved {pending_requests.get('total_pending_requests', 0)} pending requests for user {user_id}",
            extra={
                "org_id": str(org_id),
                "user_role": org_membership.get("role"),
                "total_brands": len(pending_requests.get("brands_with_requests", [])),
            }
        )

        return StandardResponse.success(
            data=pending_requests,
            message="Pending join requests retrieved successfully"
        )

    except Exception as e:
        logger.error(
            f"Error fetching pending requests: {str(e)}",
            extra={"error": str(e), "user_id": str(user_id), "org_id": str(org_id)}
        )
        return StandardResponse.error(
            message="An error occurred while fetching pending requests",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.post(
    "/membership/approve",
    status_code=status.HTTP_200_OK
)
@with_trace_id
async def approve_brand_membership_endpoint(
        data: BrandMembershipApprovalRequest,
        current_user=Depends(get_current_user),
        db_conn=Depends(get_database),
        redis_client=Depends(get_locobuzz_redis)
):
    """
    Approve a pending brand membership request.
    
    Requires that the current user is either:
    1. An organization owner (can approve any brand membership in the organization)
    2. A brand owner or admin for the specific brand
    
    Flow:
    1. Validate the approver's permissions
    2. Get the pending membership details
    3. Approve the membership
    4. Invalidate relevant caches
    5. Return success response
    """
    logger = get_logger()
    user_id = UUID(current_user.get("sub"))

    # If membership_id is not provided, we must have both brand_id and user_id
    if not data.membership_id and not data.user_id:
        return StandardResponse.error(
            message="Either membership_id or user_id must be provided",
            status_code=status.HTTP_400_BAD_REQUEST
        )

    try:
        # Get user's organization membership to check permissions
        org_membership = await get_user_organization_membership(
            db_conn=db_conn,
            redis_client=redis_client,
            user_id=user_id
        )

        if not org_membership:
            logger.warning(f"User {user_id} doesn't have an active organization membership")
            return StandardResponse.forbidden(
                message="You don't have an active organization membership"
            )

        # Get the brand details
        async with db_conn.get_db() as session:
            # First get the brand to verify it exists and get its organization ID
            brand_stmt = select(Brand).where(Brand.id == data.brand_id)
            brand_result = await session.execute(brand_stmt)
            brand = brand_result.scalar_one_or_none()

            if not brand:
                logger.warning(f"Brand {data.brand_id} not found")
                return StandardResponse.not_found(message="Brand not found")

            # Check if user has permission to approve memberships
            is_org_owner = org_membership.get("role") == OrgMemberRole.owner.value

            if not is_org_owner:
                # Check if user is brand owner or admin
                user_brand_role_stmt = select(BrandMembership).where(
                    BrandMembership.brand_id == data.brand_id,
                    BrandMembership.user_id == user_id,
                    BrandMembership.is_active == True,
                    BrandMembership.status == BrandMembershipStatus.active,
                    BrandMembership.role.in_(["brand_owner", "brand_admin"])
                )
                user_brand_role_result = await session.execute(user_brand_role_stmt)
                user_brand_role = user_brand_role_result.scalar_one_or_none()

                if not user_brand_role:
                    logger.warning(
                        f"User {user_id} does not have permission to approve memberships for brand {data.brand_id}",
                        extra={"brand_id": str(data.brand_id), "user_role": org_membership.get("role")}
                    )
                    return StandardResponse.forbidden(
                        message="You don't have permission to approve memberships for this brand"
                    )

            # Get the membership to approve
            if data.membership_id:
                # Get by membership ID
                membership_stmt = select(BrandMembership).where(
                    BrandMembership.id == data.membership_id,
                    BrandMembership.brand_id == data.brand_id,
                    BrandMembership.status == BrandMembershipStatus.pending,
                    BrandMembership.is_active == True
                )
            else:
                # Get by user ID and brand ID
                membership_stmt = select(BrandMembership).where(
                    BrandMembership.brand_id == data.brand_id,
                    BrandMembership.user_id == data.user_id,
                    BrandMembership.status == BrandMembershipStatus.pending,
                    BrandMembership.is_active == True
                )

            membership_result = await session.execute(membership_stmt)
            membership = membership_result.scalar_one_or_none()

            if not membership:
                logger.warning(
                    f"No pending membership found for brand {data.brand_id}",
                    extra={"brand_id": str(data.brand_id),
                           "membership_id": str(data.membership_id) if data.membership_id else None}
                )
                return StandardResponse.not_found(message="No pending membership request found")
            # Approve the membership
            await approve_join_request(
                session=session,
                redis_client=redis_client,
                brand_id=data.brand_id,
                user_id=membership.user_id,
                approver_id=user_id
            )

            logger.info(
                f"Membership approved successfully for user {membership.user_id} in brand {data.brand_id}",
                extra={
                    "brand_id": str(data.brand_id),
                    "user_id": str(membership.user_id),
                    "approver_id": str(user_id)
                }
            )

            return StandardResponse.success(
                message="Membership approved successfully",
                data={"membership_id": str(membership.id), "user_id": str(membership.user_id)}
            )

    except ValueError as ve:
        logger.error(
            f"Value error approving membership: {str(ve)}",
            extra={"error": str(ve), "brand_id": str(data.brand_id)}
        )
        return StandardResponse.error(
            message=str(ve),
            status_code=status.HTTP_400_BAD_REQUEST
        )
    except Exception as e:
        logger.error(
            f"Error approving membership: {str(e)}",
            extra={"error": str(e), "brand_id": str(data.brand_id)}
        )
        return StandardResponse.internal_server_error(
            message="An error occurred while approving the membership"
        )


@router.post(
    "/membership/reject",
    status_code=status.HTTP_200_OK
)
@with_trace_id
async def reject_brand_membership_endpoint(
        data: BrandMembershipApprovalRequest,
        current_user=Depends(get_current_user),
        db_conn=Depends(get_database),
        redis_client=Depends(get_locobuzz_redis)
):
    """
    Reject a pending brand membership request.
    
    Requires that the current user is either:
    1. An organization owner (can reject any brand membership in the organization)
    2. A brand owner or admin for the specific brand
    
    Flow:
    1. Validate the rejector's permissions
    2. Get the pending membership details
    3. Reject the membership
    4. Invalidate relevant caches
    5. Return success response
    """
    logger = get_logger()
    user_id = UUID(current_user.get("sub"))

    # If membership_id is not provided, we must have both brand_id and user_id
    if not data.membership_id and not data.user_id:
        return StandardResponse.error(
            message="Either membership_id or user_id must be provided",
            status_code=status.HTTP_400_BAD_REQUEST
        )

    try:
        # Get user's organization membership to check permissions
        org_membership = await get_user_organization_membership(
            db_conn=db_conn,
            redis_client=redis_client,
            user_id=user_id
        )

        if not org_membership:
            logger.warning(f"User {user_id} doesn't have an active organization membership")
            return StandardResponse.forbidden(
                message="You don't have an active organization membership"
            )

        # Get the brand details
        async with db_conn.get_db() as session:
            # First get the brand to verify it exists and get its organization ID
            brand_stmt = select(Brand).where(Brand.id == data.brand_id)
            brand_result = await session.execute(brand_stmt)
            brand = brand_result.scalar_one_or_none()

            if not brand:
                logger.warning(f"Brand {data.brand_id} not found")
                return StandardResponse.not_found(message="Brand not found")

            # Check if user has permission to reject memberships
            is_org_owner = org_membership.get("role") == OrgMemberRole.owner.value

            if not is_org_owner:
                # Check if user is brand owner or admin
                user_brand_role_stmt = select(BrandMembership).where(
                    BrandMembership.brand_id == data.brand_id,
                    BrandMembership.user_id == user_id,
                    BrandMembership.is_active == True,
                    BrandMembership.status == BrandMembershipStatus.active,
                    BrandMembership.role.in_(["brand_owner", "brand_admin"])
                )
                user_brand_role_result = await session.execute(user_brand_role_stmt)
                user_brand_role = user_brand_role_result.scalar_one_or_none()

                if not user_brand_role:
                    logger.warning(
                        f"User {user_id} does not have permission to reject memberships for brand {data.brand_id}",
                        extra={"brand_id": str(data.brand_id), "user_role": org_membership.get("role")}
                    )
                    return StandardResponse.forbidden(
                        message="You don't have permission to reject memberships for this brand"
                    )

            # Get the membership to reject
            if data.membership_id:
                # Get by membership ID
                membership_stmt = select(BrandMembership).where(
                    BrandMembership.id == data.membership_id,
                    BrandMembership.brand_id == data.brand_id,
                    BrandMembership.status == BrandMembershipStatus.pending,
                    BrandMembership.is_active == True
                )
            else:
                # Get by user ID and brand ID
                membership_stmt = select(BrandMembership).where(
                    BrandMembership.brand_id == data.brand_id,
                    BrandMembership.user_id == data.user_id,
                    BrandMembership.status == BrandMembershipStatus.pending,
                    BrandMembership.is_active == True
                )

            membership_result = await session.execute(membership_stmt)
            membership = membership_result.scalar_one_or_none()

            if not membership:
                logger.warning(
                    f"No pending membership found for brand {data.brand_id}",
                    extra={"brand_id": str(data.brand_id),
                           "membership_id": str(data.membership_id) if data.membership_id else None}
                )
                return StandardResponse.not_found(message="No pending membership request found")

            # Reject the membership in a transaction
            async with session.begin():
                await deactivate_brand_member(
                    session=session,
                    redis_client=redis_client,
                    brand_id=data.brand_id,
                    user_id=membership.user_id
                )

            logger.info(
                f"Membership rejected successfully for user {membership.user_id} in brand {data.brand_id}",
                extra={
                    "brand_id": str(data.brand_id),
                    "user_id": str(membership.user_id),
                    "rejecter_id": str(user_id)
                }
            )

            return StandardResponse.success(
                message="Membership rejected successfully",
                data={"membership_id": str(membership.id), "user_id": str(membership.user_id)}
            )

    except ValueError as ve:
        logger.error(
            f"Value error rejecting membership: {str(ve)}",
            extra={"error": str(ve), "brand_id": str(data.brand_id)}
        )
        return StandardResponse.error(
            message=str(ve),
            status_code=status.HTTP_400_BAD_REQUEST
        )
    except Exception as e:
        logger.error(
            f"Error rejecting membership: {str(e)}",
            extra={"error": str(e), "brand_id": str(data.brand_id)}
        )
        return StandardResponse.internal_server_error(
            message="An error occurred while rejecting the membership"
        )
