"""
Authentication endpoints for brands in CreatorVerse Backend.
"""
from datetime import datetime, UTC


from fastapi import APIRouter, Depends, HTTPException, Request, status
from sqlalchemy import select, func, case

from app.api.dependencies import get_database, get_locobuzz_redis, get_rbac_service_dependency
from app.core.enums import UserStatus
from app.core.logger import get_logger
from app.core.redis_keys import RedisKeys
from app.core_helper.async_logger import with_trace_id
from app.models.user_models import User, UserRoleModel, UserAuthMethod
from app.schemas.auth import (
    AuthTokenBrandResponse,
    InfluencerRegisterRequest as BrandRegisterRequest,  # Reusing the same schema for now
    VerifyOTPRequest,
    ResendOTPRequest,
    LoginRequest,
    LoginVerifyOTPRequest,
    OtpResponse,
    AuthTokenResponse
)
from app.services.email_validation_service import verify_email_exists_optimized
from app.services.rbac_utils import get_auth_method_id_by_name_cache_aside
from app.services.session_service import create_user_session
from app.services.user_service import get_user_by_email_cache_aside, get_user_by_mobile_cache_aside
from app.utilities.bloom_filter import bloom_register
from app.utilities.otp_manager import get_optimized_otp_manager
from app.utilities.user_utils import validate_and_fetch_existing_user, send_user_otp, create_user, upsert_user_otp_entry
from app.utilities.brand_organization_utils import ensure_org_and_membership

router = APIRouter()
logger = get_logger()


@router.post("/register", response_model=OtpResponse)
@bloom_register(field="identifier", add_on_success=False)  # ✅ FIXED: Use identifier instead of email
@with_trace_id
async def register_brand_endpoint(
        data: BrandRegisterRequest,
        db_conn=Depends(get_database),
        redis_client=Depends(get_locobuzz_redis)
):
    """
    Register a new brand user with email or mobile and send OTP for verification.
    
    Flow:
    1. Check if user already exists
    2. Create user with status "requested" and is_active=False
    3. Generate and send OTP via email or SMS
    4. Return success response with OTP details
    """
    identifier = data.identifier
    identifier_type = data.identifier_type

    # First check for cooldown to avoid unnecessary processing
    cooldown_key = RedisKeys.otp_cooldown_key(data.email)
    cooldown_exists = await redis_client.exists(cooldown_key)

    if cooldown_exists:
        logger.warning(
            "OTP request blocked due to cooldown",
            extra={"identifier": identifier, "type": identifier_type}
        )
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail="Please wait 30 seconds before requesting another verification code"
        )
    
    # Validate email domain if email is provided and if already entry is created then return the existing user
    user_exists = await validate_and_fetch_existing_user(identifier, identifier_type, db_conn, redis_client)

    # Create new user with status requested
    if not user_exists:
        try:
            async with db_conn.get_db() as session:
                async with session.begin():
                    new_user = await create_user(session, data)
                    session.add(new_user)

                    # Commit to get the user ID
                    await session.commit()

                    logger.info(
                        f"New brand {identifier_type} user created",
                        extra={
                            "user_id": str(new_user.id),
                            "identifier": identifier
                        }
                    )
        except Exception as e:
            logger.error(
                "Error creating new brand user",
                extra={
                    "identifier": identifier,
                    "error": str(e)
                }
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create user account"            )

    # For registration, no need to persist OTP in database since we haven't created the user yet
    # or we need to activate it - this will be done during verification
    await send_user_otp(identifier, identifier_type, redis_client, user_role="brand")

    return OtpResponse(
        success=True,
        message=f"Verification code sent to your {identifier_type}",
        **{identifier_type: identifier}
    )


@router.post("/verify-otp", response_model=AuthTokenBrandResponse)
@bloom_register(field="identifier", add_on_success=True)  # ✅ FIXED: Use identifier instead of email
@with_trace_id
async def verify_brand_otp_endpoint(
        request: Request,
        data: VerifyOTPRequest,
        db_conn=Depends(get_database),
        redis_client=Depends(get_locobuzz_redis),
        rbac_service=Depends(get_rbac_service_dependency)
):
    """
    Verify OTP for brand registration and set up brand entities.

    Flow:
    1. Verify OTP (identical to influencer)
    2. Activate user account
    3. Ensure organization membership (create or associate)
    4. Generate auth tokens
    """
    # 1) Validate identifier_type
    identifier = data.identifier
    id_type = data.identifier_type.lower()
    if id_type not in ("email", "mobile"):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid identifier_type: {data.identifier_type}",
        )

    if id_type == "mobile":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Brand registration requires email. Mobile verification is not supported."
        )

    # First fetch user to get user_id - needed for OTP DB entry
    user_id = None
    user_dict = await get_user_by_email_cache_aside(db_conn, redis_client, identifier)
    if user_dict and user_dict.get("id"):
        user_id = user_dict.get("id")

    # 2) Verify OTP
    otp_manager = get_optimized_otp_manager(redis_client)
    is_valid, message = await otp_manager.verify_otp(identifier, data.otp)

    # Get the number of failed attempts from redis for UserOTP update
    failed_attempts = 0
    otp_key = RedisKeys.otp_key(identifier)
    otp_data = await redis_client.hgetall(otp_key)
    if otp_data and "failed_attempts" in otp_data:
        failed_attempts = int(otp_data.get("failed_attempts", 0))

    # Update UserOTP record if user_id is available
    if user_id:
        try:
            # Handle db operations in a separate session since we don't want to mix it
            # with the primary transaction below
            async with db_conn.get_db() as otp_session:
                await upsert_user_otp_entry(
                    db_session=otp_session,
                    user_id=str(user_id),
                    channel=id_type,
                    new_entry=False,
                    current_failed_attempts=failed_attempts
                )
                await otp_session.commit()
        except Exception as e:
            # Log error but continue with the flow
            logger.error(
                "Failed to update UserOTP entry",
                extra={"identifier": identifier, "user_id": user_id, "error": str(e)}
            )
    
    if not is_valid:
        logger.warning(
            "OTP verification failed",
            extra={"identifier": identifier, "type": id_type, "reason": message},
        )
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=message)

    try:
        # 3) Open one session & transaction
        async with db_conn.get_db() as session:
            async with session.begin():
                # 4) Fetch the user in one atomic call
                stmt = select(User).where(func.lower(User.email) == identifier.lower())
                result = await session.scalars(stmt)
                session_user = result.one_or_none()
                
                if session_user is None:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail="User not found"
                    )

                # 5) Mark verified & activate
                session_user.is_email_verified = True
                session_user.status = UserStatus.get_status_name(UserStatus.ACTIVE.value)
                session_user.is_active = True

                # 6) Lookup auth‐method ID
                auth_key = "email_otp"
                auth_method_id = await get_auth_method_id_by_name_cache_aside(
                    db_conn, redis_client, auth_key
                )

                # 7) Assign brand role from the request
                # Check if user already has this role to avoid duplicate key error
                existing_role_stmt = select(UserRoleModel).where(
                    UserRoleModel.user_id == session_user.id,
                    UserRoleModel.role_id == data.role_uuid
                )
                existing_role_result = await session.execute(existing_role_stmt)
                existing_role = existing_role_result.scalar_one_or_none()
                
                if not existing_role:
                    session.add(
                        UserRoleModel(user_id=session_user.id, role_id=data.role_uuid)
                    )
                    logger.info(
                        "Assigned brand role to user",
                        extra={"user_id": str(session_user.id), "role_uuid": str(data.role_uuid)}
                    )
                else:
                    logger.info(
                        "User already has brand role assigned",
                        extra={"user_id": str(session_user.id), "role_uuid": str(data.role_uuid)}
                    )

                # 8) Record the auth selection
                # Check if user already has this auth method to avoid duplicate key error
                existing_auth_stmt = select(UserAuthMethod).where(
                    UserAuthMethod.user_id == session_user.id,
                    UserAuthMethod.auth_method_id == auth_method_id
                )
                existing_auth_result = await session.execute(existing_auth_stmt)
                existing_auth = existing_auth_result.scalar_one_or_none()
                
                if not existing_auth:
                    auth_method = UserAuthMethod(
                        user_id=session_user.id,
                        auth_method_id=auth_method_id,
                        enabled_at=datetime.now(UTC),
                        is_enabled=True,
                    )
                    session.add(auth_method)
                    logger.debug(
                        "Added auth method for brand user",
                        extra={"user_id": str(session_user.id), "auth_method": auth_key}
                    )
                else:
                    # Update existing auth method to ensure it's enabled
                    existing_auth.is_enabled = True
                    existing_auth.enabled_at = datetime.now(UTC)
                    logger.debug(
                        "Updated existing auth method for brand user",
                        extra={"user_id": str(session_user.id), "auth_method": auth_key}
                    )
                
                # 9) Ensure organization and membership
                org, membership, org_created = await ensure_org_and_membership(
                    session=session,
                    redis_client=redis_client,
                    user=session_user
                )
                
                # Inject RBAC service into request for permission-enabled tokens
                if rbac_service:
                    request.rbac_service = rbac_service                # Save role to Redis for future authentication
                from app.utilities.user_utils import save_user_role_to_redis
                # Default to brand role, organization creation logic already handles admin status
                role = "brand-admin" if org_created else "brand"
                await save_user_role_to_redis(str(session_user.id), role, redis_client)
                
                logger.info(
                    f"Brand user associated with organization",
                    extra={
                        "user_id": str(session_user.id),
                        "organization_id": str(org.id),
                        "org_created": org_created,
                        "role": membership.role.value
                    }
                )                # 10) mint tokens & persist session
                token_data = await create_user_session(
                    session_user=session_user,
                    db_session=session,
                    redis_client=redis_client,
                    request=request,
                )
                
                # Send welcome email if email is available
                if session_user.email:
                    from app.services.email_service import get_email_service
                    email_service = get_email_service()
                    # Send welcome email asynchronously - we don't wait for it
                    # so that the user flow isn't affected if there's an email issue
                    await email_service.send_welcome_email(session_user.email, user_role="brand")
                    logger.info(
                        "Welcome email sent to brand user",
                        extra={"user_id": str(session_user.id), "email": session_user.email}
                    )

        # 11) Build and return response
        logger.info(
            "Brand user activated after OTP verification",
            extra={
                "user_id": str(session_user.id),
                "identifier": identifier,
                "session_id": token_data.get("session_id", "unknown"),
            },        )
        
        # Fetch brands for organization with user relationship status
        from app.utilities.brand_utils import list_brands_with_user_status
        
        organization_brands = await list_brands_with_user_status(
            db_conn=db_conn,
            redis_client=redis_client,
            org_id=org.id,
            user_id=session_user.id
        )
        
        # Extract org name safely
        org_name = None
        if hasattr(org, 'name') and org.name is not None:
            org_name = str(org.name)
        
        return AuthTokenBrandResponse(
            access_token=token_data["access_token"],
            refresh_token=token_data["refresh_token"],
            token_type=token_data["token_type"],
            expires_in=int(token_data["expires_in"]),
            is_first=org_created,  # Using the org_created flag from ensure_org_and_membership
            organization_id=str(org.id),
            organization_name=org_name,
            organization_brands=organization_brands
        )

    except HTTPException:
        # pass through 4xx
        raise
    except Exception as e:
        logger.error(
            "Error during brand user activation",
            extra={"identifier": identifier, "error": str(e)},
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to activate brand user account",
        )


@router.post("/resend-otp", response_model=OtpResponse)
@with_trace_id
async def resend_brand_otp_endpoint(
        data: ResendOTPRequest,
        db_conn=Depends(get_database),
        redis_client=Depends(get_locobuzz_redis)
):
    """
    Resend OTP for brand verification with cooldown enforcement.
    
    Flow:
    1. Check if user exists
    2. Check cooldown period (30s)
    3. Generate and send new OTP via email
    4. Return success response with OTP details
    """
    identifier = data.identifier
    identifier_type = data.identifier_type

    # Check if user exists
    if identifier_type == "email":
        user_dict = await get_user_by_email_cache_aside(db_conn, redis_client, identifier)
    else:  # mobile
        user_dict = await get_user_by_mobile_cache_aside(db_conn, redis_client, identifier)

    if not user_dict:
        logger.warning(
            "Resend OTP attempt for non-existent brand user",
            extra={
                "identifier": identifier,
                "type": identifier_type
            }
        )
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"No user found with this {identifier_type}"
        )

    # Validate email domain if email is provided
    if identifier_type == "email":
        try:
            is_valid, reason = await verify_email_exists_optimized(identifier, redis_client)
            if not is_valid:
                logger.warning(
                    "Invalid email domain provided",
                    extra={"identifier": identifier, "reason": reason}
                )
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Email validation failed: {reason}"
                )
        except HTTPException:
            raise
        except Exception as e:
            logger.error(
                "Email validation error",
                extra={"identifier": identifier, "error": str(e)}
            )
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email validation failed"
            )    # Check if OTP is in cooldown period
    otp_manager = get_optimized_otp_manager(redis_client)
    
    # Check OTP status
    if identifier_type == "email":
        _, attempts, cooldown = await otp_manager.check_existing_otp_status_pipeline(identifier)
    else:
        # For mobile, we'll need to check cooldown key directly
        cooldown_key = RedisKeys.otp_cooldown_key(f"mobile:{identifier}")
        cooldown = await redis_client.exists(cooldown_key)
        attempts = 0  # Default value

    if cooldown:
        logger.warning(
            "OTP resend attempt during cooldown period",
            extra={"identifier": identifier, "type": identifier_type}
        )
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail="Please wait before requesting another verification code"
        )    # Send OTP
    if identifier_type == "email":
        is_success, message = await otp_manager.resend_otp(identifier, user_role="brand")
        if not is_success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=message
            )
    else:
        # We'll use send_user_otp for mobile since there's no resend_mobile_otp
        response = await send_user_otp(
            identifier=identifier,
            identifier_type="mobile",
            redis_client=redis_client,
            user_role="brand",
            is_login=False
        )

    return OtpResponse(
        success=True,
        message=f"Verification code resent to your {identifier_type}",
        **{identifier_type: identifier}
    )


@router.post("/login", response_model=OtpResponse)
@with_trace_id
async def login_endpoint(
        data: LoginRequest,
        db_conn=Depends(get_database),
        redis_client=Depends(get_locobuzz_redis)
):
    """
    Login for brand users with email or mobile.
    
    Flow:
    1. Validate email/mobile
    2. Check if user exists and is active
    3. Generate and send OTP
    4. Return success response with OTP details
    """
    identifier = data.identifier
    identifier_type = data.identifier_type

    # Check if OTP is in cooldown period
    cooldown_key = RedisKeys.otp_cooldown_key(identifier) if identifier_type == "email" else RedisKeys.otp_cooldown_key(
        f"mobile:{identifier}")
    cooldown_exists = await redis_client.exists(cooldown_key)

    if cooldown_exists:
        logger.warning(
            "Login OTP request blocked due to cooldown",
            extra={"identifier": identifier, "type": identifier_type}
        )
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail="Please wait before requesting another verification code"
        )

    # Check if user exists and is active
    if identifier_type == "email":
        user_dict = await get_user_by_email_cache_aside(db_conn, redis_client, identifier)
    else:  # mobile
        user_dict = await get_user_by_mobile_cache_aside(db_conn, redis_client, identifier)

    if not user_dict:
        logger.warning(
            "Login attempt for non-existent brand user",
            extra={"identifier": identifier, "type": identifier_type}
        )
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"No account found with this {identifier_type}"
        )

    # Check if user is active
    if not user_dict.get("is_active", False):
        logger.warning(
            "Login attempt for inactive brand user",
            extra={"identifier": identifier, "type": identifier_type, "user_id": user_dict.get("id")}
        )
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Account is not active"
        )    # Generate and send OTP for login and persist it in database
    await send_user_otp(
        identifier, 
        identifier_type, 
        redis_client, 
        user_role="brand", 
        is_login=True,
        db_conn=db_conn
    )

    return OtpResponse(
        success=True,
        message=f"Login verification code sent to your {identifier_type}",
        **{identifier_type: identifier}
    )


@router.post("/login/verify-otp", response_model=AuthTokenBrandResponse)
@with_trace_id
async def login_verify_otp_endpoint(
        request: Request,
        data: LoginVerifyOTPRequest,
        db_conn=Depends(get_database),
        redis_client=Depends(get_locobuzz_redis),
        rbac_service=Depends(get_rbac_service_dependency)
):
    """
    Verify login OTP for brand users.
    
    Flow:
    1. Verify OTP
    2. Fetch user
    3. Generate auth tokens
    4. Return tokens
    """
    identifier = data.identifier
    id_type = data.identifier_type.lower()
    
    # Fetch user information first for user_id
    if id_type == "email":
        user_dict = await get_user_by_email_cache_aside(db_conn, redis_client, identifier) 
    else:
        user_dict = await get_user_by_mobile_cache_aside(db_conn, redis_client, identifier)
    
    # Get user_id for UserOTP table
    user_id = user_dict.get("id") if user_dict else None
    
    # Verify OTP
    otp_manager = get_optimized_otp_manager(redis_client)
    if id_type == "email":
        is_valid, message = await otp_manager.verify_otp(identifier, data.otp)
    else:
        is_valid, message = await otp_manager.verify_mobile_otp(identifier, data.otp)
    
    # Get the number of failed attempts from redis for UserOTP update
    failed_attempts = 0
    otp_key = RedisKeys.otp_key(identifier) if id_type == "email" else RedisKeys.otp_mobile_key(identifier)
    otp_data = await redis_client.hgetall(otp_key)
    if otp_data and "failed_attempts" in otp_data:
        failed_attempts = int(otp_data.get("failed_attempts", 0))
    
    # Update UserOTP record if user_id is available
    if user_id:
        try:
            # Handle db operations in a separate session since we don't want to mix it
            # with the primary transaction below
            async with db_conn.get_db() as otp_session:
                await upsert_user_otp_entry(
                    db_session=otp_session,
                    user_id=str(user_id),
                    channel=id_type,
                    new_entry=False,
                    current_failed_attempts=failed_attempts
                )
                await otp_session.commit()
        except Exception as e:
            # Log error but continue with the flow
            logger.error(
                "Failed to update UserOTP entry",
                extra={"identifier": identifier, "user_id": user_id, "error": str(e)}
            )

    if not is_valid:
        logger.warning(
            "Brand login OTP verification failed",
            extra={"identifier": identifier, "reason": message}
        )
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=message)    # Check if the user has the correct role
    from app.utilities.user_utils import get_user_role_from_redis
    if user_dict and "id" in user_dict:
        user_role = await get_user_role_from_redis(str(user_dict["id"]), redis_client)
        if user_role and not user_role.startswith("brand"):
            logger.warning(
                "Login attempt with wrong endpoint",
                extra={"identifier": identifier, "user_id": str(user_dict["id"]), 
                      "expected_role": "brand", "actual_role": user_role}
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Please use the correct login for your account type"
            )
        # Fetch the user
        if id_type == "email":
            user_dict = await get_user_by_email_cache_aside(db_conn, redis_client, identifier)
        else:  # mobile
            user_dict = await get_user_by_mobile_cache_aside(db_conn, redis_client, identifier)

        if not user_dict:
            logger.error(
                "User not found after successful OTP verification",
                extra={"identifier": identifier, "type": id_type}
            )
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )

        if not user_dict.get("is_active", False):
            logger.warning(
                "Login attempt for inactive brand user",
                extra={"identifier": identifier, "user_id": user_dict.get("id")}
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Account is not active"
            )

        # Fetch the user for session creation
        from sqlalchemy import select
        async with db_conn.get_db() as session:
            async with session.begin():

                stmt = select(User).where(User.id == user_dict["id"])
                result = await session.scalars(stmt)
                session_user = result.one_or_none()

                if not session_user:
                    logger.error(
                        "Could not fetch user for session creation",
                        extra={"user_id": user_dict.get("id")}
                    )
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail="User not found"
                    )

                # Update last login
                session_user.last_login_at = datetime.now(UTC)
                
                # Inject RBAC service into request for permission-enabled tokens
                if rbac_service:
                    request.rbac_service = rbac_service

                # Create session and generate tokens
                token_data = await create_user_session(
                    session_user=session_user,
                    db_session=session,
                    redis_client=redis_client,
                    request=request,
                )

    logger.info(
        "Brand user logged in successfully",
        extra={
            "user_id": str(session_user.id),
            "identifier": identifier,
            "session_id": token_data.get("session_id", "unknown"),
        }
    )    # Get user's organization
    async with db_conn.get_db() as session:
        from sqlalchemy import select
        from app.models.user_models import OrganizationMembership, Organization
        
        # Find user's active organization
        stmt = (
            select(Organization, OrganizationMembership)
            .join(OrganizationMembership, Organization.id == OrganizationMembership.organization_id)
            .where(
                OrganizationMembership.user_id == session_user.id,
                OrganizationMembership.is_active == True,
                Organization.is_active == True
            )
        )
        result = await session.execute(stmt)
        org_data = result.first()
        
        if not org_data:
            # User doesn't have an organization yet
            return AuthTokenBrandResponse(
                access_token=token_data["access_token"],
                refresh_token=token_data["refresh_token"],
                token_type=token_data["token_type"],
                expires_in=int(token_data["expires_in"]),
                is_first=False
            )
        
        org, membership = org_data
        
        # Fetch brands for organization with user relationship status
        from app.utilities.brand_utils import list_brands_with_user_status
        
        organization_brands = await list_brands_with_user_status(
            db_conn=db_conn,
            redis_client=redis_client,
            org_id=org.id,
            user_id=session_user.id
        )
        
        # Check if user is the organization owner
        is_owner = membership.role == "owner"
        
    return AuthTokenBrandResponse(
        access_token=token_data["access_token"],
        refresh_token=token_data["refresh_token"],
        token_type=token_data["token_type"],
        expires_in=int(token_data["expires_in"]),
        is_first=is_owner,
        organization_id=str(org.id),
        organization_name=org.name,
        organization_brands=organization_brands
    )
