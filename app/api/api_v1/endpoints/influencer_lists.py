"""
Influencer List management endpoints for CreatorVerse Backend.
Handles influencer list creation, management, and entry operations.
"""
from uuid import UUID
from typing import Optional

from fastapi import APIRouter, Depends, HTTPException, status, Query, File, UploadFile, Form
from sqlalchemy.exc import IntegrityError

from app.api.dependencies import get_database, get_locobuzz_redis
from app.api.deps import get_current_user
from app.core.logger import get_logger
from app.core_helper.async_logger import with_trace_id
from app.models.user_models import User
from app.schemas.influencer_schemas import (
    CreateInfluencerListRequest, UpdateInfluencerListRequest,
    AddInfluencerToListRequest, UpdateInfluencerInListRequest,
    BulkAddInfluencersRequest, MoveInfluencersRequest,
    InfluencerListResponse, InfluencerListDetailResponse,
    InfluencerListsResponse, InfluencerListOptionsResponse,
    BrandInfluencerListsResponse, GenericSuccessResponse,
    BulkOperationResponse, InfluencerListFilters, InfluencerEntryFilters,
    ImportResultResponse
)
from app.services.influencer_list_service import (
    create_influencer_list, get_brand_influencer_lists,
    get_influencer_list_detail, update_influencer_list,
    delete_influencer_list, add_influencer_to_list,
    update_influencer_in_list, remove_influencer_from_list,
    get_list_options_for_brand
)
from app.utilities.influencer_list_utils import (
    get_user_brand_lists_summary, validate_influencer_data,
    parse_csv_data, get_list_analytics, duplicate_list
)
from app.utilities.response_handler import StandardResponse

router = APIRouter()
logger = get_logger()


def get_uuid(id_obj) -> UUID:
    """Convert any ID object to a UUID safely"""
    return UUID(str(id_obj))


def get_current_user_id(current_user) -> UUID:
    """Extract user ID from current user"""
    if isinstance(current_user, dict):
        return UUID(current_user.get("sub"))
    return UUID(str(current_user.id))


# ──────────────────────────────────────────────────────────────────────────────
# INFLUENCER LIST MANAGEMENT ENDPOINTS  
# ──────────────────────────────────────────────────────────────────────────────

@router.post("/lists", response_model=InfluencerListResponse)
@with_trace_id
async def create_influencer_list_endpoint(
    data: CreateInfluencerListRequest,
    db_conn=Depends(get_database),
    redis_client=Depends(get_locobuzz_redis),
    current_user: User = Depends(get_current_user)
):
    """
    Create a new influencer list for a brand.
    
    The user must be a member of the brand.
    """
    user_id = get_current_user_id(current_user)
    
    try:
        async with db_conn.get_db() as session:
            async with session.begin():
                new_list = await create_influencer_list(
                    session=session,
                    redis_client=redis_client,
                    brand_id=data.brand_id,
                    creator_id=user_id,
                    name=data.name,
                    description=data.description
                )
                
                logger.info(
                    f"Influencer list created successfully",
                    extra={
                        "user_id": str(user_id),
                        "brand_id": str(data.brand_id),
                        "list_id": str(new_list.id),
                        "list_name": data.name
                    }
                )
                
                return {
                    "id": str(new_list.id),
                    "name": new_list.name,
                    "description": new_list.description,
                    "status": new_list.status.value,
                    "brand_id": str(new_list.brand_id),
                    "created_by": str(new_list.created_by),
                    "created_at": new_list.created_at.isoformat(),
                    "updated_at": new_list.updated_at.isoformat(),
                    "total_influencers": 0
                }
                
    except ValueError as e:
        logger.warning(
            f"Invalid request for creating influencer list: {str(e)}",
            extra={"user_id": str(user_id), "brand_id": str(data.brand_id)}
        )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except IntegrityError:
        logger.warning(
            f"List name already exists",
            extra={"user_id": str(user_id), "brand_id": str(data.brand_id), "name": data.name}
        )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="A list with this name already exists for this brand"
        )
    except Exception as e:
        logger.error(
            "Error creating influencer list",
            extra={"user_id": str(user_id), "error": str(e)}
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create influencer list"
        )


@router.get("/brands/{brand_id}/lists", response_model=InfluencerListsResponse)
@with_trace_id
async def get_brand_influencer_lists_endpoint(
    brand_id: UUID,
    status: Optional[str] = Query(None, description="Filter by status"),
    search: Optional[str] = Query(None, description="Search in name and description"),
    db_conn=Depends(get_database),
    redis_client=Depends(get_locobuzz_redis),
    current_user: User = Depends(get_current_user)
):
    """
    Get all influencer lists for a brand with optional filtering.
    """
    user_id = get_current_user_id(current_user)
    
    try:
        # Create filters
        filters = InfluencerListFilters(
            status=status,
            search=search
        )
        
        lists = await get_brand_influencer_lists(
            db_conn=db_conn,
            redis_client=redis_client,
            brand_id=brand_id,
            filters=filters if any([status, search]) else None
        )
        
        logger.info(
            f"Retrieved {len(lists)} influencer lists for brand {brand_id}",
            extra={"user_id": str(user_id), "brand_id": str(brand_id)}
        )
        
        return {
            "lists": lists,
            "total": len(lists)
        }
        
    except Exception as e:
        logger.error(
            "Error retrieving influencer lists",
            extra={"user_id": str(user_id), "brand_id": str(brand_id), "error": str(e)}
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve influencer lists"
        )


@router.get("/lists/{list_id}", response_model=InfluencerListDetailResponse)
@with_trace_id
async def get_influencer_list_detail_endpoint(
    list_id: UUID,
    status: Optional[str] = Query(None, description="Filter entries by status"),
    campaign: Optional[str] = Query(None, description="Filter by campaign"),
    search: Optional[str] = Query(None, description="Search in influencer names"),
    min_audience: Optional[int] = Query(None, description="Minimum audience size"),
    max_audience: Optional[int] = Query(None, description="Maximum audience size"),
    min_engagement: Optional[int] = Query(None, description="Minimum engagement rate (percentage * 100)"),
    max_engagement: Optional[int] = Query(None, description="Maximum engagement rate (percentage * 100)"),
    db_conn=Depends(get_database),
    redis_client=Depends(get_locobuzz_redis),
    current_user: User = Depends(get_current_user)
):
    """
    Get detailed influencer list with entries and optional filtering.
    """
    user_id = get_current_user_id(current_user)
    
    try:
        # Create entry filters
        entry_filters = InfluencerEntryFilters(
            status=status,
            campaign=campaign,
            search=search,
            min_audience=min_audience,
            max_audience=max_audience,
            min_engagement=min_engagement,
            max_engagement=max_engagement
        )
        
        list_detail = await get_influencer_list_detail(
            db_conn=db_conn,
            redis_client=redis_client,
            list_id=list_id,
            entry_filters=entry_filters if any([
                status, campaign, search, min_audience, max_audience, 
                min_engagement, max_engagement
            ]) else None
        )
        
        if not list_detail:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Influencer list not found"
            )
        
        logger.info(
            f"Retrieved influencer list detail for list {list_id}",
            extra={"user_id": str(user_id), "list_id": str(list_id)}
        )
        
        return list_detail
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Error retrieving influencer list detail",
            extra={"user_id": str(user_id), "list_id": str(list_id), "error": str(e)}
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve influencer list detail"
        )


@router.post("/lists/{list_id}/influencers", response_model=GenericSuccessResponse)
@with_trace_id
async def add_influencer_to_list_endpoint(
    list_id: UUID,
    data: AddInfluencerToListRequest,
    db_conn=Depends(get_database),
    redis_client=Depends(get_locobuzz_redis),
    current_user: User = Depends(get_current_user)
):
    """
    Add an influencer to a list.
    """
    user_id = get_current_user_id(current_user)
    
    try:
        async with db_conn.get_db() as session:
            async with session.begin():
                new_entry = await add_influencer_to_list(
                    session=session,
                    redis_client=redis_client,
                    list_id=list_id,
                    user_id=user_id,
                    influencer_data=data
                )
                
                logger.info(
                    f"Influencer added to list successfully",
                    extra={
                        "user_id": str(user_id),
                        "list_id": str(list_id),
                        "influencer_id": data.influencer_id,
                        "entry_id": str(new_entry.id)
                    }
                )
                
                return {
                    "success": True,
                    "message": "Influencer added to list successfully"
                }
                
    except ValueError as e:
        logger.warning(
            f"Invalid request for adding influencer to list: {str(e)}",
            extra={"user_id": str(user_id), "list_id": str(list_id), "influencer_id": data.influencer_id}
        )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except IntegrityError:
        logger.warning(
            f"Influencer already in list",
            extra={"user_id": str(user_id), "list_id": str(list_id), "influencer_id": data.influencer_id}
        )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="This influencer is already in the list"
        )
    except Exception as e:
        logger.error(
            "Error adding influencer to list",
            extra={"user_id": str(user_id), "list_id": str(list_id), "error": str(e)}
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to add influencer to list"
        )


@router.get("/brands/{brand_id}/lists/options", response_model=InfluencerListOptionsResponse)
@with_trace_id
async def get_brand_list_options_endpoint(
    brand_id: UUID,
    db_conn=Depends(get_database),
    redis_client=Depends(get_locobuzz_redis),
    current_user: User = Depends(get_current_user)
):
    """
    Get simple list options for a brand (for dropdowns/selects).
    """
    user_id = get_current_user_id(current_user)
    
    try:
        list_options = await get_list_options_for_brand(
            db_conn=db_conn,
            redis_client=redis_client,
            brand_id=brand_id
        )
        
        logger.info(
            f"Retrieved {len(list_options)} list options for brand {brand_id}",
            extra={"user_id": str(user_id), "brand_id": str(brand_id)}
        )
        
        return {
            "lists": list_options,
            "total": len(list_options)
        }
        
    except Exception as e:
        logger.error(
            "Error retrieving list options",
            extra={"user_id": str(user_id), "brand_id": str(brand_id), "error": str(e)}
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve list options"
        )


# ──────────────────────────────────────────────────────────────────────────────
# CSV IMPORT AND ADDITIONAL ENDPOINTS
# ──────────────────────────────────────────────────────────────────────────────

@router.post("/lists/{list_id}/import-csv", response_model=ImportResultResponse)
@with_trace_id
async def import_influencers_from_csv_endpoint(
    list_id: UUID,
    file: UploadFile = File(...),
    db_conn=Depends(get_database),
    redis_client=Depends(get_locobuzz_redis),
    current_user: User = Depends(get_current_user)
):
    """
    Import influencers from a CSV file to an existing list.
    """
    user_id = get_current_user_id(current_user)
    
    try:
        # Read CSV content
        csv_content = await file.read()
        csv_text = csv_content.decode('utf-8')
        
        # Parse CSV
        parsed_data, parse_errors = parse_csv_data(csv_text)
        
        if parse_errors:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"CSV parsing errors: {'; '.join(parse_errors)}"
            )
        
        # Process each row
        imported = 0
        skipped = 0
        errors = []
        
        async with db_conn.get_db() as session:
            async with session.begin():
                for i, row_data in enumerate(parsed_data):
                    try:
                        # Validate influencer data
                        is_valid, validated_request, validation_errors = await validate_influencer_data(row_data)
                        
                        if not is_valid:
                            skipped += 1
                            errors.append(f"Row {i+1}: {'; '.join(validation_errors)}")
                            continue
                        
                        # Add to list
                        await add_influencer_to_list(
                            session=session,
                            redis_client=redis_client,
                            list_id=list_id,
                            user_id=user_id,
                            influencer_data=validated_request
                        )
                        imported += 1
                        
                    except Exception as e:
                        skipped += 1
                        errors.append(f"Row {i+1}: {str(e)}")
        
        logger.info(
            f"CSV import completed: {imported} imported, {skipped} skipped",
            extra={
                "user_id": str(user_id),
                "list_id": str(list_id),
                "total_rows": len(parsed_data)
            }
        )
        
        return {
            "success": imported > 0,
            "total_rows": len(parsed_data),
            "imported": imported,
            "skipped": skipped,
            "errors": errors,
            "list_id": str(list_id),
            "message": f"Import completed: {imported} influencers added, {skipped} skipped"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Error importing CSV",
            extra={"user_id": str(user_id), "list_id": str(list_id), "error": str(e)}
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to import CSV file"
        )


@router.get("/lists/{list_id}/analytics")
@with_trace_id
async def get_list_analytics_endpoint(
    list_id: UUID,
    db_conn=Depends(get_database),
    redis_client=Depends(get_locobuzz_redis),
    current_user: User = Depends(get_current_user)
):
    """
    Get analytics and statistics for an influencer list.
    """
    user_id = get_current_user_id(current_user)
    
    try:
        analytics = await get_list_analytics(
            db_conn=db_conn,
            redis_client=redis_client,
            list_id=list_id
        )
        
        if not analytics:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Influencer list not found"
            )
        
        logger.info(
            f"Retrieved analytics for list {list_id}",
            extra={"user_id": str(user_id), "list_id": str(list_id)}
        )
        
        return analytics
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Error retrieving list analytics",
            extra={"user_id": str(user_id), "list_id": str(list_id), "error": str(e)}
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve list analytics"
        )


@router.get("/user/summary")
@with_trace_id
async def get_user_lists_summary_endpoint(
    brand_id: Optional[UUID] = Query(None, description="Optional brand ID filter"),
    db_conn=Depends(get_database),
    redis_client=Depends(get_locobuzz_redis),
    current_user: User = Depends(get_current_user)
):
    """
    Get summary of influencer lists for the current user.
    """
    user_id = get_current_user_id(current_user)
    
    try:
        summary = await get_user_brand_lists_summary(
            db_conn=db_conn,
            redis_client=redis_client,
            user_id=user_id,
            brand_id=brand_id
        )
        
        logger.info(
            f"Retrieved user lists summary",
            extra={"user_id": str(user_id), "brand_id": str(brand_id) if brand_id else None}
        )
        
        return summary
        
    except Exception as e:
        logger.error(
            "Error retrieving user lists summary",
            extra={"user_id": str(user_id), "error": str(e)}
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve lists summary"
        )
