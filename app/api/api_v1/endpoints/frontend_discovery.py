"""
Frontend Discovery API endpoints
Handles the FIXED frontend filter format and integrates with external APIs
"""
from fastapi import APIRouter, HTTPException, Query, Body, Depends
from typing import Optional, List, Dict, Any

from app.services.external_api_service import external_api_service
from app.services.creator_discovery_service import CreatorDiscoveryService
from app.schemas.external_api.frontend_schemas import (
    FrontendSearchRequest, FrontendFilterSelections, FrontendSearchResponse,
    CreatorProfile, SearchMetadata, PlatformEnum, OptionForEnum
)
from app.schemas.external_api.provider_interface import APIProviderType
from app.schemas.discovery_schemas import DiscoveryRequest, ViewMode
from app.schemas.filter_schemas import (
    DiscoveryFilters, DemographicFilters, PerformanceFilters, 
    ContentFilters, PlatformFilters, RangeFilter
)
from app.utilities.response_handler import StandardResponse, StandardResponseModel
from app.core.exceptions import CreatorVerseError, FilterValidationError
from app.core_helper.async_logger import with_trace_id

router = APIRouter()


@router.post("/search-frontend", response_model=StandardResponseModel[Dict[str, Any]])
@with_trace_id
async def search_creators_frontend_format(
    request: FrontendSearchRequest = Body(...),
    user_id: Optional[str] = Query(None, description="User ID for tracking"),
    include_internal: bool = Query(True, description="Include internal database results"),
    include_external: bool = Query(True, description="Include external API results"),
    provider: Optional[str] = Query(None, description="Specific external provider (phyllo, modash)")
):
    """
    Search creators using the FIXED frontend filter format
    
    This endpoint accepts the exact format that frontend sends and will never change.
    It can search both internal database and external APIs, or either exclusively.
    
    Frontend Format (FIXED):
    {
        "searchQuery": "optional text search",
        "filterSelections": {
            "channel": "instagram|youtube|tiktok",
            "optionFor": "creator|audience", 
            "filters": {
                "gender": ["male", "female"],
                "follower_count": {"min": 1000, "max": 100000},
                "location": ["mumbai", "delhi"],
                // ... other filters
            },
            "page": 1,
            "pageSize": 20,
            "sortBy": "follower_count",
            "sortOrder": "desc"
        },
        "includeExternal": true,
        "cachePreference": "prefer"
    }
    """
    try:
        results = []
        combined_metadata = {
            "total_count": 0,
            "page": request.filterSelections.page or 1,
            "page_size": request.filterSelections.pageSize or 20,
            "execution_time_ms": 0,
            "cache_hit": False,
            "external_api_calls": 0,
            "data_sources": []
        }
        
        # Search internal database if requested
        if include_internal:
            try:
                internal_results = await _search_internal_database(request, user_id)
                if internal_results:
                    results.extend(internal_results["profiles"])
                    combined_metadata["data_sources"].append("internal")
                    combined_metadata["total_count"] += len(internal_results["profiles"])
            except Exception as e:
                # Log error but continue with external search
                pass
        
        # Search external APIs if requested
        if include_external and request.includeExternal:
            try:
                # Determine provider
                provider_type = None
                if provider:
                    provider_type = APIProviderType(provider.lower())
                
                # Search external API
                external_response = await external_api_service.search_creators(
                    request, 
                    provider_type
                )
                
                results.extend(external_response.profiles)
                combined_metadata["data_sources"].append(external_response.metadata.data_sources[0])
                combined_metadata["total_count"] += external_response.metadata.total_count
                combined_metadata["external_api_calls"] += external_response.metadata.external_api_calls
                
            except Exception as e:
                # Log error but continue with any internal results
                if not include_internal or not results:
                    raise HTTPException(
                        status_code=500, 
                        detail=f"External search failed: {str(e)}"
                    )
        
        # Remove duplicates and sort results
        unique_results = _deduplicate_profiles(results)
        sorted_results = _sort_profiles(
            unique_results,
            request.filterSelections.sortBy or "follower_count",
            request.filterSelections.sortOrder or "desc"
        )
        
        # Apply pagination to combined results
        page = request.filterSelections.page or 1
        page_size = request.filterSelections.pageSize or 20
        start_idx = (page - 1) * page_size
        end_idx = start_idx + page_size
        paginated_results = sorted_results[start_idx:end_idx]
        
        # Update metadata
        combined_metadata["total_count"] = len(unique_results)
        combined_metadata["total_pages"] = (len(unique_results) + page_size - 1) // page_size
        combined_metadata["has_next"] = end_idx < len(unique_results)
        combined_metadata["has_previous"] = page > 1
        
        return StandardResponse.success(
            data={
                "profiles": [profile.dict() for profile in paginated_results],
                "metadata": combined_metadata,
                "applied_filters": request.filterSelections.filters,
                "search_query": request.searchQuery
            },
            message=f"Found {len(paginated_results)} creators (total: {combined_metadata['total_count']})"
        )
        
    except FilterValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except CreatorVerseError as e:
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Search failed: {str(e)}")


@router.post("/quick-search-frontend", response_model=StandardResponseModel[List[Dict[str, Any]]])
@with_trace_id
async def quick_search_frontend(
    query: str = Query(..., min_length=1, description="Search query"),
    platform: PlatformEnum = Query(..., description="Platform to search"),
    limit: int = Query(10, ge=1, le=50, description="Maximum results"),
    provider: Optional[str] = Query(None, description="External provider (phyllo, modash)"),
    include_external: bool = Query(True, description="Include external API results")
):
    """
    Quick search for creators by username/name using frontend format
    
    This provides a simplified search interface for autocomplete and quick lookups.
    """
    try:
        results = []
        
        # Search external API if requested
        if include_external:
            try:
                provider_type = None
                if provider:
                    provider_type = APIProviderType(provider.lower())
                
                external_results = await external_api_service.quick_search(
                    query, platform, limit, provider_type
                )
                results.extend(external_results)
                
            except Exception as e:
                # Continue without external results
                pass
        
        # Remove duplicates
        unique_results = _deduplicate_profiles(results)
        
        # Limit results
        limited_results = unique_results[:limit]
        
        return StandardResponse.success(
            data=[profile.dict() for profile in limited_results],
            message=f"Quick search found {len(limited_results)} creators for '{query}'"
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Quick search failed: {str(e)}")


@router.get("/creator-analytics/{creator_id}", response_model=StandardResponseModel[Dict[str, Any]])
@with_trace_id
async def get_creator_analytics_frontend(
    creator_id: str,
    platform: PlatformEnum = Query(..., description="Platform"),
    provider: Optional[str] = Query(None, description="External provider"),
    include_audience: bool = Query(True, description="Include audience insights"),
    date_range: Optional[str] = Query("30d", description="Date range (30d, 90d, 1y)")
):
    """
    Get detailed creator analytics using external APIs
    
    Returns comprehensive analytics data in a standardized format.
    """
    try:
        analytics_data = {}
        
        # Determine provider
        provider_type = None
        if provider:
            provider_type = APIProviderType(provider.lower())
        
        # Get creator analytics
        creator_analytics = await external_api_service.get_creator_analytics(
            creator_id, platform, provider_type
        )
        analytics_data["creator_analytics"] = creator_analytics
        
        # Get audience insights if requested
        if include_audience:
            try:
                audience_insights = await external_api_service.get_audience_insights(
                    creator_id, platform, provider_type
                )
                analytics_data["audience_insights"] = audience_insights
            except Exception as e:
                # Continue without audience data
                analytics_data["audience_insights"] = None
        
        return StandardResponse.success(
            data=analytics_data,
            message=f"Analytics retrieved for creator {creator_id}"
        )
        
    except CreatorVerseError as e:
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Analytics retrieval failed: {str(e)}")


@router.get("/providers", response_model=StandardResponseModel[List[Dict[str, Any]]])
async def get_available_providers():
    """
    Get list of available external API providers and their capabilities
    """
    try:
        providers = external_api_service.get_available_providers()
        
        provider_info = []
        for provider_type in providers:
            provider = external_api_service.get_provider(provider_type)
            provider_info.append({
                "type": provider_type.value,
                "capabilities": [cap.value for cap in provider.config.capabilities],
                "base_url": provider.config.base_url
            })
        
        return StandardResponse.success(
            data=provider_info,
            message=f"Found {len(provider_info)} available providers"
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get providers: {str(e)}")


@router.get("/health", response_model=StandardResponseModel[Dict[str, Any]])
async def health_check():
    """
    Health check for external API service and providers
    """
    try:
        health_status = await external_api_service.health_check()
        
        return StandardResponse.success(
            data=health_status,
            message="Health check completed"
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Health check failed: {str(e)}")


# ============ HELPER FUNCTIONS ============

async def _search_internal_database(
    request: FrontendSearchRequest, 
    user_id: Optional[str]
) -> Optional[Dict[str, Any]]:
    """
    Search internal database using existing discovery service
    
    This converts frontend format to internal DiscoveryFilters format.
    """
    try:
        # Convert frontend filters to internal format
        discovery_filters = _convert_frontend_to_discovery_filters(request.filterSelections)
        
        # Create discovery request
        discovery_request = DiscoveryRequest(
            filters=discovery_filters,
            search_query=request.searchQuery,
            view_mode=ViewMode.QUICK_VIEW,
            page=request.filterSelections.page or 1,
            page_size=request.filterSelections.pageSize or 20,
            include_external=False  # Only internal for this call
        )
        
        # Use existing discovery service
        discovery_service = CreatorDiscoveryService()
        profiles, meta = await discovery_service.discover_creators(discovery_request, user_id)
        
        # Convert to frontend format
        frontend_profiles = []
        for profile_dict in profiles:
            frontend_profile = CreatorProfile(
                id=profile_dict.get("id", ""),
                external_id=profile_dict.get("external_id"),
                platform_username=profile_dict.get("platform_username", ""),
                full_name=profile_dict.get("full_name"),
                platform=profile_dict.get("platform", ""),
                url=profile_dict.get("url"),
                image_url=profile_dict.get("image_url"),
                follower_count=profile_dict.get("follower_count"),
                engagement_rate=profile_dict.get("engagement_rate"),
                is_verified=profile_dict.get("is_verified"),
                location=profile_dict.get("city") or profile_dict.get("location"),
                category=profile_dict.get("primary_category"),
                data_source="internal"
            )
            frontend_profiles.append(frontend_profile)
        
        return {
            "profiles": frontend_profiles,
            "metadata": meta.dict()
        }
        
    except Exception as e:
        # Return None to indicate internal search failed
        return None


def _convert_frontend_to_discovery_filters(filter_selections: FrontendFilterSelections) -> DiscoveryFilters:
    """
    Convert frontend filter selections to internal DiscoveryFilters format
    
    This is a basic conversion - can be enhanced based on specific requirements.
    """
    # Initialize filter categories
    demographic = DemographicFilters()
    performance = PerformanceFilters()
    content = ContentFilters()
    platform = PlatformFilters()
    
    # Convert each filter
    for filter_name, value in filter_selections.filters.items():
        if value is None or value == "" or value == []:
            continue
        
        # Demographic filters
        if filter_name == "gender":
            demographic.gender = value if isinstance(value, list) else [value]
        elif filter_name == "age":
            demographic.age_group = value if isinstance(value, list) else [value]
        elif filter_name == "location":
            if isinstance(value, list):
                demographic.city = value
            elif isinstance(value, str):
                demographic.city = [value]
        elif filter_name == "language":
            demographic.language = value if isinstance(value, list) else [value]
        elif filter_name in ["is_verified", "verification"]:
            if isinstance(value, bool):
                demographic.is_verified = value
            elif isinstance(value, str):
                demographic.is_verified = value.lower() == "true"
        
        # Performance filters
        elif filter_name == "follower_count":
            if isinstance(value, dict) and ("min" in value or "max" in value):
                performance.follower_count = RangeFilter(
                    min_value=value.get("min"),
                    max_value=value.get("max")
                )
        elif filter_name == "engagement_rate":
            if isinstance(value, dict) and ("min" in value or "max" in value):
                performance.engagement_rate = RangeFilter(
                    min_value=value.get("min"),
                    max_value=value.get("max")
                )
        elif filter_name == "average_likes":
            if isinstance(value, dict) and ("min" in value or "max" in value):
                performance.average_likes = RangeFilter(
                    min_value=value.get("min"),
                    max_value=value.get("max")
                )
        elif filter_name == "credibility_score":
            if isinstance(value, dict) and ("min" in value or "max" in value):
                performance.credibility_score = RangeFilter(
                    min_value=value.get("min"),
                    max_value=value.get("max")
                )
        
        # Content filters
        elif filter_name in ["category", "interests"]:
            if isinstance(value, list):
                content.primary_category = value
            elif isinstance(value, str):
                content.primary_category = [value]
        
        # Platform filters
        elif filter_name == "platform":
            platform.platform = value if isinstance(value, list) else [value]
    
    # Add platform from filter selections
    if filter_selections.channel:
        platform.platform = [filter_selections.channel.value]
    
    return DiscoveryFilters(
        demographic=demographic if any(getattr(demographic, field) is not None for field in demographic.__fields__) else None,
        performance=performance if any(getattr(performance, field) is not None for field in performance.__fields__) else None,
        content=content if any(getattr(content, field) is not None for field in content.__fields__) else None,
        platform=platform if any(getattr(platform, field) is not None for field in platform.__fields__) else None
    )


def _deduplicate_profiles(profiles: List[CreatorProfile]) -> List[CreatorProfile]:
    """Remove duplicate profiles based on external_id and username"""
    seen = set()
    unique_profiles = []
    
    for profile in profiles:
        # Create identifier using external_id or platform:username
        identifier = profile.external_id or f"{profile.platform}:{profile.platform_username}"
        
        if identifier not in seen:
            seen.add(identifier)
            unique_profiles.append(profile)
    
    return unique_profiles


def _sort_profiles(profiles: List[CreatorProfile], sort_by: str, sort_order: str) -> List[CreatorProfile]:
    """Sort profiles by specified field and order"""
    reverse = sort_order.lower() == "desc"
    
    # Define sort key functions
    def get_sort_key(profile: CreatorProfile):
        if sort_by == "follower_count":
            return profile.follower_count or 0
        elif sort_by == "engagement_rate":
            return profile.engagement_rate or 0.0
        elif sort_by == "platform_username":
            return profile.platform_username.lower()
        elif sort_by == "full_name":
            return (profile.full_name or "").lower()
        else:
            # Default to follower_count
            return profile.follower_count or 0
    
    try:
        return sorted(profiles, key=get_sort_key, reverse=reverse)
    except Exception as e:
        # If sorting fails, return original list
        return profiles
