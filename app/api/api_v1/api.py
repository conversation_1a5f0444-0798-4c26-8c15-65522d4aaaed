"""
API router for CreatorVerse Backend
"""
from fastapi import APIRouter

from app.api.api_v1.endpoints import common, auth_influencer, auth_brands, auth_logout, oauth, brands, influencer_lists, labels

# Create main API router
api_router = APIRouter()

api_router.include_router(common.common_router, prefix="/common", tags=["common"])
api_router.include_router(auth_influencer.router, prefix="/auth/influencer", tags=["influencer-auth"])
api_router.include_router(auth_brands.router, prefix="/auth/brands", tags=["brand-auth"])
api_router.include_router(auth_logout.router, prefix="/auth", tags=["Authentication"])
api_router.include_router(oauth.router, prefix="/oauth", tags=["OAuth"])
api_router.include_router(brands.router, prefix="/brands", tags=["brands"])
api_router.include_router(influencer_lists.router, prefix="/influencers", tags=["influencer-lists"])
api_router.include_router(labels.router, prefix="/labels", tags=["labels"])
