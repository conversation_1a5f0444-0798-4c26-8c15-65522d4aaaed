from typing import Any

import jwt
from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth2P<PERSON>wordBearer

from app.core.config import APP_CONFIG

oauth2_scheme = OAuth2PasswordBearer(tokenUrl=f"/auth/login")


async def get_current_user(token: str = Depends(oauth2_scheme)) -> dict[str, Any]:
    """
    Decode and validate JW<PERSON> token to get current user information using PyJWT.
    
    Returns:
        Dict containing decoded token data with user info
        
    Raises:
        HTTPException: If token is invalid or expired
    """
    try:
        # Decode the JWT token using PyJWT directly with settings from config
        payload = jwt.decode(
            token,
            APP_CONFIG.secret_key,
            algorithms=[APP_CONFIG.jwt_algorithm]
        )
        
        if payload is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authentication token",
                headers={"WWW-Authenticate": "Bearer"},
            )
            
        return payload
        
    except jwt.ExpiredSignatureError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token has expired",
            headers={"WWW-Authenticate": "Bearer"},
        )
    except jwt.InvalidTokenError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication token",
            headers={"WWW-Authenticate": "Bearer"},
        )
    except Exception as e:
        if APP_CONFIG.logger:
            APP_CONFIG.logger.error(f"Token validation error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication failed",
            headers={"WWW-Authenticate": "Bearer"},
        )


# Blocked consumer email domains for brand registration
BLOCKED_CONSUMER_DOMAINS = {
    "gmail.com",
    "yahoo.com",
    "hotmail.com",
    "outlook.com",
    "icloud.com",
    "aol.com",
    "live.com",
    "msn.com",
    "ymail.com",
    "rocketmail.com",
    "protonmail.com",
    "tutanota.com",
    "mail.com",
    "gmx.com",
    "zoho.com",
    "fastmail.com"
}