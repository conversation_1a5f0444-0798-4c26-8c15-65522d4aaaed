"""
FastAPI dependencies for Redis, Database, RBAC service, and Logger access.
Used only for endpoint dependency injection.
"""

from typing import AsyncGenerator
from fastapi import Depends
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import APP_CONFIG, get_database, get_locobuzz_redis


def get_redis_dependency():
    """FastAPI dependency for Redis client access."""
    return get_locobuzz_redis()


async def get_db_dependency() -> AsyncGenerator[AsyncSession, None]:
    """FastAPI dependency for database session access."""
    db_conn = get_database()
    async with db_conn.get_db() as session:
        yield session


def get_logger_dependency():
    """FastAPI dependency for logger access."""
    return APP_CONFIG.logger


def get_rbac_service_dependency():
    """FastAPI dependency for RBAC service access."""
    from app.services.rbac_service import get_rbac_service
    return get_rbac_service()


# Type aliases for dependency injection
RedisClient = Depends(get_redis_dependency)
DatabaseSession = Depends(get_db_dependency)
Logger = Depends(get_logger_dependency)
RBACService = Depends(get_rbac_service_dependency)