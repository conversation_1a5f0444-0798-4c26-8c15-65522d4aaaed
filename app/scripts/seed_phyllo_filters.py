#!/usr/bin/env python3
"""
Phyllo Filter Seeding Script for CreatorVerse Discovery & Profile Analytics

This script populates the filter_catalog schema with comprehensive filter
definitions based on Phyllo API capabilities and frontend requirements.
"""

import asyncio
import json
from typing import Dict, List, Any
from uuid import uuid4

from app.core.config import get_database
from app.models.filter_models import (
    FilterGroup, FilterDefinition, LocationHierarchy,
    FilterTypeEnum, OptionForTypeEnum, PlatformEnum
)
from app.services.filter_catalog_service import filter_catalog_service


class PhylloFilterSeeder:
    """Seeder for Phyllo-based filter configurations"""
    
    def __init__(self):
        self.db = get_database()
        self.logger = None
    
    async def seed_all_filters(self):
        """Seed all filter configurations for all platforms"""
        print("🌱 Starting Phyllo Filter Seeding...")
        
        try:
            # Seed location hierarchy first
            await self.seed_location_hierarchy()
            
            # Seed filter groups and definitions
            await self.seed_instagram_filters()
            await self.seed_youtube_filters()
            await self.seed_tiktok_filters()
            
            # Invalidate cache
            await filter_catalog_service.invalidate_cache()
            
            print("✅ Phyllo filter seeding completed successfully!")
            
        except Exception as e:
            print(f"❌ Error during seeding: {str(e)}")
            raise
    
    async def seed_location_hierarchy(self):
        """Seed location hierarchy data"""
        print("📍 Seeding location hierarchy...")
        
        locations = [
            # Tier 1 Cities
            {"name": "Mumbai", "code": "mumbai", "tier": "1", "level": 1, "population": 12442373},
            {"name": "Delhi", "code": "delhi", "tier": "1", "level": 1, "population": 11007835},
            {"name": "Bangalore", "code": "bangalore", "tier": "1", "level": 1, "population": 8443675},
            {"name": "Hyderabad", "code": "hyderabad", "tier": "1", "level": 1, "population": 6809970},
            {"name": "Chennai", "code": "chennai", "tier": "1", "level": 1, "population": 4681087},
            {"name": "Kolkata", "code": "kolkata", "tier": "1", "level": 1, "population": 4496694},
            {"name": "Pune", "code": "pune", "tier": "1", "level": 1, "population": 3124458},
            {"name": "Ahmedabad", "code": "ahmedabad", "tier": "1", "level": 1, "population": 5633927},
            
            # Tier 2 Cities
            {"name": "Surat", "code": "surat", "tier": "2", "level": 1, "population": 4467797},
            {"name": "Jaipur", "code": "jaipur", "tier": "2", "level": 1, "population": 3046163},
            {"name": "Lucknow", "code": "lucknow", "tier": "2", "level": 1, "population": 2817105},
            {"name": "Kanpur", "code": "kanpur", "tier": "2", "level": 1, "population": 2767031},
            {"name": "Nagpur", "code": "nagpur", "tier": "2", "level": 1, "population": 2405421},
            {"name": "Indore", "code": "indore", "tier": "2", "level": 1, "population": 1964086},
            {"name": "Bhopal", "code": "bhopal", "tier": "2", "level": 1, "population": 1798218},
            {"name": "Coimbatore", "code": "coimbatore", "tier": "2", "level": 1, "population": 1061447},
            {"name": "Visakhapatnam", "code": "visakhapatnam", "tier": "2", "level": 1, "population": 1730320},
            {"name": "Patna", "code": "patna", "tier": "2", "level": 1, "population": 1684222},
            
            # Tier 3 Cities
            {"name": "Aligarh", "code": "aligarh", "tier": "3", "level": 1, "population": 874408},
            {"name": "Gorakhpur", "code": "gorakhpur", "tier": "3", "level": 1, "population": 673446},
            {"name": "Gaya", "code": "gaya", "tier": "3", "level": 1, "population": 463454},
            {"name": "Saharanpur", "code": "saharanpur", "tier": "3", "level": 1, "population": 703345},
            {"name": "Bhavnagar", "code": "bhavnagar", "tier": "3", "level": 1, "population": 593768},
        ]
        
        async with self.db.get_db() as session:
            for location_data in locations:
                # Check if location already exists by code
                from sqlalchemy import select
                result = await session.execute(
                    select(LocationHierarchy).where(LocationHierarchy.code == location_data["code"])
                )
                existing = result.scalar_one_or_none()

                if not existing:
                    location = LocationHierarchy(
                        name=location_data["name"],
                        code=location_data["code"],
                        tier=location_data["tier"],
                        level=location_data["level"],
                        population=location_data["population"],
                        is_active=True
                    )
                    session.add(location)
            
            await session.commit()
        
        print(f"✅ Seeded {len(locations)} locations")
    
    async def seed_instagram_filters(self):
        """Seed Instagram filter configurations"""
        print("📸 Seeding Instagram filters...")
        
        # Creator filters
        await self._seed_filter_group(
            name="Demography & Identity",
            option_for=OptionForTypeEnum.creator,
            channel=PlatformEnum.instagram,
            description="Basic demographic and identity filters for creators",
            filters=self._get_instagram_creator_demographic_filters()
        )
        
        await self._seed_filter_group(
            name="Performance Metrics",
            option_for=OptionForTypeEnum.creator,
            channel=PlatformEnum.instagram,
            description="Engagement, growth, and performance indicators",
            filters=self._get_instagram_creator_performance_filters()
        )
        
        await self._seed_filter_group(
            name="Content & Niche",
            option_for=OptionForTypeEnum.creator,
            channel=PlatformEnum.instagram,
            description="Content categories, keywords, and niche classification",
            filters=self._get_instagram_creator_content_filters()
        )
        
        await self._seed_filter_group(
            name="Credibility & Platform",
            option_for=OptionForTypeEnum.creator,
            channel=PlatformEnum.instagram,
            description="Verification status and platform credibility metrics",
            filters=self._get_instagram_creator_credibility_filters()
        )
        
        # Audience filters
        await self._seed_filter_group(
            name="Demography & Identity",
            option_for=OptionForTypeEnum.audience,
            channel=PlatformEnum.instagram,
            description="Audience demographic and identity characteristics",
            filters=self._get_instagram_audience_demographic_filters()
        )
        
        await self._seed_filter_group(
            name="Interests",
            option_for=OptionForTypeEnum.audience,
            channel=PlatformEnum.instagram,
            description="Audience interests, behaviors, and preferences",
            filters=self._get_instagram_audience_interest_filters()
        )
    
    async def seed_youtube_filters(self):
        """Seed YouTube filter configurations"""
        print("📺 Seeding YouTube filters...")
        
        # Creator filters
        await self._seed_filter_group(
            name="Demography & Identity",
            option_for=OptionForTypeEnum.creator,
            channel=PlatformEnum.youtube,
            description="Creator demographic and identity filters",
            filters=self._get_youtube_creator_demographic_filters()
        )
        
        await self._seed_filter_group(
            name="Performance Metrics",
            option_for=OptionForTypeEnum.creator,
            channel=PlatformEnum.youtube,
            description="Subscriber growth, view metrics, and engagement",
            filters=self._get_youtube_creator_performance_filters()
        )
        
        await self._seed_filter_group(
            name="Content & Niche",
            option_for=OptionForTypeEnum.creator,
            channel=PlatformEnum.youtube,
            description="Video categories, keywords, and content classification",
            filters=self._get_youtube_creator_content_filters()
        )
        
        await self._seed_filter_group(
            name="Credibility & Platform",
            option_for=OptionForTypeEnum.creator,
            channel=PlatformEnum.youtube,
            description="Channel verification and credibility indicators",
            filters=self._get_youtube_creator_credibility_filters()
        )
    
    async def seed_tiktok_filters(self):
        """Seed TikTok filter configurations"""
        print("🎵 Seeding TikTok filters...")
        
        # Creator filters
        await self._seed_filter_group(
            name="Demography & Identity",
            option_for=OptionForTypeEnum.creator,
            channel=PlatformEnum.tiktok,
            description="Creator demographic and identity filters",
            filters=self._get_tiktok_creator_demographic_filters()
        )
        
        await self._seed_filter_group(
            name="Performance Metrics",
            option_for=OptionForTypeEnum.creator,
            channel=PlatformEnum.tiktok,
            description="Followers, likes, views, and engagement",
            filters=self._get_tiktok_creator_performance_filters()
        )
        
        await self._seed_filter_group(
            name="Content & Niche",
            option_for=OptionForTypeEnum.creator,
            channel=PlatformEnum.tiktok,
            description="Content categories and trending topics",
            filters=self._get_tiktok_creator_content_filters()
        )
    
    async def _seed_filter_group(
        self,
        name: str,
        option_for: OptionForTypeEnum,
        channel: PlatformEnum,
        description: str,
        filters: List[Dict[str, Any]],
        sort_order: int = 0
    ):
        """Seed a filter group with its definitions"""
        async with self.db.get_db() as session:
            # Check if group already exists
            from sqlalchemy import select
            result = await session.execute(
                select(FilterGroup).where(
                    FilterGroup.name == name,
                    FilterGroup.option_for == option_for,
                    FilterGroup.channel == channel
                )
            )
            existing_group = result.scalar_one_or_none()
            
            if existing_group:
                group = existing_group
                print(f"  📁 Updated existing group: {name}")
            else:
                group = FilterGroup(
                    name=name,
                    option_for=option_for,
                    channel=channel,
                    description=description,
                    sort_order=sort_order,
                    is_active=True
                )
                session.add(group)
                await session.flush()  # Get the ID
                print(f"  📁 Created group: {name}")
            
            # Add filter definitions
            for i, filter_data in enumerate(filters):
                # Check if definition already exists
                result = await session.execute(
                    select(FilterDefinition).where(
                        FilterDefinition.group_id == group.id,
                        FilterDefinition.name == filter_data["name"]
                    )
                )
                existing_def = result.scalar_one_or_none()
                
                if not existing_def:
                    definition = FilterDefinition(
                        group_id=group.id,
                        name=filter_data["name"],
                        filter_type=FilterTypeEnum(filter_data["filter_type"]),
                        icon=filter_data.get("icon"),
                        has_minmax=filter_data.get("has_minmax", False),
                        has_enter_value=filter_data.get("has_enter_value", False),
                        has_search_box=filter_data.get("has_search_box", False),
                        placeholder=filter_data.get("placeholder"),
                        options=filter_data.get("options", []),
                        db_field=filter_data.get("db_field"),
                        api_field=filter_data.get("api_field"),
                        validation_rules=filter_data.get("validation_rules", {}),
                        sort_order=i,
                        is_active=True
                    )
                    session.add(definition)
                    print(f"    🔧 Added filter: {filter_data['name']}")
            
            await session.commit()
    
    def _get_instagram_creator_demographic_filters(self) -> List[Dict[str, Any]]:
        """Get Instagram creator demographic filters"""
        return [
            {
                "name": "Gender",
                "filter_type": "radio_button",
                "icon": "gender-icon",
                "placeholder": "Select Gender",
                "api_field": "creator_gender",
                "options": [
                    {"label": "Male", "value": "male", "description": ""},
                    {"label": "Female", "value": "female", "description": ""},
                    {"label": "Other", "value": "other", "description": ""}
                ]
            },
            {
                "name": "Age",
                "filter_type": "checkbox",
                "icon": "age-icon",
                "has_minmax": True,
                "placeholder": "Select Age Range",
                "api_field": "creator_age",
                "options": [
                    {"label": "Teen", "value": "13-19", "description": "13-19"},
                    {"label": "Young Adult", "value": "20-35", "description": "20-35"},
                    {"label": "Adult", "value": "36-55", "description": "36-55"},
                    {"label": "Senior", "value": "56+", "description": "56+"}
                ]
            },
            {
                "name": "Location",
                "filter_type": "multilevel_checkbox",
                "icon": "location-icon",
                "has_search_box": True,
                "placeholder": "Search Location",
                "api_field": "creator_locations",
                "options": []  # Will be built dynamically from location hierarchy
            },
            {
                "name": "Language",
                "filter_type": "checkbox",
                "icon": "language-icon",
                "has_search_box": True,
                "placeholder": "Search Language",
                "api_field": "creator_language",
                "options": [
                    {"label": "English", "value": "english", "description": ""},
                    {"label": "Hindi", "value": "hindi", "description": ""},
                    {"label": "Spanish", "value": "spanish", "description": ""},
                    {"label": "French", "value": "french", "description": ""}
                ]
            }
        ]

    def _get_instagram_creator_performance_filters(self) -> List[Dict[str, Any]]:
        """Get Instagram creator performance filters"""
        return [
            {
                "name": "Follower Count",
                "filter_type": "checkbox",
                "icon": "follower-icon",
                "has_minmax": True,
                "placeholder": "Select Follower Count",
                "api_field": "follower_count",
                "options": [
                    {"label": "Nano", "value": "1000-10000", "description": "1k-10K"},
                    {"label": "Micro", "value": "10000-50000", "description": "10K-50K"},
                    {"label": "Mid", "value": "50000-500000", "description": "50K-500K"},
                    {"label": "Macro", "value": "500000-1000000", "description": "500K-1M"},
                    {"label": "Mega", "value": "1000000-", "description": "1M+"}
                ]
            },
            {
                "name": "Average Likes",
                "filter_type": "checkbox",
                "icon": "likes-icon",
                "has_minmax": True,
                "placeholder": "Select Average Likes",
                "api_field": "average_likes",
                "options": [
                    {"label": "High", "value": "5000+", "description": "5000+"},
                    {"label": "Medium", "value": "501-5000", "description": "501-5000"},
                    {"label": "Low", "value": "0-500", "description": "0-500"}
                ]
            },
            {
                "name": "Engagement Rate",
                "filter_type": "checkbox",
                "icon": "engagement-icon",
                "placeholder": "Select Engagement Rate",
                "api_field": "engagement_rate",
                "options": [
                    {"label": "Very High", "value": "10%+", "description": "10%+"},
                    {"label": "High", "value": "5%-10%", "description": "5%-10%"},
                    {"label": "Medium", "value": "2%-5%", "description": "2%-5%"},
                    {"label": "Low", "value": "<2%", "description": "<2%"}
                ]
            }
        ]

    def _get_instagram_creator_content_filters(self) -> List[Dict[str, Any]]:
        """Get Instagram creator content filters"""
        return [
            {
                "name": "Category",
                "filter_type": "checkbox",
                "icon": "category-icon",
                "has_search_box": True,
                "placeholder": "Select Category",
                "api_field": "creator_interests",
                "options": [
                    {"label": "Camera & Photography", "value": "camera_photography", "description": ""},
                    {"label": "Friends, Family & Relationships", "value": "friends_family_relationships", "description": ""},
                    {"label": "Clothes, Handbags & Accessories", "value": "clothes_shoes_handbags_accessories", "description": ""},
                    {"label": "Travel, Tourism & Aviation", "value": "travel_tourism_aviation", "description": ""},
                    {"label": "Restaurants, Food & Grocery", "value": "restaurants_food_grocery", "description": ""},
                    {"label": "Beauty & Cosmetics", "value": "beauty_cosmetics", "description": ""}
                ]
            },
            {
                "name": "Keywords",
                "filter_type": "enter_value",
                "icon": "keywords-icon",
                "has_enter_value": True,
                "placeholder": "Enter Keywords description",
                "api_field": "content_keywords"
            }
        ]

    def _get_instagram_creator_credibility_filters(self) -> List[Dict[str, Any]]:
        """Get Instagram creator credibility filters"""
        return [
            {
                "name": "Verification",
                "filter_type": "radio_button",
                "icon": "verification-icon",
                "placeholder": "Select Verification Status",
                "api_field": "is_verified",
                "options": [
                    {"label": "Any", "value": "any", "description": ""},
                    {"label": "Verified", "value": "verified", "description": ""},
                    {"label": "Unverified", "value": "unverified", "description": ""}
                ]
            }
        ]

    def _get_instagram_audience_demographic_filters(self) -> List[Dict[str, Any]]:
        """Get Instagram audience demographic filters"""
        return [
            {
                "name": "Gender",
                "filter_type": "radio_button",
                "icon": "gender-icon",
                "placeholder": "Select Gender",
                "api_field": "audience_gender",
                "options": [
                    {"label": "Male", "value": "male", "description": ""},
                    {"label": "Female", "value": "female", "description": ""},
                    {"label": "Other", "value": "other", "description": ""}
                ]
            },
            {
                "name": "Age",
                "filter_type": "checkbox",
                "icon": "age-icon",
                "has_minmax": True,
                "placeholder": "Select Age",
                "api_field": "audience_age",
                "options": [
                    {"label": "Teen", "value": "13-19", "description": "13-19"},
                    {"label": "Young Adult", "value": "20-35", "description": "20-35"},
                    {"label": "Adult", "value": "36-55", "description": "36-55"},
                    {"label": "Senior", "value": "56+", "description": "56+"}
                ]
            }
        ]

    def _get_instagram_audience_interest_filters(self) -> List[Dict[str, Any]]:
        """Get Instagram audience interest filters"""
        return [
            {
                "name": "Interests",
                "filter_type": "enter_value",
                "icon": "interests-icon",
                "has_enter_value": True,
                "placeholder": "Enter Interests",
                "api_field": "audience_interests"
            },
            {
                "name": "Brand Affinities",
                "filter_type": "enter_value",
                "icon": "brand-icon",
                "has_enter_value": True,
                "placeholder": "Enter Brand Affinities",
                "api_field": "audience_brand_affinities"
            }
        ]

    def _get_youtube_creator_demographic_filters(self) -> List[Dict[str, Any]]:
        """Get YouTube creator demographic filters"""
        return self._get_instagram_creator_demographic_filters()  # Same structure

    def _get_youtube_creator_performance_filters(self) -> List[Dict[str, Any]]:
        """Get YouTube creator performance filters"""
        return [
            {
                "name": "Subscriber Count",
                "filter_type": "checkbox",
                "icon": "subscriber-icon",
                "has_minmax": True,
                "placeholder": "Select Subscriber Count",
                "api_field": "subscriber_count",
                "options": [
                    {"label": "Nano", "value": "1000-10000", "description": "1k-10K"},
                    {"label": "Micro", "value": "10000-100000", "description": "10K-100K"},
                    {"label": "Mid", "value": "100000-1000000", "description": "100K-1M"},
                    {"label": "Macro", "value": "1000000-", "description": "1M+"}
                ]
            },
            {
                "name": "Average Views",
                "filter_type": "checkbox",
                "icon": "views-icon",
                "has_minmax": True,
                "placeholder": "Select Average Views",
                "api_field": "average_views",
                "options": [
                    {"label": "Very High", "value": "500001+", "description": "500001+"},
                    {"label": "High", "value": "50001-500000", "description": "50001-500000"},
                    {"label": "Medium", "value": "5001-50000", "description": "5001-50000"},
                    {"label": "Low", "value": "1000-5000", "description": "1000-5000"}
                ]
            }
        ]

    def _get_youtube_creator_content_filters(self) -> List[Dict[str, Any]]:
        """Get YouTube creator content filters"""
        return [
            {
                "name": "Category",
                "filter_type": "checkbox",
                "icon": "category-icon",
                "placeholder": "Select Category",
                "api_field": "creator_interests",
                "options": [
                    {"label": "Fashion", "value": "fashion", "description": ""},
                    {"label": "Travel", "value": "travel", "description": ""},
                    {"label": "Food", "value": "food", "description": ""},
                    {"label": "Fitness", "value": "fitness", "description": ""}
                ]
            }
        ]

    def _get_youtube_creator_credibility_filters(self) -> List[Dict[str, Any]]:
        """Get YouTube creator credibility filters"""
        return [
            {
                "name": "Official",
                "filter_type": "radio_button",
                "icon": "official-icon",
                "placeholder": "",
                "api_field": "is_official",
                "options": [
                    {"label": "Any", "value": "any", "description": ""},
                    {"label": "Official", "value": "official", "description": ""},
                    {"label": "Unofficial", "value": "unofficial", "description": ""}
                ]
            }
        ]

    def _get_tiktok_creator_demographic_filters(self) -> List[Dict[str, Any]]:
        """Get TikTok creator demographic filters"""
        return self._get_instagram_creator_demographic_filters()  # Same structure

    def _get_tiktok_creator_performance_filters(self) -> List[Dict[str, Any]]:
        """Get TikTok creator performance filters"""
        return [
            {
                "name": "Follower Count",
                "filter_type": "checkbox",
                "icon": "follower-icon",
                "has_minmax": True,
                "placeholder": "Select Follower Count",
                "api_field": "follower_count",
                "options": [
                    {"label": "Nano", "value": "1000-10000", "description": "1k-10K"},
                    {"label": "Micro", "value": "10000-100000", "description": "10K-100K"},
                    {"label": "Mid", "value": "100000-1000000", "description": "100K-1M"},
                    {"label": "Macro", "value": "1000000-", "description": "1M+"}
                ]
            }
        ]

    def _get_tiktok_creator_content_filters(self) -> List[Dict[str, Any]]:
        """Get TikTok creator content filters"""
        return [
            {
                "name": "Category",
                "filter_type": "checkbox",
                "icon": "category-icon",
                "placeholder": "Select Category",
                "api_field": "creator_interests",
                "options": [
                    {"label": "Dance", "value": "dance", "description": ""},
                    {"label": "Comedy", "value": "comedy", "description": ""},
                    {"label": "Music", "value": "music", "description": ""},
                    {"label": "Education", "value": "education", "description": ""}
                ]
            }
        ]


async def main():
    """Main seeding function"""
    seeder = PhylloFilterSeeder()
    await seeder.seed_all_filters()


if __name__ == "__main__":
    asyncio.run(main())
