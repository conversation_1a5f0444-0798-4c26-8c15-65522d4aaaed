"""
SQLAlchemy models for CreatorVerse Discovery & Analytics
"""
import uuid
from datetime import datetime
from typing import Optional

from sqlalchemy import Column, <PERSON>, Inte<PERSON>, Float, <PERSON><PERSON>an, DateTime, Text, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

Base = declarative_base()


class Profile(Base):
    """
    Profile Analytics model - stores normalized influencer profile and performance data.
    Powers both Quick View and Detailed View in the discovery flow.
    """
    __tablename__ = "profile"
    __table_args__ = {'schema': 'profile_analytics'}

    # Primary fields
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    work_platform_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    
    # User relationship - nullable indicates external vs internal creators
    user_id = Column(UUID(as_uuid=True), nullable=True, index=True)  # FK to users.users
    
    # External platform data
    external_id = Column(String(255), nullable=True, index=True)
    platform_username = Column(String(255), nullable=False, index=True)
    platform = Column(String(50), nullable=False, default="instagram", index=True)  # instagram, youtube, tiktok
    
    # Profile information
    full_name = Column(String(255), nullable=True)
    url = Column(Text, nullable=True)
    image_url = Column(Text, nullable=True)
    introduction = Column(Text, nullable=True)
    platform_account_type = Column(String(50), nullable=True)  # personal, business, creator
    
    # Verification and demographics
    is_verified = Column(Boolean, default=False, index=True)
    gender = Column(String(20), nullable=True, index=True)  # male, female, other
    age_group = Column(String(50), nullable=True, index=True)  # teen, young_adult, adult, senior
    language = Column(String(10), nullable=True, index=True)  # en, hi, es, etc.
    
    # Location data
    country = Column(String(5), nullable=True, index=True)  # ISO country code
    state = Column(String(100), nullable=True)
    city = Column(String(100), nullable=True, index=True)
    location_tier = Column(String(20), nullable=True, index=True)  # tier1, tier2, tier3, rural
    
    # Content metrics
    content_count = Column(Integer, default=0)
    follower_count = Column(Integer, default=0, index=True)
    following_count = Column(Integer, default=0)
    subscriber_count = Column(Integer, default=0)  # For YouTube
    
    # Engagement metrics
    average_likes = Column(Float, default=0.0, index=True)
    average_comments = Column(Float, default=0.0, index=True)
    average_views = Column(Float, default=0.0, index=True)
    average_reels_views = Column(Float, default=0.0)
    average_shares = Column(Float, default=0.0)
    
    # Calculated metrics
    engagement_rate = Column(Float, default=0.0, index=True)  # Percentage
    credibility_score = Column(Float, default=0.0, index=True)  # 0-100
    
    # Growth metrics
    follower_growth_rate = Column(Float, default=0.0)  # Percentage
    follower_growth_30d = Column(Integer, default=0)
    follower_growth_90d = Column(Integer, default=0)
    
    # Content categories and interests
    primary_category = Column(String(100), nullable=True, index=True)
    secondary_categories = Column(Text, nullable=True)  # JSON array
    interests = Column(Text, nullable=True)  # JSON array
    brand_affinity = Column(Text, nullable=True)  # JSON array
    
    # Audience demographics (cached from external APIs)
    audience_age_groups = Column(Text, nullable=True)  # JSON object
    audience_gender = Column(Text, nullable=True)  # JSON object
    audience_locations = Column(Text, nullable=True)  # JSON object
    audience_interests = Column(Text, nullable=True)  # JSON array
    
    # External API and caching
    last_updated_external = Column(DateTime(timezone=True), nullable=True)
    data_source = Column(String(50), default="internal")  # internal, phyllo, manual
    external_data_hash = Column(String(64), nullable=True)  # For change detection
    cache_expires_at = Column(DateTime(timezone=True), nullable=True)
    
    # Status and quality
    profile_status = Column(String(20), default="active", index=True)  # active, inactive, suspended
    data_quality_score = Column(Float, default=0.0)  # 0-1, indicates data completeness
    last_profile_update = Column(DateTime(timezone=True), nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    
    def __repr__(self):
        return f"<Profile(id={self.id}, platform_username={self.platform_username}, platform={self.platform})>"
    
    @property
    def is_external_creator(self) -> bool:
        """Check if this is an externally fetched creator (not registered in CreatorVerse)"""
        return self.user_id is None
    
    @property 
    def is_internal_creator(self) -> bool:
        """Check if this is a CreatorVerse registered creator"""
        return self.user_id is not None
    
    @property
    def engagement_tier(self) -> str:
        """Categorize engagement rate into tiers"""
        if self.engagement_rate >= 10.0:
            return "very_high"
        elif self.engagement_rate >= 5.0:
            return "high"
        elif self.engagement_rate >= 2.0:
            return "average"
        else:
            return "low"
    
    @property
    def follower_tier(self) -> str:
        """Categorize follower count into tiers"""
        if self.follower_count >= 1_000_000:
            return "mega"
        elif self.follower_count >= 500_000:
            return "macro"
        elif self.follower_count >= 100_000:
            return "mid"
        elif self.follower_count >= 10_000:
            return "micro"
        elif self.follower_count >= 1_000:
            return "nano"
        else:
            return "small"
    
    def to_quick_view_dict(self) -> dict:
        """Return dictionary for Quick View display"""
        return {
            "id": str(self.id),
            "platform_username": self.platform_username,
            "full_name": self.full_name,
            "platform": self.platform,
            "image_url": self.image_url,
            "follower_count": self.follower_count,
            "engagement_rate": self.engagement_rate,
            "is_verified": self.is_verified,
            "follower_tier": self.follower_tier,
            "engagement_tier": self.engagement_tier,
            "primary_category": self.primary_category,
            "is_external": self.is_external_creator
        }
    
    def is_cache_valid(self) -> bool:
        """Check if cached external data is still valid"""
        if not self.cache_expires_at:
            return False
        return datetime.utcnow() < self.cache_expires_at


class SavedFilterSet(Base):
    """
    Model for saved filter sets that brands can reuse
    """
    __tablename__ = "saved_filter_sets"
    __table_args__ = {'schema': 'profile_analytics'}
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), nullable=False, index=True)  # Brand user ID
    
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    
    # Filter criteria stored as JSON
    filters = Column(Text, nullable=False)  # JSON object with filter criteria
    
    # Metadata
    is_public = Column(Boolean, default=False)
    is_favorite = Column(Boolean, default=False)
    use_count = Column(Integer, default=0)
    last_used_at = Column(DateTime(timezone=True), nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    
    def __repr__(self):
        return f"<SavedFilterSet(id={self.id}, name={self.name}, user_id={self.user_id})>"


class ExternalAPILog(Base):
    """
    Log table for tracking external API calls (Phyllo, etc.)
    """
    __tablename__ = "external_api_logs"
    __table_args__ = {'schema': 'profile_analytics'}
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # API call details
    api_provider = Column(String(50), nullable=False, index=True)  # phyllo, etc.
    endpoint = Column(String(255), nullable=False)
    method = Column(String(10), default="GET")
    
    # Request details
    request_params = Column(Text, nullable=True)  # JSON
    profile_id = Column(UUID(as_uuid=True), nullable=True, index=True)  # If related to specific profile
    external_profile_id = Column(String(255), nullable=True, index=True)
    
    # Response details
    status_code = Column(Integer, nullable=False)
    response_time_ms = Column(Integer, nullable=True)
    success = Column(Boolean, default=False, index=True)
    error_message = Column(Text, nullable=True)
    
    # Rate limiting and quota tracking
    rate_limit_remaining = Column(Integer, nullable=True)
    quota_used = Column(Integer, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    
    def __repr__(self):
        return f"<ExternalAPILog(id={self.id}, api_provider={self.api_provider}, success={self.success})>"
