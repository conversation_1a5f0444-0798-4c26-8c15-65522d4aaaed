"""
Actual Profile model that matches the existing database schema
"""
import uuid
from datetime import datetime
from typing import Optional, List, Dict, Any

from sqlalchemy import Column, String, Integer, Float, Boolean, DateTime, Text, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship

Base = declarative_base()


class ActualProfile(Base):
    """
    Actual Profile model matching the existing database schema
    """
    __tablename__ = "profile"
    __table_args__ = {'schema': 'profile_analytics'}

    # Columns that actually exist in the database (now 29 columns)
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    work_platform_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    external_id = Column(Text, nullable=False, index=True)
    platform_username = Column(Text, nullable=False, index=True)
    url = Column(Text, nullable=True)
    image_url = Column(Text, nullable=True)
    full_name = Column(Text, nullable=True)
    introduction = Column(Text, nullable=True)
    content_count = Column(Integer, nullable=True)
    is_verified = Column(Boolean, default=False, index=True)
    platform_account_type = Column(Text, nullable=True)
    gender = Column(Text, nullable=True, index=True)
    age_group = Column(Text, nullable=True, index=True)
    language = Column(String(1), nullable=True, index=True)  # character(1) in DB
    follower_count = Column(Integer, nullable=True, index=True)
    subscriber_count = Column(Integer, nullable=True)
    average_likes = Column(Integer, nullable=True, index=True)
    average_comments = Column(Integer, nullable=True, index=True)
    average_views = Column(Integer, nullable=True, index=True)
    average_reels_views = Column(Integer, nullable=True)
    engagement_rate = Column(Float, nullable=True, index=True)
    credibility_score = Column(Float, nullable=True, index=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=True)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=True)
    
    # Newly added columns
    platform = Column(String(20), nullable=True, default="instagram", index=True)
    user_id = Column(UUID(as_uuid=True), nullable=True, index=True)
    country = Column(String(5), nullable=True, index=True)
    state = Column(String(100), nullable=True) 
    city = Column(String(100), nullable=True, index=True)
    
    def __repr__(self):
        return f"<ActualProfile(id={self.id}, platform_username={self.platform_username})>"
    
    @property
    def is_external_creator(self) -> bool:
        """Check if this is an externally fetched creator (not registered in CreatorVerse)"""
        return self.user_id is None
    
    @property 
    def is_internal_creator(self) -> bool:
        """Check if this is a CreatorVerse registered creator"""
        return self.user_id is not None
    
    @property
    def engagement_tier(self) -> str:
        """Categorize engagement rate into tiers"""
        if not self.engagement_rate:
            return "unknown"
        if self.engagement_rate >= 10.0:
            return "very_high"
        elif self.engagement_rate >= 5.0:
            return "high"
        elif self.engagement_rate >= 2.0:
            return "average"
        else:
            return "low"
    
    @property
    def follower_tier(self) -> str:
        """Categorize follower count into tiers"""
        if not self.follower_count:
            return "unknown"
        if self.follower_count >= 1_000_000:
            return "mega"
        elif self.follower_count >= 500_000:
            return "macro"
        elif self.follower_count >= 100_000:
            return "mid"
        elif self.follower_count >= 10_000:
            return "micro"
        elif self.follower_count >= 1_000:
            return "nano"
        else:
            return "small"
    
    # Add missing fields as properties with defaults (columns that don't exist in DB)
    @property
    def following_count(self) -> int:
        """Default following count since column doesn't exist"""
        return 0
    
    @property
    def average_shares(self) -> float:
        """Default average shares since column doesn't exist"""
        return 0.0
    
    @property
    def follower_growth_rate(self) -> float:
        return 0.0
    
    @property
    def follower_growth_30d(self) -> int:
        return 0
    
    @property
    def follower_growth_90d(self) -> int:
        return 0
    
    @property
    def primary_category(self) -> Optional[str]:
        return None
    
    @property
    def secondary_categories(self) -> str:
        return "[]"
    
    @property
    def interests(self) -> str:
        return "[]"
    
    @property
    def brand_affinity(self) -> str:
        return "[]"
    
    @property
    def audience_age_groups(self) -> str:
        return "{}"
    
    @property
    def audience_gender(self) -> str:
        return "{}"
    
    @property
    def audience_locations(self) -> str:
        return "{}"
    
    @property
    def audience_interests(self) -> str:
        return "[]"
    
    @property
    def last_updated_external(self) -> Optional[datetime]:
        return None
    
    @property
    def data_source(self) -> str:
        return "unknown"
    
    @property
    def external_data_hash(self) -> Optional[str]:
        return None
    
    @property
    def cache_expires_at(self) -> Optional[datetime]:
        return None
    
    @property
    def profile_status(self) -> str:
        return "active"
    
    @property
    def data_quality_score(self) -> float:
        return 0.5
    
    @property
    def location_tier(self) -> Optional[str]:
        """Default location tier since column doesn't exist"""
        return None
    
    def to_quick_view_dict(self) -> dict:
        """Return dictionary for Quick View display"""
        return {
            "id": str(self.id),
            "platform_username": self.platform_username,
            "full_name": self.full_name,
            "platform": self.platform,
            "image_url": self.image_url,
            "follower_count": self.follower_count,
            "engagement_rate": self.engagement_rate,
            "is_verified": self.is_verified,
            "follower_tier": self.follower_tier,
            "engagement_tier": self.engagement_tier,
            "primary_category": self.primary_category,
            "is_external": self.is_external_creator
        }
    
    def is_cache_valid(self) -> bool:
        """Check if cached external data is still valid"""
        return False  # Since we don't have cache_expires_at
