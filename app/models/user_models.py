import enum
import uuid

from sqlalchemy import (
    Column, String, Integer, Boolean, SmallInteger, DateTime, UniqueConstraint, text, func, Text, ForeignKey, TIMESTAMP,
    PrimaryKeyConstraint, BigInteger, JSON, Index
)
from sqlalchemy.dialects.postgresql import UUID, JSONB, INET, ENUM as PGEnum
from sqlalchemy.orm import declarative_base, relationship

Base = declarative_base()


class OrgMemberRole(enum.Enum):
    owner = "owner"
    admin = "admin"
    member = "member"


class Organization(Base):
    __tablename__ = 'organizations'
    __table_args__ = (
        UniqueConstraint('domain', 'organization_code', name='organizations_domain_code_unique'),
        {'schema': 'users'}
    )

    id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid.uuid4,
        server_default=text('gen_random_uuid()'),
        nullable=False
    )
    domain = Column(String(255), nullable=False)
    organization_code = Column(
        String(50),
        nullable=False,
        server_default=text("''")
    )
    name = Column(String(100), nullable=False)
    description = Column(String(250))
    logo_url = Column(String(2048))
    contact_email = Column(String(255))
    is_active = Column(
        Boolean,
        nullable=False,
        server_default=text('true')
    )
    timezone = Column(String(50))
    deleted_at = Column(DateTime(timezone=True))
    created_at = Column(
        DateTime(timezone=True),
        nullable=False,
        server_default=func.now()
    )
    updated_at = Column(
        DateTime(timezone=True),
        nullable=False,
        server_default=func.now()
    )
    memberships = relationship(
        "OrganizationMembership",
        back_populates="organization",
        cascade="all, delete-orphan",
    )

    brands = relationship(
        "Brand",
        back_populates="organization",
        cascade="all, delete-orphan"
    )


class User(Base):
    __tablename__ = "users"
    __table_args__ = (
        UniqueConstraint("email", name="users_email_key"),
        {"schema": "users"},
    )

    id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid.uuid4,
        server_default=text('gen_random_uuid()'),
        nullable=False
    )
    email = Column(
        String(255),
        nullable=False
    )
    name = Column(
        String(255)
    )
    profile_image = Column(
        Text
    )
    status = Column(
        String(20),
        nullable=False,
        server_default=text("'requested'")
    )
    country_code = Column(
        String(3)
    )
    phone_number = Column(
        String(15)
    )
    is_email_verified = Column(
        Boolean,
        nullable=False,
        server_default=text('false')
    )
    is_phone_verified = Column(
        Boolean,
        nullable=False,
        server_default=text('false')
    )
    is_active = Column(
        Boolean,
        nullable=False,
        server_default=text('true')
    )
    deactivated_at = Column(
        DateTime(timezone=True)
    )
    metadata_json = Column(
        JSONB,
        nullable=False,
        server_default=text("'{}'::JSONB")
    )
    last_login_at = Column(
        DateTime(timezone=True)
    )
    register_source = Column(
        SmallInteger
    )
    created_at = Column(
        DateTime(timezone=True),
        nullable=False,
        server_default=func.now()
    )
    updated_at = Column(
        DateTime(timezone=True),
        nullable=False,
        server_default=func.now()
    )
    organization_membership = relationship(
        "OrganizationMembership",
        back_populates="user",
        uselist=False,  # one active org per user by business rule
    )


class MasterAuthMethod(Base):
    __tablename__ = 'master_auth_methods'
    __table_args__ = {'schema': 'users'}

    id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid.uuid4,
        server_default=text('gen_random_uuid()'),
        nullable=False
    )
    method_key = Column(
        String(50),
        nullable=False,
        unique=True
    )
    description = Column(
        String(255)
    )
    created_at = Column(
        DateTime(timezone=True),
        nullable=False,
        server_default=func.now()
    )
    updated_at = Column(
        DateTime(timezone=True),
        nullable=False,
        server_default=func.now()
    )


class UserAuthMethod(Base):
    __tablename__ = 'user_auth_methods'
    __table_args__ = {'schema': 'users'}

    user_id = Column(
        UUID(as_uuid=True),
        ForeignKey('users.users.id', ondelete='CASCADE'),
        primary_key=True,
        nullable=False
    )
    auth_method_id = Column(
        UUID(as_uuid=True),
        ForeignKey('users.master_auth_methods.id', ondelete='CASCADE'),
        primary_key=True,
        nullable=False
    )
    is_enabled = Column(
        Boolean,
        nullable=False,
        server_default=text('false')
    )
    enabled_at = Column(
        DateTime(timezone=True),
        nullable=True
    )
    disabled_at = Column(
        DateTime(timezone=True),
        nullable=True
    )


class MasterRole(Base):
    __tablename__ = 'master_roles'
    __table_args__ = {'schema': 'users'}

    id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid.uuid4,
        server_default=text('gen_random_uuid()'),
        nullable=False
    )
    role_name = Column(
        String(20),
        nullable=False,
        unique=True
    )
    description = Column(
        String(100)
    )
    parent_role_id = Column(
        UUID(as_uuid=True),
        ForeignKey('users.master_roles.id', ondelete='SET NULL'),
        nullable=True
    )
    created_at = Column(
        DateTime(timezone=True),
        nullable=False,
        server_default=func.now()
    )
    updated_at = Column(
        DateTime(timezone=True),
        nullable=False,
        server_default=func.now()
    )


class UserRoleModel(Base):
    __tablename__ = 'user_roles'
    __table_args__ = {'schema': 'users'}

    user_id = Column(
        UUID(as_uuid=True),
        ForeignKey('users.users.id', ondelete='CASCADE'),
        primary_key=True,
        nullable=False
    )
    role_id = Column(
        UUID(as_uuid=True),
        ForeignKey('users.master_roles.id', ondelete='CASCADE'),
        primary_key=True,
        nullable=False
    )
    assigned_at = Column(
        DateTime(timezone=True),
        nullable=False,
        server_default=func.now()
    )


class MasterPermission(Base):
    __tablename__ = 'master_permissions'
    __table_args__ = {'schema': 'users'}

    id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid.uuid4,
        server_default=text('gen_random_uuid()'),
        nullable=False
    )
    permission_key = Column(
        String(40),
        nullable=False,
        unique=True
    )
    description = Column(
        String(80)
    )
    created_at = Column(
        DateTime(timezone=True),
        nullable=False,
        server_default=func.now()
    )
    updated_at = Column(
        DateTime(timezone=True),
        nullable=False,
        server_default=func.now()
    )


class RolePermission(Base):
    __tablename__ = 'role_permissions'
    __table_args__ = {'schema': 'users'}

    role_id = Column(
        UUID(as_uuid=True),
        ForeignKey('users.master_roles.id', ondelete='CASCADE'),
        primary_key=True,
        nullable=False
    )
    permission_id = Column(
        UUID(as_uuid=True),
        ForeignKey('users.master_permissions.id', ondelete='CASCADE'),
        primary_key=True,
        nullable=False
    )
    assigned_at = Column(
        DateTime(timezone=True),
        nullable=False,
        server_default=func.now()
    )


class UserOTP(Base):
    __tablename__ = 'user_otps'
    __table_args__ = (

        {'schema': 'users'},
    )

    id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid.uuid4,
        server_default=text('gen_random_uuid()'),
        nullable=False
    )
    user_id = Column(
        UUID(as_uuid=True),
        nullable=False,
        # ForeignKey declaration omitted for brevity; assume relationship defined elsewhere
    )
    hashed_code = Column(
        String(255),
        nullable=False,
        comment="Hashed OTP code"
    )
    channel = Column(
        String(20),
        nullable=False
    )
    created_at = Column(
        DateTime(timezone=True),
        nullable=False,
        server_default=func.now()
    )
    consumed_at = Column(
        DateTime(timezone=True),
        nullable=True
    )
    failed_attempts = Column(
        SmallInteger,
        nullable=False,
        server_default=text('0')
    )
    lockout_until = Column(
        DateTime(timezone=True),
        nullable=True
    )
    is_active = Column(
        Boolean,
        nullable=False,
        server_default=text('true')
    )


class MagicLink(Base):
    __tablename__ = 'magic_links'
    __table_args__ = (
        {'schema': 'users'},
    )

    id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid.uuid4,
        server_default=text('gen_random_uuid()'),
        nullable=False
    )
    user_id = Column(
        UUID(as_uuid=True),
        nullable=False,
        # ForeignKey declaration omitted for brevity; assume relationship defined elsewhere
    )
    token = Column(
        String(64),
        nullable=False,
        unique=True
    )
    created_at = Column(
        DateTime(timezone=True),
        nullable=False,
        server_default=func.now()
    )
    expires_at = Column(
        DateTime(timezone=True),
        nullable=False
    )
    consumed_at = Column(
        DateTime(timezone=True),
        nullable=True
    )
    is_active = Column(
        Boolean,
        nullable=False,
        server_default=text('true')
    )


class UserSession(Base):
    __tablename__ = "user_sessions"
    __table_args__ = {"schema": "users"}

    id = Column(UUID(as_uuid=True), primary_key=True, server_default=text("gen_random_uuid()"))
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.users.id", ondelete="CASCADE"), nullable=False)
    access_token = Column(Text, nullable=False)
    refresh_token = Column(Text, nullable=False)
    issued_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=True)
    expires_at = Column(DateTime(timezone=True), nullable=True)
    ip_address = Column(INET, nullable=True)
    user_agent = Column(String(512), nullable=True)
    is_revoked = Column(Boolean, nullable=True, server_default=text("false"))


class OAuthAccount(Base):
    """
    Third-party account linkage (e.g., Google, Facebook, etc.).
    One row per provider-user pair, linked to a local `users.users` record.
    """
    __tablename__ = "oauth_accounts"
    __table_args__ = (
        UniqueConstraint(
            "provider",
            "provider_user_id",
            name="uq_provider_user",
        ),
        {"schema": "users"},
    )

    # Primary key
    id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        server_default=text("gen_random_uuid()"),
    )

    # Link back to your local user
    user_id = Column(
        UUID(as_uuid=True),
        ForeignKey("users.users.id", ondelete="CASCADE"),
        nullable=False,
    )

    # OAuth provider metadata
    provider = Column(String(32), nullable=False)
    provider_user_id = Column(String(255), nullable=False)

    # Access - refresh tokens (secure storage recommended!)
    access_token = Column(Text, nullable=False)
    refresh_token = Column(Text)
    expires_at = Column(TIMESTAMP(timezone=True))
    scope = Column(Text, nullable=True)

    # Audit columns
    created_at = Column(
        TIMESTAMP(timezone=True),
        nullable=False,
        server_default=func.now(),
    )
    updated_at = Column(
        TIMESTAMP(timezone=True),
        nullable=False,
        server_default=func.now(),
        onupdate=func.now(),
    )


class DomainClaim(Base):
    __tablename__ = "domain_claims"
    __table_args__ = {"schema": "users"}

    domain = Column(String(255), primary_key=True)
    verification_token = Column(String(255), nullable=False)
    verified_at = Column(DateTime(timezone=True))
    claimed_by_user_id = Column(UUID(as_uuid=True), ForeignKey("users.users.id"))
    organization_id = Column(UUID(as_uuid=True), ForeignKey("users.organizations.id", ondelete="CASCADE"))

    claimed_by_user = relationship("User", foreign_keys=[claimed_by_user_id])
    organization = relationship("Organization", foreign_keys=[organization_id])


# ──────────────────────────────────────────────────────────────────────────────
#  ORGANISATION MEMBERSHIP MODEL
# ──────────────────────────────────────────────────────────────────────────────
class OrganizationMembership(Base):
    """
    Links a user to one organisation.

    Business rules enforced via partial indexes:
      • each user can have at most ONE active membership (`uq_user_one_org`)
      • each organisation can have only ONE active owner (`uq_org_single_owner`)
    Soft-delete uses `is_active` + `deleted_at`.
    """

    __tablename__ = "organization_memberships"
    __table_args__ = (
        # composite primary key for quick look-ups like “all members of org X”
        PrimaryKeyConstraint("organization_id", "user_id", name="pk_organization_memberships"),

        # 1) one-org-per-user guarantee (acts only on active rows)
        Index(
            "uq_user_one_org",
            "user_id",
            postgresql_where=text("is_active"),
            unique=True,
        ),

        # 2) one active owner per organisation
        Index(
            "uq_org_single_owner",
            "organization_id",
            postgresql_where=text("role = 'owner' AND is_active"),
            unique=True,
        ),
        {"schema": "users"},
    )

    # ── foreign keys ───────────────────────────────────────────────────────
    organization_id = Column(
        UUID(as_uuid=True),
        ForeignKey("users.organizations.id", ondelete="CASCADE"),
        nullable=False,
    )

    user_id = Column(
        UUID(as_uuid=True),
        ForeignKey("users.users.id", ondelete="CASCADE"),
        nullable=False,
    )

    # ── membership role (ENUM) ─────────────────────────────────────────────
    role = Column(
        PGEnum(
            OrgMemberRole,
            name="org_member_role_e",
            schema="users",
            native_enum=True,
            create_constraint=False,  # the enum type already exists in the DB
            validate_strings=True,
        ),
        nullable=False,
        server_default=OrgMemberRole.member.value,
    )

    # ── soft-delete fields ─────────────────────────────────────────────────
    is_active = Column(Boolean, nullable=False, server_default=text("true"))
    deleted_at = Column(DateTime(timezone=True))

    # ── audit helpers ──────────────────────────────────────────────────────
    joined_at = Column(DateTime(timezone=True), nullable=False, server_default=func.now())
    created_at = Column(DateTime(timezone=True), nullable=False, server_default=func.now())
    updated_at = Column(
        DateTime(timezone=True),
        nullable=False,
        server_default=func.now(),
        onupdate=func.now(),
    )

    # ── relationships (assuming Organization / User define back_populates) ─
    organization = relationship(
        "Organization",
        back_populates="memberships",
        foreign_keys=[organization_id],
    )

    user = relationship(
        "User",
        back_populates="organization_membership",  # define on User side
        foreign_keys=[user_id],
    )

    # ── convenience properties ─────────────────────────────────────────────
    @property
    def is_owner(self) -> bool:
        return self.role == OrgMemberRole.owner

    @property
    def is_admin(self) -> bool:
        return self.role == OrgMemberRole.admin

    # ── repr ───────────────────────────────────────────────────────────────
    def __repr__(self) -> str:
        return (
            f"<OrganizationMembership(org_id={self.organization_id}, "
            f"user_id={self.user_id}, role={self.role.value}, active={self.is_active})>"
        )


class OwnershipTransfer(Base):
    __tablename__ = "ownership_transfers"
    __table_args__ = {"schema": "users"}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    organization_id = Column(UUID(as_uuid=True), ForeignKey("users.organizations.id", ondelete="CASCADE"),
                             nullable=False)
    from_user_id = Column(UUID(as_uuid=True), ForeignKey("users.users.id"), nullable=False)
    to_user_id = Column(UUID(as_uuid=True), ForeignKey("users.users.id"), nullable=False)
    requested_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    accepted_at = Column(DateTime(timezone=True), nullable=True)
    status = Column(String(20), nullable=False, default="pending")  # 'pending', 'accepted', 'declined'


class SocialProfile(Base):
    __tablename__ = "social_profiles"
    __table_args__ = (
        UniqueConstraint("oauth_account_id", "service", name="uq_profile_owner"),
        {"schema": "users"},
    )

    id = Column(UUID(as_uuid=True), primary_key=True, server_default=text("gen_random_uuid()"))
    oauth_account_id = Column(
        UUID(as_uuid=True),
        ForeignKey("users.oauth_accounts.id", ondelete="CASCADE"),
        nullable=False
    )

    service = Column(String(32), nullable=False)  # 'youtube', 'instagram', etc.
    external_id = Column(String(255), nullable=False)

    username = Column(String(255))
    display_name = Column(String(255))
    avatar_url = Column(String(2048))

    follower_count = Column(BigInteger)
    post_count = Column(BigInteger)

    raw_json = Column(JSON, nullable=False, default={})
    fetched_at = Column(TIMESTAMP(timezone=True), nullable=False, server_default=func.now())

    def __repr__(self):
        return f"<SocialProfile(service='{self.service}', external_id='{self.external_id}')>"


class Brand(Base):
    """
    Brand model to support multiple brands within organizations.
    Each brand belongs to an organization and has its own identity and settings.
    """
    __tablename__ = 'brands'
    __table_args__ = (
        UniqueConstraint('organization_id', 'name', name='brands_org_name_unique'),
        {'schema': 'users'}
    )

    # Primary key
    id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid.uuid4,
        server_default=text('gen_random_uuid()'),
        nullable=False
    )

    # Foreign key to organization
    organization_id = Column(
        UUID(as_uuid=True),
        ForeignKey('users.organizations.id', ondelete='CASCADE'),
        nullable=False,
        comment="Organization this brand belongs to"
    )

    # Brand identity fields
    name = Column(
        String(100),
        nullable=False,
        comment="Brand name (must be unique within organization)"
    )

    description = Column(
        String(500),
        nullable=True,
        comment="Brand description or tagline"
    )

    # Brand settings and metadata
    logo_url = Column(
        String(2048),
        nullable=True,
        comment="URL to brand logo"
    )

    website_url = Column(
        String(2048),
        nullable=True,
        comment="Brand website URL"
    )

    contact_email = Column(
        String(255),
        nullable=True,
        comment="Brand-specific contact email"
    )

    created_by = Column(
        UUID(as_uuid=True),
        ForeignKey('users.users.id'),
        nullable=True,
        comment="User who created this brand"
    )

    # Brand status
    is_active = Column(
        Boolean,
        nullable=False,
        server_default=text('true'),
        comment="Whether the brand is active"
    )

    # Audit fields
    created_at = Column(
        DateTime(timezone=True),
        nullable=False,
        server_default=func.now(),
        comment="When the brand was created"
    )

    updated_at = Column(
        DateTime(timezone=True),
        nullable=False,
        server_default=func.now(),
        onupdate=func.now(),
        comment="When the brand was last updated"
    )

    deleted_at = Column(
        DateTime(timezone=True),
        nullable=True,
        comment="When the brand was soft deleted"
    )

    # Relationships
    organization = relationship(
        "Organization",
        back_populates="brands",
        foreign_keys=[organization_id]
    )

    memberships = relationship(
        "BrandMembership",
        back_populates="brand",
        cascade="all, delete-orphan"
    )

    influencer_lists = relationship(
        "BrandInfluencerList",
        cascade="all, delete-orphan"
    )

    def __repr__(self) -> str:
        return f"<Brand(id={self.id}, name='{self.name}', organization_id={self.organization_id})>"

    @property
    def is_deleted(self) -> bool:
        """Check if the brand is soft deleted."""
        return self.deleted_at is not None


class BrandMembershipStatus(enum.Enum):
    pending = "pending"
    active = "active"
    rejected = "rejected"


class BrandMembership(Base):
    __tablename__ = "brand_memberships"
    __table_args__ = (
        {"schema": "users"}
    )

    id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid.uuid4,
        nullable=False,
    )
    brand_id = Column(
        UUID(as_uuid=True),
        ForeignKey("users.brands.id", ondelete="CASCADE"),
        nullable=False,
    )
    user_id = Column(
        UUID(as_uuid=True),
        ForeignKey("users.users.id", ondelete="CASCADE"),
        nullable=False,
    )
    role = Column(String(20), nullable=False, default="member")
    invited_by = Column(
        UUID(as_uuid=True),
        ForeignKey("users.users.id"),
        nullable=True,
    )
    joined_at = Column(DateTime(timezone=True), nullable=False, server_default=func.now())
    created_at = Column(DateTime(timezone=True), nullable=False, server_default=func.now())
    updated_at = Column(
        DateTime(timezone=True),
        nullable=False,
        server_default=func.now(),
        onupdate=func.now(),
    )
    is_active = Column(Boolean, nullable=True, default=True)
    status = Column(
        PGEnum(BrandMembershipStatus, name="brand_membership_status_e", schema="users"),
        nullable=False,
        server_default=BrandMembershipStatus.pending.value,
    )

    # Relationships
    brand = relationship(
        "Brand",
        back_populates="memberships",
        foreign_keys=[brand_id]
    )

    user = relationship(
        "User",
        foreign_keys=[user_id]
    )

    inviter = relationship(
        "User",
        foreign_keys=[invited_by]
    )

    def __repr__(self) -> str:
        return f"<BrandMembership(brand_id={self.brand_id}, user_id={self.user_id}, role='{self.role}')>"

    @property
    def is_admin(self) -> bool:
        """Check if user has admin privileges for this brand."""
        return self.role == 'brand_admin'

    @property
    def is_active_member(self) -> bool:
        """Check if user is an active member of this brand."""
        return self.status == BrandMembershipStatus.active

    @property
    def is_pending(self) -> bool:
        """Check if membership is pending approval."""
        return self.status == BrandMembershipStatus.pending


class BrandInfluencerListStatus(enum.Enum):
    active = "active"
    archived = "archived"
    draft = "draft"


class BrandInfluencerList(Base):
    """
    Brand Influencer Lists - allows brands to organize influencers into categorized lists.
    Examples: "Summer Campaign", "Lifestyle Creators", "Fashion Events", etc.
    """
    __tablename__ = 'brand_influencer_lists'
    __table_args__ = (
        UniqueConstraint('brand_id', 'name', name='brand_influencer_lists_brand_name_unique'),
        {'schema': 'users'}
    )

    # Primary key
    id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid.uuid4,
        server_default=text('gen_random_uuid()'),
        nullable=False
    )

    # Foreign key to brand
    brand_id = Column(
        UUID(as_uuid=True),
        ForeignKey('users.brands.id', ondelete='CASCADE'),
        nullable=False,
        comment="Brand this list belongs to"
    )

    # List details
    name = Column(
        String(100),
        nullable=False,
        comment="List name (must be unique within brand)"
    )

    description = Column(
        String(500),
        nullable=True,
        comment="Optional description of the list purpose"
    )

    # List metadata
    status = Column(
        PGEnum(
            BrandInfluencerListStatus,
            name="brand_influencer_list_status_e",
            schema="users",
            native_enum=True,
            create_constraint=False,
            validate_strings=True,
        ),
        nullable=False,
        server_default=BrandInfluencerListStatus.active.value,
        comment="Status of the list"
    )

    # Creator info
    created_by = Column(
        UUID(as_uuid=True),
        ForeignKey('users.users.id'),
        nullable=False,
        comment="User who created this list"
    )

    # Audit fields
    created_at = Column(
        DateTime(timezone=True),
        nullable=False,
        server_default=func.now(),
        comment="When the list was created"
    )

    updated_at = Column(
        DateTime(timezone=True),
        nullable=False,
        server_default=func.now(),
        onupdate=func.now(),
        comment="When the list was last updated"
    )

    deleted_at = Column(
        DateTime(timezone=True),
        nullable=True,
        comment="When the list was soft deleted"
    )

    # Relationships
    brand = relationship(
        "Brand",
        foreign_keys=[brand_id]
    )

    creator = relationship(
        "User",
        foreign_keys=[created_by]
    )

    influencer_entries = relationship(
        "BrandInfluencerListEntry",
        back_populates="influencer_list",
        cascade="all, delete-orphan"
    )

    def __repr__(self) -> str:
        return f"<BrandInfluencerList(id={self.id}, name='{self.name}', brand_id={self.brand_id})>"

    @property
    def is_deleted(self) -> bool:
        """Check if the list is soft deleted."""
        return self.deleted_at is not None


class InfluencerStatus(enum.Enum):
    """Status options for influencers in lists"""
    shortlisted = "shortlisted"
    contacted = "contacted"
    in_progress = "in_progress"
    completed = "completed"
    rejected = "rejected"


class BrandInfluencerListEntry(Base):
    """
    Individual entries in brand influencer lists.
    Links influencers to specific lists with additional metadata.
    """
    __tablename__ = 'brand_influencer_list_entries'
    __table_args__ = (
        UniqueConstraint('list_id', 'influencer_id', name='brand_influencer_list_entries_unique'),
        {'schema': 'users'}
    )

    # Primary key
    id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid.uuid4,
        server_default=text('gen_random_uuid()'),
        nullable=False
    )

    # Foreign keys
    list_id = Column(
        UUID(as_uuid=True),
        ForeignKey('users.brand_influencer_lists.id', ondelete='CASCADE'),
        nullable=False,
        comment="List this entry belongs to"
    )

    influencer_id = Column(
        String(255),
        nullable=False,
        comment="Influencer ID from profile analytics schema"
    )

    # Optional link to creatorverse user if they exist in our system
    user_id = Column(
        UUID(as_uuid=True),
        ForeignKey('users.users.id', ondelete='SET NULL'),
        nullable=True,
        comment="Creatorverse user ID if influencer is in our system"
    )

    # Influencer details (cached from analytics schema)
    influencer_name = Column(
        String(255),
        nullable=True,
        comment="Cached influencer name"
    )

    influencer_username = Column(
        String(255),
        nullable=True,
        comment="Cached influencer username/handle"
    )

    influencer_avatar_url = Column(
        String(2048),
        nullable=True,
        comment="Cached influencer profile image URL"
    )

    # Campaign management fields
    status = Column(
        PGEnum(
            InfluencerStatus,
            name="influencer_status_e",
            schema="users",
            native_enum=True,
            create_constraint=False,
            validate_strings=True,
        ),
        nullable=False,
        server_default=InfluencerStatus.shortlisted.value,
        comment="Current status of this influencer in the campaign"
    )

    # Social media and engagement data (cached)
    channels = Column(
        JSONB,
        nullable=True,
        server_default=text("'[]'::JSONB"),
        comment="Social media channels (cached from analytics)"
    )

    audience_size = Column(
        BigInteger,
        nullable=True,
        comment="Total audience/followers across channels"
    )

    engagement_rate = Column(
        Integer,  # Store as percentage * 100 (e.g., 10.5% = 1050)
        nullable=True,
        comment="Engagement rate as percentage * 100"
    )

    # Campaign-specific fields
    campaign = Column(
        String(255),
        nullable=True,
        comment="Campaign this influencer is associated with"
    )

    labels = Column(
        JSONB,
        nullable=True,
        server_default=text("'[]'::JSONB"),
        comment="Labels/tags for categorization"
    )

    notes = Column(
        Text,
        nullable=True,
        comment="Private notes about this influencer"
    )

    # Audit fields
    added_by = Column(
        UUID(as_uuid=True),
        ForeignKey('users.users.id'),
        nullable=False,
        comment="User who added this influencer to the list"
    )

    added_at = Column(
        DateTime(timezone=True),
        nullable=False,
        server_default=func.now(),
        comment="When the influencer was added to the list"
    )

    updated_at = Column(
        DateTime(timezone=True),
        nullable=False,
        server_default=func.now(),
        onupdate=func.now(),
        comment="When the entry was last updated"
    )

    # Relationships
    influencer_list = relationship(
        "BrandInfluencerList",
        back_populates="influencer_entries",
        foreign_keys=[list_id]
    )

    user = relationship(
        "User",
        foreign_keys=[user_id]
    )

    added_by_user = relationship(
        "User",
        foreign_keys=[added_by]
    )

    def __repr__(self) -> str:
        return f"<BrandInfluencerListEntry(id={self.id}, list_id={self.list_id}, influencer_id='{self.influencer_id}')>"


class GlobalLabel(Base):
    """
    Global label vocabulary shared across all brands.
    Provides a centralized system for managing labels with usage tracking.
    """
    __tablename__ = 'global_labels'
    __table_args__ = (
        UniqueConstraint('name', name='global_labels_name_unique'),
        {'schema': 'users'}
    )

    # Primary key
    id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid.uuid4,
        server_default=text('gen_random_uuid()'),
        nullable=False
    )

    # Label details
    name = Column(
        String(100),
        nullable=False,
        comment="Label name (unique globally)"
    )

    description = Column(
        Text,
        nullable=True,
        comment="Optional description of what the label represents"
    )

    color = Column(
        String(7),
        nullable=False,
        server_default=text("'#6B7280'"),
        comment="Hex color code for UI display"
    )

    usage_count = Column(
        Integer,
        nullable=False,
        server_default=text('0'),
        comment="Global usage count across all brands"
    )

    # Audit fields
    created_at = Column(
        DateTime(timezone=True),
        nullable=False,
        server_default=func.now(),
        comment="When the label was created"
    )

    updated_at = Column(
        DateTime(timezone=True),
        nullable=False,
        server_default=func.now(),
        onupdate=func.now(),
        comment="When the label was last updated"
    )

    # Relationships
    brand_labels = relationship(
        "BrandLabel",
        back_populates="global_label",
        cascade="all, delete-orphan"
    )

    entry_assignments = relationship(
        "BrandInfluencerListEntryLabel",
        back_populates="global_label",
        cascade="all, delete-orphan"
    )

    def __repr__(self) -> str:
        return f"<GlobalLabel(id={self.id}, name='{self.name}', usage_count={self.usage_count})>"


class BrandLabel(Base):
    """
    Brand-specific label usage tracking and metadata.
    Links global labels to specific brands with usage statistics.
    """
    __tablename__ = 'brand_labels'
    __table_args__ = (
        UniqueConstraint('brand_id', 'global_label_id', name='brand_labels_brand_label_unique'),
        {'schema': 'users'}
    )

    # Primary key
    id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid.uuid4,
        server_default=text('gen_random_uuid()'),
        nullable=False
    )

    # Foreign keys
    brand_id = Column(
        UUID(as_uuid=True),
        ForeignKey('users.brands.id', ondelete='CASCADE'),
        nullable=False,
        comment="Brand using this label"
    )

    global_label_id = Column(
        UUID(as_uuid=True),
        ForeignKey('users.global_labels.id', ondelete='CASCADE'),
        nullable=False,
        comment="Reference to global label"
    )

    # Usage tracking
    usage_count = Column(
        Integer,
        nullable=False,
        server_default=text('0'),
        comment="Usage count within this brand"
    )

    # Creator info
    created_by = Column(
        UUID(as_uuid=True),
        ForeignKey('users.users.id'),
        nullable=False,
        comment="User who first used this label in the brand"
    )

    # Audit fields
    created_at = Column(
        DateTime(timezone=True),
        nullable=False,
        server_default=func.now(),
        comment="When the label was first used in this brand"
    )

    updated_at = Column(
        DateTime(timezone=True),
        nullable=False,
        server_default=func.now(),
        onupdate=func.now(),
        comment="When the label usage was last updated"
    )

    # Relationships
    brand = relationship(
        "Brand",
        foreign_keys=[brand_id]
    )

    global_label = relationship(
        "GlobalLabel",
        back_populates="brand_labels",
        foreign_keys=[global_label_id]
    )

    creator = relationship(
        "User",
        foreign_keys=[created_by]
    )

    def __repr__(self) -> str:
        return f"<BrandLabel(id={self.id}, brand_id={self.brand_id}, global_label_id={self.global_label_id})>"


class BrandInfluencerListEntryLabel(Base):
    """
    Many-to-many assignments of labels to influencer entries.
    Tracks which labels are assigned to which entries with audit trail.
    """
    __tablename__ = 'brand_influencer_list_entry_labels'
    __table_args__ = (
        UniqueConstraint('entry_id', 'global_label_id', name='entry_labels_entry_label_unique'),
        {'schema': 'users'}
    )

    # Primary key
    id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid.uuid4,
        server_default=text('gen_random_uuid()'),
        nullable=False
    )

    # Foreign keys
    entry_id = Column(
        UUID(as_uuid=True),
        ForeignKey('users.brand_influencer_list_entries.id', ondelete='CASCADE'),
        nullable=False,
        comment="Influencer list entry this label is assigned to"
    )

    global_label_id = Column(
        UUID(as_uuid=True),
        ForeignKey('users.global_labels.id', ondelete='CASCADE'),
        nullable=False,
        comment="Label assigned to the entry"
    )

    # Assignment audit
    assigned_by = Column(
        UUID(as_uuid=True),
        ForeignKey('users.users.id'),
        nullable=False,
        comment="User who assigned this label"
    )

    assigned_at = Column(
        DateTime(timezone=True),
        nullable=False,
        server_default=func.now(),
        comment="When the label was assigned"
    )

    # Relationships
    entry = relationship(
        "BrandInfluencerListEntry",
        foreign_keys=[entry_id]
    )

    global_label = relationship(
        "GlobalLabel",
        back_populates="entry_assignments",
        foreign_keys=[global_label_id]
    )

    assigned_by_user = relationship(
        "User",
        foreign_keys=[assigned_by]
    )

    def __repr__(self) -> str:
        return f"<BrandInfluencerListEntryLabel(id={self.id}, entry_id={self.entry_id}, global_label_id={self.global_label_id})>"
