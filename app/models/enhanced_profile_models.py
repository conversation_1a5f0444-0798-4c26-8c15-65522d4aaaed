"""
Enhanced Profile models to support full Phyllo API response structure
"""
import uuid
from datetime import datetime
from typing import Optional, List, Dict, Any

from sqlalchemy import Column, String, Integer, Float, Boolean, DateTime, Text, ForeignKey, JSON
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship

Base = declarative_base()


class EnhancedProfile(Base):
    """
    Enhanced Profile model to store complete Phyllo API response data
    """
    __tablename__ = "enhanced_profile"
    __table_args__ = {'schema': 'profile_analytics'}

    # Core fields (existing)
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    work_platform_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    external_id = Column(Text, nullable=False, index=True)
    platform_username = Column(Text, nullable=False, index=True)
    url = Column(Text, nullable=True)
    image_url = Column(Text, nullable=True)
    full_name = Column(Text, nullable=True)
    introduction = Column(Text, nullable=True)
    content_count = Column(Integer, nullable=True)
    is_verified = Column(Boolean, default=False, index=True)
    platform_account_type = Column(Text, nullable=True)
    gender = Column(Text, nullable=True, index=True)
    age_group = Column(Text, nullable=True, index=True)
    language = Column(String(10), nullable=True, index=True)
    
    # Performance metrics
    follower_count = Column(Integer, nullable=True, index=True)
    following_count = Column(Integer, nullable=True)
    subscriber_count = Column(Integer, nullable=True)
    average_likes = Column(Integer, nullable=True, index=True)
    average_comments = Column(Integer, nullable=True, index=True)
    average_views = Column(Integer, nullable=True, index=True)
    average_reels_views = Column(Integer, nullable=True)
    average_shares = Column(Integer, nullable=True)
    engagement_rate = Column(Float, nullable=True, index=True)
    credibility_score = Column(Float, nullable=True, index=True)
    
    # NEW: Phyllo-specific fields
    sponsored_posts_performance = Column(Float, nullable=True, index=True)
    
    # Location (structured as JSON)
    location_data = Column(JSONB, nullable=True)  # {city, state, country}
    
    # Content insights (JSON arrays/objects)
    top_hashtags = Column(JSONB, nullable=True)  # [{"name": "fashion", "value": 42.1}]
    top_mentions = Column(JSONB, nullable=True)  # [{"name": "brand", "value": 15.2}]
    top_interests = Column(JSONB, nullable=True)  # [{"name": "Beauty & Cosmetics"}]
    
    # Historical data
    reputation_history = Column(JSONB, nullable=True)  # Monthly performance data
    
    # Audience insights
    audience_demographics = Column(JSONB, nullable=True)  # Age, gender, location distribution
    audience_interests = Column(JSONB, nullable=True)  # Audience interest data
    brand_affinities = Column(JSONB, nullable=True)  # Brand affinity scores
    
    # Content performance tracking
    top_content_ids = Column(JSONB, nullable=True)  # References to top performing content
    recent_content_ids = Column(JSONB, nullable=True)  # References to recent content
    sponsored_content_ids = Column(JSONB, nullable=True)  # References to sponsored content
    
    # Metadata
    platform = Column(String(20), nullable=True, default="instagram", index=True)
    user_id = Column(UUID(as_uuid=True), nullable=True, index=True)
    data_source = Column(String(50), nullable=True, default="phyllo", index=True)
    last_updated_external = Column(DateTime(timezone=True), nullable=True)
    cache_expires_at = Column(DateTime(timezone=True), nullable=True)
    data_quality_score = Column(Float, nullable=True, default=0.5)
    profile_status = Column(String(20), nullable=True, default="active")
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    
    # Relationships
    content_items = relationship("ContentItem", back_populates="profile", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<EnhancedProfile(id={self.id}, platform_username={self.platform_username})>"
    
    @property
    def is_external_creator(self) -> bool:
        """Check if this is an externally fetched creator"""
        return self.user_id is None
    
    @property
    def location_city(self) -> Optional[str]:
        """Extract city from location data"""
        if self.location_data:
            return self.location_data.get('city')
        return None
    
    @property
    def location_country(self) -> Optional[str]:
        """Extract country from location data"""
        if self.location_data:
            return self.location_data.get('country')
        return None
    
    def to_quick_view_dict(self) -> dict:
        """Return dictionary for Quick View display"""
        return {
            "id": str(self.id),
            "platform_username": self.platform_username,
            "full_name": self.full_name,
            "platform": self.platform,
            "image_url": self.image_url,
            "follower_count": self.follower_count,
            "engagement_rate": self.engagement_rate,
            "is_verified": self.is_verified,
            "location": self.location_city,
            "primary_interests": self.top_interests[:3] if self.top_interests else [],
            "is_external": self.is_external_creator,
            "sponsored_posts_performance": self.sponsored_posts_performance
        }
    
    def to_detailed_dict(self) -> dict:
        """Return complete dictionary for detailed analytics view"""
        return {
            "id": str(self.id),
            "work_platform_id": str(self.work_platform_id),
            "profile": {
                "external_id": self.external_id,
                "platform_username": self.platform_username,
                "url": self.url,
                "image_url": self.image_url,
                "full_name": self.full_name,
                "introduction": self.introduction,
                "content_count": self.content_count,
                "sponsored_posts_performance": self.sponsored_posts_performance,
                "is_verified": self.is_verified,
                "platform_account_type": self.platform_account_type,
                "gender": self.gender,
                "age_group": self.age_group,
                "language": self.language,
                "follower_count": self.follower_count,
                "subscriber_count": self.subscriber_count,
                "average_likes": self.average_likes,
                "average_comments": self.average_comments,
                "average_views": self.average_views,
                "average_reels_views": self.average_reels_views,
                "engagement_rate": self.engagement_rate,
                "reputation_history": self.reputation_history,
                "location": self.location_data,
                "top_hashtags": self.top_hashtags,
                "top_mentions": self.top_mentions,
                "top_interests": self.top_interests,
                "audience_demographics": self.audience_demographics,
                "brand_affinities": self.brand_affinities
            },
            "metadata": {
                "data_source": self.data_source,
                "last_updated": self.last_updated_external.isoformat() if self.last_updated_external else None,
                "data_quality_score": self.data_quality_score,
                "is_external": self.is_external_creator
            }
        }


class ContentItem(Base):
    """
    Individual content items (posts, videos, reels) for detailed analytics
    """
    __tablename__ = "content_items"
    __table_args__ = {'schema': 'profile_analytics'}
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    profile_id = Column(UUID(as_uuid=True), ForeignKey('profile_analytics.enhanced_profile.id'), nullable=False)
    external_content_id = Column(String(255), nullable=False, index=True)
    
    # Content details
    content_type = Column(String(20), nullable=False, index=True)  # VIDEO, IMAGE, REELS
    url = Column(Text, nullable=False)
    title = Column(Text, nullable=True)
    description = Column(Text, nullable=True)
    thumbnail_url = Column(Text, nullable=True)
    
    # Engagement metrics
    like_count = Column(Integer, nullable=True)
    comment_count = Column(Integer, nullable=True)
    view_count = Column(Integer, nullable=True)
    play_count = Column(Integer, nullable=True)
    share_count = Column(Integer, nullable=True)
    save_count = Column(Integer, nullable=True)
    
    # Content metadata
    mentions = Column(JSONB, nullable=True)  # [{"name": "brand"}]
    hashtags = Column(JSONB, nullable=True)  # [{"name": "fashion"}]
    is_sponsored = Column(Boolean, default=False, index=True)
    
    # Timestamps
    published_at = Column(DateTime(timezone=True), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    
    # Relationships
    profile = relationship("EnhancedProfile", back_populates="content_items")
    
    def __repr__(self):
        return f"<ContentItem(id={self.id}, type={self.content_type}, profile_id={self.profile_id})>"


class ProfileAnalyticsCache(Base):
    """
    Cache table for storing Phyllo API responses
    """
    __tablename__ = "profile_analytics_cache"
    __table_args__ = {'schema': 'profile_analytics'}
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    profile_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    api_endpoint = Column(String(100), nullable=False, index=True)  # quick_search, advanced_search, profile_analytics
    request_hash = Column(String(64), nullable=False, index=True)  # MD5 hash of request parameters
    
    # Cached data
    response_data = Column(JSONB, nullable=False)
    
    # Cache metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    expires_at = Column(DateTime(timezone=True), nullable=False, index=True)
    hit_count = Column(Integer, default=0)
    last_accessed = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    def __repr__(self):
        return f"<ProfileAnalyticsCache(profile_id={self.profile_id}, endpoint={self.api_endpoint})>"
    
    @property
    def is_expired(self) -> bool:
        """Check if cache entry is expired"""
        return datetime.utcnow() > self.expires_at
