"""
OTP Manager utility for CreatorVerse Backend.
Implements optimized OTP operations using Redis pipelines with cache-aside pattern.
"""
import random
import string
import time
from functools import lru_cache
from typing import Dict, List, Optional, Tuple

from app.core.config import DEFAULT_OTP_LENGTH, MAX_OTP_ATTEMPTS, OTP_LOCKOUT_DURATION
from app.core.logger import get_logger
from app.core.redis_keys import RedisKeys, RedisConfig
from app.core.security import hash_otp, verify_otp_hash
from app.core_helper.async_logger import with_trace_id
from app.services.email_service import get_email_service
from app.services.sms_service import get_sms_service


def generate_otp(length: int = 6) -> str:
    """
    Generate a numeric OTP (One-Time Password).
    
    Args:
        length: Length of the OTP (default: 6)
        
    Returns:
        str: Generated OTP consisting of digits only
    """
    if length < 4 or length > 10:
        raise ValueError("OTP length must be between 4 and 10 digits")

    # Generate random digits
    otp = ''.join(random.choices(string.digits, k=length))

    # Ensure OTP doesn't start with 0 for better user experience
    if otp[0] == '0' and length > 1:
        otp = random.choice('123456789') + otp[1:]

    return otp


def generate_secure_otp(length: int = 6) -> Tuple[str, str]:
    """
    Generate a secure numeric OTP and its hash.
    
    Args:
        length: Length of the OTP (default: 6)
        
    Returns:
        Tuple[str, str]: (original_otp, hashed_otp)
    """
    otp = generate_otp(length)
    otp_hash = hash_otp(otp)
    return otp, otp_hash


class OptimizedOTPManager:
    """    
    Optimized OTP manager using Redis pipelines for batch operations.
    Implements cache-aside pattern with performance optimizations.
    """

    def __init__(self, redis_client):
        self.redis_client = redis_client
        self.logger = get_logger()
        self.email_service = get_email_service()
        self.sms_service = get_sms_service()

    @with_trace_id
    async def check_existing_otp_status_pipeline(self, user_email: str) -> Tuple[bool, int, str]:
        """
        Optimized OTP status check using Redis pipeline.
        
        Args:
            user_email: User email address
            
        Returns:
            (has_valid_otp, remaining_seconds, message)
        """
        otp_key = RedisKeys.otp_key(user_email)

        try:
            # Use pipeline for atomic operations
            pipe = self.redis_client.pipeline()
            await pipe.hgetall(otp_key)
            await pipe.ttl(otp_key)
            results = await pipe.execute()

            stored_data, ttl_seconds = results

            if not stored_data or ttl_seconds <= 0:
                self.logger.debug(f"No valid OTP found for {user_email}")
                return False, 0, "No active OTP found"

            # Check if locked due to too many failed attempts
            lockout_until = int(stored_data.get('lockout_until', 0))
            current_time = int(time.time())

            if lockout_until > current_time:
                remaining_lockout = lockout_until - current_time
                self.logger.warning(f"OTP locked for {user_email}, remaining: {remaining_lockout}s")
                return False, remaining_lockout, f"OTP locked. Try again in {remaining_lockout} seconds"

            self.logger.debug(f"Valid OTP found for {user_email}, TTL: {ttl_seconds}s")
            return True, ttl_seconds, "Valid OTP exists"

        except Exception as e:
            self.logger.error(f"Failed to check OTP status for {user_email}", extra={"error": str(e)})
            # Fallback to individual operations
            return await self._check_otp_status_fallback(user_email)

    @with_trace_id
    async def _check_otp_status_fallback(self, user_email: str) -> Tuple[bool, int, str]:
        """Fallback method for OTP status check."""
        otp_key = RedisKeys.otp_key(user_email)

        try:
            stored_data = await self.redis_client.hgetall(otp_key)
            if not stored_data:
                self.logger.debug(f"No OTP data found for {user_email} (fallback)")
                return False, 0, "No active OTP found"

            ttl_seconds = await self.redis_client.ttl(otp_key)
            if ttl_seconds <= 0:
                self.logger.debug(f"OTP expired for {user_email} (fallback)")
                return False, 0, "OTP has expired"
            # Check lockout status
            lockout_until = int(stored_data.get('lockout_until', 0))
            current_time = int(time.time())

            if lockout_until > current_time:
                remaining_lockout = lockout_until - current_time
                return False, remaining_lockout, f"OTP locked. Try again in {remaining_lockout} seconds"
            return True, ttl_seconds, "Valid OTP exists"

        except Exception as e:
            self.logger.error(f"Fallback OTP check failed for {user_email}", extra={"error": str(e)})
            return False, 0, "Error checking OTP status"    
    
    @with_trace_id
    async def create_otp_optimized(self, user_email: str, user_role: str = "influencer", is_login: bool = False) -> Optional[str]:
        """
        Create OTP with optimized Redis operations and send email.
        
        Args:
            user_email: Email address to send OTP to
            user_role: User role ("influencer" or "brand") for template selection
            is_login: Whether this is for login (True) or registration (False)
            
        Returns:
            str: The generated OTP or None on failure
        """
        otp, otp_hash = generate_secure_otp(DEFAULT_OTP_LENGTH)
        otp_key = RedisKeys.otp_key(user_email)

        now_ts = int(time.time())

        otp_data = {
            "otp_hash": otp_hash,
            "generated_at": str(now_ts),
            "failed_attempts": "0",
            "lockout_until": "0",
            "email": user_email
        }
        
        try:
            # Use pipeline for atomic operations
            pipe = self.redis_client.pipeline()
            await pipe.hset(otp_key, mapping=otp_data)
            await pipe.expire(otp_key, RedisConfig.OTP_TTL)

            # Set 30-second cooldown
            await pipe.setex(RedisKeys.otp_cooldown_key(user_email), 30, "1")

            # Rate limiting
            rate_limit_key = f"CreatorVerse:otp_rate_limit:{user_email}"
            await pipe.incr(rate_limit_key)
            await pipe.expire(rate_limit_key, 3600)

            # Execute pipeline properly
            results = await pipe.execute()

            # Check if Redis operations succeeded
            if results and results[0] is not None:  # hset successful
                self.logger.info(f"OTP created successfully for {user_email}")

                # Send email based on login or registration
                email_sent = False
                if is_login:
                    # Use login template
                    email_sent = await self.email_service.send_login_otp_email(
                        email=user_email,
                        otp=otp,
                        user_role=user_role
                    )
                    self.logger.info(f"Login OTP email requested for {user_email}")
                else:
                    # Use registration template
                    email_sent = await self.email_service.send_registration_otp_email(
                        email=user_email,
                        otp=otp,
                        user_role=user_role
                    )
                    self.logger.info(f"Registration OTP email requested for {user_email}")

                if email_sent:
                    self.logger.info(f"OTP email sent successfully to {user_email}")
                else:
                    self.logger.warning(f"OTP created but email failed for {user_email}")

                return otp
            else:
                self.logger.error(f"Failed to store OTP in Redis for {user_email}")
                return None
                
        except Exception as e:
            self.logger.error(f"Failed to create OTP for {user_email}", extra={"error": str(e)})
            return await self._create_otp_fallback(user_email, user_role, is_login)

    @with_trace_id
    async def _create_otp_fallback(self, user_email: str, user_role: str, is_login: bool = False) -> Optional[str]:
        """Fallback method for OTP creation."""
        otp, otp_hash = generate_secure_otp(DEFAULT_OTP_LENGTH)  # Generate OTP and hash together
        otp_key = RedisKeys.otp_key(user_email)

        now_ts = int(time.time())

        otp_data = {
            "otp_hash": otp_hash,  # Store hashed OTP
            "generated_at": str(now_ts),
            "failed_attempts": "0",
            "lockout_until": "0",
            "email": user_email
        }

        try:
            await self.redis_client.hset(otp_key, mapping=otp_data)
            await self.redis_client.expire(otp_key, RedisConfig.OTP_TTL)

            self.logger.info(f"OTP created with fallback method for {user_email}")

            # Send email based on login or registration
            email_sent = False
            if is_login:
                # Use login template
                email_sent = await self.email_service.send_login_otp_email(
                    email=user_email,
                    otp=otp,
                    user_role=user_role
                )
            else:
                # Use registration template  
                email_sent = await self.email_service.send_registration_otp_email(
                    email=user_email,
                    otp=otp,
                    user_role=user_role
                )

            if not email_sent:
                self.logger.warning(f"OTP created but email failed (fallback) for {user_email}")

            return otp

        except Exception as e:
            self.logger.error(f"Fallback OTP creation failed for {user_email}", extra={"error": str(e)})
            return None

    @with_trace_id
    async def verify_otp(self, user_email: str, provided_otp: str) -> Tuple[bool, str]:
        """
        Verify OTP with attempt tracking and lockout mechanism.
        
        Args:
            user_email: User email address
            provided_otp: OTP provided by user
            
        Returns:
            (is_valid, message)
        """
        otp_key = RedisKeys.otp_key(user_email)

        try:
            # Check if OTP exists and is valid
            has_otp, remaining_time, status_msg = await self.check_existing_otp_status_pipeline(user_email)

            if not has_otp:
                return False, status_msg

            # Get current OTP data
            stored_data = await self.redis_client.hgetall(otp_key)
            stored_otp = stored_data.get('otp_hash', '')
            failed_attempts = int(stored_data.get('failed_attempts', 0))
            # Verify OTP using secure hash comparison
            if verify_otp_hash(provided_otp, stored_otp):
                self.logger.info(f"OTP verified successfully for {user_email}")
                return True, "OTP verified successfully"
            else:  # OTP is incorrect - increment failed attempts
                failed_attempts += 1

                if failed_attempts >= MAX_OTP_ATTEMPTS:  # Max attempts reached
                    # Lock the OTP for the configured duration
                    lockout_until = int(time.time()) + OTP_LOCKOUT_DURATION
                    await self.redis_client.hset(otp_key, mapping={
                        'failed_attempts': str(failed_attempts),
                        'lockout_until': str(lockout_until)
                    })
                    self.logger.warning(f"OTP locked due to max attempts for {user_email}")
                    return False, f"Too many failed attempts. OTP locked for {OTP_LOCKOUT_DURATION // 60} minutes"
                else:
                    # Update failed attempts
                    await self.redis_client.hset(otp_key, 'failed_attempts', str(failed_attempts))
                    remaining_attempts = MAX_OTP_ATTEMPTS - failed_attempts
                    self.logger.warning(f"Invalid OTP for {user_email}, attempts remaining: {remaining_attempts}")
                    return False, f"Invalid OTP. {remaining_attempts} attempts remaining"

        except Exception as e:
            self.logger.error(f"Error verifying OTP for {user_email}", extra={"error": str(e)})
            return False, "Error verifying OTP"

    @with_trace_id
    async def resend_otp(self, user_email: str, user_role: str = "influencer") -> Tuple[bool, str]:
        """
        Resend OTP with rate limiting.
        
        Args:
            user_email: User email address
            user_role: User role for email template
            
        Returns:
            (success, message)
        """
        # Check rate limiting
        rate_limit_key = f"CreatorVerse:otp_rate_limit:{user_email}"

        try:
            current_count = await self.redis_client.get(rate_limit_key)
            if current_count and int(current_count) >= 5:  # Max 5 OTPs per hour
                self.logger.warning(f"Rate limit exceeded for {user_email}")
                return False, "Rate limit exceeded. Please try again later"

            # Delete existing OTP
            otp_key = RedisKeys.otp_key(user_email)
            await self.redis_client.delete(otp_key)            # Create new OTP - for resend, we'll preserve the login/registration intent
            # By default for resend, assume registration (is_login=False)
            new_otp = await self.create_otp_optimized(user_email, user_role, is_login=False)

            if new_otp:
                self.logger.info(f"OTP resent successfully for {user_email}")
                return True, "OTP sent successfully"
            else:
                return False, "Failed to send OTP"

        except Exception as e:
            self.logger.error(f"Error resending OTP for {user_email}", extra={"error": str(e)})
            return False, "Error sending OTP"

    @with_trace_id
    async def batch_check_otp_status(self, user_emails: List[str]) -> Dict[str, Tuple[bool, int, str]]:
        """
        Check OTP status for multiple emails in a single pipeline operation.
        
        Args:
            user_emails: List of email addresses to check
            
        Returns:
            Dictionary mapping email to (has_valid_otp, remaining_seconds, message)
        """
        if not user_emails:
            return {}

        try:
            # Build pipeline for all emails
            pipe = self.redis_client.pipeline()

            for email in user_emails:
                otp_key = RedisKeys.otp_key(email)
                await pipe.hgetall(otp_key)
                await pipe.ttl(otp_key)

            results = await pipe.execute()

            # Process results in pairs (data, ttl)
            batch_results = {}
            for i, email in enumerate(user_emails):
                data_idx = i * 2
                ttl_idx = data_idx + 1

                stored_data = results[data_idx]
                ttl_seconds = results[ttl_idx]

                if not stored_data or ttl_seconds <= 0:
                    batch_results[email] = (False, 0, "No active OTP found")
                else:
                    # Check lockout
                    lockout_until = int(stored_data.get('lockout_until', 0))
                    current_time = int(time.time())

                    if lockout_until > current_time:
                        remaining_lockout = lockout_until - current_time
                        batch_results[email] = (False, remaining_lockout, f"OTP locked for {remaining_lockout}s")
                    else:
                        batch_results[email] = (True, ttl_seconds, "Valid OTP exists")

            self.logger.debug(f"Batch OTP status check completed for {len(user_emails)} emails")
            return batch_results

        except Exception as e:
            self.logger.error(f"Batch OTP status check failed", extra={"error": str(e)})
            # Fallback to individual checks
            batch_results = {}
            for email in user_emails:
                batch_results[email] = await self.check_existing_otp_status_pipeline(email)
            return batch_results

    @with_trace_id
    async def cleanup_expired_otps_batch(self, user_emails: List[str]) -> int:
        """
        Clean up expired OTPs for multiple users in batch.
        
        Args:
            user_emails: List of email addresses to clean up
            
        Returns:
            Number of OTPs cleaned up
        """
        if not user_emails:
            return 0

        try:
            # Check which OTPs are expired
            pipe = self.redis_client.pipeline()

            for email in user_emails:
                otp_key = RedisKeys.otp_key(email)
                await pipe.ttl(otp_key)

            ttl_results = await pipe.execute()

            # Delete expired OTPs
            cleanup_pipe = self.redis_client.pipeline()
            cleanup_count = 0

            for i, email in enumerate(user_emails):
                if ttl_results[i] <= 0:  # Expired or doesn't exist
                    otp_key = RedisKeys.otp_key(email)
                    await cleanup_pipe.delete(otp_key)
                    cleanup_count += 1

            if cleanup_count > 0:
                await cleanup_pipe.execute()
                self.logger.info(f"Cleaned up {cleanup_count} expired OTPs")

            return cleanup_count

        except Exception as e:
            self.logger.error(f"Batch OTP cleanup failed", extra={"error": str(e)})
            return 0

    @with_trace_id
    async def check_mobile_otp_status_pipeline(self, mobile: str) -> Tuple[bool, int, str]:
        """
        Optimized OTP status check for mobile numbers using Redis pipeline.
        
        Args:
            mobile: Mobile number in E.164 format
            
        Returns:
            (has_valid_otp, remaining_seconds, message)
        """
        otp_key = RedisKeys.otp_mobile_key(mobile)

        try:
            # Use pipeline for atomic operations
            pipe = self.redis_client.pipeline()
            await pipe.hgetall(otp_key)
            await pipe.ttl(otp_key)
            results = await pipe.execute()

            stored_data, ttl_seconds = results

            if not stored_data or ttl_seconds <= 0:
                self.logger.debug(f"No valid OTP found for mobile {mobile}")
                return False, 0, "No active OTP found"

            # Check if locked due to too many failed attempts
            lockout_until = int(stored_data.get('lockout_until', 0))
            current_time = int(time.time())

            if lockout_until > current_time:
                remaining_lockout = lockout_until - current_time
                self.logger.warning(f"OTP locked for mobile {mobile}, remaining: {remaining_lockout}s")
                return False, remaining_lockout, f"OTP locked. Try again in {remaining_lockout} seconds"
            self.logger.debug(f"Valid OTP found for mobile {mobile}, TTL: {ttl_seconds}s")
            return True, ttl_seconds, "Valid OTP exists"

        except Exception as e:
            self.logger.error(f"Failed to check OTP status for mobile {mobile}", extra={"error": str(e)})
            return False, 0, "Error checking OTP status"
    
    @with_trace_id
    async def create_mobile_otp_optimized(self, mobile: str, user_role: str = "influencer", is_login: bool = False) -> Optional[str]:
        """
        Create OTP for mobile number with optimized Redis operations and send SMS.
        
        Args:
            mobile: Mobile number in E.164 format
            user_role: User role for SMS template selection
            is_login: Whether this is for login (True) or registration (False)
            
        Returns:
            Generated OTP string if successful, None if failed
        """
        otp, otp_hash = generate_secure_otp(DEFAULT_OTP_LENGTH)  # Generate OTP and hash together
        otp_key = RedisKeys.otp_mobile_key(mobile)

        now_ts = int(time.time())

        otp_data = {
            "otp_hash": otp_hash,  # Store hashed OTP
            "generated_at": str(now_ts),
            "failed_attempts": "0",
            "lockout_until": "0",
            "mobile": mobile
        }

        try:
            # Use pipeline for atomic OTP creation
            pipe = self.redis_client.pipeline()
            await pipe.hset(otp_key, mapping=otp_data)
            await pipe.expire(otp_key, RedisConfig.OTP_TTL)
            # Optional: Set rate limiting key
            rate_limit_key = f"CreatorVerse:otp_rate_limit:mobile:{mobile}"
            await pipe.incr(rate_limit_key)
            await pipe.expire(rate_limit_key, 3600)  # 1 hour rate limit window

            results = await pipe.execute()

            if results[0]:  # hset successful
                self.logger.info(f"OTP created successfully for mobile {mobile}")                # Send OTP via SMS
                # Note: The SMS service currently uses the same template for both login and registration
                # In the future, we may want to enhance the SMS service to have different templates
                sms_sent = await self.sms_service.send_otp_sms(
                    phone_number=mobile,
                    otp=otp,
                    user_role=user_role
                )
                
                if is_login:
                    self.logger.info(f"Login OTP SMS requested for {mobile}")
                else:
                    self.logger.info(f"Registration OTP SMS requested for {mobile}")

                if sms_sent:
                    self.logger.info(f"OTP SMS sent successfully to {mobile}")
                else:
                    self.logger.warning(f"OTP created but SMS failed for {mobile}")

                return otp
            else:
                self.logger.error(f"Failed to store OTP in Redis for mobile {mobile}")
                return None

        except Exception as e:
            self.logger.error(f"Failed to create OTP for mobile {mobile}", extra={"error": str(e)})
            return None

    @with_trace_id
    async def verify_mobile_otp(self, mobile: str, provided_otp: str) -> Tuple[bool, str]:
        """
        Verify mobile OTP with attempt tracking and lockout mechanism.
        
        Args:
            mobile: Mobile number in E.164 format
            provided_otp: OTP provided by user
            
        Returns:
            (is_valid, message)
        """
        otp_key = RedisKeys.otp_mobile_key(mobile)

        try:
            # Check if OTP exists and is valid
            has_otp, remaining_time, status_msg = await self.check_mobile_otp_status_pipeline(mobile)

            if not has_otp:
                return False, status_msg

            # Get current OTP data
            stored_data = await self.redis_client.hgetall(otp_key)
            stored_otp = stored_data.get('otp_hash', '')
            failed_attempts = int(stored_data.get('failed_attempts', 0))

            # Verify OTP using secure hash comparison
            if verify_otp_hash(provided_otp, stored_otp):
                # OTP is correct - delete it and return success
                await self.redis_client.delete(otp_key)
                self.logger.info(f"OTP verified successfully for mobile {mobile}")
                return True, "OTP verified successfully"
            else:
                # OTP is incorrect - increment failed attempts
                failed_attempts += 1

                if failed_attempts >= MAX_OTP_ATTEMPTS:  # Max attempts reached
                    # Lock the OTP for the configured duration
                    lockout_until = int(time.time()) + OTP_LOCKOUT_DURATION
                    await self.redis_client.hset(otp_key, mapping={
                        'failed_attempts': str(failed_attempts),
                        'lockout_until': str(lockout_until)
                    })
                    self.logger.warning(f"OTP locked due to max attempts for mobile {mobile}")
                    return False, f"Too many failed attempts. OTP locked for {OTP_LOCKOUT_DURATION // 60} minutes"
                else:
                    # Update failed attempts
                    await self.redis_client.hset(otp_key, 'failed_attempts', str(failed_attempts))
                    remaining_attempts = MAX_OTP_ATTEMPTS - failed_attempts
                    self.logger.warning(f"Invalid OTP for mobile {mobile}, attempts remaining: {remaining_attempts}")
                    return False, f"Invalid OTP. You have {remaining_attempts} attempts remaining."

        except Exception as e:
            self.logger.error(f"Error verifying OTP for mobile {mobile}", extra={"error": str(e)})
            return False, "Error verifying OTP"


@lru_cache(maxsize=1)
def get_optimized_otp_manager(redis_client=None) -> OptimizedOTPManager:
    """
    Get optimized OTP manager instance.
    
    Args:
        redis_client: Redis client instance (optional, will use default if not provided)
        
    Returns:
        OptimizedOTPManager instance
    """
    if redis_client is None:
        from app.api.dependencies import get_locobuzz_redis
        redis_client = get_locobuzz_redis()

    return OptimizedOTPManager(redis_client)


# Convenience functions for backward compatibility
@with_trace_id
async def check_existing_otp_status_optimized(redis_client, user_email: str) -> Tuple[bool, int, str]:
    """
    Optimized version of check_existing_otp_status using pipeline operations.
    
    Args:
        redis_client: Redis client instance
        user_email: User email address
        
    Returns:
        (has_valid_otp, remaining_seconds, message)
    """
    otp_manager = get_optimized_otp_manager(redis_client)
    return await otp_manager.check_existing_otp_status_pipeline(user_email)


@with_trace_id
async def create_otp_optimized(redis_client, user_email: str, user_role: str = "influencer", is_login: bool = False) -> Optional[str]:
    """
    Optimized OTP creation using pipeline operations.
    
    Args:
        redis_client: Redis client instance
        user_email: User email address
        user_role: User role for email template selection
        is_login: Whether this is for login (True) or registration (False)
        
    Returns:
        Generated OTP string if successful, None if failed
    """
    otp_manager = get_optimized_otp_manager(redis_client)
    return await otp_manager.create_otp_optimized(user_email, user_role, is_login)


@with_trace_id
async def verify_otp_optimized(redis_client, user_email: str, provided_otp: str) -> Tuple[bool, str]:
    """
    Optimized OTP verification using pipeline operations.
    
    Args:
        redis_client: Redis client instance
        user_email: User email address
        provided_otp: OTP provided by user
        
    Returns:
        (is_valid, message)
    """
    otp_manager = get_optimized_otp_manager(redis_client)
    return await otp_manager.verify_otp(user_email, provided_otp)


@with_trace_id
async def resend_otp_optimized(redis_client, user_email: str, user_role: str = "influencer") -> Tuple[bool, str]:
    """
    Optimized OTP resend using pipeline operations.
    
    Args:
        redis_client: Redis client instance
        user_email: User email address
        user_role: User role for email template
        
    Returns:
        (success, message)
    """
    otp_manager = get_optimized_otp_manager(redis_client)
    return await otp_manager.resend_otp(user_email, user_role)


@with_trace_id
async def create_mobile_otp_optimized(redis_client, mobile: str, user_role: str = "influencer", is_login: bool = False) -> Optional[str]:
    """
    Optimized OTP creation for mobile using pipeline operations.
    
    Args:
        redis_client: Redis client instance
        mobile: Mobile number in E.164 format
        user_role: User role for SMS template selection
        is_login: Whether this is for login (True) or registration (False)
        
    Returns:
        Generated OTP string if successful, None if failed
    """
    otp_manager = get_optimized_otp_manager(redis_client)
    return await otp_manager.create_mobile_otp_optimized(mobile, user_role, is_login)


@with_trace_id
async def verify_mobile_otp_optimized(redis_client, mobile: str, provided_otp: str) -> Tuple[bool, str]:
    """
    Optimized mobile OTP verification using pipeline operations.
    
    Args:
        redis_client: Redis client instance
        mobile: Mobile number in E.164 format
        provided_otp: OTP provided by user
        
    Returns:
        (is_valid, message)
    """
    otp_manager = get_optimized_otp_manager(redis_client)
    return await otp_manager.verify_mobile_otp(mobile, provided_otp)
