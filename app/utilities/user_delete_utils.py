"""
Utility functions for deleting users and cleaning up associated data.
"""

from typing import List, Optional
from uuid import UUID

from sqlalchemy import delete, select
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.logger import get_logger
from app.core.redis_keys import RedisKeys
from app.core_helper.async_logger import with_trace_id
from app.models.user_models import (
    User, 
    UserRoleModel, 
    UserAuthMethod, 
    OrganizationMembership, 
    BrandMembership,
    UserOTP
)
from app.utilities.bloom_filter import CreatorBloomFilter

logger = get_logger()


@with_trace_id
async def delete_user_and_cleanup(
    *,
    db_conn,
    redis_client,
    user_id: UUID,
    email: Optional[str] = None,
    phone_number: Optional[str] = None
) -> bool:
    """
    Delete a user and clean up all associated data including:
    - Database entries (user, roles, auth methods, organization/brand memberships)
    - Redis cache keys
    - Bloom filter entries
    
    Args:
        db_conn: Database connection
        redis_client: Redis client
        user_id: User ID to delete
        email: User email (optional, for cache cleanup)
        phone_number: User phone number (optional, for cache cleanup)
        
    Returns:
        bool: True if successfully deleted, False otherwise
    """
    try:
        # First fetch user details if email/phone not provided
        if not email or not phone_number:
            async with db_conn.get_db() as session:
                user = await session.get(User, user_id)
                if not user:
                    logger.warning(f"User not found for deletion: {user_id}")
                    return False
                
                email = email or user.email
                phone_number = phone_number or user.phone_number
        
        # Delete all DB records in a transaction
        async with db_conn.get_db() as session:
            async with session.begin():
                # 1. Delete user roles
                await session.execute(
                    delete(UserRoleModel).where(UserRoleModel.user_id == user_id)
                )
                
                # 2. Delete user auth methods
                await session.execute(
                    delete(UserAuthMethod).where(UserAuthMethod.user_id == user_id)
                )
                
                # 3. Delete organization memberships
                await session.execute(
                    delete(OrganizationMembership).where(OrganizationMembership.user_id == user_id)
                )
                
                # 4. Delete brand memberships
                await session.execute(
                    delete(BrandMembership).where(BrandMembership.user_id == user_id)
                )
                
                # 5. Delete user OTP records
                await session.execute(
                    delete(UserOTP).where(UserOTP.user_id == user_id)
                )
                
                # 6. Delete the user
                await session.execute(
                    delete(User).where(User.id == user_id)
                )
        
        # Clean up Redis cache keys
        redis_keys_to_delete = []
        
        # User ID based keys
        redis_keys_to_delete.append(f"CreatorVerse:user:id:{str(user_id)}")
        redis_keys_to_delete.append(f"CreatorVerse:user:organizations:{str(user_id)}")
        redis_keys_to_delete.append(f"CreatorVerse:user:brands:{str(user_id)}")
        redis_keys_to_delete.append(RedisKeys.rbac_user_roles(user_id))
        redis_keys_to_delete.append(f"CreatorVerse:user:auth_methods:{str(user_id)}")
        
        # Email based keys
        if email:
            redis_keys_to_delete.append(RedisKeys.user_by_email(email))
            redis_keys_to_delete.append(RedisKeys.otp_key(email))
            
            # Remove user email from bloom filter
            bloom_filter = CreatorBloomFilter(
                redis_client,
                filter_name=RedisKeys.get_email_bloom_filter_key(),
            )
            await bloom_filter.remove(email)
        
        # Phone based keys
        if phone_number:
            redis_keys_to_delete.append(RedisKeys.user_by_mobile(phone_number))
            redis_keys_to_delete.append(RedisKeys.otp_mobile_key(phone_number))
        
        # Delete all Redis keys
        if redis_keys_to_delete:
            await redis_client.delete(*redis_keys_to_delete)
        
        logger.info(f"Successfully deleted user and cleaned up associated data: {user_id}")
        return True
        
    except Exception as e:
        logger.error(f"Error deleting user {user_id}: {str(e)}")
        return False


@with_trace_id
async def soft_delete_user(
    *,
    db_conn,
    redis_client,
    user_id: UUID,
    email: Optional[str] = None,
    phone_number: Optional[str] = None
) -> bool:
    """
    Soft delete a user by marking as inactive but preserving data.
    Still cleans up Redis cache to ensure inactive user cannot authenticate.
    
    Args:
        db_conn: Database connection
        redis_client: Redis client
        user_id: User ID to delete
        email: User email (optional, for cache cleanup)
        phone_number: User phone number (optional, for cache cleanup)
        
    Returns:
        bool: True if successfully marked inactive, False otherwise
    """
    try:
        # First fetch user details if email/phone not provided
        if not email or not phone_number:
            async with db_conn.get_db() as session:
                user = await session.get(User, user_id)
                if not user:
                    logger.warning(f"User not found for soft deletion: {user_id}")
                    return False
                
                email = email or user.email
                phone_number = phone_number or user.phone_number
        
        # Mark user inactive
        async with db_conn.get_db() as session:
            async with session.begin():
                user = await session.get(User, user_id)
                if not user:
                    return False
                
                user.is_active = False
                user.status = "inactive"
        
        # Clean up Redis cache keys
        redis_keys_to_delete = []
        
        # User ID based keys
        redis_keys_to_delete.append(f"CreatorVerse:user:id:{str(user_id)}")
        redis_keys_to_delete.append(f"CreatorVerse:user:organizations:{str(user_id)}")
        redis_keys_to_delete.append(f"CreatorVerse:user:brands:{str(user_id)}")
        redis_keys_to_delete.append(RedisKeys.rbac_user_roles(user_id))
        
        # Email based keys
        if email:
            redis_keys_to_delete.append(RedisKeys.user_by_email(email))
        
        # Phone based keys
        if phone_number:
            redis_keys_to_delete.append(RedisKeys.user_by_mobile(phone_number))
        
        # Delete all Redis keys
        if redis_keys_to_delete:
            await redis_client.delete(*redis_keys_to_delete)
        
        logger.info(f"Successfully soft deleted user: {user_id}")
        return True
        
    except Exception as e:
        logger.error(f"Error soft deleting user {user_id}: {str(e)}")
        return False


@with_trace_id
async def get_user_active_sessions(
    *,
    db_conn,
    redis_client,
    user_id: UUID
) -> List[str]:
    """
    Get all active session IDs for a user.
    Useful for terminating sessions during user deletion.
    
    Args:
        db_conn: Database connection
        redis_client: Redis client
        user_id: User ID
        
    Returns:
        List[str]: List of active session IDs
    """
    try:
        session_key_pattern = f"CreatorVerse:session:user:{str(user_id)}:*"
        sessions = []
        
        # Scan Redis for user session keys
        async for key in redis_client.scan_iter(match=session_key_pattern):
            # Extract session ID from key
            parts = key.split(':')
            if len(parts) >= 5:
                session_id = parts[-1]
                sessions.append(session_id)
        
        return sessions
        
    except Exception as e:
        logger.error(f"Error fetching user sessions for {user_id}: {str(e)}")
        return []


@with_trace_id
async def invalidate_user_sessions(
    *,
    redis_client,
    user_id: UUID
) -> bool:
    """
    Invalidate all active sessions for a user.
    Should be called when changing passwords or during account deletion.
    
    Args:
        redis_client: Redis client
        user_id: User ID
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Find all session keys for this user
        session_key_pattern = f"CreatorVerse:session:user:{str(user_id)}:*"
        deleted = await redis_client.delete_pattern(session_key_pattern)
        
        logger.info(f"Invalidated {deleted} sessions for user {user_id}")
        return True
        
    except Exception as e:
        logger.error(f"Error invalidating sessions for user {user_id}: {str(e)}")
        return False
