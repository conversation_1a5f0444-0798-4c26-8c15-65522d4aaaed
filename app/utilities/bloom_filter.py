import hashlib
import math
import threading
from functools import wraps
from typing import Any, cast

from fastapi import HTTPException, status

from app.core.redis_keys import RedisKeys
from app.core_helper.redis_client import RedisClient


class CreatorBloomFilter:
    """
    Async Bloom Filter backed by Redis.bitops.
    No false negatives; configurable false-positive rate.
    """

    def __init__(
            self,
            redis_client: RedisClient,
            filter_name: str,
            capacity: int = 10000,
            error_rate: float = 0.01,
    ) -> None:
        self.redis_client: RedisClient = redis_client
        self.filter_name = filter_name
        self.capacity = capacity
        self.error_rate = error_rate

        # compute m, k
        self.bit_array_size = int(-capacity * math.log(error_rate) / (math.log(2) ** 2))
        self.hash_count = int((self.bit_array_size / capacity) * math.log(2))

        # keys
        self.metadata_key = f"{filter_name}:metadata"
        self.bitmap_key = f"{filter_name}:bitmap"

    async def initialize(self) -> None:
        """Ensure metadata exists in Redis."""
        if not await self.redis_client.exists(self.metadata_key):
            metadata = {
                "capacity": self.capacity,
                "error_rate": str(self.error_rate),
                "bit_array_size": self.bit_array_size,
                "hash_count": self.hash_count,
                "element_count": 0,
            }
            await self.redis_client.hset_mapping(self.metadata_key, mapping=metadata)  # type: ignore

    def _hash_functions(self, item: str) -> list[int]:
        """Double-hashing to generate k indices."""
        data = item.encode()
        hash1 = int(hashlib.md5(data).hexdigest(), 16)
        hash2 = int(hashlib.sha1(data).hexdigest(), 16)
        return [
            (hash1 + i * hash2) % self.bit_array_size
            for i in range(self.hash_count)
        ]

    async def add(self, item: str) -> bool:
        """
        Add an item. Returns True if it was definitely new.
        """
        await self.initialize()
        bits = self._hash_functions(item)
        was_new = True

        # check for at least one zero
        for b in bits:
            if await self.redis_client.getbit(self.bitmap_key, b):
                was_new = False
                break

        # set all bits
        pipe = self.redis_client.pipeline()
        for b in bits:
            await pipe.setbit(self.bitmap_key, b, 1)
        await pipe.execute()

        if was_new:
            await self.redis_client.hincrby(self.metadata_key, "element_count", 1)
        return was_new

    async def exists(self, item: str) -> bool:
        """
        Returns True if item _might_ exist (all bits = 1),
        False if definitely not (any bit = 0).
        """
        await self.initialize()
        bits = self._hash_functions(item)
        pipe = self.redis_client.pipeline()
        for b in bits:
            await pipe.getbit(self.bitmap_key, b)
        results = await pipe.execute()
        return all(results)

    async def not_exists(self, item: str) -> bool:
        """
        Returns True if item is definitely not in the set (any bit = 0).
        """
        await self.initialize()
        for b in self._hash_functions(item):
            if (await self.redis_client.getbit(self.bitmap_key, b)) == 0:
                return True
        return False

    async def get_stats(self) -> dict[str, Any]:
        """Fetch metadata and compute current false-positive probability."""
        raw = cast(dict[bytes, bytes], await self.redis_client.hgetall(self.metadata_key))
        if not raw:
            return {}
        meta = {k.decode(): v.decode() for k, v in raw.items()}
        count = int(meta.get("element_count", "0"))
        cap = int(meta.get("capacity", str(self.capacity)))
        m = int(meta.get("bit_array_size", str(self.bit_array_size)))
        k = int(meta.get("hash_count", str(self.hash_count)))

        if count > 0:
            fpp = (1 - math.exp(-k * count / m)) ** k
        else:
            fpp = 0.0

        return {
            "capacity": cap,
            "element_count": count,
            "bit_array_size": m,
            "hash_count": k,
            "current_false_positive_probability": fpp,
            "load_factor": count / cap if cap else 0,
        }

    async def clear(self) -> bool:
        """Reset both bitmap and metadata."""
        try:
            pipe = self.redis_client.pipeline()
            await pipe.delete(self.bitmap_key)
            await pipe.delete(self.metadata_key)
            await pipe.execute()
            # re-init metadata
            await self.initialize()
            return True
        except Exception:
            return False


class CreatorBloomFilterManager:
    """
    Singleton manager for per-feature Bloom filters.
    """
    _instances: dict[str, "CreatorBloomFilterManager"] = {}
    _global_lock = threading.Lock()

    def __new__(cls, redis_client: RedisClient):
        key = getattr(redis_client, "connection_pool", None) or str(redis_client)
        with cls._global_lock:
            if key not in cls._instances:
                cls._instances[key] = super().__new__(cls)
        return cls._instances[key]

    def __init__(self, redis_client: RedisClient) -> None:
        # Might run multiple times, but idempotent
        self.redis_client = redis_client
        self._email_filter: CreatorBloomFilter | None = None
        self._lock = threading.Lock()

    def get_email_filter(
            self, capacity: int = 100_000, error_rate: float = 0.001
    ) -> CreatorBloomFilter:
        with self._lock:
            if self._email_filter is None:
                self._email_filter = CreatorBloomFilter(
                    self.redis_client,
                    RedisKeys.get_email_bloom_filter_key(),
                    capacity=capacity,
                    error_rate=error_rate,
                )
        return self._email_filter

    async def check_email_exists(self, email: str) -> bool:
        return await self.get_email_filter().exists(email)

    async def check_email_not_exists(self, email: str) -> bool:
        return await self.get_email_filter().not_exists(email)

    async def add_email(self, email: str) -> bool:
        return await self.get_email_filter().add(email)

    async def get_email_filter_stats(self) -> dict[str, Any]:
        return await self.get_email_filter().get_stats()

    async def clear_email_filter(self) -> bool:
        ok = await self.get_email_filter().clear()
        if ok:
            with self._lock:
                self._email_filter = None
        return ok


# factory
def get_bloom_filter_manager(redis_client: RedisClient) -> CreatorBloomFilterManager:
    return CreatorBloomFilterManager(redis_client)


def bloom_register(field: str = "email", add_on_success: bool = False):
    """
    Pre-check BloomFilter.check_email_exists(...) and optionally
    add_on_success after the wrapped endpoint returns without error.

    Args:
        field: name of the identifier kwarg or Pydantic body attribute
        add_on_success: if True, call add_email(...) on success
    """

    def decorator(fn):
        @wraps(fn)
        async def wrapper(*args, **kwargs):
            # 1) Extract identifier
            identifier = None
            if field in kwargs:
                identifier = kwargs[field]
            else:
                body = kwargs.get("data") or kwargs.get("body")
                if body and hasattr(body, field):
                    identifier = getattr(body, field)

            # 2) If we got an identifier, do the BF check
            if identifier:
                redis_client = kwargs.get("redis_client")
                if not redis_client:
                    raise RuntimeError("redis_client dependency not found")

                mgr = get_bloom_filter_manager(redis_client)
                if await mgr.check_email_exists(identifier):
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"{field} '{identifier}' already registered"
                    )

            # 3) Call the real endpoint
            result = await fn(*args, **kwargs)

            # 4) On success, optionally add to BF
            if identifier and add_on_success:
                await mgr.add_email(identifier)

            return result

        return wrapper

    return decorator
