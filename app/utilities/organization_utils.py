# app/services/organization_service.py
"""
Organization-level helpers (async, cache-aware).

Pattern:
    • Write helpers: expect an **open** AsyncSession inside `session.begin()`
      to guarantee atomicity.
    • Read helpers: open/close their own session via db_conn.
    • All helpers accept a redis_client and keep Redis keys in sync.
    • Idempotent + race-safe (`SELECT … FOR UPDATE` where needed).

Redis key helpers assumed in app.core.redis_keys:
    organization_key(org_id)
    organization_domain_key(domain)
    org_member_status_key(org_id, user_id)
    org_members_list_key(org_id)
    org_by_user_key(user_id)
"""

from __future__ import annotations

import json
from datetime import UTC, datetime
from typing import Dict, List
from uuid import UUID

from sqlalchemy import delete, func, select, text, update
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.redis_keys import RedisKeys
from app.core_helper.async_logger import with_trace_id
from app.models.user_models import (
    Organization,
    OrganizationMembership,
    Org<PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,  # global RBAC table
)

# ────────────────────────────────────────────────────────────────────────
# WRITE HELPERS (SESSION INSIDE TRANSACTION BOUNDARY)
# ────────────────────────────────────────────────────────────────────────
@with_trace_id
async def update_organization_info(
    *,
    session: AsyncSession,
    redis_client,
    org: Organization,
    **fields,
) -> Organization:
    """
    Update mutable fields (name, logo_url, contact_email, timezone…).
    Caller provides SA instance already bound to `session`.
    """
    for k, v in fields.items():
        if hasattr(org, k):
            setattr(org, k, v)
    org.updated_at = datetime.now(UTC)

    await _cache_org(redis_client, org)
    return org


@with_trace_id
async def soft_delete_organization(
    *,
    session: AsyncSession,
    redis_client,
    org_id: UUID,
) -> bool:
    """
    Mark organisation & its memberships inactive.
    """
    now = datetime.now(UTC)
    stmt = (
        update(Organization)
        .where(Organization.id == org_id, Organization.is_active)
        .values(is_active=False, deleted_at=now, updated_at=now)
        .execution_options(synchronize_session="fetch")
    )
    res = await session.execute(stmt)
    if res.rowcount == 0:
        return False

    # deactivate all active memberships
    await session.execute(
        update(OrganizationMembership)
        .where(OrganizationMembership.organization_id == org_id, OrganizationMembership.is_active)
        .values(is_active=False, deleted_at=now)
    )

    # revoke global roles
    await session.execute(
        delete(UserRoleModel).where(
            UserRoleModel.user_id.in_(
                select(OrganizationMembership.user_id).where(
                    OrganizationMembership.organization_id == org_id
                )
            ),
            UserRoleModel.role_id.in_(
                select(MasterRole.id).where(MasterRole.role_name.like("org_%"))
            ),
        )
    )

    # evict caches
    await redis_client.delete(
        RedisKeys.organization_key(org_id),
        RedisKeys.org_members_list_key(org_id),
    )
    return True


@with_trace_id
async def transfer_organization_ownership(
    *,
    session: AsyncSession,
    redis_client,
    org_id: UUID,
    old_user_id: UUID,
    new_user_id: UUID,
) -> None:
    """
    Demote old owner → admin; promote new owner.
    """
    # lock both rows
    stmt = (
        select(OrganizationMembership)
        .where(
            OrganizationMembership.organization_id == org_id,
            OrganizationMembership.user_id.in_([old_user_id, new_user_id]),
        )
        .with_for_update()
    )
    rows = {m.user_id: m async for m in (await session.stream(stmt))}
    if old_user_id not in rows or new_user_id not in rows:
        raise ValueError("Owners not found")

    rows[old_user_id].role = OrgMemberRole.admin
    rows[new_user_id].role = OrgMemberRole.owner

    await _evict_member_cache(redis_client, org_id, old_user_id)
    await _evict_member_cache(redis_client, org_id, new_user_id)


@with_trace_id
async def change_member_role(
    *,
    session: AsyncSession,
    redis_client,
    org_id: UUID,
    user_id: UUID,
    new_role: OrgMemberRole,
) -> None:
    """
    Promote/demote user inside organisation.
    """
    stmt = (
        update(OrganizationMembership)
        .where(
            OrganizationMembership.organization_id == org_id,
            OrganizationMembership.user_id == user_id,
            OrganizationMembership.is_active,
        )
        .values(role=new_role, updated_at=datetime.now(UTC))
        .execution_options(synchronize_session="fetch")
    )
    await session.execute(stmt)
    await _evict_member_cache(redis_client, org_id, user_id)


@with_trace_id
async def deactivate_member(
    *,
    session: AsyncSession,
    redis_client,
    org_id: UUID,
    user_id: UUID,
) -> None:
    """
    Soft-delete a member row, revoke global org_* roles.
    """
    now = datetime.now(UTC)
    await session.execute(
        update(OrganizationMembership)
        .where(
            OrganizationMembership.organization_id == org_id,
            OrganizationMembership.user_id == user_id,
            OrganizationMembership.is_active,
        )
        .values(is_active=False, deleted_at=now)
    )
    await session.execute(
        delete(UserRoleModel).where(
            UserRoleModel.user_id == user_id,
            UserRoleModel.role_id.in_(
                select(MasterRole.id).where(MasterRole.role_name.like("org_%"))
            ),
        )
    )
    await _evict_member_cache(redis_client, org_id, user_id)


# ────────────────────────────────────────────────────────────────────────
# READ HELPERS (CACHE-ASIDE)
# ────────────────────────────────────────────────────────────────────────
@with_trace_id
async def list_user_organizations(
    *,
    db_conn,
    redis_client,
    user_id: UUID,
    ttl: int = 600,
):
    key = RedisKeys.org_by_user_key(user_id)
    cached = await redis_client.get(key)
    if cached:
        return json.loads(cached)

    async with db_conn.get_db() as s:
        stmt = (
            select(Organization)
            .join(OrganizationMembership,
                  OrganizationMembership.organization_id == Organization.id)
            .where(
                OrganizationMembership.user_id == user_id,
                OrganizationMembership.is_active,
                Organization.is_active,
            )
        )
        org = (await s.execute(stmt)).scalar_one_or_none()

    if not org:
        return None

    payload = _org_payload(org)
    await redis_client.set(key, json.dumps(payload), ex=ttl)
    return payload


@with_trace_id
async def get_organization_by_id(
    *,
    db_conn,
    redis_client,
    org_id: UUID,
    ttl: int = 3600,
):
    key = RedisKeys.organization_key(org_id)
    cached = await redis_client.get(key)
    if cached:
        return json.loads(cached)

    async with db_conn.get_db() as s:
        org = await s.get(Organization, org_id)
        if not org:
            return None

    payload = _org_payload(org)
    await redis_client.set(key, json.dumps(payload), ex=ttl)
    return payload


@with_trace_id
async def count_active_members(*, db_conn, org_id: UUID) -> int:
    async with db_conn.get_db() as s:
        stmt = select(func.count()).select_from(
            OrganizationMembership
        ).where(
            OrganizationMembership.organization_id == org_id,
            OrganizationMembership.is_active,
        )
        return (await s.scalar(stmt)) or 0


# ────────────────────────────────────────────────────────────────────────
# INTERNAL UTILS
# ────────────────────────────────────────────────────────────────────────
@with_trace_id
async def get_user_organization_membership(
    *,
    db_conn,
    redis_client,
    user_id: UUID,
    ttl: int = 900
) -> Dict | None:
    """
    Get the active organization membership for a user.
    
    Args:
        db_conn: Database connection
        redis_client: Redis client
        user_id: UUID of the user
        ttl: Cache TTL in seconds
        
    Returns:
        Dict with organization membership details or None
    """
    key = RedisKeys.org_by_user_key(user_id)
    cached = await redis_client.get(key)
    if cached:
        return json.loads(cached)
        
    async with db_conn.get_db() as s:
        # Get active membership with organization data
        stmt = (
            select(OrganizationMembership, Organization)
            .join(Organization, OrganizationMembership.organization_id == Organization.id)
            .where(
                OrganizationMembership.user_id == user_id,
                OrganizationMembership.is_active == True,
                Organization.is_active == True
            )
        )
        result = await s.execute(stmt)
        row = result.first()
        
        if not row:
            return None
            
        membership, org = row
        
        # Create payload
        payload = {
            "organization_id": str(org.id),
            "organization_name": org.name,
            "organization_domain": org.domain,
            "role": membership.role.value if hasattr(membership.role, "value") else membership.role,
            "joined_at": membership.joined_at.isoformat() if membership.joined_at else None,
        }
        
        await redis_client.setex(key,  ttl, json.dumps(payload))
        return payload


def _org_payload(org: Organization) -> Dict:
    return {
        "id": str(org.id),
        "domain": org.domain,
        "name": org.name,
        "is_active": org.is_active,
    }


async def _cache_org(redis_client, org: Organization, ttl: int = 3600):
    payload = _org_payload(org)
    await redis_client.set(RedisKeys.organization_key(org.id), json.dumps(payload), ex=ttl)
    await redis_client.set(RedisKeys.organization_by_domain(org.domain), json.dumps(payload), ex=ttl)


async def _evict_member_cache(redis_client, org_id: UUID, user_id: UUID):
    await redis_client.delete(
        RedisKeys.org_member_status_key(org_id, user_id),
        RedisKeys.org_members_list_key(org_id),
    )
