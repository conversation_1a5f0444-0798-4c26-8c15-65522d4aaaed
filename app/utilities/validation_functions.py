"""
Validation and utility functions for CreatorVerse Backend.
"""
from typing import Optional, <PERSON><PERSON>


def validate_email_format(email: str) -> bool:
    """
    Basic email format validation.
    
    Args:
        email: Email address to validate
        
    Returns:
        bool: True if email format is valid, False otherwise
    """
    import re

    if not email or len(email) < 5:
        return False

    # Basic email regex pattern
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(pattern, email))


def validate_phone_number(phone: str) -> bool:
    """
    Basic phone number validation.
    
    Args:
        phone: Phone number to validate
        
    Returns:
        bool: True if phone format is valid, False otherwise
    """
    if not phone:
        return False

    # Remove spaces, dashes, and parentheses
    cleaned = ''.join(char for char in phone if char.isdigit() or char == '+')

    # Check if it's a valid length (10-15 digits, optionally with + prefix)
    if cleaned.startswith('+'):
        return 10 <= len(cleaned) <= 16  # Including the + sign
    else:
        return 10 <= len(cleaned) <= 15


def sanitize_input(text: Optional[str], max_length: int = 255) -> Optional[str]:
    """
    Sanitize user input by removing potentially harmful characters.
    
    Args:
        text: Input text to sanitize
        max_length: Maximum allowed length
        
    Returns:
        str: Sanitized text or None if input was None
    """
    if text is None:
        return None

    # Strip whitespace
    text = text.strip()

    if not text:
        return None

    # Truncate if too long
    if len(text) > max_length:
        text = text[:max_length]

    # Remove or escape potentially harmful characters
    # This is a basic implementation - you might want to use a proper sanitization library
    dangerous_chars = ['<', '>', '"', "'", '&', '\x00', '\n', '\r', '\t']
    for char in dangerous_chars:
        text = text.replace(char, '')

    return text


def validate_password_strength(password: str) -> Tuple[bool, str]:
    """
    Validate password strength.
    
    Args:
        password: Password to validate
        
    Returns:
        Tuple[bool, str]: (is_valid, message)
    """
    if not password:
        return False, "Password is required"

    if len(password) < 8:
        return False, "Password must be at least 8 characters long"

    if len(password) > 128:
        return False, "Password must be less than 128 characters"

    # Check for at least one digit
    if not any(char.isdigit() for char in password):
        return False, "Password must contain at least one digit"

    # Check for at least one letter
    if not any(char.isalpha() for char in password):
        return False, "Password must contain at least one letter"

    # Check for at least one special character
    special_chars = "!@#$%^&*()_+-=[]{}|;:,.<>?"
    if not any(char in special_chars for char in password):
        return False, "Password must contain at least one special character"

    return True, "Password is strong"


def normalize_email(email: str) -> str:
    """
    Normalize email address to lowercase and trim whitespace.
    
    Args:
        email: Email address to normalize
        
    Returns:
        str: Normalized email address
    """
    if not email:
        return ""

    return email.strip().lower()


def extract_domain_from_email(email: str) -> Optional[str]:
    """
    Extract domain from email address.
    
    Args:
        email: Email address
        
    Returns:
        str: Domain part of email or None if invalid
    """
    normalized_email = normalize_email(email)

    if '@' not in normalized_email:
        return None

    try:
        domain = normalized_email.split('@')[1]
        return domain if domain else None
    except IndexError:
        return None


# Import required for type hints
from typing import Tuple
