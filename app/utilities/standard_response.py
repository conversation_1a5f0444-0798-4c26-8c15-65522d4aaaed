"""
Standard response utility for CreatorVerse Discovery Profile Analytics.

This module provides a standardized response format for all API endpoints
to ensure consistency across the application.
"""

from datetime import datetime
from typing import Any, Dict, Generic, Optional, TypeVar, Union
from pydantic import BaseModel, Field

from app.core.config import APP_CONFIG

# Generic type for response data
T = TypeVar('T')


class StandardResponse(BaseModel, Generic[T]):
    """
    Standard response format for all API endpoints.
    
    This ensures consistent response structure across the entire application.
    """
    success: bool = Field(..., description="Whether the request was successful")
    message: str = Field(..., description="Human-readable message about the operation")
    data: Optional[T] = Field(None, description="Response data")
    error: Optional[Dict[str, Any]] = Field(None, description="Error details if unsuccessful")
    timestamp: str = Field(..., description="ISO timestamp of the response")
    meta: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")
    
    @classmethod
    def success(
        cls,
        data: Optional[T] = None,
        message: str = "Operation completed successfully",
        meta: Optional[Dict[str, Any]] = None
    ) -> "StandardResponse[T]":
        """
        Create a successful response.
        
        Args:
            data: Response data
            message: Success message
            meta: Additional metadata
            
        Returns:
            StandardResponse instance for success
        """
        timestamp = datetime.utcnow().isoformat() + "Z"
        
        return cls(
            success=True,
            message=message,
            data=data,
            error=None,
            timestamp=timestamp,
            meta=meta
        )
    
    @classmethod
    def error(
        cls,
        message: str,
        error_code: str = "UNKNOWN_ERROR",
        error_details: Optional[Dict[str, Any]] = None,
        data: Optional[T] = None,
        meta: Optional[Dict[str, Any]] = None
    ) -> "StandardResponse[T]":
        """
        Create an error response.
        
        Args:
            message: Error message
            error_code: Error code for identification
            error_details: Additional error details
            data: Optional data to include even in error cases
            meta: Additional metadata
            
        Returns:
            StandardResponse instance for error
        """
        timestamp = datetime.utcnow().isoformat() + "Z"
        
        error_obj = {
            "code": error_code,
            "message": message
        }
        
        if error_details:
            error_obj["details"] = error_details
        
        return cls(
            success=False,
            message=message,
            data=data,
            error=error_obj,
            timestamp=timestamp,
            meta=meta
        )
    
    @classmethod
    def paginated(
        cls,
        data: T,
        page: int,
        page_size: int,
        total_count: int,
        message: str = "Data retrieved successfully",
        meta: Optional[Dict[str, Any]] = None
    ) -> "StandardResponse[T]":
        """
        Create a paginated response.
        
        Args:
            data: Response data (usually a list)
            page: Current page number
            page_size: Items per page
            total_count: Total number of items
            message: Success message
            meta: Additional metadata
            
        Returns:
            StandardResponse instance with pagination metadata
        """
        total_pages = (total_count + page_size - 1) // page_size
        
        pagination_meta = {
            "pagination": {
                "page": page,
                "page_size": page_size,
                "total_count": total_count,
                "total_pages": total_pages,
                "has_next": page < total_pages,
                "has_previous": page > 1
            }
        }
        
        if meta:
            pagination_meta.update(meta)
        
        return cls.success(
            data=data,
            message=message,
            meta=pagination_meta
        )
    
    @classmethod
    def created(
        cls,
        data: Optional[T] = None,
        message: str = "Resource created successfully",
        resource_id: Optional[str] = None,
        meta: Optional[Dict[str, Any]] = None
    ) -> "StandardResponse[T]":
        """
        Create a response for successful resource creation.
        
        Args:
            data: Created resource data
            message: Success message
            resource_id: ID of the created resource
            meta: Additional metadata
            
        Returns:
            StandardResponse instance for creation
        """
        creation_meta = {}
        
        if resource_id:
            creation_meta["resource_id"] = resource_id
        
        if meta:
            creation_meta.update(meta)
        
        return cls.success(
            data=data,
            message=message,
            meta=creation_meta if creation_meta else None
        )
    
    @classmethod
    def updated(
        cls,
        data: Optional[T] = None,
        message: str = "Resource updated successfully",
        resource_id: Optional[str] = None,
        meta: Optional[Dict[str, Any]] = None
    ) -> "StandardResponse[T]":
        """
        Create a response for successful resource update.
        
        Args:
            data: Updated resource data
            message: Success message
            resource_id: ID of the updated resource
            meta: Additional metadata
            
        Returns:
            StandardResponse instance for update
        """
        update_meta = {}
        
        if resource_id:
            update_meta["resource_id"] = resource_id
        
        if meta:
            update_meta.update(meta)
        
        return cls.success(
            data=data,
            message=message,
            meta=update_meta if update_meta else None
        )
    
    @classmethod
    def deleted(
        cls,
        message: str = "Resource deleted successfully",
        resource_id: Optional[str] = None,
        meta: Optional[Dict[str, Any]] = None
    ) -> "StandardResponse[None]":
        """
        Create a response for successful resource deletion.
        
        Args:
            message: Success message
            resource_id: ID of the deleted resource
            meta: Additional metadata
            
        Returns:
            StandardResponse instance for deletion
        """
        deletion_meta = {}
        
        if resource_id:
            deletion_meta["resource_id"] = resource_id
        
        if meta:
            deletion_meta.update(meta)
        
        return cls.success(
            data=None,
            message=message,
            meta=deletion_meta if deletion_meta else None
        )
    
    @classmethod
    def validation_error(
        cls,
        validation_errors: list,
        message: str = "Validation failed"
    ) -> "StandardResponse[None]":
        """
        Create a response for validation errors.
        
        Args:
            validation_errors: List of validation error details
            message: Error message
            
        Returns:
            StandardResponse instance for validation error
        """
        return cls.error(
            message=message,
            error_code="VALIDATION_ERROR",
            error_details={"validation_errors": validation_errors}
        )
    
    @classmethod
    def not_found(
        cls,
        resource_type: str = "Resource",
        resource_id: Optional[str] = None,
        message: Optional[str] = None
    ) -> "StandardResponse[None]":
        """
        Create a response for resource not found.
        
        Args:
            resource_type: Type of resource that wasn't found
            resource_id: ID of the resource that wasn't found
            message: Custom error message
            
        Returns:
            StandardResponse instance for not found error
        """
        if not message:
            if resource_id:
                message = f"{resource_type} with ID '{resource_id}' not found"
            else:
                message = f"{resource_type} not found"
        
        error_details = {"resource_type": resource_type}
        if resource_id:
            error_details["resource_id"] = resource_id
        
        return cls.error(
            message=message,
            error_code="RESOURCE_NOT_FOUND",
            error_details=error_details
        )
    
    @classmethod
    def unauthorized(
        cls,
        message: str = "Authentication required"
    ) -> "StandardResponse[None]":
        """
        Create a response for unauthorized access.
        
        Args:
            message: Error message
            
        Returns:
            StandardResponse instance for unauthorized error
        """
        return cls.error(
            message=message,
            error_code="UNAUTHORIZED",
            error_details={"requires_authentication": True}
        )
    
    @classmethod
    def forbidden(
        cls,
        message: str = "Access forbidden",
        required_permissions: Optional[list] = None
    ) -> "StandardResponse[None]":
        """
        Create a response for forbidden access.
        
        Args:
            message: Error message
            required_permissions: List of required permissions
            
        Returns:
            StandardResponse instance for forbidden error
        """
        error_details = {}
        if required_permissions:
            error_details["required_permissions"] = required_permissions
        
        return cls.error(
            message=message,
            error_code="FORBIDDEN",
            error_details=error_details if error_details else None
        )
    
    @classmethod
    def rate_limited(
        cls,
        retry_after: int,
        message: str = "Rate limit exceeded"
    ) -> "StandardResponse[None]":
        """
        Create a response for rate limit exceeded.
        
        Args:
            retry_after: Seconds to wait before retrying
            message: Error message
            
        Returns:
            StandardResponse instance for rate limit error
        """
        return cls.error(
            message=message,
            error_code="RATE_LIMIT_EXCEEDED",
            error_details={
                "retry_after_seconds": retry_after,
                "suggestion": f"Please wait {retry_after} seconds before making another request"
            }
        )
    
    def add_execution_time(self, execution_time_ms: int) -> "StandardResponse[T]":
        """
        Add execution time to the response metadata.
        
        Args:
            execution_time_ms: Execution time in milliseconds
            
        Returns:
            Self for method chaining
        """
        if not self.meta:
            self.meta = {}
        
        self.meta["execution_time_ms"] = execution_time_ms
        return self
    
    def add_cache_info(self, cache_hit: bool, cache_key: Optional[str] = None) -> "StandardResponse[T]":
        """
        Add cache information to the response metadata.
        
        Args:
            cache_hit: Whether the response came from cache
            cache_key: Cache key used (optional)
            
        Returns:
            Self for method chaining
        """
        if not self.meta:
            self.meta = {}
        
        cache_info = {"cache_hit": cache_hit}
        if cache_key:
            cache_info["cache_key"] = cache_key
        
        self.meta["cache"] = cache_info
        return self


# Type aliases for common response types
ResponseData = StandardResponse[Any]
ListResponse = StandardResponse[list]
DictResponse = StandardResponse[dict]
StringResponse = StandardResponse[str]
BoolResponse = StandardResponse[bool]
IntResponse = StandardResponse[int]
