"""
Brand organization utilities for CreatorVerse Backend.
Contains helper functions for ensuring organization and membership for brand users.
"""
from __future__ import annotations
from datetime import datetime, UTC
from typing import Tuple, Optional
from uuid import UUID

from sqlalchemy import select, func, and_
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.logger import get_logger
from app.core_helper.async_logger import with_trace_id
from app.models.user_models import (
    Organization,
    OrganizationMembership,
    OrgMemberRole,
    User,
    Brand,
    BrandMembership,
    BrandMembershipStatus
)
from app.utilities.validation_functions import extract_domain_from_email

logger = get_logger()


@with_trace_id
async def ensure_org_and_membership(
    *,
    session: AsyncSession,
    redis_client,
    user: User,
    org_name: Optional[str] = None
) -> Tuple[Organization, OrganizationMembership, bool]:
    """
    Ensure that an organization exists for the user's email domain and the user
    is a member of that organization.
    
    Args:
        session: Open AsyncSession inside transaction boundary
        redis_client: Redis client instance
        user: User instance (must have valid email)
        org_name: Optional name for the organization if it needs to be created
        
    Returns:
        Tuple[Organization, OrganizationMembership, bool]: 
            - Organization instance
            - OrganizationMembership instance
            - True if the organization was created, False if it already existed
    
    Note: This function must be called inside a transaction boundary.
    """
    if not user.email:
        raise ValueError("User must have an email address to ensure organization membership")
    
    # Extract domain from email
    domain = extract_domain_from_email(user.email)
    if not domain:
        raise ValueError(f"Could not extract domain from email: {user.email}")
    
    # Look for existing organization by domain
    stmt = select(Organization).where(
        Organization.domain == domain,
        Organization.is_active == True
    )
    result = await session.execute(stmt)
    org = result.scalars().first()
    
    org_created = False
    # Create organization if it doesn't exist
    if not org:
        logger.info(f"Creating new organization for domain: {domain}")
        org_created = True
        org = Organization(
            domain=domain,
            name=org_name or f"{domain.split('.')[0].title()} Organization",
            organization_code=domain.split('.')[0],
            is_active=True,
            created_at=datetime.now(UTC),
            updated_at=datetime.now(UTC)
        )
        session.add(org)
        # Flush to get the ID
        await session.flush()
        logger.info(f"Created organization: {org.id} for domain: {domain}")
    
    # Check if user already has a membership with this organization
    stmt = select(OrganizationMembership).where(
        OrganizationMembership.user_id == user.id,
        OrganizationMembership.organization_id == org.id,
        OrganizationMembership.is_active == True
    )
    result = await session.execute(stmt)
    membership = result.scalars().first()
    
    if not membership:
        # If organization was just created, make user the owner
        # Otherwise, make them a regular member
        role = OrgMemberRole.owner if org_created else OrgMemberRole.member
        
        logger.info(f"Creating new organization membership for user: {user.id} with role: {role.value}")
        membership = OrganizationMembership(
            organization_id=org.id,
            user_id=user.id,
            role=role,
            is_active=True,
            joined_at=datetime.now(UTC),
            created_at=datetime.now(UTC),
            updated_at=datetime.now(UTC)
        )
        session.add(membership)
    
    return org, membership, org_created
