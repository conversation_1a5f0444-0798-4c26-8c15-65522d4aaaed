from typing import Any, Union, Mapping, Sequence, Optional
from uuid import uuid4

from fastapi import <PERSON><PERSON><PERSON><PERSON>xception
from pydantic import BaseModel
from sqlalchemy import select, inspect, and_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Declarative<PERSON>eta
from starlette import status

from app.core.enums import UserStatus
from app.core.exceptions import EmailValidationError
from app.core.logger import get_logger
from app.core.redis_keys import RedisKeys
from app.models.user_models import User, UserOTP
from app.schemas.auth import OtpResponse
from app.services.email_validation_service import verify_email_exists_optimized
from app.services.user_service import get_user_by_email_cache_aside, get_user_by_mobile_cache_aside
from app.utilities.otp_manager import get_optimized_otp_manager

logger = get_logger()


async def create_user(
        session: AsyncSession,
        user_data: Union[Mapping, BaseModel],
) -> User:
    """
    Create a brand‐new User. Flags start out all False.
    Must be inside session.begin().
    """
    # Normalize to dict
    if hasattr(user_data, "dict"):
        data = user_data.dict(exclude_unset=True)
    else:
        data = dict(user_data)

    user = User(
        id=uuid4(),
        email=data.get("email"),
        phone_number=data.get("mobile") or data.get("phone_number"),
        status=data.get("status", UserStatus.get_status_name(UserStatus.REQUESTED.value)),
        is_email_verified=False,
        is_phone_verified=False,
        is_active=False,  # <-- explicitly false
        register_source=data.get("register_source"),
        # any other fields you want to map…
    )
    session.add(user)
    await session.flush()  # populate id + defaults
    return user


async def create_or_update(
        session: AsyncSession,
        model: DeclarativeMeta,
        data: Union[Mapping[str, Any], BaseModel],
        lookup_fields: Sequence[str],
        update_if_exists: bool = False,
) -> Any:
    """
    Create or update a row for `model` given incoming `data`.

    - `lookup_fields`: list of column names (e.g. ["email"] or ["email","phone_number"])
       that uniquely identify the row. All must be present in `data` to trigger lookup.
    - If a row is found and `update_if_exists=True`, it’s updated; otherwise returned.
    - If no row, a new one is created with a generated PK (UUID) if needed.
    - Only columns actually present on the model are used.
    - Must be called inside an open transaction (e.g. async with session.begin()).
    """
    # 1) Normalize to dict
    if hasattr(data, "dict"):
        raw = data.dict(exclude_unset=True)
    else:
        raw = dict(data)

    # 2) Get all column names for this model
    mapper = inspect(model)
    valid_cols = {c.key for c in mapper.mapper.column_attrs}

    # 3) Filter incoming data
    filtered = {k: v for k, v in raw.items() if k in valid_cols}

    # 4) Try lookup if all lookup_fields provided
    can_lookup = all(f in filtered and filtered[f] is not None for f in lookup_fields)
    instance = None
    if can_lookup:
        # build WHERE clauses
        clauses = []
        for f in lookup_fields:
            col = getattr(model, f)
            val = filtered[f]
            # case-insensitive on strings?
            if isinstance(val, str) and col.type.python_type is str:
                clauses.append(col.ilike(val))
            else:
                clauses.append(col == val)
        stmt = select(model).where(and_(*clauses))
        result = await session.execute(stmt)
        instance = result.scalar_one_or_none()

    # 5) If exists
    if instance:
        if update_if_exists:
            for k, v in filtered.items():
                setattr(instance, k, v)
            await session.flush()
        return instance

    # 6) Else create new
    # auto-populate PK if it's a UUID primary key with default
    init_kwargs = filtered.copy()
    pk_name = mapper.primary_key[0].key
    if pk_name not in init_kwargs:
        # generate a UUID if the column default is uuid4 or server_default gen_random_uuid
        init_kwargs[pk_name] = uuid4()

    new_obj = model(**init_kwargs)
    session.add(new_obj)
    await session.flush()
    return new_obj


async def validate_and_fetch_existing_user(
        identifier: str,
        identifier_type: str,
        db_conn,
        redis_client,
):
    """
    Validate the identifier (email or mobile), then do a cache-aside lookup.
    Returns the existing user object or None.
    Raises HTTPException(400) on validation failure or invalid identifier_type.
    """
    id_type = identifier_type.lower()
    if id_type not in ("email", "mobile"):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid identifier_type: {identifier_type}",
        )

    if id_type == "email":
        # 1) Validate email format & domain
        try:
            is_valid, reason = await verify_email_exists_optimized(identifier, redis_client)
            if not is_valid:
                logger.warning(
                    "Invalid email domain during registration",
                    extra={"identifier": identifier, "reason": reason},
                )
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Email validation failed: {reason}",
                )
        except EmailValidationError as e:
            logger.warning(
                "Invalid email provided during registration",
                extra={"identifier": identifier, "reason": str(e)},
            )
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Please use a correct email address",
            )
        except HTTPException:
            raise
        except Exception as e:
            logger.error(
                "Email validation error during registration",
                extra={"identifier": identifier, "error": str(e)},
            )
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Please use a correct email address",
            )

        # 2) Cache-aside lookup
        existing_user = await get_user_by_email_cache_aside(db_conn, redis_client, identifier)

    else:  # mobile path
        existing_user = await get_user_by_mobile_cache_aside(db_conn, redis_client, identifier)

    return existing_user


async def send_user_otp(
        identifier: str,
        identifier_type: str,
        redis_client,
        user_role: str = "influencer",
        is_login: bool = False,
        db_conn = None
) -> OtpResponse:
    """
    Log & send an OTP to an existing user (email or mobile).
    
    Args:
        identifier: Email or mobile number of the user
        identifier_type: Type of the identifier ("email" or "mobile")
        redis_client: Redis client for OTP storage
        user_role: Type of user ("influencer" or "brand")
        is_login: Whether this OTP is for login (True) or registration (False)
        db_conn: Optional database connection to persist OTP in database
        
    Raises:
        HTTPException: 500 on failure
    """
    logger.info(
        f"User requesting OTP for {'login' if is_login else 'registration'}",
        extra={"identifier": identifier, "type": identifier_type, "role": user_role},
    )

    otp_manager = get_optimized_otp_manager(redis_client)

    if identifier_type == "email":
        otp = await otp_manager.create_otp_optimized(
            identifier, 
            user_role=user_role, 
            is_login=is_login
        )
    else:  # mobile
        otp = await otp_manager.create_mobile_otp_optimized(
            identifier, 
            user_role=user_role,
            is_login=is_login
        )

    if not otp:
        logger.error(
            "Failed to create OTP for existing user",
            extra={"identifier": identifier, "type": identifier_type},
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to send verification {identifier_type}",
        )
        
    # If db_conn is provided, persist OTP in database
    if db_conn and is_login:
        # For login we need to get the user ID from cache or database
        if identifier_type == "email":
            user_dict = await get_user_by_email_cache_aside(db_conn, redis_client, identifier)
        else:  # mobile
            user_dict = await get_user_by_mobile_cache_aside(db_conn, redis_client, identifier)
            
        if user_dict and user_dict.get("id"):
            # Get the hash using the secure hash function since we have the plaintext OTP
            from app.core.security import hash_otp
            hashed_code = hash_otp(otp)
            
            # Persist OTP record to database
            try:
                async with db_conn.get_db() as session:
                    await upsert_user_otp_entry(
                        db_session=session,
                        user_id=str(user_dict.get("id")),  # Ensure it's a string
                        hashed_code=hashed_code,
                        channel=identifier_type
                    )
                    await session.commit()
                    logger.info(
                        "OTP persisted in database",
                        extra={"identifier": identifier, "user_id": user_dict.get("id")}
                    )
            except Exception as e:
                logger.error(
                    "Failed to persist OTP in database",
                    extra={"identifier": identifier, "type": identifier_type, "error": str(e)}
                )
                # Continue execution - don't block OTP delivery due to DB error

    return OtpResponse(
        success=True,
        message=f"Verification code sent to your {identifier_type}",
        **{identifier_type: identifier},
    )


async def upsert_user_otp_entry(
    db_session: AsyncSession,
    user_id: str,
    hashed_code: Optional[str] = None,
    channel: str = "email",
    new_entry: bool = True,
    current_failed_attempts: int = 0,
    update_threshold: int = 5  # DB update only every 5 failed attempts
) -> None:
    """
    Create or update the UserOTP record during login or verification.
    
    For a new OTP generation (new_entry=True), it creates a new record.
    For a failed OTP attempt (new_entry=False), it updates the failed_attempts
    only if current_failed_attempts modulo update_threshold is zero.
    
    Args:
        db_session: AsyncSession to perform DB operations.
        user_id: The user identifier.
        hashed_code: The hashed OTP code. (Only used in new_entry)
        channel: OTP channel e.g. "email" or "mobile".
        new_entry: True for creating a new record; False for a failed attempt update.
        current_failed_attempts: The failure count from Redis (for optimization).
        update_threshold: Update DB only when this threshold is met.
    """
    try:
        if new_entry:
            # Create a brand-new OTP record in the DB with failed_attempts = 0.
            new_otp = UserOTP(
                user_id=user_id,
                hashed_code=hashed_code,  # already hashed OTP code
                channel=channel,
                failed_attempts=0,
                is_active=True
            )
            db_session.add(new_otp)
            try:
                await db_session.flush()
                logger.info("Created new UserOTP entry", extra={"user_id": user_id, "channel": channel})
            except Exception as e:
                logger.error("Error creating UserOTP entry", extra={"user_id": user_id, "error": str(e)})
        elif current_failed_attempts % update_threshold == 0:
            # Only update DB every 'update_threshold' failed attempts
            stmt = select(UserOTP).where(
                UserOTP.user_id == user_id,
                UserOTP.channel == channel,
                UserOTP.is_active == True
            )
            result = await db_session.execute(stmt)
            otp_entry = result.scalar_one_or_none()
            
            if otp_entry:
                # Use setattr to avoid typing issues
                setattr(otp_entry, 'failed_attempts', current_failed_attempts)
                try:
                    await db_session.flush()
                    logger.info(
                        "Updated UserOTP failed_attempts count",
                        extra={"user_id": user_id, "failed_attempts": current_failed_attempts}
                    )
                except Exception as e:
                    logger.error("Error updating UserOTP entry", extra={"user_id": user_id, "error": str(e)})
            else:
                logger.warning(
                    "No active OTP entry found to update failed attempts",
                    extra={"user_id": user_id, "channel": channel}
                )
        else:
            logger.debug(
                "Skipped DB update for UserOTP failed_attempts", 
                extra={"user_id": user_id, "current_failed_attempts": current_failed_attempts}
            )
    except Exception as e:
        logger.error(
            "Error in upsert_user_otp_entry", 
            extra={"user_id": user_id, "channel": channel, "error": str(e)}
        )


async def save_user_role_to_redis(
    user_id: str,
    role: str,
    redis_client
) -> None:
    """
    Save user role to Redis for future authentication checks.
    
    Args:
        user_id: User ID as string
        role: Role name (e.g., "brand", "brand-admin", "influencer")
        redis_client: Redis client instance
    """
    role_key = RedisKeys.user_role_key(user_id)
    await redis_client.setex(role_key, 86400*7, role)  # Cache for 7 days
    logger.info(
        "User role saved to Redis",
        extra={"user_id": user_id, "role": role}
    )


async def get_user_role_from_redis(
    user_id: str,
    redis_client
) -> Optional[str]:
    """
    Get user role from Redis.
    
    Args:
        user_id: User ID as string
        redis_client: Redis client instance
        
    Returns:
        Role name or None if not found
    """
    role_key = RedisKeys.user_role_key(user_id)
    return await redis_client.get(role_key)


async def verify_user_role(
    user_id: str,
    expected_role: str, 
    redis_client
) -> bool:
    """
    Verify if user has the expected role.
    
    Args:
        user_id: User ID as string
        expected_role: Expected role or role prefix (e.g., "brand" will match both "brand" and "brand-admin")
        redis_client: Redis client instance
        
    Returns:
        True if user has the expected role, False otherwise
    """
    user_role = await get_user_role_from_redis(user_id, redis_client)
    
    if not user_role:
        # If no role in Redis, default to allowing access
        # This ensures backward compatibility with existing users
        logger.warning(
            "No role found in Redis for user",
            extra={"user_id": user_id, "expected_role": expected_role}
        )
        return True
        
    # For "brand" expected role, accept both "brand" and "brand-admin"
    if expected_role == "brand" and user_role.startswith("brand"):
        return True
    
    # For exact matches
    return user_role == expected_role
