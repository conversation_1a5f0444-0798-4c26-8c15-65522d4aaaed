"""
Brand-level helpers (async, cache-aware).

They follow exactly the same conventions as the organisation helpers:

• **Write helpers**: expect an open AsyncSession already wrapped in
  `async with session.begin(): …` so everything remains atomic.
• **Read helpers**: open/close their own session via `db_conn`.
• Every helper receives a `redis_client`; caches are kept in sync.
• Idempotent, race-safe (`SELECT … FOR UPDATE` where needed).

Redis-key helpers assumed in `app.core.redis_keys`:

    brand_key(id)                         →  CreatorVerse:brand:{id}
    brand_members_list_key(id, mode)      →  …:brand:members:{id}:{mode}
    brand_member_status_key(bid, uid)     →  …:brand:mem:{bid}:{uid}
    brands_by_org_key(org_id)             →  …:org:{org_id}:brands
"""

from __future__ import annotations

import json
from datetime import UTC, datetime
from typing import Dict, List, Tuple, Any
from uuid import UUID

from fastapi import HTTPException
from sqlalchemy import update, delete, select, func
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.logger import get_logger
from app.core.redis_keys import RedisKeys
from app.core_helper.async_logger import with_trace_id
from app.models.user_models import (
    Brand,
    BrandMembership,
    BrandMembershipStatus,
    Organization,
    User,
    MasterRole,
    UserRoleModel,
    OrgMemberRole,
)
from app.utilities.organization_utils import get_user_organization_membership


# ────────────────────────────────────────────────────────────────────────
# WRITE HELPERS (require session.begin)
# ────────────────────────────────────────────────────────────────────────
@with_trace_id
async def create_brand(
        *,
        session: AsyncSession,
        redis_client,
        org: Organization,
        creator_id: str | UUID,
        name: str,
        description: str | None = None,
        logo_url: str | None = None,
) -> Tuple[Brand, BrandMembership]:
    """
    Insert a new brand + owner membership.  Caller ensures creator is allowed
    (org owner/admin).  Duplicate brand names within the same org raise
    IntegrityError via unique constraint.
    """
    brand = Brand(
        organization_id=org.id,
        name=name,
        description=description,
        logo_url=logo_url,
        created_by=creator_id,
    )
    session.add(brand)
    await session.flush()  # materialise brand.id

    membership = BrandMembership(
        brand_id=brand.id,
        user_id=creator_id,
        role="brand_owner",
        status=BrandMembershipStatus.active,
        joined_at=datetime.now(UTC),
    )
    session.add(membership)

    await _cache_brand(redis_client, brand)
    await _evict_brand_lists(redis_client, org.id)
    return brand, membership


@with_trace_id
async def request_join_brand(
        *,
        session: AsyncSession,
        redis_client,
        brand: Brand,
        user_id: str | UUID,
) -> BrandMembership:
    """
    If already active/pending membership → return it.
    Else insert status='pending'.  Idempotent, race-safe.
    """
    mem = await _lock_and_get_membership(session, brand.id, user_id)
    if mem:
        return mem

    mem = BrandMembership(
        brand_id=brand.id,
        user_id=user_id,
        role="member",
        status=BrandMembershipStatus.pending,
        joined_at=datetime.now(UTC),
    )
    session.add(mem)
    await _evict_member_caches(redis_client, brand.id, user_id)
    return mem


@with_trace_id
async def approve_join_request(
        *,
        session: AsyncSession,
        redis_client,
        brand_id: UUID,
        user_id: UUID,
        approver_id: UUID,
) -> None:
    """
    Promote a pending membership to active. 
    
    Approver must have one of these roles:
    - Organization owner
    - Brand owner
    - Brand admin
    
    The function performs the following actions:
    1. Gets the membership record with a SELECT FOR UPDATE to prevent race conditions
    2. Updates the membership status to active and sets joined_at to current time
    3. Assigns the brand_member role to the user in the RBAC system
    4. Invalidates all relevant caches:
       - Individual member caches
       - Brand member lists
       - Pending requests lists
       - Aggregated pending requests view for organization
       
    Args:
        session: AsyncSession within an active transaction
        redis_client: Redis client instance
        brand_id: UUID of the brand
        user_id: UUID of the user whose membership is being approved
        approver_id: UUID of the approving user (for audit trail)
        
    Raises:
        ValueError: If no pending request exists for the given user/brand
    """
    mem = await _lock_and_get_membership(session, brand_id, user_id)
    if not mem or mem.status != BrandMembershipStatus.pending:
        raise ValueError("No pending request")

    mem.status = BrandMembershipStatus.active
    mem.joined_at = datetime.now(UTC)

    # Add global role mapping (brand_member)
    role_id_stmt = select(MasterRole.id).where(MasterRole.role_name == "brand_member")
    role_id = (await session.execute(role_id_stmt)).scalar_one_or_none()
    if role_id is None:  # changed code
        raise HTTPException(status_code=404, detail="Role not found for brand membership approval")
    session.add(UserRoleModel(user_id=user_id, role_id=role_id))

    # Get the organization ID to invalidate org-wide caches
    brand_query = select(Brand.organization_id).where(Brand.id == brand_id)
    org_id = (await session.execute(brand_query)).scalar_one_or_none()
    if org_id is None:
        raise HTTPException(status_code=404, detail="Brand not found")

    # Evict member-specific caches  
    await _evict_member_caches(redis_client, brand_id, user_id)
    
    # Invalidate pending request caches across the organization
    await invalidate_pending_requests_cache(redis_client, brand_id, org_id)


@with_trace_id
async def change_brand_member_role(
        *,
        session: AsyncSession,
        redis_client,
        brand_id: UUID,
        user_id: UUID,
        new_role: str,  # 'brand_owner' | 'brand_admin' | 'member'
) -> None:
    mem = await _lock_and_get_membership(session, brand_id, user_id)
    if not mem or mem.status != BrandMembershipStatus.active:
        raise ValueError("member not found / not active")

    mem.role = new_role
    await _evict_member_caches(redis_client, brand_id, user_id)


@with_trace_id
async def deactivate_brand_member(
        *,
        session: AsyncSession,
        redis_client,
        brand_id: UUID,
        user_id: UUID,
) -> None:
    """
    Deactivate or reject a brand membership.
    
    This function can be used to:
    1. Reject a pending membership request
    2. Remove an active member from a brand
    
    The function performs the following actions:
    1. Gets the membership record with a SELECT FOR UPDATE to prevent race conditions
    2. Updates the membership status to rejected and sets is_active to False
    3. Revokes all brand_* roles for the user in the RBAC system
    4. Invalidates all relevant caches including:
       - Individual member caches
       - Brand member lists
       - Pending requests lists for the organization
       - Aggregated pending requests view for organization
       
    Args:
        session: AsyncSession within an active transaction
        redis_client: Redis client instance
        brand_id: UUID of the brand
        user_id: UUID of the user whose membership is being deactivated
        
    Note: No error is raised if the membership doesn't exist
    """
    mem = await _lock_and_get_membership(session, brand_id, user_id)
    if not mem:
        return
    mem.status = BrandMembershipStatus.rejected
    mem.is_active = False
    mem.deleted_at = datetime.now(UTC)    # revoke brand_* roles
    await session.execute(
        delete(UserRoleModel).where(
            UserRoleModel.user_id == user_id,
            UserRoleModel.role_id.in_(
                select(MasterRole.id).where(MasterRole.role_name.like("brand_%"))
            ),
        )
    )
    
    # Get the organization ID to invalidate org-wide caches
    brand_query = select(Brand.organization_id).where(Brand.id == brand_id)
    org_id = (await session.execute(brand_query)).scalar_one()

    # Evict member-specific caches
    await _evict_member_caches(redis_client, brand_id, user_id)
    
    # Invalidate pending request caches across the organization
    await invalidate_pending_requests_cache(redis_client, brand_id, org_id)


@with_trace_id
async def soft_delete_brand(
        *,
        session: AsyncSession,
        redis_client,
        brand_id: UUID,
) -> None:
    now = datetime.now(UTC)
    await session.execute(
        update(Brand)
        .where(Brand.id == brand_id, Brand.is_active)
        .values(is_active=False, deleted_at=now, updated_at=now)
    )
    await session.execute(
        update(BrandMembership)
        .where(BrandMembership.brand_id == brand_id, BrandMembership.is_active)
        .values(is_active=False, deleted_at=now)
    )
    await redis_client.delete(
        RedisKeys.brand_key(brand_id),
        RedisKeys.brand_members_list_key(brand_id, "active"),
        RedisKeys.brand_members_list_key(brand_id, "all"),
    )


# ────────────────────────────────────────────────────────────────────────
# READ HELPERS (cache-aside)
# ────────────────────────────────────────────────────────────────────────
@with_trace_id
async def get_brand_by_id(
        *, db_conn, redis_client, brand_id: UUID, ttl: int = 3600
):
    key = RedisKeys.brand_key(brand_id)
    cached = await redis_client.get(key)
    if cached:
        return json.loads(cached)

    async with db_conn.get_db() as s:
        brand = await s.get(Brand, brand_id)
    if not brand:
        return None

    payload = _brand_payload(brand)
    await redis_client.setex(key, ttl,  json.dumps(payload))
    return payload


@with_trace_id
async def list_brand_members(
        *, db_conn, redis_client, brand_id: UUID, include_pending: bool = False, ttl: int = 900
) -> List[Dict]:
    mode = "all" if include_pending else "active"
    key = RedisKeys.brand_members_list_key(brand_id, mode)
    cached = await redis_client.get(key)
    if cached:
        return json.loads(cached)

    async with db_conn.get_db() as s:
        stmt = (
            select(BrandMembership, User)
            .join(User, User.id == BrandMembership.user_id)
            .where(BrandMembership.brand_id == brand_id)
        )
        if not include_pending:
            stmt = stmt.where(BrandMembership.status == BrandMembershipStatus.active)
        rows = (await s.execute(stmt)).all()

    payload = [
        {
            "user_id": str(u.id),
            "name": u.name,
            "email": u.email,
            "role": bm.role,
            "status": bm.status.value,
            "joined_at": bm.joined_at.isoformat() if bm.joined_at is not None else None,
        }
        for bm, u in rows
    ]
    await redis_client.set(key, json.dumps(payload), ex=ttl)
    return payload


@with_trace_id
async def get_user_brand_status(
        *, db_conn, redis_client, brand_id: UUID, user_id: UUID, ttl: int = 900
):
    key = RedisKeys.brand_member_status_key(brand_id, user_id)
    cached = await redis_client.get(key)
    if cached:
        return json.loads(cached)

    async with db_conn.get_db() as s:
        stmt = select(BrandMembership).where(
            BrandMembership.brand_id == brand_id, BrandMembership.user_id == user_id
        )
        bm = (await s.execute(stmt)).scalar_one_or_none()
    if not bm:
        return None

    payload = _mem_payload(bm)
    await redis_client.set(key, json.dumps(payload), ex=ttl)
    return payload


@with_trace_id
async def list_brands_for_org(
        *, db_conn, redis_client, org_id: UUID, ttl: int = 900
) -> List[Dict]:
    key = RedisKeys.brands_by_org_key(org_id)
    cached = await redis_client.get(key)
    if cached:
        return json.loads(cached)

    async with db_conn.get_db() as s:
        rows = (
            await s.execute(
                select(Brand).where(Brand.organization_id == org_id, Brand.is_active)
            )
        ).scalars()

    payload = [_brand_payload(b) for b in rows]
    await redis_client.set(key, json.dumps(payload), ex=ttl)
    return payload


@with_trace_id
async def list_pending_requests_for_brand(
        *, db_conn, redis_client, brand_id: UUID, ttl: int = 900
) -> List[Dict]:
    """
    Get all pending join requests for a brand with user details.
    Uses cache-aside pattern for performance.

    Args:
        db_conn: Database connection
        redis_client: Redis client
        brand_id: UUID of the brand
        ttl: Cache TTL in seconds (default 15 minutes)
        
    Returns:
        List[Dict]: List of pending requests with user details
    """
    key = RedisKeys.brand_pending_requests_key(brand_id)
    cached = await redis_client.get(key)
    if cached:
        return json.loads(cached)

    async with db_conn.get_db() as s:
        stmt = (
            select(BrandMembership, User)
            .join(User, User.id == BrandMembership.user_id)
            .where(
                BrandMembership.brand_id == brand_id,
                BrandMembership.status == BrandMembershipStatus.pending,
                BrandMembership.is_active == True
            )
        )
        rows = (await s.execute(stmt)).all()

    payload = [
        {
            "user_id": str(u.id),
            "name": u.name,
            "email": u.email,
            "requested_at": bm.created_at.isoformat() if bm.created_at is not None else None,
            "updated_at": bm.updated_at.isoformat() if bm.updated_at is not None else None,
        }
        for bm, u in rows
    ]
    await redis_client.setex(key, ttl, json.dumps(payload))
    return payload


@with_trace_id
async def list_all_pending_requests_for_user(
    *,
    db_conn,
    redis_client,
    user_id: UUID,
    org_id: UUID,
    ttl: int = 300
) -> Dict:
    """
    Get all pending brand join requests that a user can view based on their role.
    
    For organization owners, returns all pending requests across all brands in the organization.
    For brand owners/admins, returns only pending requests for brands they manage.
    
    Args:
        db_conn: Database connection
        redis_client: Redis client
        user_id: UUID of the user requesting the data
        org_id: UUID of the organization
        ttl: Cache TTL in seconds (default: 300 seconds = 5 minutes)
        
    Returns:
        Dict with aggregated pending requests data:
        {
            "total_pending_requests": int,
            "brands_with_requests": [
                {
                    "brand_id": str,
                    "brand_name": str,
                    "total_requests": int,
                    "pending_requests": [
                        {
                            "user_id": str,
                            "name": str,
                            "email": str,
                            "requested_at": str,
                            "updated_at": str
                        },
                        ...
                    ]
                },
                ...
            ]
        }
    """
    # Check cache first
    logger = get_logger()
    key = RedisKeys.user_pending_requests_key(user_id, org_id)
    cached = await redis_client.get(key)
    
    if cached:
        logger.info(f"Returning cached pending requests for user {user_id} in org {org_id}")
        return json.loads(cached)
    
    # Get user's organization membership to determine role
    org_membership = await get_user_organization_membership(
        db_conn=db_conn,
        redis_client=redis_client,
        user_id=user_id
    )
    
    if not org_membership or str(org_id) != org_membership.get("organization_id"):
        logger.warning(f"User {user_id} is not a member of organization {org_id}")
        return {
            "total_pending_requests": 0,
            "brands_with_requests": []
        }
    
    # Get brands based on user's role
    is_org_owner = org_membership.get("role") == OrgMemberRole.owner.value
    brands_to_check = []
    
    async with db_conn.get_db() as s:
        if is_org_owner:
            # Organization owners can see all brands in the org
            stmt = (
                select(Brand)
                .where(
                    Brand.organization_id == org_id,
                    Brand.is_active == True
                )
            )
            result = await s.execute(stmt)
            brands_to_check = result.scalars().all()
            logger.info(f"Found {len(brands_to_check)} brands for org owner in org {org_id}")
        else:
            # Get only brands where user is admin or owner
            stmt = (
                select(Brand)
                .join(
                    BrandMembership, 
                    (BrandMembership.brand_id == Brand.id) & 
                    (BrandMembership.user_id == user_id)
                )
                .where(
                    Brand.organization_id == org_id,
                    Brand.is_active == True,
                    BrandMembership.is_active == True,
                    BrandMembership.status == BrandMembershipStatus.active,
                    BrandMembership.role.in_(["brand_owner", "brand_admin"])
                )
            )
            result = await s.execute(stmt)
            brands_to_check = result.scalars().all()
            logger.info(f"Found {len(brands_to_check)} brands for brand admin/owner {user_id}")
    
    # Get pending requests for each brand
    brands_with_requests = []
    total_pending_requests = 0
    
    for brand in brands_to_check:
        brand_requests = await list_pending_requests_for_brand(
            db_conn=db_conn,
            redis_client=redis_client,
            brand_id=brand.id
        )
        
        if brand_requests:
            brand_data = {
                "brand_id": str(brand.id),
                "brand_name": brand.name,
                "total_requests": len(brand_requests),
                "pending_requests": brand_requests
            }
            brands_with_requests.append(brand_data)
            total_pending_requests += len(brand_requests)
    
    # Prepare the result
    result = {
        "total_pending_requests": total_pending_requests,
        "brands_with_requests": brands_with_requests
    }
      # Cache the result
    await redis_client.setex(key, ttl, json.dumps(result))
    
    return result


@with_trace_id
async def invalidate_pending_requests_cache(
    redis_client,
    brand_id: UUID,
    org_id: UUID
) -> None:
    """
    Invalidate caches for pending requests when brand membership statuses change.
    
    This is a critical function for maintaining cache consistency when memberships are
    approved or rejected. It ensures that users see up-to-date pending request data
    without having to wait for TTL expiration.
    
    When to call this function:
    - After approving a membership request
    - After rejecting a membership request
    - After any change to membership status that affects pending lists
    
    Cache keys invalidated:
    1. Brand-specific pending requests: 
       - CreatorVerse:brand:pending_requests:{brand_id}
    2. All user aggregated pending request views for the organization:
       - CreatorVerse:user:*:pending_requests:{org_id}
    
    Args:
        redis_client: Redis client instance
        brand_id: UUID of the brand affected
        org_id: UUID of the organization the brand belongs to
        
    Returns:
        None
    """
    logger = get_logger()
    
    # Delete the brand's pending requests cache
    key = RedisKeys.brand_pending_requests_key(brand_id)
    await redis_client.delete(key)
    
    # Delete all user pending request caches for the organization
    # Use pattern deletion for this to ensure all users' caches are invalidated
    pattern = f"CreatorVerse:user:*:pending_requests:{org_id}"
    deleted = await redis_client.delete_pattern(pattern)
    
    logger.info(
        f"Invalidated pending requests cache for brand {brand_id}",
        extra={"org_id": str(org_id), "keys_deleted": deleted}
    )


# ────────────────────────────────────────────────────────────────────────
# INTERNAL UTILS
# ────────────────────────────────────────────────────────────────────────
async def _lock_and_get_membership(session: AsyncSession, bid: UUID, uid: UUID):
    stmt = (
        select(BrandMembership)
        .where(BrandMembership.brand_id == bid, BrandMembership.user_id == uid)
        .with_for_update()
    )
    return (await session.execute(stmt)).scalar_one_or_none()


async def _cache_brand(redis_client, brand: Brand, ttl: int = 3600):
    await redis_client.setex(
        RedisKeys.brand_key(brand.id),
        ttl,
        json.dumps(_brand_payload(brand))
    )


async def _evict_member_caches(redis_client, brand_id: UUID, user_id: UUID):
    await redis_client.delete_keys(
        RedisKeys.brand_member_status_key(brand_id, user_id),
        RedisKeys.brand_members_list_key(brand_id, "all"),
        RedisKeys.brand_members_list_key(brand_id, "active"),
    )


async def _evict_brand_lists(redis_client, org_id: UUID):
    await redis_client.delete(RedisKeys.brands_by_org_key(org_id))


def _brand_payload(b: Brand) -> Dict:
    return {
        "id": str(b.id),
        "organization_id": str(b.organization_id),
        "name": b.name,
        "description": b.description,
        "logo_url": b.logo_url,
        "is_active": b.is_active,
    }


def _mem_payload(bm: BrandMembership) -> Dict:
    return {
        "brand_id": str(bm.brand_id),
        "user_id": str(bm.user_id),
        "role": bm.role,
        "status": bm.status.value,
        "joined_at": bm.joined_at.isoformat() if bm.joined_at is not None else None,
    }


# app/services/brand_service.py  (add to the bottom of the file)

@with_trace_id
async def list_brands_with_user_status(
        *,
        db_conn,
        redis_client, org_id: UUID | Any,
        user_id: UUID | Any,
        ttl: int = 1800,  # 30 minutes
):
    """
    Get all active brands for an organization, with total member counts
    and the relationship status of a specific user for each brand.
    
    Uses cache-aside pattern for performance.
    
    Args:
        db_conn: Database connection
        redis_client: Redis client
        org_id: UUID of the organization
        user_id: UUID of the user to check relationship status
        ttl: Cache TTL in seconds (default 30 minutes)
        
    Returns:
        List[Dict]: List of brand information dictionaries with user relationship status
    """
    # Check cache first
    cache_key = f"{RedisKeys.brands_by_org_key(org_id)}:with_stats:{user_id}"
    cached_data = await redis_client.get(cache_key)

    if cached_data:
        try:
            cached_data = json.loads(cached_data)
            if cached_data:
                # Deserialize and return cached data
                return cached_data
        except Exception:
            # If cache deserialization fails, continue to database query
            pass

    # If not in cache, query database
    async with db_conn.get_db() as session:
        # Subquery to count active members per brand
        count_subquery = (
            select(
                BrandMembership.brand_id,
                func.count(BrandMembership.user_id).label('member_count')
            )
            .where(BrandMembership.status == BrandMembershipStatus.active)
            .group_by(BrandMembership.brand_id)
            .subquery()
        )

        # Main query to get brands with membership info
        query = (
            select(
                Brand,
                func.coalesce(count_subquery.c.member_count, 0).label('total_members'),
                BrandMembership.status,
                BrandMembership.role,
            )
            .select_from(Brand)
            .outerjoin(count_subquery, Brand.id == count_subquery.c.brand_id)
            .outerjoin(
                BrandMembership,
                (Brand.id == BrandMembership.brand_id) &
                (BrandMembership.user_id == user_id)
            )
            .where(
                Brand.organization_id == org_id,
                Brand.is_active == True,
                Brand.deleted_at.is_(None)
            )
        )

        result = await session.execute(query)
        rows = result.all()

        # Transform into response format
        brand_list = []
        for row in rows:
            brand = row[0]  # The Brand object
            total_members = row[1]  # Integer count
            status = row[2]  # BrandMembershipStatus enum value or None
            role = row[3]  # String role or None

            # Format brand data
            brand_data = {
                "id": str(brand.id),
                "name": brand.name,
                "description": brand.description,
                "logo_url": brand.logo_url,
                "website_url": brand.website_url,
                "total_members": total_members,
                "is_active": brand.is_active,
                "created_at": brand.created_at.isoformat() if brand.created_at else None,
                "updated_at": brand.updated_at.isoformat() if brand.updated_at else None,
                "user_relationship": {
                    "status": status.value if status else "none",
                    "role": role if role else None
                }
            }
            brand_list.append(brand_data)

        # Cache the result
        await redis_client.setex(
            cache_key,
            ttl,
            json.dumps(brand_list)
        )

        return brand_list
