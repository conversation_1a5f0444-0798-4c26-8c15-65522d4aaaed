"""
OAuth utility functions for CreatorVerse Backend.
Handles OAuth-specific operations with proper transaction management and error handling.
"""
from datetime import UTC, datetime, timedelta
from typing import Dict, Optional, Tuple, Any, List
from uuid import UUID

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.enums import User<PERSON>tatus, SourceRegister
from app.core.logger import get_logger
from app.core_helper.async_logger import with_trace_id
from app.models.user_models import (
    UserAuthMethod,
    UserRoleModel,
    OAuthAccount,
    SocialProfile,
    OrgMemberRole,
    MasterRole,
    MasterAuthMethod,
    User
)
from app.schemas.auth import AuthTokenResponse, AuthTokenBrandResponse
from app.schemas.oauth_schemas import OAuthProviderTokens, OAuthProviderProfile
from app.services.session_service import create_oauth_user_session
from app.services.user_service import get_user_by_email_cache_aside, create_user_cache_aside
from app.utilities.brand_organization_utils import ensure_org_and_membership
from app.utilities.validation_functions import extract_domain_from_email

logger = get_logger()

# List of consumer domains not allowed for brand users
BLOCKED_CONSUMER_DOMAINS = [
    'gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com',
    'icloud.com', 'aol.com', 'protonmail.com', 'mail.com'
]


@with_trace_id
async def create_oauth_user_with_proper_source(
        db_conn,
        redis_client,
        user_data: Dict[str, Any],
        provider: str
) -> Tuple[bool, Optional[Dict[str, Any]], str]:
    """
    Create a new user specifically for OAuth flows with proper register_source.
    This function ensures the register_source field is set correctly based on provider.
    
    Args:
        db_conn: Database connection
        redis_client: Redis client
        user_data: User data to create
        provider: OAuth provider name (google, facebook, instagram)
        
    Returns:
        Tuple of (success, user_dict, message)
    """
    email = user_data.get("email")
    if not email:
        return False, None, "Email is required"

    # Map provider to register_source
    provider_mapping = {
        "google": SourceRegister.GOOGLE_OAUTH.value,  # 5
        "facebook": SourceRegister.FACEBOOK_OAUTH.value,  # 4  
        "instagram": SourceRegister.INSTAGRAM_OAUTH.value  # 3
    }
    
    register_source = provider_mapping.get(provider.lower())
    if not register_source:
        return False, None, f"Unsupported OAuth provider: {provider}"

    # Add register_source to user_data
    user_data["register_source"] = register_source
    
    logger.info("Creating OAuth user with proper register_source", extra={
        "email": email,
        "provider": provider,
        "register_source": register_source
    })

    # Check if email already exists using bloom filter
    try:
        from app.utilities.bloom_filter import CreatorBloomFilter
        from app.core.redis_keys import RedisKeys
        
        bloom_filter = CreatorBloomFilter(
            redis_client,
            filter_name=RedisKeys.get_email_bloom_filter_key(),
        )

        if await bloom_filter.exists(email):
            # Double-check in the database because bloom filter may have false positives
            existing_user = await get_user_by_email_cache_aside(db_conn, redis_client, email)
            if existing_user:
                return False, None, "User with this email already exists"
    except Exception as bloom_err:
        logger.warning("Error checking bloom filter", extra={"error": str(bloom_err)})
        # Continue with database check if bloom filter fails

    # Create user in database
    try:
        async with db_conn.get_db() as session:
            async with session.begin():
                # Create the user with proper register_source
                new_user = User(
                    email=email,
                    name=user_data.get("name", ""),
                    status=user_data.get("status", "active"),
                    is_email_verified=user_data.get("is_email_verified", False),
                    is_phone_verified=user_data.get("is_phone_verified", False),
                    is_active=user_data.get("is_active", True),
                    phone_number=user_data.get("phone_number", None),
                    country_code=user_data.get("country_code", None),
                    profile_image=user_data.get("profile_image", None),
                    register_source=register_source,  # ✅ FIXED: Set proper register_source
                    created_at=datetime.now(UTC),
                    updated_at=datetime.now(UTC)
                )

                session.add(new_user)
                await session.flush()  # Get the ID without committing yet

                # Convert to Dictionary (detach from session)
                user_dict = {
                    "id": str(new_user.id),
                    "email": new_user.email,
                    "name": new_user.name or "",
                    "status": new_user.status,
                    "is_active": str(new_user.is_active),
                    "is_email_verified": str(new_user.is_email_verified),
                    "phone_number": new_user.phone_number or "",
                    "profile_image": new_user.profile_image or "",
                    "register_source": str(new_user.register_source),
                    "created_at": str(new_user.created_at),
                    "updated_at": str(new_user.updated_at)
                }

                # Commit transaction first
                await session.commit()

                # Add to bloom filter after successful commit
                try:
                    await bloom_filter.add(email)  # ✅ FIXED: Proper bloom filter update
                    logger.info("User added to bloom filter", extra={"email": email})
                except Exception as bloom_err:
                    logger.warning("Failed to update bloom filter", extra={
                        "email": email,
                        "error": str(bloom_err)
                    })

                # Update Cache after successful commit
                try:
                    from app.core.redis_keys import RedisKeys, RedisConfig
                    # Cache by email
                    email_cache_key = RedisKeys.user_by_email(email)
                    id_cache_key = f"CreatorVerse:user:id:{str(new_user.id)}"

                    pipe = redis_client.pipeline()
                    pipe.hset(email_cache_key, mapping=user_dict)
                    pipe.expire(email_cache_key, RedisConfig.USER_TTL)

                    pipe.hset(id_cache_key, mapping=user_dict)
                    pipe.expire(id_cache_key, RedisConfig.USER_TTL)

                    await pipe.execute()
                    logger.debug("OAuth user cached successfully", extra={"email": email})
                except Exception as cache_err:
                    logger.warning("Failed to cache OAuth user", extra={
                        "email": email,
                        "error": str(cache_err)
                    })

                logger.info("OAuth user created successfully", extra={
                    "user_id": str(new_user.id),
                    "provider": provider,
                    "register_source": register_source
                })
                return True, user_dict, "OAuth user created successfully"

    except Exception as e:
        logger.error("Error creating OAuth user", extra={
            "email": email,
            "provider": provider,
            "error": str(e)
        })
        return False, None, f"Error creating OAuth user: {str(e)}"


@with_trace_id
async def ensure_bloom_filter_updated(
        redis_client,
        email: str
) -> bool:
    """
    Ensure user email is added to bloom filter after successful registration.
    This function should be called after any successful user creation.
    
    Args:
        redis_client: Redis client
        email: User email address
        
    Returns:
        bool: True if successfully added, False otherwise
    """
    try:
        from app.utilities.bloom_filter import CreatorBloomFilter
        from app.core.redis_keys import RedisKeys
        
        bloom_filter = CreatorBloomFilter(
            redis_client,
            filter_name=RedisKeys.get_email_bloom_filter_key(),
        )
        
        # Check if already exists
        if await bloom_filter.exists(email):
            logger.debug("Email already in bloom filter", extra={"email": email})
            return True
            
        # Add to bloom filter
        await bloom_filter.add(email)
        logger.info("Email added to bloom filter", extra={"email": email})
        return True
        
    except Exception as e:
        logger.error("Failed to update bloom filter", extra={
            "email": email,
            "error": str(e)
        })
        return False


@with_trace_id
async def handle_existing_user_oauth_registration(
        db_conn,
        redis_client,
        user: User,
        provider: str,
        provider_profile: OAuthProviderProfile
) -> User:
    """
    Handle OAuth registration for existing users.
    Updates user data and ensures proper register_source.
    
    Args:
        db_conn: Database connection
        redis_client: Redis client
        user: Existing user entity
        provider: OAuth provider name
        provider_profile: OAuth provider profile
        
    Returns:
        Updated user entity
    """
    # Map provider to register_source
    provider_mapping = {
        "google": SourceRegister.GOOGLE_OAUTH.value,  # 5
        "facebook": SourceRegister.FACEBOOK_OAUTH.value,  # 4  
        "instagram": SourceRegister.INSTAGRAM_OAUTH.value  # 3
    }
    
    expected_register_source = provider_mapping.get(provider.lower())
    
    try:
        async with db_conn.get_db() as session:
            async with session.begin():
                # Fetch fresh user instance in this session
                user_query = select(User).where(User.id == user.id)
                user_result = await session.execute(user_query)
                fresh_user = user_result.scalar_one_or_none()
                
                if not fresh_user:
                    raise ValueError(f"User not found: {user.id}")

                # Track what changes are made
                changes_made = []
                
                # Update user fields from OAuth data
                if provider_profile.name and provider_profile.name != fresh_user.name:
                    fresh_user.name = provider_profile.name
                    changes_made.append("name")
                    
                if provider_profile.picture and provider_profile.picture != fresh_user.profile_image:
                    fresh_user.profile_image = provider_profile.picture
                    changes_made.append("profile_image")
                    
                if not fresh_user.is_email_verified and provider_profile.is_email_verified:
                    fresh_user.is_email_verified = provider_profile.is_email_verified
                    changes_made.append("email_verified")
                    
                if fresh_user.status != "active":
                    fresh_user.status = UserStatus.get_status_name(UserStatus.ACTIVE.value)
                    changes_made.append("status")
                    
                if not fresh_user.is_active:
                    fresh_user.is_active = True
                    changes_made.append("is_active")
                
                # Update register_source if needed
                if expected_register_source and fresh_user.register_source != expected_register_source:
                    logger.info("Updating register_source for existing user", extra={
                        "user_id": str(fresh_user.id),
                        "old_register_source": fresh_user.register_source,
                        "new_register_source": expected_register_source,
                        "provider": provider
                    })
                    fresh_user.register_source = expected_register_source
                    changes_made.append("register_source")
                
                # Always update timestamp if any changes were made
                if changes_made:
                    fresh_user.updated_at = datetime.now(UTC)
                    changes_made.append("updated_at")

                await session.flush()
                await session.commit()
                
                logger.info("Updated existing user for OAuth", extra={
                    "user_id": str(fresh_user.id),
                    "provider": provider,
                    "changes_made": changes_made
                })
                
                # Ensure bloom filter is updated
                await ensure_bloom_filter_updated(redis_client, fresh_user.email)
                
                # Update cache
                try:
                    from app.core.redis_keys import RedisKeys, RedisConfig
                    user_dict = {
                        "id": str(fresh_user.id),
                        "email": fresh_user.email,
                        "name": fresh_user.name or "",
                        "status": fresh_user.status,
                        "is_active": str(fresh_user.is_active),
                        "is_email_verified": str(fresh_user.is_email_verified),
                        "phone_number": fresh_user.phone_number or "",
                        "profile_image": fresh_user.profile_image or "",
                        "register_source": str(fresh_user.register_source),
                        "created_at": str(fresh_user.created_at),
                        "updated_at": str(fresh_user.updated_at)
                    }
                    
                    email_cache_key = RedisKeys.user_by_email(fresh_user.email)
                    id_cache_key = f"CreatorVerse:user:id:{str(fresh_user.id)}"
                    
                    pipe = redis_client.pipeline()
                    pipe.delete(email_cache_key)
                    pipe.delete(id_cache_key)
                    
                    pipe.hset(email_cache_key, mapping=user_dict)
                    pipe.expire(email_cache_key, RedisConfig.USER_TTL)
                    
                    pipe.hset(id_cache_key, mapping=user_dict)
                    pipe.expire(id_cache_key, RedisConfig.USER_TTL)
                    
                    await pipe.execute()
                    
                except Exception as cache_err:
                    logger.warning("Failed to update user cache", extra={
                        "user_id": str(fresh_user.id),
                        "error": str(cache_err)
                    })
                
                return fresh_user
                
    except Exception as e:
        logger.error("Error updating user for OAuth", extra={
            "user_id": str(user.id),
            "provider": provider,
            "error": str(e)
        })
        raise


@with_trace_id
async def update_existing_user_for_oauth(
        db_conn,
        redis_client,
        user: User,
        provider_profile: OAuthProviderProfile,
        provider: str
) -> User:
    """
    Update existing user for OAuth flow with proper register_source if needed.
    
    Args:
        db_conn: Database connection
        redis_client: Redis client
        user: Existing user entity
        provider_profile: OAuth provider profile
        provider: OAuth provider name
        
    Returns:
        Updated user entity
    """
    try:
        async with db_conn.get_db() as session:
            async with session.begin():
                # Fetch fresh user instance in this session
                user_query = select(User).where(User.id == user.id)
                user_result = await session.execute(user_query)
                fresh_user = user_result.scalar_one_or_none()
                
                if not fresh_user:
                    raise ValueError(f"User not found: {user.id}")

                # Update user fields from OAuth data
                if provider_profile.name:
                    fresh_user.name = provider_profile.name
                if provider_profile.picture:
                    fresh_user.profile_image = provider_profile.picture
                    
                fresh_user.is_email_verified = provider_profile.is_email_verified
                fresh_user.status = UserStatus.get_status_name(UserStatus.ACTIVE.value)
                fresh_user.is_active = True
                fresh_user.updated_at = datetime.now(UTC)
                
                # Update register_source if it's not set or is different
                provider_mapping = {
                    "google": SourceRegister.GOOGLE_OAUTH.value,
                    "facebook": SourceRegister.FACEBOOK_OAUTH.value,  
                    "instagram": SourceRegister.INSTAGRAM_OAUTH.value
                }
                
                expected_register_source = provider_mapping.get(provider.lower())
                if expected_register_source and fresh_user.register_source != expected_register_source:
                    logger.info("Updating register_source for existing user", extra={
                        "user_id": str(fresh_user.id),
                        "old_register_source": fresh_user.register_source,
                        "new_register_source": expected_register_source,
                        "provider": provider
                    })
                    fresh_user.register_source = expected_register_source

                await session.flush()
                await session.commit()
                
                # Update cache
                try:
                    from app.core.redis_keys import RedisKeys, RedisConfig
                    user_dict = {
                        "id": str(fresh_user.id),
                        "email": fresh_user.email,
                        "name": fresh_user.name or "",
                        "status": fresh_user.status,
                        "is_active": str(fresh_user.is_active),
                        "is_email_verified": str(fresh_user.is_email_verified),
                        "phone_number": fresh_user.phone_number or "",
                        "profile_image": fresh_user.profile_image or "",
                        "register_source": str(fresh_user.register_source),
                        "created_at": str(fresh_user.created_at),
                        "updated_at": str(fresh_user.updated_at)
                    }
                    
                    email_cache_key = RedisKeys.user_by_email(fresh_user.email)
                    id_cache_key = f"CreatorVerse:user:id:{str(fresh_user.id)}"
                    
                    pipe = redis_client.pipeline()
                    pipe.delete(email_cache_key)
                    pipe.delete(id_cache_key)
                    
                    pipe.hset(email_cache_key, mapping=user_dict)
                    pipe.expire(email_cache_key, RedisConfig.USER_TTL)
                    
                    pipe.hset(id_cache_key, mapping=user_dict)
                    pipe.expire(id_cache_key, RedisConfig.USER_TTL)
                    
                    await pipe.execute()
                    
                except Exception as cache_err:
                    logger.warning("Failed to update user cache", extra={
                        "user_id": str(fresh_user.id),
                        "error": str(cache_err)
                    })
                
                logger.info("Updated existing user for OAuth", extra={
                    "user_id": str(fresh_user.id),
                    "provider": provider
                })
                
                return fresh_user
                
    except Exception as e:
        logger.error("Error updating user for OAuth", extra={
            "user_id": str(user.id),
            "provider": provider,
            "error": str(e)
        })
        raise


@with_trace_id
async def get_user_with_database_validation(
        db_conn,
        redis_client,
        email: str
) -> Optional[User]:
    """
    Get user with database validation for critical operations like OAuth.
    This function ensures data consistency by validating cache against database.
    
    Args:
        db_conn: Database connection
        redis_client: Redis client
        email: User email address
        
    Returns:
        User entity if found and valid, None otherwise
    """
    # First, try to get user directly from database (most reliable)
    try:
        async with db_conn.get_db() as session:
            user_query = select(User).where(User.email == email)
            user_result = await session.execute(user_query)
            db_user = user_result.scalar_one_or_none()
            
            if db_user:
                logger.debug("User found in database", extra={
                    "email": email,
                    "user_id": str(db_user.id)
                })
                return db_user
            
            # If not found in database, check if stale cache exists
            try:
                cached_user = await get_user_by_email_cache_aside(db_conn, redis_client, email, False)
                if cached_user:
                    logger.warning("Stale cache detected - user in cache but not in database", extra={
                        "email": email,
                        "cached_user_id": cached_user.get("id"),
                        "action": "invalidating_cache"
                    })
                    
                    # Invalidate stale cache
                    from app.core.redis_keys import RedisKeys
                    cache_key = RedisKeys.user_by_email(email)
                    await redis_client.delete(cache_key)
                    
                    # Also try to clean by ID if available
                    if cached_user.get("id"):
                        id_cache_key = f"CreatorVerse:user:id:{cached_user['id']}"
                        await redis_client.delete(id_cache_key)
                    
                    logger.info("Stale cache invalidated", extra={"email": email})
            except Exception as cache_err:
                logger.error("Error checking/invalidating cache", extra={
                    "email": email,
                    "error": str(cache_err)
                })
            
            return None
            
    except Exception as e:
        logger.error("Database error during user validation", extra={
            "email": email,
            "error": str(e)
        })
        return None


@with_trace_id
async def get_auth_method_id_by_name_with_session(
        session: AsyncSession,
        redis_client,
        method_name: str
) -> Optional[str]:
    """
    Get authentication method ID by name using an existing session.
    This is a session-aware version of get_auth_method_id_by_name_cache_aside.
    
    Args:
        session: Database session
        redis_client: Redis client
        method_name: Auth method name (e.g., "email", "mobile", "oauth_google")
        
    Returns:
        Auth method ID if found, None otherwise
    """
    from app.core.redis_keys import RedisKeys, RedisConfig
    
    # Try to get from cache first
    cache_key = RedisKeys.auth_method_to_id(method_name)
    
    try:
        cached_id = await redis_client.get(cache_key)
        if cached_id:
            logger.debug("Auth method ID cache hit", extra={"name": method_name, "id": cached_id})
            return cached_id
    except Exception as e:
        logger.error("Redis error when retrieving auth method ID", extra={"error": str(e)})
    
    # Fetch from database using existing session
    try:
        query = select(MasterAuthMethod.id).where(MasterAuthMethod.method_key == method_name)
        result = await session.execute(query)
        auth_method_id = result.scalar_one_or_none()
        
        if auth_method_id:
            # Cache the result
            try:
                str_auth_id = str(auth_method_id)
                await redis_client.set(cache_key, str_auth_id)
                await redis_client.expire(cache_key, RedisConfig.RBAC_TTL)
                logger.debug("Auth method ID cached", extra={"name": method_name, "id": str_auth_id})
                return str_auth_id
            except Exception as cache_err:
                logger.warning("Failed to cache auth method ID", extra={"error": str(cache_err)})
                return str(auth_method_id)
        
        return None
    except Exception as db_err:
        logger.error("Error retrieving auth method ID from database", extra={"error": str(db_err)})
        return None


@with_trace_id
async def upsert_oauth_account_safe(
        session: AsyncSession,
        user_id: UUID,
        provider: str,
        provider_id: str,
        tokens: OAuthProviderTokens
) -> OAuthAccount:
    """
    Create or update OAuth account for user with proper error handling.
    
    Args:
        session: Database session
        user_id: User ID
        provider: OAuth provider name
        provider_id: Provider's unique identifier for the user
        tokens: OAuth provider tokens
        
    Returns:
        OAuthAccount: The created or updated account
    """
    # Find existing account
    stmt = select(OAuthAccount).where(
        OAuthAccount.user_id == user_id,
        OAuthAccount.provider == provider
    )
    result = await session.execute(stmt)
    oauth_account = result.scalar_one_or_none()

    now = datetime.now(UTC)

    if not oauth_account:
        # Create new OAuth account
        oauth_account = OAuthAccount(
            user_id=user_id,
            provider=provider,
            provider_user_id=provider_id,
            access_token=tokens.access_token,
            refresh_token=tokens.refresh_token,
            expires_at=now + timedelta(seconds=tokens.expires_in) if tokens.expires_in else None,
            scope=getattr(tokens, 'scope', None),  # Safe attribute access
            created_at=now,
            updated_at=now
        )
        session.add(oauth_account)
        logger.info("Created new OAuth account", extra={
            "user_id": str(user_id),
            "provider": provider,
            "provider_user_id": provider_id
        })
    else:
        # Update existing account
        oauth_account.access_token = tokens.access_token
        oauth_account.refresh_token = tokens.refresh_token or oauth_account.refresh_token
        oauth_account.expires_at = now + timedelta(seconds=tokens.expires_in) if tokens.expires_in else oauth_account.expires_at
        oauth_account.scope = getattr(tokens, 'scope', None) or oauth_account.scope  # Safe attribute access
        oauth_account.updated_at = now
        logger.info("Updated existing OAuth account", extra={
            "user_id": str(user_id),
            "provider": provider,
            "provider_user_id": provider_id
        })

    return oauth_account


@with_trace_id
async def upsert_user_auth_method_safe(
        session: AsyncSession,
        redis_client,
        user_id: UUID,
        provider: str
) -> UserAuthMethod:
    """
    Create or update user authentication method with proper session handling.

    Args:
        session: Database session
        redis_client: Redis client
        user_id: User ID
        provider: Authentication provider name (e.g., 'google', 'facebook')

    Returns:
        UserAuthMethod: The created or updated auth method
    """
    now = datetime.now(UTC)

    # Map provider to SourceRegister enum
    provider = provider.lower()
    if provider == "google":
        method_enum = SourceRegister.GOOGLE_OAUTH
    elif provider == "facebook":
        method_enum = SourceRegister.FACEBOOK_OAUTH
    elif provider == "instagram":
        method_enum = SourceRegister.INSTAGRAM_OAUTH
    else:
        raise ValueError(f"Unsupported OAuth provider: {provider}")

    # Get the method key and ID using session-aware function
    method_key = SourceRegister.get_method_key(method_enum.value)
    auth_method_id = await get_auth_method_id_by_name_with_session(
        session=session,  # Use session instead of db_conn
        redis_client=redis_client,
        method_name=method_key
    )

    if not auth_method_id:
        raise ValueError(f"Auth method not found: {method_key}")

    # Check if already exists
    stmt = select(UserAuthMethod).where(
        UserAuthMethod.user_id == user_id,
        UserAuthMethod.auth_method_id == auth_method_id
    )
    result = await session.execute(stmt)
    auth_method = result.scalar_one_or_none()

    if not auth_method:
        # Insert new method
        auth_method = UserAuthMethod(
            user_id=user_id,
            auth_method_id=auth_method_id,
            enabled_at=now,
            is_enabled=True
        )
        session.add(auth_method)
        logger.info("Created new user auth method", extra={
            "user_id": str(user_id),
            "provider": provider,
            "method_key": method_key
        })
    else:
        # Ensure it's enabled
        auth_method.is_enabled = True
        auth_method.enabled_at = now
        logger.info("Updated existing user auth method", extra={
            "user_id": str(user_id),
            "provider": provider,
            "method_key": method_key
        })

    await session.flush()
    return auth_method


@with_trace_id
async def create_or_update_youtube_profile_safe(
        session: AsyncSession,
        user_id: UUID,
        channel_data: Dict[str, Any]
) -> SocialProfile:
    """
    Create or update user's YouTube profile with proper error handling.
    
    Args:
        session: Database session
        user_id: User ID
        channel_data: YouTube channel data
        
    Returns:
        SocialProfile: The created or updated profile
    """
    channel_id = channel_data.get("id", "")
    snippet = channel_data.get("snippet", {})

    # First, we need to get the OAuth account to link the social profile
    oauth_stmt = select(OAuthAccount).where(
        OAuthAccount.user_id == user_id,
        OAuthAccount.provider == "google"
    )
    oauth_result = await session.execute(oauth_stmt)
    oauth_account = oauth_result.scalar_one_or_none()
    
    if not oauth_account:
        raise ValueError(f"Google OAuth account not found for user {user_id}")

    # Find existing profile
    stmt = select(SocialProfile).where(
        SocialProfile.oauth_account_id == oauth_account.id,
        SocialProfile.service == "youtube"
    )
    result = await session.execute(stmt)
    profile = result.scalar_one_or_none()

    now = datetime.now(UTC)

    if not profile:
        # Create new profile
        profile = SocialProfile(
            oauth_account_id=oauth_account.id,
            service="youtube",
            external_id=channel_id,
            username=snippet.get("title", ""),
            display_name=snippet.get("title", ""),
            avatar_url=snippet.get("thumbnails", {}).get("default", {}).get("url", ""),
            follower_count=0,  # Would need additional API call for subscriber count
            post_count=0,  # Would need additional API call for video count
            raw_json={
                "channel_data": channel_data,
                "description": snippet.get("description", ""),
                "country": snippet.get("country", ""),
                "custom_url": snippet.get("customUrl", "")
            },
            fetched_at=now
        )
        session.add(profile)
        logger.info("Created new YouTube profile", extra={
            "user_id": str(user_id),
            "channel_id": channel_id,
            "channel_title": snippet.get("title", "")
        })
    else:
        # Update existing profile
        profile.username = snippet.get("title", profile.username)
        profile.display_name = snippet.get("title", profile.display_name)
        profile.avatar_url = snippet.get("thumbnails", {}).get("default", {}).get("url", profile.avatar_url)
        profile.raw_json = {
            **(profile.raw_json or {}),
            "channel_data": channel_data,
            "description": snippet.get("description", ""),
            "country": snippet.get("country", ""),
            "custom_url": snippet.get("customUrl", "")
        }
        profile.fetched_at = now
        logger.info("Updated existing YouTube profile", extra={
            "user_id": str(user_id),
            "channel_id": channel_id,
            "channel_title": snippet.get("title", "")
        })

    await session.flush()
    return profile


@with_trace_id
async def handle_influencer_oauth_flow(
        db_conn,
        redis_client,
        provider_tokens: OAuthProviderTokens,
        provider_profile: OAuthProviderProfile,
        role_uuid: str,
        provider: str,
        rbac_service = None
) -> Tuple[AuthTokenResponse, Optional[str], bool]:
    """
    Handle the complete OAuth flow for influencers with proper transaction management.
    
    Args:
        db_conn: Database connection
        redis_client: Redis client
        provider_tokens: OAuth provider tokens
        provider_profile: User profile from provider
        role_uuid: Role UUID (should be influencer role)
        provider: OAuth provider name
        
    Returns:
        Tuple[AuthTokenResponse, Optional[str], bool]:
            - Auth token response
            - Redirection URL (if needed)
            - Is new user flag
    """
    email = provider_profile.email
    if not email:
        raise ValueError("Missing email from provider")

    needs_redirect = None
    is_new_user = False

    # Step 1: Use database validation to check if user exists
    existing_user = await get_user_with_database_validation(db_conn, redis_client, email)
    
    if not existing_user:
        # Step 2: Create new user with proper register_source
        user_data = {
            "email": email,
            "name": f"{provider_profile.given_name or ''} {provider_profile.family_name or ''}".strip() or provider_profile.name or "",
            "profile_image": provider_profile.picture,
            "is_email_verified": provider_profile.is_email_verified,
            "is_phone_verified": False,
            "is_active": True,
            "status": UserStatus.get_status_name(UserStatus.ACTIVE.value),
            "created_at": datetime.now(UTC),
            "updated_at": datetime.now(UTC)
        }
        
        success, user_dict, message = await create_oauth_user_with_proper_source(
            db_conn=db_conn,
            redis_client=redis_client,
            user_data=user_data,
            provider=provider  # ✅ FIXED: Pass provider for proper register_source
        )

        if not success:
            raise ValueError(f"Failed to create user: {message}")
        
        is_new_user = True
        user_id = UUID(user_dict["id"])
        logger.info("Created new influencer user with proper register_source", extra={
            "email": email, 
            "user_id": str(user_id),
            "register_source": user_dict.get("register_source"),
            "provider": provider
        })
        
        # Ensure bloom filter is updated for new user
        await ensure_bloom_filter_updated(redis_client, email)
        
    else:
        # User exists - update with OAuth data and proper register_source
        existing_user = await handle_existing_user_oauth_registration(
            db_conn=db_conn,
            redis_client=redis_client,
            user=existing_user,
            provider=provider,
            provider_profile=provider_profile
        )
        user_id = existing_user.id
        logger.info("Updated existing user for OAuth", extra={
            "email": email, 
            "user_id": str(user_id),
            "provider": provider
        })

    # Step 3: Handle all OAuth operations in a single transaction
    try:
        async with db_conn.get_db() as session:
            async with session.begin():
                # Fetch the user entity for session operations
                user_query = select(User).where(User.id == user_id)
                user_result = await session.execute(user_query)
                user = user_result.scalar_one_or_none()
                
                if not user:
                    raise ValueError(f"User not found: {user_id}")

                # Update user profile from OAuth data (final update)
                if provider_profile.name:
                    user.name = provider_profile.name
                if provider_profile.picture:
                    user.profile_image = provider_profile.picture
                user.is_email_verified = provider_profile.is_email_verified
                user.status = UserStatus.get_status_name(UserStatus.ACTIVE.value)
                user.is_active = True
                user.updated_at = datetime.now(UTC)

                # Ensure user has the influencer role
                stmt = select(UserRoleModel).where(
                    UserRoleModel.user_id == user_id,
                    UserRoleModel.role_id == role_uuid
                )
                result = await session.execute(stmt)
                user_role = result.scalar_one_or_none()

                if not user_role:
                    # Assign role
                    user_role = UserRoleModel(
                        user_id=user_id,
                        role_id=role_uuid,
                        assigned_at=datetime.now(UTC)
                    )
                    session.add(user_role)
                    logger.info("Assigned influencer role", extra={"user_id": str(user_id), "role_uuid": role_uuid})
                else:
                    logger.info("User already has influencer role", extra={"user_id": str(user_id), "role_uuid": role_uuid})

                # Create/update OAuth account
                oauth_account = await upsert_oauth_account_safe(
                    session=session,
                    user_id=user_id,
                    provider=provider,
                    provider_id=provider_profile.provider_id,
                    tokens=provider_tokens
                )

                # Create/update user auth method
                auth_method = await upsert_user_auth_method_safe(
                    session=session,
                    redis_client=redis_client,
                    user_id=user_id,
                    provider=provider
                )

                # Handle provider-specific operations
                if provider == "google":
                    # Get YouTube channels
                    from app.oauth.oauth_handlers import GoogleOAuthHandler
                    handler = GoogleOAuthHandler()
                    
                    try:
                        channels = await handler.get_youtube_channels(provider_tokens)
                        
                        if len(channels) == 1:
                            # Single channel - create profile immediately
                            await create_or_update_youtube_profile_safe(
                                session=session,
                                user_id=user_id,
                                channel_data=channels[0]
                            )
                            logger.info("Created YouTube profile for single channel", extra={
                                "user_id": str(user_id),
                                "channel_count": 1
                            })
                        elif len(channels) > 1:
                            # Multiple channels - set flag for selection
                            if not user.metadata_json:
                                user.metadata_json = {}
                            user.metadata_json["needs_youtube_channel_selection"] = True
                            user.metadata_json["youtube_channels"] = [
                                {
                                    "id": c.get("id", ""),
                                    "title": c.get("snippet", {}).get("title", ""),
                                    "thumbnail": c.get("snippet", {}).get("thumbnails", {}).get("default", {}).get("url", "")
                                }
                                for c in channels
                            ]
                            needs_redirect = f"/youtube/select?user_id={user_id}"
                            logger.info("Multiple YouTube channels found", extra={
                                "user_id": str(user_id),
                                "channel_count": len(channels)
                            })
                        else:
                            logger.warning("No YouTube channels found", extra={"user_id": str(user_id)})
                    
                    except Exception as youtube_err:
                        logger.warning("Failed to fetch YouTube channels", extra={
                            "user_id": str(user_id),
                            "error": str(youtube_err)
                        })
                        # Continue without YouTube data

                elif provider in ["facebook", "instagram"]:
                    # Handle Facebook/Instagram specific operations
                    logger.info("OAuth setup complete for social provider", extra={
                        "user_id": str(user_id),
                        "provider": provider
                    })

                # Commit all changes
                await session.commit()
                logger.info("OAuth transaction completed successfully", extra={
                    "user_id": str(user_id),
                    "provider": provider,
                    "is_new_user": is_new_user
                })

    except Exception as transaction_err:
        logger.error("OAuth transaction failed", extra={
            "user_id": str(user_id),
            "provider": provider,
            "error": str(transaction_err)
        })
        raise ValueError(f"OAuth setup failed: {str(transaction_err)}")

    # Step 4: Create session and tokens (outside transaction)
    try:
        # For session creation, get a fresh user instance
        if is_new_user:
            # For new users, get from cache (just created)
            fresh_user = await get_user_by_email_cache_aside(db_conn, redis_client, email, True)
        else:
            # For existing users, use database validation to ensure fresh data
            fresh_user = await get_user_with_database_validation(db_conn, redis_client, email)
        
        if not fresh_user:
            raise ValueError("Could not fetch user for session creation")
        
        session_data = await create_oauth_user_session(db_conn, fresh_user, redis_client, None, rbac_service)
        
        response = AuthTokenResponse(
            access_token=session_data["access_token"],
            refresh_token=session_data["refresh_token"],
            token_type=session_data["token_type"],
            expires_in=session_data["expires_in"]
        )
        
        logger.info("Influencer OAuth flow completed successfully", extra={
            "user_id": str(user_id),
            "provider": provider,
            "is_new_user": is_new_user,
            "needs_redirect": needs_redirect is not None
        })
        
        return response, needs_redirect, is_new_user
        
    except Exception as session_err:
        logger.error("Session creation failed after OAuth setup", extra={
            "user_id": str(user_id),
            "error": str(session_err)
        })
        raise ValueError(f"Session creation failed: {str(session_err)}")
@with_trace_id
async def handle_brand_oauth_flow(
        db_conn,
        redis_client,
        provider_tokens: OAuthProviderTokens,
        provider_profile: OAuthProviderProfile,
        role_uuid: str,
        provider: str,
        rbac_service = None
) -> Tuple[AuthTokenBrandResponse, Optional[str], bool]:
    """
    Handle the complete OAuth flow for brand users with proper transaction management.
    This function mirrors the brand OTP registration flow to ensure consistency.
    
    Args:
        db_conn: Database connection
        redis_client: Redis client
        provider_tokens: OAuth provider tokens
        provider_profile: User profile from provider
        role_uuid: Role UUID (should be brand role)
        provider: OAuth provider name
        
    Returns:
        Tuple[AuthTokenBrandResponse, Optional[str], bool]:
            - Auth token response with organization data
            - Redirection URL (if needed)
            - Is new user flag
    """
    email = provider_profile.email
    if not email:
        raise ValueError("Missing email from provider")

    # Brand-specific domain validation
    domain = extract_domain_from_email(email)
    if domain in BLOCKED_CONSUMER_DOMAINS:
        raise ValueError("Consumer email domains are not allowed for brand accounts")

    needs_redirect = None
    is_new_user = False
    is_first_org_user = False

    # Step 1: Use database validation to check if user exists
    existing_user = await get_user_with_database_validation(db_conn, redis_client, email)
    
    if not existing_user:
        # Step 2: Create new brand user with proper register_source
        user_data = {
            "email": email,
            "name": f"{provider_profile.given_name or ''} {provider_profile.family_name or ''}".strip() or provider_profile.name or "",
            "profile_image": provider_profile.picture,
            "is_email_verified": provider_profile.is_email_verified,
            "is_phone_verified": False,
            "is_active": True,
            "status": UserStatus.get_status_name(UserStatus.ACTIVE.value),
            "created_at": datetime.now(UTC),
            "updated_at": datetime.now(UTC)
        }
        
        success, user_dict, message = await create_oauth_user_with_proper_source(
            db_conn=db_conn,
            redis_client=redis_client,
            user_data=user_data,
            provider=provider
        )

        if not success:
            raise ValueError(f"Failed to create brand user: {message}")
        
        is_new_user = True
        user_id = UUID(user_dict["id"])
        logger.info("Created new brand user with proper register_source", extra={
            "email": email, 
            "user_id": str(user_id),
            "register_source": user_dict.get("register_source"),
            "provider": provider
        })
        
        # Ensure bloom filter is updated for new user
        await ensure_bloom_filter_updated(redis_client, email)
        
    else:
        # User exists - update with OAuth data and proper register_source
        existing_user = await handle_existing_user_oauth_registration(
            db_conn=db_conn,
            redis_client=redis_client,
            user=existing_user,
            provider=provider,
            provider_profile=provider_profile
        )
        user_id = existing_user.id
        logger.info("Updated existing brand user for OAuth", extra={
            "email": email, 
            "user_id": str(user_id),
            "provider": provider
        })

    # Step 3: Handle all brand-specific OAuth operations in a single transaction
    try:
        async with db_conn.get_db() as session:
            async with session.begin():
                # Fetch the user entity for session operations
                user_query = select(User).where(User.id == user_id)
                user_result = await session.execute(user_query)
                user = user_result.scalar_one_or_none()
                
                if not user:
                    raise ValueError(f"Brand user not found: {user_id}")

                # Update user profile from OAuth data (final update)
                if provider_profile.name:
                    user.name = provider_profile.name
                if provider_profile.picture:
                    user.profile_image = provider_profile.picture
                user.is_email_verified = provider_profile.is_email_verified
                user.status = UserStatus.get_status_name(UserStatus.ACTIVE.value)
                user.is_active = True
                user.updated_at = datetime.now(UTC)

                # Ensure user has the brand role
                stmt = select(UserRoleModel).where(
                    UserRoleModel.user_id == user_id,
                    UserRoleModel.role_id == role_uuid
                )
                result = await session.execute(stmt)
                user_role = result.scalar_one_or_none()

                if not user_role:
                    # Assign brand role
                    user_role = UserRoleModel(
                        user_id=user_id,
                        role_id=role_uuid,
                        assigned_at=datetime.now(UTC)
                    )
                    session.add(user_role)
                    logger.info("Assigned brand role", extra={"user_id": str(user_id), "role_uuid": role_uuid})
                else:
                    logger.info("User already has brand role", extra={"user_id": str(user_id), "role_uuid": role_uuid})

                # Create/update OAuth account
                oauth_account = await upsert_oauth_account_safe(
                    session=session,
                    user_id=user_id,
                    provider=provider,
                    provider_id=provider_profile.provider_id,
                    tokens=provider_tokens
                )

                # Create/update user auth method
                auth_method = await upsert_user_auth_method_safe(
                    session=session,
                    redis_client=redis_client,
                    user_id=user_id,
                    provider=provider
                )

                # Brand user flow - ensure organization exists (mirrors OTP verification flow)
                from app.utilities.brand_organization_utils import ensure_org_and_membership
                org, membership, org_created = await ensure_org_and_membership(
                    session=session,
                    redis_client=redis_client,
                    user=user,
                    org_name=None  # Will be set from domain
                )
                is_first_org_user = org_created
                
                # Save role to Redis for future authentication (mirrors OTP flow)
                from app.utilities.user_utils import save_user_role_to_redis
                role = "brand-admin" if org_created else "brand"
                await save_user_role_to_redis(str(user.id), role, redis_client)
                
                logger.info("Brand organization setup completed", extra={
                    "user_id": str(user.id),
                    "organization_id": str(org.id),
                    "organization_created": org_created,
                    "is_first_org_user": is_first_org_user,
                    "membership_role": membership.role.value
                })
                
                # Commit all changes
                await session.commit()
                logger.info("Brand OAuth transaction completed successfully", extra={
                    "user_id": str(user_id),
                    "provider": provider,
                    "is_new_user": is_new_user
                })

    except Exception as transaction_err:
        logger.error("Brand OAuth transaction failed", extra={
            "user_id": str(user_id),
            "provider": provider,
            "error": str(transaction_err)
        })
        raise ValueError(f"Brand OAuth setup failed: {str(transaction_err)}")

    # Step 4: Create session and tokens (outside transaction)
    try:
        # For session creation, get a fresh user instance
        if is_new_user:
            # For new users, get from cache (just created)
            fresh_user = await get_user_by_email_cache_aside(db_conn, redis_client, email, True)
        else:
            # For existing users, use database validation to ensure fresh data
            fresh_user = await get_user_with_database_validation(db_conn, redis_client, email)
        
        if not fresh_user:
            raise ValueError("Could not fetch brand user for session creation")
        
        session_data = await create_oauth_user_session(db_conn, fresh_user, redis_client, None, rbac_service)
        
        # Fetch organization brands for brand users (mirrors OTP verification response)
        from app.utilities.brand_utils import list_brands_with_user_status
        
        organization_brands = await list_brands_with_user_status(
            db_conn=db_conn,
            redis_client=redis_client,
            org_id=org.id,
            user_id=user_id
        )
        
        # Extract org name safely
        org_name = None
        if hasattr(org, 'name') and org.name is not None:
            org_name = str(org.name)
        
        response = AuthTokenBrandResponse(
            access_token=session_data["access_token"],
            refresh_token=session_data["refresh_token"],
            token_type=session_data["token_type"],
            expires_in=session_data["expires_in"],
            is_first=is_first_org_user,
            organization_id=str(org.id),
            organization_name=org_name,
            organization_brands=organization_brands
        )
        
        logger.info("Brand OAuth flow completed successfully", extra={
            "user_id": str(user_id),
            "provider": provider,
            "is_new_user": is_new_user,
            "is_first_org_user": is_first_org_user,
            "organization_id": str(org.id)
        })
        
        return response, needs_redirect, is_new_user
        
    except Exception as session_err:
        logger.error("Session creation failed after brand OAuth setup", extra={
            "user_id": str(user_id),
            "error": str(session_err)
        })
        raise ValueError(f"Brand session creation failed: {str(session_err)}")


async def check_oauth_account_exists(
        db_conn,
        provider: str,
        provider_user_id: str
) -> Optional[UUID]:
    """
    Check if an OAuth account already exists for the given provider and provider user ID.
    
    Args:
        db_conn: Database connection
        provider: OAuth provider name
        provider_user_id: Provider's user ID
        
    Returns:
        User ID if account exists, None otherwise
    """
    try:
        async with db_conn.get_db() as session:
            stmt = select(OAuthAccount.user_id).where(
                OAuthAccount.provider == provider,
                OAuthAccount.provider_user_id == provider_user_id
            )
            result = await session.execute(stmt)
            user_id = result.scalar_one_or_none()
            return user_id
    except Exception as e:
        logger.error("Error checking OAuth account existence", extra={
            "provider": provider,
            "provider_user_id": provider_user_id,
            "error": str(e)
        })
        return None


@with_trace_id
async def cleanup_failed_oauth_attempt(
        db_conn,
        redis_client,
        user_id: UUID,
        provider: str
) -> None:
    """
    Clean up any partial OAuth setup if the flow fails.
    
    Args:
        db_conn: Database connection
        redis_client: Redis client
        user_id: User ID
        provider: OAuth provider name
    """
    try:
        async with db_conn.get_db() as session:
            async with session.begin():
                # Remove OAuth account if it was created
                stmt = select(OAuthAccount).where(
                    OAuthAccount.user_id == user_id,
                    OAuthAccount.provider == provider
                )
                result = await session.execute(stmt)
                oauth_account = result.scalar_one_or_none()
                
                if oauth_account:
                    await session.delete(oauth_account)
                    logger.info("Cleaned up OAuth account", extra={
                        "user_id": str(user_id),
                        "provider": provider
                    })
                
                # Remove user auth method if it was created
                provider_lower = provider.lower()
                if provider_lower == "google":
                    method_key = "oauth_google"
                elif provider_lower == "facebook":
                    method_key = "oauth_facebook"
                elif provider_lower == "instagram":
                    method_key = "oauth_instagram"
                else:
                    method_key = None
                
                if method_key:
                    auth_method_id = await get_auth_method_id_by_name_with_session(
                        session=session,
                        redis_client=redis_client,
                        method_name=method_key
                    )
                    
                    if auth_method_id:
                        auth_stmt = select(UserAuthMethod).where(
                            UserAuthMethod.user_id == user_id,
                            UserAuthMethod.auth_method_id == auth_method_id
                        )
                        auth_result = await session.execute(auth_stmt)
                        auth_method = auth_result.scalar_one_or_none()
                        
                        if auth_method:
                            await session.delete(auth_method)
                            logger.info("Cleaned up user auth method", extra={
                                "user_id": str(user_id),
                                "method_key": method_key
                            })
                
                await session.commit()
                
    except Exception as e:
        logger.error("Error during OAuth cleanup", extra={
            "user_id": str(user_id),
            "provider": provider,
            "error": str(e)
        })
