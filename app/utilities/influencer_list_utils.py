"""
Influencer List Utilities for CreatorVerse Backend.
Helper functions for common influencer list operations.
"""
import csv
import json
from io import String<PERSON>
from typing import List, Dict, Any, Optional, Tuple
from uuid import UUID

from sqlalchemy import select, func
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import joinedload

from app.core.logger import get_logger
from app.models.user_models import (
    BrandInfluencerList, BrandInfluencerListEntry, Brand,
    BrandInfluencerListStatus, InfluencerStatus, BrandMembership
)
from app.schemas.influencer_schemas import AddInfluencerToListRequest

logger = get_logger()


async def get_user_brand_lists_summary(
    db_conn,
    redis_client,
    user_id: UUID,
    brand_id: Optional[UUID] = None
) -> Dict[str, Any]:
    """
    Get summary of influencer lists for a user across all brands or specific brand.
    
    Args:
        db_conn: Database connection manager
        redis_client: Redis client
        user_id: User ID
        brand_id: Optional specific brand ID
        
    Returns:
        Summary dictionary with lists and statistics
    """
    async with db_conn.get_db() as session:
        # Get user's brand memberships
        membership_query = select(BrandMembership).where(
            BrandMembership.user_id == user_id,
            BrandMembership.is_active == True
        ).options(joinedload(BrandMembership.brand))
        
        if brand_id:
            membership_query = membership_query.where(
                BrandMembership.brand_id == brand_id
            )
        
        membership_result = await session.execute(membership_query)
        memberships = membership_result.scalars().all()
        
        if not memberships:
            return {
                "brands": [],
                "total_lists": 0,
                "total_influencers": 0,
                "summary": {
                    "active_lists": 0,
                    "draft_lists": 0,
                    "archived_lists": 0
                }
            }
        
        brand_summaries = []
        total_lists = 0
        total_influencers = 0
        status_summary = {"active_lists": 0, "draft_lists": 0, "archived_lists": 0}
        
        for membership in memberships:
            brand = membership.brand
            
            # Get lists for this brand
            lists_query = select(
                BrandInfluencerList.id,
                BrandInfluencerList.name,
                BrandInfluencerList.status,
                BrandInfluencerList.created_at,
                BrandInfluencerList.updated_at,
                func.count(BrandInfluencerListEntry.id).label('influencer_count')
            ).select_from(
                BrandInfluencerList
            ).outerjoin(
                BrandInfluencerListEntry,
                BrandInfluencerList.id == BrandInfluencerListEntry.list_id
            ).where(
                BrandInfluencerList.brand_id == brand.id,
                BrandInfluencerList.deleted_at.is_(None)
            ).group_by(
                BrandInfluencerList.id
            ).order_by(
                BrandInfluencerList.updated_at.desc()
            )
            
            lists_result = await session.execute(lists_query)
            lists_data = lists_result.all()
            
            brand_lists = []
            brand_influencer_count = 0
            
            for list_row in lists_data:
                influencer_count = list_row.influencer_count or 0
                brand_influencer_count += influencer_count
                
                brand_lists.append({
                    "id": str(list_row.id),
                    "name": list_row.name,
                    "status": list_row.status.value,
                    "influencer_count": influencer_count,
                    "created_at": list_row.created_at.isoformat(),
                    "updated_at": list_row.updated_at.isoformat()
                })
                
                # Update status summary
                if list_row.status == BrandInfluencerListStatus.active:
                    status_summary["active_lists"] += 1
                elif list_row.status == BrandInfluencerListStatus.draft:
                    status_summary["draft_lists"] += 1
                elif list_row.status == BrandInfluencerListStatus.archived:
                    status_summary["archived_lists"] += 1
            
            brand_summaries.append({
                "brand_id": str(brand.id),
                "brand_name": brand.name,
                "lists": brand_lists,
                "total_lists": len(brand_lists),
                "total_influencers": brand_influencer_count
            })
            
            total_lists += len(brand_lists)
            total_influencers += brand_influencer_count
        
        return {
            "brands": brand_summaries,
            "total_lists": total_lists,
            "total_influencers": total_influencers,
            "summary": status_summary
        }


async def validate_influencer_data(
    influencer_data: Dict[str, Any]
) -> Tuple[bool, Optional[AddInfluencerToListRequest], List[str]]:
    """
    Validate influencer data from CSV import or API input.
    
    Args:
        influencer_data: Raw influencer data dictionary
        
    Returns:
        Tuple of (is_valid, validated_request_object, error_messages)
    """
    errors = []
    
    # Required fields
    influencer_id = influencer_data.get('influencer_id') or influencer_data.get('profile_url')
    if not influencer_id:
        errors.append("influencer_id or profile_url is required")
    
    # Optional fields with validation
    engagement_rate = influencer_data.get('engagement_rate')
    if engagement_rate is not None:
        try:
            engagement_rate = int(float(engagement_rate) * 100)  # Convert to percentage * 100
            if engagement_rate < 0 or engagement_rate > 10000:  # 0-100%
                errors.append("engagement_rate must be between 0 and 100")
        except (ValueError, TypeError):
            errors.append("engagement_rate must be a valid number")
            engagement_rate = None
    
    audience_size = influencer_data.get('audience_size') or influencer_data.get('followers')
    if audience_size is not None:
        try:
            audience_size = int(audience_size)
            if audience_size < 0:
                errors.append("audience_size must be non-negative")
        except (ValueError, TypeError):
            errors.append("audience_size must be a valid number")
            audience_size = None
    
    # Parse channels
    channels = influencer_data.get('channels', [])
    if isinstance(channels, str):
        try:
            channels = json.loads(channels)
        except json.JSONDecodeError:
            # Try splitting by comma
            channels = [ch.strip() for ch in channels.split(',') if ch.strip()]
    
    # Parse labels
    labels = influencer_data.get('labels', [])
    if isinstance(labels, str):
        try:
            labels = json.loads(labels)
        except json.JSONDecodeError:
            # Try splitting by comma
            labels = [label.strip() for label in labels.split(',') if label.strip()]
    
    # Validate status
    status = influencer_data.get('status', 'shortlisted')
    valid_statuses = [s.value for s in InfluencerStatus]
    if status not in valid_statuses:
        errors.append(f"status must be one of: {', '.join(valid_statuses)}")
        status = InfluencerStatus.shortlisted.value
    
    if errors:
        return False, None, errors
    
    try:
        validated_request = AddInfluencerToListRequest(
            influencer_id=influencer_id,
            influencer_name=influencer_data.get('influencer_name') or influencer_data.get('name'),
            influencer_username=influencer_data.get('influencer_username') or influencer_data.get('username'),
            influencer_avatar_url=influencer_data.get('influencer_avatar_url') or influencer_data.get('avatar_url'),
            user_id=influencer_data.get('user_id'),
            status=InfluencerStatus(status),
            channels=channels,
            audience_size=audience_size,
            engagement_rate=engagement_rate,
            campaign=influencer_data.get('campaign'),
            labels=labels,
            notes=influencer_data.get('notes')
        )
        return True, validated_request, []
    except Exception as e:
        return False, None, [f"Validation error: {str(e)}"]


def parse_csv_data(csv_content: str) -> Tuple[List[Dict[str, Any]], List[str]]:
    """
    Parse CSV content and return list of dictionaries.
    
    Args:
        csv_content: CSV content as string
        
    Returns:
        Tuple of (parsed_data_list, error_messages)
    """
    errors = []
    parsed_data = []
    
    try:
        # Create CSV reader
        csv_file = StringIO(csv_content)
        reader = csv.DictReader(csv_file)
        
        # Normalize headers (strip whitespace, convert to lowercase)
        if reader.fieldnames:
            reader.fieldnames = [header.strip().lower().replace(' ', '_') for header in reader.fieldnames]
        
        for row_num, row in enumerate(reader, start=2):  # Start at 2 for header row
            # Clean row data (strip whitespace)
            cleaned_row = {key: value.strip() if isinstance(value, str) else value 
                          for key, value in row.items() if key}
            
            # Skip empty rows
            if not any(cleaned_row.values()):
                continue
            
            parsed_data.append(cleaned_row)
    
    except Exception as e:
        errors.append(f"CSV parsing error: {str(e)}")
    
    return parsed_data, errors


async def get_list_analytics(
    db_conn,
    redis_client,
    list_id: UUID
) -> Dict[str, Any]:
    """
    Get analytics and statistics for an influencer list.
    
    Args:
        db_conn: Database connection manager
        redis_client: Redis client
        list_id: List ID
        
    Returns:
        Analytics dictionary
    """
    async with db_conn.get_db() as session:
        # Get list info
        list_query = select(BrandInfluencerList).where(
            BrandInfluencerList.id == list_id,
            BrandInfluencerList.deleted_at.is_(None)
        )
        list_result = await session.execute(list_query)
        list_obj = list_result.scalar_one_or_none()
        
        if not list_obj:
            return {}
        
        # Get status distribution
        status_query = select(
            BrandInfluencerListEntry.status,
            func.count(BrandInfluencerListEntry.id).label('count')
        ).where(
            BrandInfluencerListEntry.list_id == list_id
        ).group_by(
            BrandInfluencerListEntry.status
        )
        
        status_result = await session.execute(status_query)
        status_data = status_result.all()
        
        status_distribution = {status.value: 0 for status in InfluencerStatus}
        total_influencers = 0
        
        for row in status_data:
            status_distribution[row.status.value] = row.count
            total_influencers += row.count
        
        # Get audience size statistics
        audience_query = select(
            func.min(BrandInfluencerListEntry.audience_size).label('min_audience'),
            func.max(BrandInfluencerListEntry.audience_size).label('max_audience'),
            func.avg(BrandInfluencerListEntry.audience_size).label('avg_audience'),
            func.sum(BrandInfluencerListEntry.audience_size).label('total_audience')
        ).where(
            BrandInfluencerListEntry.list_id == list_id,
            BrandInfluencerListEntry.audience_size.is_not(None)
        )
        
        audience_result = await session.execute(audience_query)
        audience_stats = audience_result.first()
        
        # Get engagement rate statistics
        engagement_query = select(
            func.min(BrandInfluencerListEntry.engagement_rate).label('min_engagement'),
            func.max(BrandInfluencerListEntry.engagement_rate).label('max_engagement'),
            func.avg(BrandInfluencerListEntry.engagement_rate).label('avg_engagement')
        ).where(
            BrandInfluencerListEntry.list_id == list_id,
            BrandInfluencerListEntry.engagement_rate.is_not(None)
        )
        
        engagement_result = await session.execute(engagement_query)
        engagement_stats = engagement_result.first()
        
        # Get top channels
        channels_query = select(BrandInfluencerListEntry.channels).where(
            BrandInfluencerListEntry.list_id == list_id,
            BrandInfluencerListEntry.channels.is_not(None)
        )
        
        channels_result = await session.execute(channels_query)
        all_channels = []
        
        for row in channels_result:
            if row.channels:
                all_channels.extend(row.channels)
        
        # Count channel frequency
        channel_counts = {}
        for channel in all_channels:
            channel_counts[channel] = channel_counts.get(channel, 0) + 1
        
        top_channels = sorted(channel_counts.items(), key=lambda x: x[1], reverse=True)[:10]
        
        return {
            "list_id": str(list_id),
            "list_name": list_obj.name,
            "total_influencers": total_influencers,
            "status_distribution": status_distribution,
            "audience_stats": {
                "min_audience": audience_stats.min_audience if audience_stats else None,
                "max_audience": audience_stats.max_audience if audience_stats else None,
                "avg_audience": float(audience_stats.avg_audience) if audience_stats.avg_audience else None,
                "total_audience": audience_stats.total_audience if audience_stats else None
            },
            "engagement_stats": {
                "min_engagement": engagement_stats.min_engagement if engagement_stats else None,
                "max_engagement": engagement_stats.max_engagement if engagement_stats else None,
                "avg_engagement": float(engagement_stats.avg_engagement) if engagement_stats.avg_engagement else None
            },
            "top_channels": [{"channel": channel, "count": count} for channel, count in top_channels],
            "created_at": list_obj.created_at.isoformat(),
            "updated_at": list_obj.updated_at.isoformat()
        }


async def duplicate_list(
    session: AsyncSession,
    redis_client,
    source_list_id: UUID,
    target_brand_id: UUID,
    new_name: str,
    user_id: UUID,
    copy_entries: bool = True
) -> BrandInfluencerList:
    """
    Duplicate an influencer list to another brand.
    
    Args:
        session: Database session
        redis_client: Redis client
        source_list_id: Source list ID
        target_brand_id: Target brand ID
        new_name: New list name
        user_id: User performing the operation
        copy_entries: Whether to copy entries or just the list structure
        
    Returns:
        New duplicated list
        
    Raises:
        ValueError: If source list not found or user doesn't have permission
    """
    # Get source list
    source_query = select(BrandInfluencerList).where(
        BrandInfluencerList.id == source_list_id,
        BrandInfluencerList.deleted_at.is_(None)
    )
    source_result = await session.execute(source_query)
    source_list = source_result.scalar_one_or_none()
    
    if not source_list:
        raise ValueError("Source list not found")
    
    # Check permissions for target brand
    membership_query = select(BrandMembership).where(
        BrandMembership.brand_id == target_brand_id,
        BrandMembership.user_id == user_id,
        BrandMembership.is_active == True
    )
    membership_result = await session.execute(membership_query)
    membership = membership_result.scalar_one_or_none()
    
    if not membership:
        raise ValueError("User is not a member of the target brand")
    
    # Create new list
    new_list = BrandInfluencerList(
        brand_id=target_brand_id,
        name=new_name,
        description=f"Copy of {source_list.name}",
        created_by=user_id,
        status=BrandInfluencerListStatus.active
    )
    
    session.add(new_list)
    await session.flush()  # To get the ID
    
    if copy_entries:
        # Get source entries
        entries_query = select(BrandInfluencerListEntry).where(
            BrandInfluencerListEntry.list_id == source_list_id
        )
        entries_result = await session.execute(entries_query)
        source_entries = entries_result.scalars().all()
        
        # Copy entries
        for source_entry in source_entries:
            new_entry = BrandInfluencerListEntry(
                list_id=new_list.id,
                influencer_id=source_entry.influencer_id,
                user_id=source_entry.user_id,
                influencer_name=source_entry.influencer_name,
                influencer_username=source_entry.influencer_username,
                influencer_avatar_url=source_entry.influencer_avatar_url,
                status=InfluencerStatus.shortlisted,  # Reset to shortlisted
                channels=source_entry.channels,
                audience_size=source_entry.audience_size,
                engagement_rate=source_entry.engagement_rate,
                campaign=source_entry.campaign,
                labels=source_entry.labels,
                notes=source_entry.notes,
                added_by=user_id
            )
            session.add(new_entry)
    
    # Evict caches
    from app.services.influencer_list_service import _evict_list_caches
    await _evict_list_caches(redis_client, target_brand_id)
    
    logger.info(
        f"Duplicated list {source_list_id} to new list {new_list.id}",
        extra={
            "source_list_id": str(source_list_id),
            "new_list_id": str(new_list.id),
            "target_brand_id": str(target_brand_id),
            "user_id": str(user_id),
            "copy_entries": copy_entries
        }
    )
    
    return new_list
