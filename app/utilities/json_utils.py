"""
JSON utilities for CreatorVerse Discovery Profile Analytics.

This module contains helpers for JSON serialization and handling common data types.
"""

import json
import uuid
from datetime import datetime


class CustomJSONEncoder(json.JSONEncoder):
    """Custom JSON encoder that handles common data types."""
    def default(self, obj):
        if isinstance(obj, uuid.UUID):
            return str(obj)
        if isinstance(obj, datetime):
            return obj.isoformat()
        return super().default(obj)


def json_dumps(obj, **kwargs):
    """
    Serialize obj to a JSON formatted str using the CustomJSONEncoder.
    
    Args:
        obj: The object to serialize
        **kwargs: Additional arguments to pass to json.dumps
    
    Returns:
        A JSON formatted string
    """
    return json.dumps(obj, cls=CustomJSONEncoder, **kwargs)
