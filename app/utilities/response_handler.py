"""
Response handler utilities for CreatorVerse Backend
"""
from typing import Any, Dict, Optional
from fastapi.responses import JSONResponse
from fastapi import status


def create_success_response(
    message: str = "Success",
    data: Optional[Dict[str, Any]] = None,
    **kwargs
) -> Dict[str, Any]:
    """Create a standardized success response - Legacy format for backward compatibility"""
    response = {
        "success": True,
        "message": message,
    }
    
    if data is not None:
        response["data"] = data
    
    # Add any additional fields
    response.update(kwargs)
    
    return response


def create_error_response(
    message: str = "Error",
    error_type: str = "GeneralError",
    details: Optional[Dict[str, Any]] = None,
    **kwargs
) -> Dict[str, Any]:
    """Create a standardized error response - Legacy format for backward compatibility"""
    response = {
        "success": False,
        "message": message,
        "error_type": error_type
    }
    
    if details is not None:
        response["details"] = details
    
    # Add any additional fields
    response.update(kwargs)
    
    return response


class StandardResponse:
    """Standard response handler for all API endpoints - New format"""
    
    @staticmethod
    def success(
        data: Any = None,
        message: str = "Success",
        status_code: int = status.HTTP_200_OK
    ) -> JSONResponse:
        """Create a successful response"""
        response_data = {
            "data": data,
            "message": message
        }
        return JSONResponse(
            content=response_data,
            status_code=status_code
        )
    
    @staticmethod
    def error(
        message: str = "An error occurred",
        data: Any = None,
        status_code: int = status.HTTP_400_BAD_REQUEST
    ) -> JSONResponse:
        """Create an error response"""
        response_data = {
            "data": data,
            "message": message
        }
        return JSONResponse(
            content=response_data,
            status_code=status_code
        )
    
    @staticmethod
    def created(
        data: Any = None,
        message: str = "Resource created successfully"
    ) -> JSONResponse:
        """Create a 201 response for resource creation"""
        return StandardResponse.success(
            data=data,
            message=message,
            status_code=status.HTTP_201_CREATED
        )
    
    @staticmethod
    def not_found(
        message: str = "Resource not found",
        data: Any = None
    ) -> JSONResponse:
        """Create a 404 response"""
        return StandardResponse.error(
            message=message,
            data=data,
            status_code=status.HTTP_404_NOT_FOUND
        )
    
    @staticmethod
    def unauthorized(
        message: str = "Unauthorized access",
        data: Any = None
    ) -> JSONResponse:
        """Create a 401 response"""
        return StandardResponse.error(
            message=message,
            data=data,
            status_code=status.HTTP_401_UNAUTHORIZED
        )
    
    @staticmethod
    def forbidden(
        message: str = "Forbidden access",
        data: Any = None
    ) -> JSONResponse:
        """Create a 403 response"""
        return StandardResponse.error(
            message=message,
            data=data,
            status_code=status.HTTP_403_FORBIDDEN
        )
    
    @staticmethod
    def internal_server_error(
        message: str = "Internal server error",
        data: Any = None
    ) -> JSONResponse:
        """Create a 500 response"""
        return StandardResponse.error(
            message=message,
            data=data,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


def create_health_response(
    status: str = "healthy",
    additional_data: Optional[Dict[str, Any]] = None,
    **kwargs
) -> Dict[str, Any]:
    """Create a health check response"""
    response = {
        "status": status,
        "service": "CreatorVerse Backend",
        "timestamp": "2025-06-18T00:57:00Z"
    }
    
    if additional_data is not None:
        response.update(additional_data)
    
    # Add any additional fields
    response.update(kwargs)
    
    return response
