"""
Response handler utility for CreatorVerse Discovery & Analytics
Provides standardized response format following creatorverse_user_backend patterns
"""
from typing import Any, Optional, Dict, List, Generic, TypeVar
from pydantic import BaseModel, Field
from fastapi.responses import JSONResponse

T = TypeVar('T')

class StandardResponseModel(BaseModel, Generic[T]):
    """Pydantic model for standard API responses"""
    status: str = Field(..., description="Response status (success/error)")
    message: str = Field(..., description="Response message")
    data: Optional[T] = Field(None, description="Response payload data")

class PaginationMetaModel(BaseModel):
    """Pydantic model for pagination metadata"""
    total_count: int
    page: int
    page_size: int
    total_pages: int
    has_next: bool
    has_previous: bool

class PaginatedResponseModel(BaseModel, Generic[T]):
    """Pydantic model for paginated responses"""
    items: List[T]
    meta: PaginationMetaModel


class StandardResponse:
    @staticmethod
    def success(
        data: Any = None, 
        message: str = "Success", 
        status_code: int = 200
    ) -> JSONResponse:
        return JSONResponse(
            status_code=status_code,
            content={
                "status": "success",
                "message": message,
                "data": data
            }
        )

    @staticmethod
    def error(
        message: str = "Error occurred", 
        status_code: int = 400, 
        data: Optional[Any] = None,
        error_code: Optional[str] = None,
        details: Optional[Any] = None
    ) -> JSONResponse:
        error_data = data if data is not None else {}
        if isinstance(error_data, dict):
            if error_code:
                error_data["error_code"] = error_code
            if details:
                error_data["details"] = details
        
        return JSONResponse(
            status_code=status_code,
            content={
                "status": "error",
                "message": message,
                "data": error_data
            }
        )


class PaginatedResponse(StandardResponse):
    """Paginated response with metadata"""
    
    @classmethod
    def paginated_success(
        cls,
        data: List[Any],
        total_count: int,
        page: int,
        page_size: int,
        message: str = "Success",
        additional_meta: Optional[Dict[str, Any]] = None,
        request_id: Optional[str] = None
    ) -> JSONResponse:
        """Create a paginated success response"""
        total_pages = (total_count + page_size - 1) // page_size
        has_next = page < total_pages
        has_previous = page > 1
        
        meta = {
            "total_count": total_count,
            "page": page,
            "page_size": page_size,
            "total_pages": total_pages,
            "has_next": has_next,
            "has_previous": has_previous
        }
        
        if additional_meta:
            meta.update(additional_meta)
        
        response_data = {
            "items": data,
            "meta": meta
        }
        
        return cls.success(
            data=response_data,
            message=message
        )


# Helper functions for common response patterns
def create_success_response(data: Any = None, message: str = "Success") -> Dict[str, Any]:
    """Create a success response dictionary"""
    return {
        "status": "success",
        "message": message,
        "data": data
    }


def create_error_response(
    message: str,
    error_code: Optional[str] = None,
    details: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """Create an error response dictionary"""
    data = {}
    if error_code:
        data["error_code"] = error_code
    if details:
        data["details"] = details
        
    return {
        "status": "error",
        "message": message,
        "data": data
    }


def create_health_response(service_name: str, environment: str) -> Dict[str, Any]:
    """Create a health check response"""
    return {
        "status": "success",
        "message": "Service is healthy",
        "data": {
            "status": "healthy",
            "service": service_name,
            "environment": environment
        }
    }


def create_discovery_response(
    profiles: List[Any],
    total_count: int,
    page: int,
    page_size: int,
    query_time_ms: int,
    cache_hit: bool = False,
    external_api_calls: int = 0,
    filters_applied: Optional[Dict[str, Any]] = None,
    sort_criteria: Optional[List[Dict[str, str]]] = None
) -> Dict[str, Any]:
    """Create a discovery response with metadata"""
    total_pages = (total_count + page_size - 1) // page_size if page_size > 0 else 0
    has_next = page < total_pages
    has_previous = page > 1
    
    meta = {
        "total_count": total_count,
        "page": page,
        "page_size": page_size,
        "total_pages": total_pages,
        "has_next": has_next,
        "has_previous": has_previous,
        "query_time_ms": query_time_ms,
        "cache_hit": cache_hit,
        "external_api_calls": external_api_calls,
        "filters_applied": filters_applied or {},
        "sort_criteria": sort_criteria or []
    }
    
    return {
        "status": "success",
        "message": "Discovery completed successfully",
        "data": {
            "items": profiles,
            "meta": meta
        }
    }
