"""
Startup tasks for CreatorVerse Discovery & Analytics service
"""
import asyncio
from app.core.config import APP_CONFIG


async def initialize_discovery_startup_tasks():
    """Initialize all startup tasks for the discovery service"""
    logger = APP_CONFIG.logger or APP_CONFIG.initialize_logger()
    
    try:
        logger.info("Starting discovery service initialization tasks...")
        
        # Task 1: Initialize filter metadata cache
        await initialize_filter_metadata_cache()
        
        # Task 2: Verify external API connections
        await verify_external_api_connections()
        
        # Task 3: Initialize performance monitoring
        await initialize_performance_monitoring()
        
        logger.info("All discovery startup tasks completed successfully")
        
    except Exception as e:
        logger.error(f"Discovery startup tasks failed: {e}")
        raise


async def initialize_filter_metadata_cache():
    """Initialize filter metadata in Redis cache"""
    logger = APP_CONFIG.logger
    try:
        logger.info("Initializing filter metadata cache...")
        
        # Import here to avoid circular imports
        from app.core.config import get_discovery_redis
        from app.schemas.filter_schemas import DEFAULT_FILTER_CATEGORIES
        import json
        
        redis_client = get_discovery_redis()
        
        # Cache default filter categories
        filter_metadata = {
            "categories": [category.dict() for category in DEFAULT_FILTER_CATEGORIES],
            "version": "1.0",
            "total_filters": sum(len(cat.filters) for cat in DEFAULT_FILTER_CATEGORIES)
        }
        
        cache_key = "discovery:filter_metadata"
        await redis_client.set(
            cache_key,
            json.dumps(filter_metadata),
            expire=APP_CONFIG.filter_cache_ttl
        )
        
        logger.info("Filter metadata cache initialized successfully")
        
    except Exception as e:
        logger.error(f"Failed to initialize filter metadata cache: {e}")
        # Don't raise - this is not critical for service startup


async def verify_external_api_connections():
    """Verify connections to external APIs like Phyllo"""
    logger = APP_CONFIG.logger
    try:
        logger.info("Verifying external API connections...")
        
        # For now, just log the configuration
        if APP_CONFIG.phyllo_base_url:
            logger.info(f"Phyllo API configured: {APP_CONFIG.phyllo_base_url}")
        else:
            logger.warning("Phyllo API not configured - external data fetching will be disabled")
        
        # Add actual API health checks here when implementing external services
        
        logger.info("External API connection verification completed")
        
    except Exception as e:
        logger.warning(f"External API verification failed: {e}")
        # Don't raise - service can work without external APIs


async def initialize_performance_monitoring():
    """Initialize performance monitoring and metrics"""
    logger = APP_CONFIG.logger
    try:
        logger.info("Initializing performance monitoring...")
        
        # Initialize performance counters in Redis
        from app.core.config import get_discovery_redis
        
        redis_client = get_discovery_redis()
        
        # Initialize counters
        counters = [
            "discovery:requests:total",
            "discovery:requests:cache_hits",
            "discovery:requests:external_api_calls",
            "discovery:profiles:total",
            "discovery:filters:saved_total"
        ]
        
        for counter in counters:
            if not await redis_client.exists(counter):
                await redis_client.set(counter, "0")
        
        logger.info("Performance monitoring initialized successfully")
        
    except Exception as e:
        logger.warning(f"Performance monitoring initialization failed: {e}")
        # Don't raise - this is not critical for service startup


async def cleanup_expired_cache():
    """Clean up expired cache entries"""
    logger = APP_CONFIG.logger
    try:
        logger.info("Cleaning up expired cache entries...")
        
        from app.core.config import get_discovery_redis
        redis_client = get_discovery_redis()
        
        # Clean up expired profile cache entries
        expired_count = await redis_client.delete_pattern("discovery:profile:*:expired")
        
        if expired_count > 0:
            logger.info(f"Cleaned up {expired_count} expired cache entries")
        
    except Exception as e:
        logger.warning(f"Cache cleanup failed: {e}")


# Health check functions
async def health_check_database():
    """Check database health"""
    try:
        from app.core.config import get_database
        db = get_database()
        return await db.test_connection()
    except Exception:
        return False


async def health_check_redis():
    """Check Redis health"""
    try:
        from app.core.config import get_discovery_redis
        redis_client = get_discovery_redis()
        await redis_client.get("health_check")
        return True
    except Exception:
        return False


async def health_check_external_apis():
    """Check external API health"""
    # Implement actual health checks for external APIs
    return True


async def get_service_health():
    """Get overall service health status"""
    db_healthy = await health_check_database()
    redis_healthy = await health_check_redis()
    apis_healthy = await health_check_external_apis()
    
    return {
        "database": db_healthy,
        "redis": redis_healthy,
        "external_apis": apis_healthy,
        "overall": db_healthy and redis_healthy and apis_healthy
    }
