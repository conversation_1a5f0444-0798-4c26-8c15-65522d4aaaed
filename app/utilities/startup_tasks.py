"""
Startup tasks for CreatorVerse Backend
"""
from app.core.config import APP_CONFIG, get_database, get_locobuzz_redis
from app.core_helper.async_logger import with_trace_id
from app.services.rbac_service import RBACService


def initialize_all_startup_tasks_sync():
    """Initialize all synchronous startup tasks"""
    try:
        # Ensure logger is initialized
        logger = APP_CONFIG.logger or APP_CONFIG.initialize_logger()
        logger.info("Starting initialization of synchronous startup tasks")

        # Add any synchronous startup tasks here if needed
        # For now, we'll just log that sync tasks are complete

        logger.info("Synchronous startup tasks completed successfully")
        return True
    except Exception as e:
        # Fallback to print if logger is not available
        error_msg = f"Failed to initialize synchronous startup tasks: {e}"
        if APP_CONFIG.logger:
            APP_CONFIG.logger.error(error_msg)
        else:
            print(error_msg)
        return False


@with_trace_id
async def initialize_all_startup_tasks_async():
    """Initialize all async startup tasks"""
    logger = APP_CONFIG.logger or APP_CONFIG.initialize_logger()
    logger.info("Starting async startup tasks initialization")

    try:
        # Initialize RBAC cache
        await initialize_rbac_cache()

        logger.info("All async startup tasks completed successfully")
    except Exception as e:
        logger.error(f"Failed to initialize async startup tasks: {e}", extra={"error": str(e)})
        raise


@with_trace_id
async def initialize_rbac_cache():
    """
    Initialize RBAC cache on application startup.
    This loads all roles, permissions, role-permission mappings,
    and auth methods into Redis for faster access.
    """
    logger = APP_CONFIG.logger or APP_CONFIG.initialize_logger()
    logger.info("Initializing RBAC cache")

    try:
        # Get database and redis instances
        db_conn = get_database()
        redis_client = get_locobuzz_redis()
        # Ensure they are initialized (this should be done in main.py lifespan, but just in case)
        if not hasattr(db_conn, 'engine') or db_conn.engine is None:
            await db_conn.initialize()
        if not hasattr(redis_client, 'redis_client') or redis_client.redis_client is None:
            await redis_client.initialize()

        # Get RBAC service
        rbac_service = RBACService(redis_client=redis_client, db_conn=db_conn)

        # Load RBAC data into Redis
        await rbac_service.load_rbac_data()

        logger.info("RBAC cache initialized successfully")
        return True
    except Exception as e:
        logger.error(f"Failed to initialize RBAC cache: {str(e)}", extra={"error": str(e)})
        # Log error but don't prevent application startup
        # The service will fall back to database queries when needed
        return False
