"""
SQLAlchemy models for PostgreSQL database schema.
These models represent the database tables used by the bulk insert functionality.
"""

from sqlalchemy import (
    Column, String, Integer, Float, Boolean, DateTime, Text, 
    ForeignKey, UUID, Enum, UniqueConstraint, Index
)
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID as PostgresUUID
from datetime import datetime
import enum

Base = declarative_base()

# Enum definitions
class PostTypeEnum(enum.Enum):
    REEL = "REEL"
    STORY = "STORY"
    STATIC_POST = "STATIC_POST"
    CAROUSEL = "CAROUSEL"

class GenderEnum(enum.Enum):
    MALE = "MALE"
    FEMALE = "FEMALE"

class DeviceTypeEnum(enum.Enum):
    MOBILE = "MOBILE"
    DESKTOP = "DESKTOP"
    TABLET = "TABLET"

class ContactTypeEnum(enum.Enum):
    EMAIL = "EMAIL"
    PHONE = "PHONE"
    LINKTREE = "LINKTREE"

# Master data tables (api_provider schema)
class MasterWorkPlatform(Base):
    __tablename__ = 'master_work_platform'
    __table_args__ = {'schema': 'api_provider'}
    
    id = Column(PostgresUUID(as_uuid=True), primary_key=True)
    name = Column(String(255), nullable=False)
    logo_url = Column(Text)
    status = Column(String(50), default='ACTIVE')

class MasterInterest(Base):
    __tablename__ = 'master_interest'
    __table_args__ = {'schema': 'api_provider'}
    
    id = Column(PostgresUUID(as_uuid=True), primary_key=True, server_default='gen_random_uuid()')
    name = Column(String(255), nullable=False, unique=True)

class MasterBrand(Base):
    __tablename__ = 'master_brand'
    __table_args__ = {'schema': 'api_provider'}
    
    id = Column(PostgresUUID(as_uuid=True), primary_key=True, server_default='gen_random_uuid()')
    name = Column(String(255), nullable=False)

class MasterLocation(Base):
    __tablename__ = 'master_location'
    __table_args__ = {'schema': 'api_provider'}
    
    id = Column(PostgresUUID(as_uuid=True), primary_key=True, server_default='gen_random_uuid()')
    name = Column(String(255), nullable=False)
    type = Column(String(50))  # COUNTRY, STATE, CITY
    is_deleted = Column(Boolean, default=False)

# Profile analytics tables (profile_analytics schema)
class Profile(Base):
    __tablename__ = 'profile'
    __table_args__ = {'schema': 'profile_analytics'}
    
    id = Column(PostgresUUID(as_uuid=True), primary_key=True)
    work_platform_id = Column(PostgresUUID(as_uuid=True), ForeignKey('api_provider.master_work_platform.id'))
    external_id = Column(String(255))
    platform_username = Column(String(255))
    url = Column(Text)
    image_url = Column(Text)
    full_name = Column(String(255))
    introduction = Column(Text)
    content_count = Column(Integer)
    is_verified = Column(Boolean)
    platform_account_type = Column(String(50))
    gender = Column(Enum(GenderEnum))
    age_group = Column(String(20))
    language = Column(String(10))
    follower_count = Column(Integer)
    subscriber_count = Column(Integer)
    average_likes = Column(Integer)
    average_comments = Column(Integer)
    average_views = Column(Integer)
    average_reels_views = Column(Integer)
    engagement_rate = Column(Float)
    credibility_score = Column(Float)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    work_platform = relationship("MasterWorkPlatform")

class ReputationHistory(Base):
    __tablename__ = 'reputation_history'
    __table_args__ = (
        UniqueConstraint('profile_id', 'month'),
        {'schema': 'profile_analytics'}
    )
    
    id = Column(PostgresUUID(as_uuid=True), primary_key=True, server_default='gen_random_uuid()')
    profile_id = Column(PostgresUUID(as_uuid=True), ForeignKey('profile_analytics.profile.id'))
    month = Column(String(10))  # YYYY-MM format
    follower_count = Column(Integer)
    subscriber_count = Column(Integer)
    following_count = Column(Integer)
    average_likes = Column(Integer)
    average_views = Column(Integer)
    average_comments = Column(Integer)
    total_views = Column(Integer)
    total_likes = Column(Integer)
    
    # Relationships
    profile = relationship("Profile")

class ProfileHashtag(Base):
    __tablename__ = 'profile_hashtag'
    __table_args__ = (
        UniqueConstraint('profile_id', 'hashtag_id'),
        {'schema': 'profile_analytics'}
    )
    
    id = Column(PostgresUUID(as_uuid=True), primary_key=True, server_default='gen_random_uuid()')
    profile_id = Column(PostgresUUID(as_uuid=True), ForeignKey('profile_analytics.profile.id'))
    hashtag_id = Column(PostgresUUID(as_uuid=True), ForeignKey('api_provider.master_interest.id'))
    weight = Column(Float)
    
    # Relationships
    profile = relationship("Profile")
    hashtag = relationship("MasterInterest")

class ProfileInterest(Base):
    __tablename__ = 'profile_interest'
    __table_args__ = (
        UniqueConstraint('profile_id', 'interest_id'),
        {'schema': 'profile_analytics'}
    )
    
    id = Column(PostgresUUID(as_uuid=True), primary_key=True, server_default='gen_random_uuid()')
    profile_id = Column(PostgresUUID(as_uuid=True), ForeignKey('profile_analytics.profile.id'))
    interest_id = Column(PostgresUUID(as_uuid=True), ForeignKey('api_provider.master_interest.id'))
    weight = Column(Float)
    
    # Relationships
    profile = relationship("Profile")
    interest = relationship("MasterInterest")

class ProfileBrandAffinity(Base):
    __tablename__ = 'profile_brand_affinity'
    __table_args__ = (
        UniqueConstraint('profile_id', 'brand_id'),
        {'schema': 'profile_analytics'}
    )
    
    id = Column(PostgresUUID(as_uuid=True), primary_key=True, server_default='gen_random_uuid()')
    profile_id = Column(PostgresUUID(as_uuid=True), ForeignKey('profile_analytics.profile.id'))
    brand_id = Column(PostgresUUID(as_uuid=True), ForeignKey('api_provider.master_brand.id'))
    weight = Column(Float)
    
    # Relationships
    profile = relationship("Profile")
    brand = relationship("MasterBrand")

class Content(Base):
    __tablename__ = 'content'
    __table_args__ = {'schema': 'profile_analytics'}
    
    id = Column(PostgresUUID(as_uuid=True), primary_key=True)
    profile_id = Column(PostgresUUID(as_uuid=True), ForeignKey('profile_analytics.profile.id'))
    group_type = Column(String(50))
    kind = Column(String(50))
    is_sponsored = Column(Boolean)
    url = Column(Text)
    thumbnail_url = Column(Text)
    title = Column(Text)
    description = Column(Text)
    posted_at = Column(DateTime)
    like_count = Column(Integer)
    comment_count = Column(Integer)
    view_count = Column(Integer)
    play_count = Column(Integer)
    
    # Relationships
    profile = relationship("Profile")

class Pricing(Base):
    __tablename__ = 'pricing'
    __table_args__ = (
        UniqueConstraint('profile_id', 'post_type'),
        {'schema': 'profile_analytics'}
    )
    
    id = Column(PostgresUUID(as_uuid=True), primary_key=True, server_default='gen_random_uuid()')
    profile_id = Column(PostgresUUID(as_uuid=True), ForeignKey('profile_analytics.profile.id'))
    post_type = Column(Enum(PostTypeEnum))
    currency = Column(String(10))
    price_min = Column(Integer)
    price_max = Column(Integer)
    
    # Relationships
    profile = relationship("Profile")

class ContactDetail(Base):
    __tablename__ = 'contact_detail'
    __table_args__ = {'schema': 'profile_analytics'}
    
    id = Column(PostgresUUID(as_uuid=True), primary_key=True, server_default='gen_random_uuid()')
    profile_id = Column(PostgresUUID(as_uuid=True), ForeignKey('profile_analytics.profile.id'))
    type = Column(Enum(ContactTypeEnum))
    value = Column(Text)
    
    # Relationships
    profile = relationship("Profile")

class ProfileStatsCurrent(Base):
    __tablename__ = 'profile_stats_current'
    __table_args__ = {'schema': 'profile_analytics'}

    profile_id = Column(PostgresUUID(as_uuid=True), ForeignKey('profile_analytics.profile.id'), primary_key=True)
    follower_count = Column(Integer)
    following_count = Column(Integer)
    average_likes = Column(Integer)
    average_views = Column(Integer)
    average_comments = Column(Integer)
    engagement_rate = Column(Float)
    updated_at = Column(DateTime, default=datetime.utcnow)

    # Relationships
    profile = relationship("Profile")

# Audience analytics tables
class AudienceGenderAge(Base):
    __tablename__ = 'audience_gender_age'
    __table_args__ = (
        UniqueConstraint('profile_id', 'gender', 'age_range'),
        {'schema': 'profile_analytics'}
    )

    id = Column(PostgresUUID(as_uuid=True), primary_key=True, server_default='gen_random_uuid()')
    profile_id = Column(PostgresUUID(as_uuid=True), ForeignKey('profile_analytics.profile.id'))
    gender = Column(Enum(GenderEnum))
    age_range = Column(String(20))
    value = Column(Float)

    # Relationships
    profile = relationship("Profile")

class AudienceLanguage(Base):
    __tablename__ = 'audience_language'
    __table_args__ = (
        UniqueConstraint('profile_id', 'language_code'),
        {'schema': 'profile_analytics'}
    )

    id = Column(PostgresUUID(as_uuid=True), primary_key=True, server_default='gen_random_uuid()')
    profile_id = Column(PostgresUUID(as_uuid=True), ForeignKey('profile_analytics.profile.id'))
    language_code = Column(String(10))
    value = Column(Float)

    # Relationships
    profile = relationship("Profile")

class AudienceLocation(Base):
    __tablename__ = 'audience_location'
    __table_args__ = (
        UniqueConstraint('profile_id', 'geo_id'),
        {'schema': 'profile_analytics'}
    )

    id = Column(PostgresUUID(as_uuid=True), primary_key=True, server_default='gen_random_uuid()')
    profile_id = Column(PostgresUUID(as_uuid=True), ForeignKey('profile_analytics.profile.id'))
    geo_id = Column(PostgresUUID(as_uuid=True), ForeignKey('api_provider.master_location.id'))
    value = Column(Float)

    # Relationships
    profile = relationship("Profile")
    location = relationship("MasterLocation")

class AudienceInterest(Base):
    __tablename__ = 'audience_interest'
    __table_args__ = (
        UniqueConstraint('profile_id', 'interest_id'),
        {'schema': 'profile_analytics'}
    )

    id = Column(PostgresUUID(as_uuid=True), primary_key=True, server_default='gen_random_uuid()')
    profile_id = Column(PostgresUUID(as_uuid=True), ForeignKey('profile_analytics.profile.id'))
    interest_id = Column(PostgresUUID(as_uuid=True), ForeignKey('api_provider.master_interest.id'))
    value = Column(Float)

    # Relationships
    profile = relationship("Profile")
    interest = relationship("MasterInterest")

class AudienceIncome(Base):
    __tablename__ = 'audience_income'
    __table_args__ = (
        UniqueConstraint('profile_id', 'income_range'),
        {'schema': 'profile_analytics'}
    )

    id = Column(PostgresUUID(as_uuid=True), primary_key=True, server_default='gen_random_uuid()')
    profile_id = Column(PostgresUUID(as_uuid=True), ForeignKey('profile_analytics.profile.id'))
    income_range = Column(String(50))
    value = Column(Float)

    # Relationships
    profile = relationship("Profile")

class AudienceDevice(Base):
    __tablename__ = 'audience_device'
    __table_args__ = (
        UniqueConstraint('profile_id', 'device_type'),
        {'schema': 'profile_analytics'}
    )

    id = Column(PostgresUUID(as_uuid=True), primary_key=True, server_default='gen_random_uuid()')
    profile_id = Column(PostgresUUID(as_uuid=True), ForeignKey('profile_analytics.profile.id'))
    device_type = Column(Enum(DeviceTypeEnum))
    value = Column(Float)

    # Relationships
    profile = relationship("Profile")

class AudienceEthnicity(Base):
    __tablename__ = 'audience_ethnicity'
    __table_args__ = (
        UniqueConstraint('profile_id', 'ethnicity_id'),
        {'schema': 'profile_analytics'}
    )

    id = Column(PostgresUUID(as_uuid=True), primary_key=True, server_default='gen_random_uuid()')
    profile_id = Column(PostgresUUID(as_uuid=True), ForeignKey('profile_analytics.profile.id'))
    ethnicity_id = Column(PostgresUUID(as_uuid=True), ForeignKey('api_provider.master_brand.id'))  # Reusing brand table for ethnicity
    value = Column(Float)

    # Relationships
    profile = relationship("Profile")
    ethnicity = relationship("MasterBrand")
