"""
Redis client singleton for CreatorVerse Discovery & Analytics Backend
"""
import asyncio
from typing import Optional, Dict, List, Any, cast, TypeVar

import redis.asyncio as redis

T = TypeVar('T')

from app.core_helper.async_logger import with_trace_id


class RedisClient:
    """Redis client singleton with connection pool management"""
    _instance = None
    _lock = asyncio.Lock()

    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            cls._instance = super(RedisClient, cls).__new__(cls)
        return cls._instance

    def __init__(self, redis_url: Optional[str] = None, logger: Any = None):
        if not hasattr(self, "initialized"):
            self.redis_url = redis_url
            self.logger = logger
            self.redis_client: Optional[redis.Redis] = None
            self.initialized = False

    @with_trace_id
    async def initialize(self) -> None:
        """Initialize Redis connection pool"""
        if self.initialized:
            return

        try:
            if self.logger:
                self.logger.info(f"Initializing Redis connection to {self.redis_url}")
            self.redis_client = redis.from_url(
                self.redis_url,
                decode_responses=True,
                socket_timeout=5,
                socket_connect_timeout=5,
                health_check_interval=30
            )

            # Verify connection
            await self.redis_client.ping()
            if self.logger:
                self.logger.info("Redis connection initialized successfully")
            self.initialized = True
        except Exception as e:
            if self.logger:
                self.logger.error(f"Failed to initialize Redis: {str(e)}")
            raise

    @with_trace_id
    async def get(self, key: str) -> Optional[str]:
        """Get value from Redis"""
        if not self.redis_client:
            return None

        try:
            return await self.redis_client.get(key)
        except Exception as e:
            if self.logger:
                self.logger.error(f"Redis get error: {str(e)}")
            return None

    @with_trace_id
    async def exists(self, key: str) -> bool:
        """Check if key exists in Redis"""
        if not self.redis_client:
            return False

        try:
            result = await self.redis_client.exists(key)  # type: ignore
            return bool(result)
        except Exception as e:
            if self.logger:
                self.logger.error(f"Redis exists error: {str(e)}")
            return False

    @with_trace_id
    async def set(self, key: str, value: str, expire: Optional[int] = None) -> bool:
        """Set value in Redis with optional expiration"""
        if not self.redis_client:
            return False

        try:
            if expire:
                return bool(await self.redis_client.setex(key, expire, value))
            return bool(await self.redis_client.set(key, value))
        except Exception as e:
            if self.logger:
                self.logger.error(f"Redis set error: {str(e)}")
            return False

    @with_trace_id
    async def delete(self, key: str) -> bool:
        """Delete key from Redis"""
        if not self.redis_client:
            return False

        try:
            result = await self.redis_client.delete(key)
            return result > 0
        except Exception as e:
            if self.logger:
                self.logger.error(f"Redis delete error: {str(e)}")
            return False

    @with_trace_id
    async def setex(self, key: str, expire: int, value: str) -> bool:
        """Set value with expiration"""
        if not self.redis_client:
            return False

        try:
            return bool(await self.redis_client.setex(key, expire, value))
        except Exception as e:
            if self.logger:
                self.logger.error(f"Redis setex error: {str(e)}")
            return False

    @with_trace_id
    async def hset(self, name: str, key: str, value: str) -> bool:
        """Set hash field value"""
        if not self.redis_client:
            return False

        try:  # Add type ignore to avoid mypy errors
            result = await self.redis_client.hset(name, key, value)  # type: ignore
            return result > 0
        except Exception as e:
            if self.logger:
                self.logger.error(f"Redis hset error: {str(e)}")
            return False

    @with_trace_id
    async def hset_mapping(self, name: str, mapping: dict[str, str | int]) -> bool:
        if not self.redis_client:
            return False
        try:
            # redis.asyncio.Redis.hset accepts mapping=...
            await self.redis_client.hset(name, mapping=mapping)  # type: ignore
            return True
        except Exception as e:
            self.logger and self.logger.error(f"Redis hset mapping error: {e}")
            return False

    @with_trace_id
    async def hget(self, name: str, key: str) -> Optional[str]:
        """Get hash field value"""
        if not self.redis_client:
            return None

        try:  # Get hash field value
            result = await self.redis_client.hget(name, key)  # type: ignore
            return result
        except Exception as e:
            if self.logger:
                self.logger.error(f"Redis hget error: {str(e)}")
            return None

    @with_trace_id
    async def hgetall(self, name: str) -> Optional[Dict[str, str]]:
        """Get all fields and values in a hash"""
        if not self.redis_client:
            return None

        try:
            # Get all hash fields and values
            result = await self.redis_client.hgetall(name)  # type: ignore
            if result is None:
                return {}
            return cast(Dict[str, str], result)
        except Exception as e:
            if self.logger:
                self.logger.error(f"Redis hgetall error: {str(e)}")
            return None

    @with_trace_id
    async def hdel(self, name: str, *keys: str) -> bool:
        """Delete one or more hash fields"""
        if not self.redis_client:
            return False

        try:
            # Delete hash fields
            result = await self.redis_client.hdel(name, *keys)  # type: ignore
            return result > 0
        except Exception as e:
            if self.logger:
                self.logger.error(f"Redis hdel error: {str(e)}")
            return False

    @with_trace_id
    async def hexists(self, name: str, key: str) -> bool:
        """Check if hash field exists"""
        if not self.redis_client:
            return False

        try:
            result = await self.redis_client.hexists(name, key)  # type: ignore
            return bool(result)
        except Exception as e:
            if self.logger:
                self.logger.error(f"Redis hexists error: {str(e)}")
            return False

    @with_trace_id
    async def incr(self, key: str) -> int:
        """Increment a key by 1"""
        if not self.redis_client:
            return 0
        try:
            return await self.redis_client.incr(key)  # type: ignore
        except Exception as e:
            if self.logger:
                self.logger.error(f"Redis incr error: {str(e)}")
            return 0

    @with_trace_id
    async def expire(self, key: str, seconds: int) -> bool:
        """Set expiration for a key"""
        if not self.redis_client:
            return False
        try:
            return await self.redis_client.expire(key, seconds)  # type: ignore
        except Exception as e:
            if self.logger:
                self.logger.error(f"Redis expire error: {str(e)}")
            return False

    @with_trace_id
    async def delete_keys(self, *keys: str) -> int:
        """Delete multiple keys"""
        if not self.redis_client:
            return 0
        try:
            return await self.redis_client.delete(*keys)  # type: ignore
        except Exception as e:
            if self.logger:
                self.logger.error(f"Redis delete_keys error: {str(e)}")
            return 0

 # end of RedisClient
