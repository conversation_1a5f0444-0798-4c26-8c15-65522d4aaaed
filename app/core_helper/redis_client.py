"""
Redis client singleton for CreatorVerse Analytics Backend
"""
import asyncio
from typing import Optional, Dict, List, Any, cast, TypeVar

import redis.asyncio as redis

T = TypeVar('T')

from app.core_helper.async_logger import with_trace_id


# Configure logger


class RedisClient:
    """Redis client singleton with connection pool management"""
    _instance = None
    _lock = asyncio.Lock()

    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            cls._instance = super(RedisClient, cls).__new__(cls)
        return cls._instance

    def __init__(self, redis_url: Optional[str] = None, logger: Any = None):
        if not hasattr(self, "initialized"):
            self.redis_url = redis_url
            self.logger = logger
            self.redis_client: Optional[redis.Redis] = None
            self.initialized = False

    @with_trace_id
    async def initialize(self) -> None:
        """Initialize Redis connection pool"""
        if self.initialized:
            return

        try:
            if self.logger:
                self.logger.info(f"Initializing Redis connection to {self.redis_url}")
            self.redis_client = redis.from_url(
                self.redis_url,
                decode_responses=True,
                socket_timeout=5,
                socket_connect_timeout=5,
                health_check_interval=30
            )

            # Verify connection
            await self.redis_client.ping()
            if self.logger:
                self.logger.info("Redis connection initialized successfully")
            self.initialized = True
        except Exception as e:
            if self.logger:
                self.logger.error(f"Failed to initialize Redis: {str(e)}")
            raise

    @with_trace_id
    async def get(self, key: str) -> Optional[str]:
        """Get value from Redis"""
        if not self.redis_client:
            return None

        try:
            return await self.redis_client.get(key)
        except Exception as e:
            if self.logger:
                self.logger.error(f"Redis get error: {str(e)}")
            return None

    # here add method getbit

    @with_trace_id
    async def exists(self, key: str) -> bool:
        """Check if key exists in Redis"""
        if not self.redis_client:
            return False

        try:
            result = await self.redis_client.exists(key)  # type: ignore
            return bool(result)
        except Exception as e:
            if self.logger:
                self.logger.error(f"Redis exists error: {str(e)}")
            return False

    @with_trace_id
    async def set(self, key: str, value: str, expire: Optional[int] = None) -> bool:
        """Set value in Redis with optional expiration"""
        if not self.redis_client:
            return False

        try:
            if expire:
                return bool(await self.redis_client.setex(key, expire, value))
            return bool(await self.redis_client.set(key, value))
        except Exception as e:
            if self.logger:
                self.logger.error(f"Redis set error: {str(e)}")
            return False

    @with_trace_id
    async def delete(self, key: str) -> bool:
        """Delete key from Redis"""
        if not self.redis_client:
            return False

        try:
            result = await self.redis_client.delete(key)
            return result > 0
        except Exception as e:
            if self.logger:
                self.logger.error(f"Redis delete error: {str(e)}")
            return False

    @with_trace_id
    async def setex(self, key: str, expire: int, value: str) -> bool:
        """Set value with expiration"""
        if not self.redis_client:
            return False

        try:
            return bool(await self.redis_client.setex(key, expire, value))
        except Exception as e:
            if self.logger:
                self.logger.error(f"Redis setex error: {str(e)}")
            return False

    @with_trace_id
    async def hset(self, name: str, key: str, value: str) -> bool:
        """Set hash field value"""
        if not self.redis_client:
            return False

        try:  # Add type ignore to avoid mypy errors
            result = await self.redis_client.hset(name, key, value)  # type: ignore
            return result > 0
        except Exception as e:
            if self.logger:
                self.logger.error(f"Redis hset error: {str(e)}")
            return False

    @with_trace_id
    async def hset_mapping(self, name: str, mapping: dict[str, str | int]) -> bool:
        if not self.redis_client:
            return False
        try:
            # redis.asyncio.Redis.hset accepts mapping=...
            await self.redis_client.hset(name, mapping=mapping)  # type: ignore
            return True
        except Exception as e:
            self.logger and self.logger.error(f"Redis hset mapping error: {e}")
            return False

    @with_trace_id
    async def hget(self, name: str, key: str) -> Optional[str]:
        """Get hash field value"""
        if not self.redis_client:
            return None

        try:  # Get hash field value
            result = await self.redis_client.hget(name, key)  # type: ignore
            return result
        except Exception as e:
            if self.logger:
                self.logger.error(f"Redis hget error: {str(e)}")
            return None

    @with_trace_id
    async def hgetall(self, name: str) -> Optional[Dict[str, str]]:
        """Get all fields and values in a hash"""
        if not self.redis_client:
            return None

        try:
            # Get all hash fields and values
            result = await self.redis_client.hgetall(name)  # type: ignore
            if result is None:
                return {}
            return cast(Dict[str, str], result)
        except Exception as e:
            if self.logger:
                self.logger.error(f"Redis hgetall error: {str(e)}")
            return None

    @with_trace_id
    async def hdel(self, name: str, *keys: str) -> bool:
        """Delete one or more hash fields"""
        if not self.redis_client:
            return False

        try:
            # Delete hash fields
            result = await self.redis_client.hdel(name, *keys)  # type: ignore
            return result > 0
        except Exception as e:
            if self.logger:
                self.logger.error(f"Redis hdel error: {str(e)}")
            return False

    @with_trace_id
    async def hexists(self, name: str, key: str) -> bool:
        """Check if hash field exists"""
        if not self.redis_client:
            return False
        try:
            # Check if hash field exists
            result = await self.redis_client.hexists(name, key)  # type: ignore
            return bool(result)
        except Exception as e:
            if self.logger:
                self.logger.error(f"Redis hexists error: {str(e)}")
            return False

    @with_trace_id
    async def hincrby(self, name: str, key: str, amount: int = 1) -> int:
        """Increment a hash field by integer"""
        if not self.redis_client:
            return 0
        try:
            return await self.redis_client.hincrby(name, key, amount)
        except Exception as e:
            if self.logger:
                self.logger.error(f"Redis hincrby error: {str(e)}")
            return 0

    @with_trace_id
    async def hmset(self, name: str, mapping: Dict[str, str]) -> bool:
        """Set multiple hash fields"""
        if not self.redis_client:
            return False

        try:
            # Set multiple hash fields using hset with mapping
            result = await self.redis_client.hset(name, mapping=mapping)  # type: ignore
            return result > 0
        except Exception as e:
            if self.logger:
                self.logger.error(f"Redis hmset error: {str(e)}")
            return False

    @with_trace_id
    def pipeline(self):
        """Get Redis pipeline for batch operations"""
        if not self.redis_client:
            return None

        try:
            return self.redis_client.pipeline()
        except Exception as e:
            if self.logger:
                self.logger.error(f"Redis pipeline error: {str(e)}")
            return None

    @with_trace_id
    async def getbit(self, key: str, offset: int) -> int:
        """Get a bit from a Redis string"""
        if not self.redis_client:
            return 0
        try:
            return await self.redis_client.getbit(key, offset)
        except Exception as e:
            if self.logger:
                self.logger.error(f"Redis getbit error: {str(e)}")
            return 0

    @with_trace_id
    async def setbit(self, key: str, offset: int, value: int) -> int:
        """Set a bit in a Redis string"""
        if not self.redis_client:
            return 0
        try:
            return await self.redis_client.setbit(key, offset, value)
        except Exception as e:
            if self.logger:
                self.logger.error(f"Redis setbit error: {str(e)}")
            return 0

    @with_trace_id
    async def hmget(self, name: str, keys: List[str]) -> Optional[List[Optional[str]]]:
        """Get values for multiple hash fields"""
        if not self.redis_client:
            return None

        try:
            # Get values for multiple hash fields
            result = await self.redis_client.hmget(name, keys)  # type: ignore
            return cast(List[Optional[str]], result)
        except Exception as e:
            if self.logger:
                self.logger.error(f"Redis hmget error: {str(e)}")
            return None

    @with_trace_id
    async def expire(self, name: str, time: int) -> bool:
        """Set an expiration time on a key"""
        if not self.redis_client:
            return False

        try:  # Set expiration time on key
            result = await self.redis_client.expire(name, time)  # type: ignore
            return bool(result)
        except Exception as e:
            if self.logger:
                self.logger.error(f"Redis expire error: {str(e)}")
            return False

    @with_trace_id
    async def smembers(self, name: str) -> Optional[List[str]]:
        """Get all members in a set"""
        if not self.redis_client:            return None

        try:
            result = await self.redis_client.smembers(name)  # type: ignore
            return list(result) if result else []
        except Exception as e:
            if self.logger:
                self.logger.error(f"Redis smembers error: {str(e)}")
            return None

    @with_trace_id
    async def sadd(self, name: str, *values) -> bool:
        """Add members to a set"""
        if not self.redis_client:
            return False

        try:
            result = await self.redis_client.sadd(name, *values)  # type: ignore
            return result > 0
        except Exception as e:
            if self.logger:
                self.logger.error(f"Redis sadd error: {str(e)}")
            return False

    @with_trace_id
    async def close(self) -> None:
        """Close Redis connection pool"""
        if self.redis_client:
            try:
                await self.redis_client.close()
                if self.logger:
                    self.logger.info("Redis connection closed successfully")
                self.initialized = False
            except Exception as e:
                if self.logger:
                    self.logger.error(f"Error closing Redis connection: {str(e)}")
                    
    @with_trace_id
    async def delete_keys(self, *keys: str) -> int:
        """Delete multiple keys from Redis."""
        if not self.redis_client:
            return 0

        try:
            # Delete each key individually and count successes
            success_count = 0
            for key in keys:
                result = await self.redis_client.delete(key)
                success_count += result
            return success_count
        except Exception as e:
            if self.logger:
                self.logger.error(f"Redis delete_keys error: {str(e)}")
            return 0

    @with_trace_id
    async def delete_pattern(self, pattern: str) -> int:
        """
        Delete all keys matching the given pattern.
        Uses SCAN + DELETE for efficient deletion of multiple keys.
        
        Args:
            pattern: Pattern to match keys (using Redis pattern matching)
            
        Returns:
            int: Number of keys deleted
        """
        if not self.redis_client:
            return 0
            
        try:
            # Use scan_iter to get all keys matching the pattern
            deleted_count = 0
            async for key in self.redis_client.scan_iter(match=pattern):
                # Delete each key individually
                await self.redis_client.delete(key)
                deleted_count += 1
                
            if self.logger and deleted_count > 0:
                self.logger.debug(f"Deleted {deleted_count} keys matching pattern: {pattern}")
                
            return deleted_count
        except Exception as e:
            if self.logger:
                self.logger.error(f"Redis delete_pattern error: {str(e)}")
            return 0
