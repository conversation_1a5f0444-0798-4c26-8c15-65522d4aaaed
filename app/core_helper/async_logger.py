import asyncio
import contextvars
import json
import linecache
import logging
import os
import sys
import traceback
import uuid
from datetime import datetime
from enum import Enum
from functools import wraps
from typing import Union, Optional, Dict, Any, Callable

from pytz import timezone

# Create a trace_id context variable for propagation across async calls
trace_id_var = contextvars.ContextVar("trace_id", default=None)


def generate_structured_trace_id(
        prefix: str = "",
        include_timestamp: bool = True,
        include_sequence: bool = True,
        environment: str = "",
        request_info: str = ""
) -> str:
    """Generate a structured trace ID with deterministic components.

    Args:
        prefix: Optional service/component identifier
        include_timestamp: Whether to include timestamp component
        include_sequence: Whether to include a sequence number
        environment: Optional environment indicator (dev/prod/etc)
        request_info: Optional request-specific information
    """
    components = []

    # Add prefix if provided
    if prefix:
        components.append(prefix)

    # Add environment if provided
    if environment:
        components.append(environment[:3].lower())

    # Add timestamp component
    if include_timestamp:
        # Format: YYMMDDHHMMSS
        timestamp = datetime.now().strftime("%y%m%d%H%M%S")
        components.append(timestamp)

    # Add sequence number (using a simple counter)
    if include_sequence:
        # Get sequence from a module variable that increments
        components.append(f"{get_next_sequence():04d}")

    # Add request-specific info if provided
    if request_info:
        # Use first 6 chars of hash for brevity
        hash_value = str(hash(request_info))[-6:]
        components.append(hash_value)

    # Add random component for uniqueness
    random_part = str(uuid.uuid4())[:8]
    components.append(random_part)

    return "-".join(components)


# Sequence counter for trace IDs
_trace_sequence_counter = 0


def get_next_sequence() -> int:
    """Get next sequence number for trace IDs."""
    global _trace_sequence_counter
    _trace_sequence_counter += 1
    # Reset if it gets too large
    if _trace_sequence_counter > 9999:
        _trace_sequence_counter = 1
    return _trace_sequence_counter


def get_trace_id(generate_if_none: bool = True, **trace_id_args) -> Optional[str]:
    """Get current trace ID or generate a new one with specified parameters.

    Args:
        generate_if_none: Whether to generate a new ID if none exists
        **trace_id_args: Arguments to pass to generate_structured_trace_id
    """
    current = trace_id_var.get()
    if current is None and generate_if_none:
        # Use structured trace ID generation
        current = generate_structured_trace_id(**trace_id_args)
        trace_id_var.set(current)
    return current


def set_trace_id(trace_id: str) -> contextvars.Token:
    """Set trace ID and return token for resetting."""
    return trace_id_var.set(trace_id)


def reset_trace_id(token: contextvars.Token) -> None:
    """Reset trace ID using token from set_trace_id."""
    trace_id_var.reset(token)


def time_tz(*args) -> datetime.timetuple:
    """Get the current time in the Asia/Kolkata timezone."""
    return datetime.now(timezone('Asia/Kolkata')).timetuple()


logging.Formatter.converter = time_tz


class LogType(Enum):
    JSON = "json"
    CONSOLE = "console"
    STRUCTURED = "structured"


class LogLevel(Enum):
    DEBUG = logging.DEBUG
    INFO = logging.INFO
    WARNING = logging.WARNING
    ERROR = logging.ERROR
    CRITICAL = logging.CRITICAL


class JsonFormatter(logging.Formatter):
    def __init__(self, include_extra: bool = True, custom_fields: Dict[str, Any] = None):
        super().__init__()
        self.include_extra = include_extra
        self.custom_fields = custom_fields or {}

    def format(self, record):
        log_record = {
            "level": record.levelname,
            "time": self.formatTime(record, self.datefmt),
            "message": record.getMessage(),
            "logger": record.name,
            **self.custom_fields
        }

        # Add trace_id from context if available and not already in the record
        if not hasattr(record, 'trace_id'):
            trace_id = trace_id_var.get()
            if trace_id:
                log_record["trace_id"] = trace_id

        # Add extra fields from record
        if self.include_extra:
            for key, value in record.__dict__.items():
                if key not in ['name', 'msg', 'args', 'levelname', 'levelno', 'pathname',
                               'filename', 'module', 'lineno', 'funcName', 'created', 'msecs',
                               'relativeCreated', 'thread', 'threadName', 'processName',
                               'process', 'getMessage', 'exc_info', 'exc_text', 'stack_info']:
                    log_record[key] = value

        if record.exc_info:
            log_record["exception"] = self.formatException(record.exc_info)

        return json.dumps(log_record, ensure_ascii=False, default=str)


class StructuredFormatter(logging.Formatter):
    def format(self, record):
        trace_info = f"[trace_id={get_trace_id()}]" if trace_id_var.get() else ""
        return f"[{record.levelname}] {self.formatTime(record)} {trace_info} | {record.name} | {record.getMessage()}"


class FastAPILogger:
    """Enhanced logger for FastAPI applications with advanced customization."""

    def __init__(self,
                 service_name: str,
                 sys_module,
                 log_level: Union[int, str, LogLevel] = LogLevel.INFO,
                 log_type: Union[str, LogType] = LogType.JSON,
                 include_request_id: bool = True,
                 custom_fields: Dict[str, Any] = {},
                 log_file: Optional[str] = None,
                 max_bytes: int = 10485760,  # 10MB
                 backup_count: int = 5,
                 timezone_name: str = 'Asia/Kolkata',
                 trace_id_prefix: str = "",
                 trace_id_include_env: bool = True,
                 environment: str = ""):

        self.service_name = service_name
        self.log_level = self._normalize_log_level(log_level)
        self.log_type = log_type if isinstance(log_type, LogType) else LogType(log_type)
        self.include_request_id = include_request_id
        self.custom_fields = custom_fields or {}
        self.timezone_name = timezone_name
        # Store trace ID configuration
        self.trace_id_prefix = trace_id_prefix or service_name[:3].upper()
        self.trace_id_include_env = trace_id_include_env
        self.environment = environment or os.getenv('ENVIRONMENT', 'dev')
        # Set timezone for logging
        self._filters = []  # Initialize filters before setup_logger
        self._sys_module = sys_module
        self._logger = self._setup_logger(log_file, max_bytes, backup_count)

    def _normalize_log_level(self, log_level: Union[int, str, LogLevel]) -> int:
        """Normalize different log level inputs to integer."""
        if isinstance(log_level, LogLevel):
            return log_level.value
        elif isinstance(log_level, str):
            return getattr(logging, log_level.upper(), logging.INFO)
        return log_level

    def _setup_logger(self, log_file: Optional[str], max_bytes: int, backup_count: int) -> logging.Logger:
        """Setup logger with handlers and formatters."""
        logger = logging.getLogger(self.service_name)

        # Clear existing handlers to avoid duplicates
        logger.handlers.clear()
        logger.setLevel(self.log_level)

        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(self.log_level)
        console_handler.setFormatter(self._get_formatter())
        logger.addHandler(console_handler)
        # Set up exception handling
        self._sys_module.excepthook = create_custom_exception_handler(logger, self._sys_module)
        # File handler (optional)
        if log_file:
            from logging.handlers import RotatingFileHandler
            file_handler = RotatingFileHandler(
                log_file, maxBytes=max_bytes, backupCount=backup_count
            )
            file_handler.setLevel(self.log_level)
            file_handler.setFormatter(self._get_formatter())
            logger.addHandler(file_handler)

        # Add custom filters
        for filter_func in self._filters:
            logger.addFilter(filter_func)

        return logger

    def _get_formatter(self) -> logging.Formatter:
        """Get appropriate formatter based on log type."""
        formatters = {
            LogType.JSON: JsonFormatter(custom_fields=self.custom_fields),
            LogType.CONSOLE: logging.Formatter(
                '%(levelname)s - %(asctime)s - [trace_id=%(trace_id)s] - %(name)s - %(message)s',
                datefmt='%Y-%m-%dT%H:%M:%S'
            ),
            LogType.STRUCTURED: StructuredFormatter(datefmt='%Y-%m-%dT%H:%M:%S')
        }
        return formatters.get(self.log_type, formatters[LogType.JSON])

    def add_filter(self, filter_func: Callable[[logging.LogRecord], bool]):
        """Add custom log filter."""
        self._filters.append(filter_func)
        self._logger.addFilter(filter_func)

    def set_context(self, **context):
        """Set context fields for logging."""
        self.custom_fields.update(context)

    def _log(self, level: int, msg: str, *args, **kwargs):
        """Internal logging method with context injection."""
        extra = kwargs.get('extra', {})

        # Always include trace_id in extra if available
        trace_id = trace_id_var.get()
        if trace_id and 'trace_id' not in extra:
            extra['trace_id'] = trace_id

        extra.update(self.custom_fields)
        kwargs['extra'] = extra
        self._logger.log(level, msg, *args, **kwargs)

    # Synchronous logging methods
    def debug(self, msg: str, *args, **kwargs):
        self._log(logging.DEBUG, msg, *args, **kwargs)

    def info(self, msg: str, *args, **kwargs):
        self._log(logging.INFO, msg, *args, **kwargs)

    def warning(self, msg: str, *args, **kwargs):
        self._log(logging.WARNING, msg, *args, **kwargs)

    def error(self, msg: str, *args, **kwargs):
        if 'exc_info' not in kwargs:
            kwargs['exc_info'] = True
        self._log(logging.ERROR, msg, *args, **kwargs)

    def critical(self, msg: str, *args, **kwargs):
        self._log(logging.CRITICAL, msg, *args, **kwargs)

    # Async logging methods - simplified without unnecessary thread pool
    async def _async_log(self, sync_method: Callable, msg: str, *args, **kwargs):
        """Generic async logging method to eliminate duplication."""
        # For logging, we don't need thread pool execution as it's usually fast
        # Only use thread pool for potentially blocking operations
        if kwargs.get('_use_executor', False):
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(None, sync_method, msg, *args)
        else:
            sync_method(msg, *args, **kwargs)

    async def adebug(self, msg: str, *args, **kwargs):
        await self._async_log(self.debug, msg, *args, **kwargs)

    async def ainfo(self, msg: str, *args, **kwargs):
        await self._async_log(self.info, msg, *args, **kwargs)

    async def awarning(self, msg: str, *args, **kwargs):
        await self._async_log(self.warning, msg, *args, **kwargs)

    async def aerror(self, msg: str, *args, **kwargs):
        await self._async_log(self.error, msg, *args, **kwargs)

    async def acritical(self, msg: str, *args, **kwargs):
        await self._async_log(self.critical, msg, *args, **kwargs)

    # Convenience methods for structured logging
    def log_request(self, method: str, url: str, status_code: int = None,
                    duration: float = None, **extra):
        """Log HTTP request with structured data."""
        data = {"method": method, "url": str(url)}
        if status_code:
            data["status_code"] = status_code
        if duration:
            data["duration_ms"] = round(duration * 1000, 2)

        # Add trace_id if not already in extra
        trace_id = trace_id_var.get()
        if trace_id and 'trace_id' not in extra:
            data["trace_id"] = trace_id

        data.update(extra)
        self.debug("HTTP Request", extra=data)

    def log_error_with_context(self, error: Exception, context: Dict[str, Any] = None):
        """Log error with additional context."""
        error_data = {
            "error_type": type(error).__name__,
            "error_message": str(error),
            "traceback": traceback.format_exc()
        }

        # Add trace_id if not already in context
        trace_id = trace_id_var.get()
        if trace_id and (context is None or 'trace_id' not in context):
            error_data["trace_id"] = trace_id

        if context:
            error_data.update(context)
        self.error("Error occurred", extra=error_data)

    def log_performance(self, operation: str, duration: float, **metrics):
        """Log performance metrics."""
        perf_data = {
            "operation": operation,
            "duration_ms": round(duration * 1000, 2),
        }

        # Add trace_id if available
        trace_id = trace_id_var.get()
        if trace_id:
            perf_data["trace_id"] = trace_id

        perf_data.update(metrics)
        self.info("Performance metric", extra=perf_data)


# Decorator for automatic request logging
def log_execution_time(logger: FastAPILogger, operation_name: str = None):
    """Decorator to log function execution time."""

    def decorator(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            # Preserve existing trace_id or create new one
            current_trace_id = trace_id_var.get()
            if current_trace_id is None:
                current_trace_id = str(uuid.uuid4())
                token = trace_id_var.set(current_trace_id)

            start_time = datetime.now()
            op_name = operation_name or f"{func.__module__}.{func.__name__}"
            try:
                result = await func(*args, **kwargs)
                duration = (datetime.now() - start_time).total_seconds()
                logger.log_performance(op_name, duration, status="success")
                return result
            except Exception as e:
                duration = (datetime.now() - start_time).total_seconds()
                logger.log_performance(op_name, duration, status="error")
                logger.log_error_with_context(e, {"operation": op_name})
                raise

        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            # Preserve existing trace_id or create new one
            current_trace_id = trace_id_var.get()
            if current_trace_id is None:
                current_trace_id = str(uuid.uuid4())
                token = trace_id_var.set(current_trace_id)

            start_time = datetime.now()
            op_name = operation_name or f"{func.__module__}.{func.__name__}"
            try:
                result = func(*args, **kwargs)
                duration = (datetime.now() - start_time).total_seconds()
                logger.log_performance(op_name, duration, status="success")
                return result
            except Exception as e:
                duration = (datetime.now() - start_time).total_seconds()
                logger.log_performance(op_name, duration, status="error")
                logger.log_error_with_context(e, {"operation": op_name})
                raise

        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper

    return decorator


# Decorator for trace ID propagation
def with_trace_id(func):
    """Decorator to ensure trace_id is available and propagated."""

    @wraps(func)
    async def async_wrapper(*args, **kwargs):
        # Get trace ID from kwargs if present, otherwise use current or generate new
        if 'trace_id' in kwargs:
            trace_id = kwargs.pop('trace_id')
            token = trace_id_var.set(trace_id)
        else:
            # Use existing or create new
            trace_id = trace_id_var.get()
            if trace_id is None:
                trace_id = str(uuid.uuid4())
                token = trace_id_var.set(trace_id)
            else:
                token = None  # No need to reset

        try:
            return await func(*args, **kwargs)
        finally:
            # Only reset if we set a new value
            if token is not None:
                trace_id_var.reset(token)

    @wraps(func)
    def sync_wrapper(*args, **kwargs):
        # Get trace ID from kwargs if present, otherwise use current or generate new
        if 'trace_id' in kwargs:
            trace_id = kwargs.pop('trace_id')
            token = trace_id_var.set(trace_id)
        else:
            # Use existing or create new
            trace_id = trace_id_var.get()
            if trace_id is None:
                trace_id = str(uuid.uuid4())
                token = trace_id_var.set(trace_id)
            else:
                token = None  # No need to reset

        try:
            return func(*args, **kwargs)
        finally:
            # Only reset if we set a new value
            if token is not None:
                trace_id_var.reset(token)

    return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper


def create_custom_exception_handler(logger, sys_module):
    def handler(exc_type, exc_value, exc_traceback):
        if issubclass(exc_type, KeyboardInterrupt):
            sys_module.__excepthook__(exc_type, exc_value, exc_traceback)
            return

        tb = ''.join(traceback.format_exception(exc_type, exc_value, exc_traceback))
        error_data = {
            "type": exc_type.__name__,
            "message": str(exc_value),
            "traceback": tb
        }

        logger.error(error_data)

    return handler
