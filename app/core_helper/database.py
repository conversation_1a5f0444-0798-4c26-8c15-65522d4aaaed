import asyncio
import logging
import traceback
from contextlib import asynccontextmanager
from typing import <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>, Optional

from sqlalchemy import MetaD<PERSON>
from sqlalchemy.engine import make_url
from sqlalchemy.ext.asyncio import (
    AsyncEngine,
    AsyncSession,
    async_sessionmaker,
    create_async_engine,
)
from sqlalchemy.sql import text

from app.core_helper.async_logger import with_trace_id

MAX_CONNECTIONS = 5
MIN_CONNECTIONS = 1

model_mapping = {}


class AsyncDatabaseDB:
    _instance = None
    _lock = asyncio.Lock()

    def __new__(cls, *args, **kwargs):
        # Note: Use get_instance() method for proper async singleton
        if cls._instance is None:
            cls._instance = super(AsyncDatabaseDB, cls).__new__(cls)
        return cls._instance

    @classmethod
    async def get_instance(cls, connection_string=None, *args, **kwargs):
        """Async singleton pattern with proper locking"""
        if cls._instance is None:
            async with cls._lock:
                if cls._instance is None:
                    cls._instance = super(AsyncDatabaseDB, cls).__new__(cls)
                    # FIXED: Pass connection_string to __init__
                    await cls._instance.__init_async__(connection_string, *args, **kwargs)
        return cls._instance

    def __init__(self, connection_string=None,
                 pool_size=MIN_CONNECTIONS, max_overflow=MAX_CONNECTIONS, logger=None):
        if not hasattr(self, "initialized"):
            self.connection_string = connection_string
            self.pool_size = pool_size
            self.max_overflow = max_overflow
            self.engine: Optional[AsyncEngine] = None
            self.SessionLocal = None
            self.tables = {}
            self.engines: Dict[str, AsyncEngine] = {}
            self.session_makers: Dict[str, async_sessionmaker] = {}
            self.current_db = None  # Will be set after connection string validation
            self.initialized = False  # FIXED: Set to False initially
            self.logger = logger if logger is not None else self._create_default_logger()
            self._metadata_cache: Dict[str, MetaData] = {}

    async def __init_async__(self, connection_string=None,
                             pool_size=MIN_CONNECTIONS, max_overflow=MAX_CONNECTIONS, logger=None):
        """Async initialization method"""
        if not hasattr(self, "initialized") or not self.initialized:
            self.connection_string = connection_string
            self.pool_size = pool_size
            self.max_overflow = max_overflow
            self.engine: Optional[AsyncEngine] = None
            self.SessionLocal = None
            self.tables = {}
            self.engines: Dict[str, AsyncEngine] = {}
            self.session_makers: Dict[str, async_sessionmaker] = {}
            self.logger = logger if logger is not None else self._create_default_logger()
            self._metadata_cache: Dict[str, MetaData] = {}

            # FIXED: Extract database name after setting connection string
            self.current_db = self.extract_dbname()
            self.initialized = True

    def _create_default_logger(self):
        """Create a default logger if none provided"""
        logger = logging.getLogger(__name__)
        logger.setLevel(logging.INFO)
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        return logger

    def extract_dbname(self) -> Optional[str]:
        """Extract database name from connection string."""
        if not self.connection_string:
            self.logger.warning("No connection string provided")
            return None

        try:
            url = make_url(self.connection_string)
            db_name = url.database
            if not db_name:
                self.logger.warning("No database name found in connection string")
            return db_name
        except Exception as e:
            self.logger.error(f"Failed to extract database name: {e}")
            return None

    def set_connection_string(self, connection_string: str):
        """Method to set connection string after initialization"""
        self.connection_string = connection_string
        self.current_db = self.extract_dbname()
        # Reset engine to force re-initialization with new connection string
        self.engine = None
        self.SessionLocal = None

    @with_trace_id
    async def initialize(self) -> None:
        """Initialize database connection pool and session factory."""
        if self.engine is not None:
            self.logger.info("Database already initialized")
            return

        if not self.connection_string:
            raise ValueError(
                "Connection string is required for database initialization. Use set_connection_string() method.")

        try:
            self.logger.info(f"Initializing database connection pool with {self.pool_size} connections")

            # Validate connection string format
            try:
                url = make_url(self.connection_string)
                self.logger.info(
                    f"Connecting to database: {url.drivername}://{url.username}@{url.host}:{url.port}/{url.database}")
            except Exception as e:
                raise ValueError(f"Invalid connection string format: {e}")

            # Create async engine
            self.engine = create_async_engine(
                self.connection_string,
                pool_size=self.pool_size,
                max_overflow=self.max_overflow,
                pool_pre_ping=True,
                pool_recycle=3600,  # Recycle connections after 1 hour
                echo=False  # Set to True for SQL debugging
            )

            # Create session factory
            self.SessionLocal = async_sessionmaker(
                autocommit=False,
                autoflush=False,
                bind=self.engine,
                expire_on_commit=False
            )

            self.logger.info("Database connection pool initialized successfully")

            # Add to engines cache
            if self.current_db:
                self.engines[self.current_db] = self.engine
                self.session_makers[self.current_db] = self.SessionLocal

            # Verify connection
            async with self.engine.begin() as conn:
                result = await conn.execute(text("SELECT 1"))
                self.logger.info("Database connection verified successfully")

        except Exception as e:
            self.logger.error(f"Failed to initialize database: {str(e)}\n{traceback.format_exc()}")
            # Clean up on failure
            if self.engine:
                await self.engine.dispose()
                self.engine = None
                self.SessionLocal = None
            raise

    @asynccontextmanager
    async def get_db(self) -> AsyncGenerator[AsyncSession, None]:
        """Provide a database session for dependency injection."""
        # FIXED: Always ensure initialization before use
        if self.engine is None or self.SessionLocal is None:
            await self.initialize()

        if self.SessionLocal is None:
            raise RuntimeError("Database not properly initialized - SessionLocal is None")

        session: AsyncSession = self.SessionLocal()
        try:
            yield session
            # FIXED: Commit successful transactions
            await session.commit()
        except Exception as e:
            # FIXED: Rollback on error
            await session.rollback()
            self.logger.error(f"Database session error: {e}")
            raise
        finally:
            await session.close()

    @with_trace_id
    async def execute_sql(self, sql: str, parameters: Optional[Dict] = None) -> bool:
        """Execute raw SQL statement with optional parameters."""
        if self.engine is None:
            await self.initialize()

        if self.engine is None:
            raise RuntimeError("Database engine not initialized")

        async with self.engine.begin() as conn:
            try:
                if parameters:
                    await conn.execute(text(sql), parameters)
                else:
                    await conn.execute(text(sql))
                return True
            except Exception as e:
                self.logger.error(f"Failed to execute SQL: {str(e)}\nSQL: {sql}\nParameters: {parameters}")
                return False

    @with_trace_id
    async def test_connection(self) -> bool:
        """Test database connection"""
        try:
            if self.engine is None:
                await self.initialize()

            async with self.engine.begin() as conn:
                await conn.execute(text("SELECT 1"))
                return True
        except Exception as e:
            self.logger.error(f"Connection test failed: {e}")
            return False

    @with_trace_id
    async def shutdown(self) -> None:
        """Graceful shutdown of all connections"""
        try:
            if self.engine:
                await self.engine.dispose()
                self.logger.info("Database engine disposed successfully")
                self.engine = None
                self.SessionLocal = None

            # Clear caches
            self.engines.clear()
            self.session_makers.clear()
            self._metadata_cache.clear()

            self.logger.info("Database shutdown completed successfully")
        except Exception as e:
            self.logger.error(f"Error during database shutdown: {str(e)}")


# FIXED: Don't create instance at module level - let users control initialization
# db = AsyncDatabaseDB()


# FIXED: Updated function to work with proper initialization
async def get_db_instance(connection_string: str = None) -> AsyncDatabaseDB:
    """Get or create database instance with connection string"""
    db_instance = await AsyncDatabaseDB.get_instance(connection_string)
    return db_instance


async def get_db(connection_string: str = None) -> AsyncGenerator[AsyncSession, None]:
    """Function to get DB session for dependency injection"""
    db_instance = await get_db_instance(connection_string)
    async with db_instance.get_db() as session:
        yield session


# Example usage:
"""
# Method 1: Using get_instance with connection string
db = await AsyncDatabaseDB.get_instance("postgresql+asyncpg://user:pass@localhost/dbname")

# Method 2: Create instance and set connection string later
db = AsyncDatabaseDB()
db.set_connection_string("postgresql+asyncpg://user:pass@localhost/dbname")
await db.initialize()

# Method 3: Using the helper function
async with get_db("postgresql+asyncpg://user:pass@localhost/dbname") as session:
    # Use session here
    pass
"""
