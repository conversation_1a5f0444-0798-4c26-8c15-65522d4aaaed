from pydantic_settings import BaseSettings
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

class Settings(BaseSettings):
    # API settings
    API_PORT: int = 8002
    API_HOST: str = "0.0.0.0"
    DEBUG: bool = False

    # Phyllo API settings
    PHYLLO_API_VERSION: str = "v1"
    PHYLLO_ENVIRONMENT: str = "sandbox"

    # Database settings
    DATABASE_URL: str = "****************************************************************"
    DATABASE_POOL_SIZE: int = 10
    DATABASE_MAX_OVERFLOW: int = 20
    DATABASE_POOL_TIMEOUT: int = 30
    DATABASE_POOL_RECYCLE: int = 3600

    # Database mode: "memory" for in-memory, "postgres" for PostgreSQL
    DATABASE_MODE: str = "postgres"

    # Bulk insert settings
    BULK_INSERT_BATCH_SIZE: int = 50
    BULK_INSERT_MAX_PROFILES: int = 10000

    class Config:
        case_sensitive = True
        env_file = ".env"


settings = Settings()
