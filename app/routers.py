from fastapi import APIRouter, HTTPException, Query, Path, Depends, Body
from typing import List, Optional, Dict, Any
from uuid import uuid4
from datetime import datetime
from .models import <PERSON>yllo<PERSON>ser, PhylloAccount, PhylloData
from .database import get_db, get_platforms
from .webhooks import WebhookRegistration, WebhookEvent, WebhookTestRequest
import httpx
import asyncio
import json

# Create router with API prefix
router = APIRouter(prefix="/api/v1")


@router.post("/users", response_model=PhylloUser, status_code=201)
async def create_user(user: PhylloUser, db: Dict = Depends(get_db)):
    """Create a new Phyllo user."""
    if user.id in db["users"]:
        raise HTTPException(status_code=400, detail="User already exists")
    
    db["users"][user.id] = user.model_dump()
    return user


@router.get("/users", response_model=List[PhylloUser])
async def get_users(
    status: Optional[str] = Query(None, description="Filter users by status"),
    db: Dict = Depends(get_db)
):
    """Get all users, with optional status filter."""
    if status:
        return [user for user in db["users"].values() if user["status"] == status]
    return list(db["users"].values())


@router.get("/users/{user_id}", response_model=PhylloUser)
async def get_user(
    user_id: str = Path(..., description="The ID of the user"),
    db: Dict = Depends(get_db)
):
    """Get a specific user by ID."""
    if user_id not in db["users"]:
        raise HTTPException(status_code=404, detail="User not found")
    return db["users"][user_id]


@router.post("/accounts", response_model=PhylloAccount, status_code=201)
async def create_account(account: PhylloAccount, db: Dict = Depends(get_db)):
    """Create a new connected account."""
    if account.id in db["accounts"]:
        raise HTTPException(status_code=400, detail="Account already exists")
    
    if account.user_id not in db["users"]:
        raise HTTPException(status_code=404, detail="User not found")
    
    db["accounts"][account.id] = account.model_dump()
    return account


@router.get("/accounts", response_model=List[PhylloAccount])
async def get_accounts(
    user_id: Optional[str] = Query(None, description="Filter accounts by user ID"),
    db: Dict = Depends(get_db)
):
    """Get all accounts, with optional user_id filter."""
    if user_id:
        return [account for account in db["accounts"].values() if account["user_id"] == user_id]
    return list(db["accounts"].values())


@router.get("/accounts/{account_id}", response_model=PhylloAccount)
async def get_account(
    account_id: str = Path(..., description="The ID of the account"),
    db: Dict = Depends(get_db)
):
    """Get a specific account by ID."""
    if account_id not in db["accounts"]:
        raise HTTPException(status_code=404, detail="Account not found")
    return db["accounts"][account_id]


@router.post("/data", response_model=PhylloData, status_code=201)
async def create_data(data: PhylloData, db: Dict = Depends(get_db)):
    """Add new data for an account."""
    if data.id in db["data"]:
        raise HTTPException(status_code=400, detail="Data entry already exists")
    
    if data.account_id not in db["accounts"]:
        raise HTTPException(status_code=404, detail="Account not found")
    
    db["data"][data.id] = data.model_dump()
    return data


@router.get("/data", response_model=List[PhylloData])
async def get_data(
    account_id: Optional[str] = Query(None, description="Filter data by account ID"),
    db: Dict = Depends(get_db)
):
    """Get all data, with optional account_id filter."""
    if account_id:
        return [data for data in db["data"].values() if data["account_id"] == account_id]
    return list(db["data"].values())


@router.get("/data/{data_id}", response_model=PhylloData)
async def get_data_entry(
    data_id: str = Path(..., description="The ID of the data entry"),
    db: Dict = Depends(get_db)
):
    """Get a specific data entry by ID."""
    if data_id not in db["data"]:
        raise HTTPException(status_code=404, detail="Data entry not found")
    return db["data"][data_id]


@router.get("/platforms", response_model=List[Dict[str, Any]])
async def get_available_platforms():
    """Get all available platforms."""
    return get_platforms()


# Global storage for registered webhooks
webhook_registry = []


@router.post("/webhooks", response_model=Dict[str, Any], status_code=201)
async def register_webhook(webhook: WebhookRegistration):
    """Register a new webhook endpoint."""
    webhook_id = f"webhook_{uuid4().hex[:8]}"
    
    webhook_entry = {
        "id": webhook_id,
        "url": str(webhook.url),
        "description": webhook.description,
        "events": webhook.events,
        "active": webhook.active,
        "created_at": datetime.now().isoformat()
    }
    
    webhook_registry.append(webhook_entry)
    return {
        "id": webhook_id,
        "message": "Webhook registered successfully",
        "webhook": webhook_entry
    }


@router.get("/webhooks", response_model=List[Dict[str, Any]])
async def list_webhooks():
    """List all registered webhooks."""
    return webhook_registry


@router.post("/webhooks/test", response_model=Dict[str, Any])
async def test_webhook(test_req: WebhookTestRequest):
    """Send a test event to a webhook URL."""
    event_data = {
        "event_type": test_req.event_type,
        "resource_type": test_req.resource_type,
        "resource_id": test_req.resource_id,
        "action": test_req.action,
        "timestamp": datetime.now().isoformat(),
        "data": {
            "message": "This is a test webhook event",
            "test": True
        }
    }
    
    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(
                str(test_req.url),
                json=event_data,
                headers={"Content-Type": "application/json"}
            )
            
            return {
                "success": response.status_code >= 200 and response.status_code < 300,
                "status_code": response.status_code,
                "message": "Test webhook sent",
                "event": event_data,
                "response": response.text
            }
    except Exception as e:
        return {
            "success": False,
            "message": f"Failed to send test webhook: {str(e)}",
            "event": event_data
        }


@router.post("/users/bulk", response_model=Dict[str, Any])
async def create_users_bulk(
    users: List[PhylloUser] = Body(..., description="List of users to create"),
    db: Dict = Depends(get_db)
):
    """Create multiple users at once."""
    results = {
        "successful": [],
        "failed": []
    }
    
    for user in users:
        try:
            if user.id in db["users"]:
                results["failed"].append({
                    "user": user.model_dump(),
                    "error": "User already exists"
                })
                continue
                
            db["users"][user.id] = user.model_dump()
            results["successful"].append(user.model_dump())
        except Exception as e:
            results["failed"].append({
                "user": user.model_dump(),
                "error": str(e)
            })
    
    return {
        "summary": {
            "total": len(users),
            "successful": len(results["successful"]),
            "failed": len(results["failed"])
        },
        "results": results
    }


@router.post("/accounts/bulk", response_model=Dict[str, Any])
async def create_accounts_bulk(
    accounts: List[PhylloAccount] = Body(..., description="List of accounts to create"),
    db: Dict = Depends(get_db)
):
    """Create multiple accounts at once."""
    results = {
        "successful": [],
        "failed": []
    }
    
    for account in accounts:
        try:
            if account.id in db["accounts"]:
                results["failed"].append({
                    "account": account.model_dump(),
                    "error": "Account already exists"
                })
                continue
                
            if account.user_id not in db["users"]:
                results["failed"].append({
                    "account": account.model_dump(),
                    "error": "User not found"
                })
                continue
                
            db["accounts"][account.id] = account.model_dump()
            results["successful"].append(account.model_dump())
        except Exception as e:
            results["failed"].append({
                "account": account.model_dump(),
                "error": str(e)
            })
    
    return {
        "summary": {
            "total": len(accounts),
            "successful": len(results["successful"]),
            "failed": len(results["failed"])
        },
        "results": results
    }


@router.get("/metrics", response_model=Dict[str, Any])
async def get_metrics(db: Dict = Depends(get_db)):
    """Get overall metrics for the API."""
    # Platform distribution for users
    platform_stats = {}
    for user in db["users"].values():
        platform = user.get("platform")
        if platform:
            platform_stats[platform] = platform_stats.get(platform, 0) + 1
    
    # Status distribution
    user_status_stats = {}
    for user in db["users"].values():
        status = user["status"]
        user_status_stats[status] = user_status_stats.get(status, 0) + 1
    
    account_status_stats = {}
    for account in db["accounts"].values():
        status = account["status"]
        account_status_stats[status] = account_status_stats.get(status, 0) + 1
    
    # Data type distribution
    data_type_stats = {}
    for data in db["data"].values():
        data_type = data["data_type"]
        data_type_stats[data_type] = data_type_stats.get(data_type, 0) + 1
    
    return {
        "total_users": len(db["users"]),
        "total_accounts": len(db["accounts"]),
        "total_data_entries": len(db["data"]),
        "platform_distribution": platform_stats,
        "user_status_distribution": user_status_stats,
        "account_status_distribution": account_status_stats,
        "data_type_distribution": data_type_stats,
    }


@router.get("/search", response_model=Dict[str, List])
async def search_resources(
    query: str = Query(..., description="Search query string"),
    resource_type: Optional[str] = Query(None, description="Resource type to search (users, accounts, data)"),
    db: Dict = Depends(get_db)
):
    """
    Search across users, accounts, and data.
    If resource_type is provided, only search within that resource type.
    """
    results = {
        "users": [],
        "accounts": [],
        "data": []
    }
    
    query = query.lower()
    
    # Search in users
    if resource_type is None or resource_type == "users":
        for user in db["users"].values():
            if (query in user["name"].lower() or 
                query in user["email"].lower() or 
                (user["platform"] and query in user["platform"].lower())):
                results["users"].append(user)
    
    # Search in accounts
    if resource_type is None or resource_type == "accounts":
        for account in db["accounts"].values():
            if (query in account["id"].lower() or 
                query in account["user_id"].lower() or 
                query in account["platform_id"].lower() or 
                query in account["status"].lower()):
                results["accounts"].append(account)
    
    # Search in data
    if resource_type is None or resource_type == "data":
        for data in db["data"].values():
            if (query in data["id"].lower() or 
                query in data["account_id"].lower() or 
                query in data["data_type"].lower() or 
                query in str(data["content"]).lower()):
                results["data"].append(data)
    
    return results
