"""
OAuth flow manager for CreatorVerse Backend.
Handles state generation, storage, and validation for OAuth flows.
"""
import secrets
from typing import Dict, Op<PERSON>, Tuple
from urllib.parse import urlencode

from app.core.config import APP_CONFIG
from app.core.logger import get_logger
from app.core.redis_keys import RedisKeys
from app.core_helper.async_logger import with_trace_id
from app.schemas.oauth_schemas import OAuthCallbackPayload

logger = get_logger()

# In-memory store as fallback when Redis is unavailable
_memory_state_store: Dict[str, Dict] = {}


class OAuthFlowManager:
    """
    Manages OAuth flows including state generation, storage, and validation.
    Implements fallback to in-memory store when <PERSON><PERSON> is unavailable.
    """

    def __init__(self, redis_client):
        self.redis_client = redis_client
        self.ttl = 300  # 5 minutes TTL for state

    @with_trace_id
    async def generate_state(self) -> str:
        """Generate a cryptographically secure random state token"""
        return secrets.token_urlsafe(32)

    @with_trace_id
    async def store_state(self, state: str, provider: str, role_uuid: str) -> bool:
        """
        Store OAuth state data in Redis with fallback to memory store
        
        Args:
            state: Random state token
            provider: OAuth provider (google, facebook, instagram)
            role_uuid: Role UUID for the user
        
        Returns:
            bool: True if successful
        """
        state_data = {
            "provider": provider,
            "role_uuid": role_uuid
        }
        
        # Try to store in Redis first
        key = RedisKeys.oauth_state(state)
        try:
            await self.redis_client.hset_mapping(key, state_data)
            await self.redis_client.expire(key, self.ttl)
            return True
        except Exception as e:
            logger.warning(f"Redis error storing OAuth state, using memory fallback: {e}")
            # Fallback to memory store
            _memory_state_store[state] = state_data
            return True

    @with_trace_id
    async def get_and_delete_state(self, state: str) -> Optional[OAuthCallbackPayload]:
        """
        Get and delete the OAuth state data from Redis or memory store
        
        Args:
            state: State token to retrieve
            
        Returns:
            Optional[OAuthCallbackPayload]: State data if found, None otherwise
        """
        key = RedisKeys.oauth_state(state)
        
        # Try Redis first
        try:
            data = await self.redis_client.hgetall(key)
            if data:
                await self.redis_client.delete(key)
                return OAuthCallbackPayload(**data)
        except Exception as e:
            logger.warning(f"Redis error retrieving OAuth state: {e}")
        
        # Try memory store as fallback
        if state in _memory_state_store:
            data = _memory_state_store.pop(state)
            return OAuthCallbackPayload(**data)
            
        return None

    @with_trace_id
    async def get_google_auth_url(self, state: str) -> str:
        """
        Generate Google OAuth URL
        
        Args:
            state: State token for CSRF protection
            
        Returns:
            str: Google authorization URL
        """
        params = {
            'client_id': APP_CONFIG.google_client_id,
            'redirect_uri': APP_CONFIG.google_redirect_uri,
            'scope': 'email profile https://www.googleapis.com/auth/youtube.readonly',
            'response_type': 'code',
            'state': state,
            'access_type': 'offline',
            'prompt': 'consent'
        }
        return f"https://accounts.google.com/o/oauth2/auth?{urlencode(params)}"

    @with_trace_id
    async def get_facebook_auth_url(self, state: str) -> str:
        """
        Generate Facebook OAuth URL
        
        Args:
            state: State token for CSRF protection
            
        Returns:
            str: Facebook authorization URL
        """
        params = {
            'client_id': APP_CONFIG.instagram_client_id,
            'redirect_uri': APP_CONFIG.instagram_redirect_uri,
            'scope': 'email public_profile',
            'state': state,
            'response_type': 'code'
        }
        return f"https://www.facebook.com/v13.0/dialog/oauth?{urlencode(params)}"

    @with_trace_id
    async def get_instagram_auth_url(self, state: str) -> str:
        """
        Generate Instagram OAuth URL
        
        Args:
            state: State token for CSRF protection
            
        Returns:
            str: Instagram authorization URL
        """
        params = {
            'client_id': APP_CONFIG.instagram_client_id,
            'redirect_uri': APP_CONFIG.instagram_redirect_uri,
            'scope': 'user_profile,user_media',
            'response_type': 'code',
            'state': state
        }
        return f"https://api.instagram.com/oauth/authorize?{urlencode(params)}"

    @with_trace_id
    async def get_provider_auth_url(self, provider: str, state: str) -> str:
        """
        Get the authorization URL for the specified provider
        
        Args:
            provider: OAuth provider (google, facebook, instagram)
            state: State token for CSRF protection
            
        Returns:
            str: Authorization URL
        """
        if provider == "google":
            return await self.get_google_auth_url(state)
        elif provider == "facebook":
            return await self.get_facebook_auth_url(state)
        elif provider == "instagram":
            return await self.get_instagram_auth_url(state)
        else:
            raise ValueError(f"Unsupported provider: {provider}")


def get_oauth_flow_manager(redis_client):
    """Factory function to get OAuthFlowManager instance"""
    return OAuthFlowManager(redis_client)
