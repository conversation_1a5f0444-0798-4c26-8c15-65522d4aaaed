"""
OAuth providers implementation for CreatorVerse Backend.
"""
import httpx
from typing import Dict, <PERSON><PERSON>, Tu<PERSON>, Any

from app.core.config import APP_CONFIG
from app.core.logger import get_logger
from app.core_helper.async_logger import with_trace_id
from app.schemas.oauth_schemas import OAuthProviderTokens, OAuthProviderProfile

logger = get_logger()


class OAuthHandler:
    """Base class for OAuth provider handlers"""
    
    def __init__(self, http_client=None):
        """Initialize with optional HTTP client"""
        self.http_client = http_client or httpx.AsyncClient(timeout=10.0)

    @with_trace_id
    async def exchange_code_for_tokens(self, code: str, redirect_uri: str) -> OAuthProviderTokens:
        """Exchange authorization code for tokens"""
        raise NotImplementedError("Subclasses must implement this method")

    @with_trace_id
    async def get_user_profile(self, tokens: OAuthProviderTokens) -> OAuthProviderProfile:
        """Get user profile from provider"""
        raise NotImplementedError("Subclasses must implement this method")


class GoogleOAuthHandler(OAuthHandler):
    """Handler for Google OAuth"""
    
    @with_trace_id
    async def exchange_code_for_tokens(self, code: str, redirect_uri: str) -> OAuthProviderTokens:
        """Exchange authorization code for Google tokens"""
        token_url = "https://oauth2.googleapis.com/token"
        
        data = {
            "code": code,
            "client_id": APP_CONFIG.google_client_id,
            "client_secret": APP_CONFIG.google_client_secret,
            "redirect_uri": redirect_uri,
            "grant_type": "authorization_code"
        }
        
        try:
            response = await self.http_client.post(token_url, data=data)
            response.raise_for_status()
            tokens_data = response.json()
            
            return OAuthProviderTokens(
                access_token=tokens_data["access_token"],
                refresh_token=tokens_data.get("refresh_token"),
                expires_in=tokens_data.get("expires_in"),
                token_type=tokens_data.get("token_type"),
                scope=tokens_data.get("scope")
            )
        except httpx.HTTPError as e:
            logger.error(f"Google token exchange error: {e}")
            raise ValueError(f"Failed to exchange code for tokens: {e}")

    @with_trace_id
    async def get_user_profile(self, tokens: OAuthProviderTokens) -> OAuthProviderProfile:
        """Get user profile from Google"""
        user_info_url = "https://www.googleapis.com/oauth2/v3/userinfo"
        
        try:
            headers = {"Authorization": f"Bearer {tokens.access_token}"}
            response = await self.http_client.get(user_info_url, headers=headers)
            response.raise_for_status()
            user_data = response.json()
            
            return OAuthProviderProfile(
                provider_id=user_data["sub"],
                email=user_data.get("email"),
                name=user_data.get("name"),
                given_name=user_data.get("given_name"),
                family_name=user_data.get("family_name"),
                picture=user_data.get("picture"),
                is_email_verified=user_data.get("email_verified", False)
            )
        except httpx.HTTPError as e:
            logger.error(f"Google user profile fetch error: {e}")
            raise ValueError(f"Failed to fetch user profile: {e}")

    @with_trace_id
    async def get_youtube_channels(self, tokens: OAuthProviderTokens) -> list[Dict[str, Any]]:
        """Get YouTube channels for user"""
        try:
            headers = {"Authorization": f"Bearer {tokens.access_token}"}
            response = await self.http_client.get(
                "https://www.googleapis.com/youtube/v3/channels",
                params={"part": "snippet", "mine": "true"},
                headers=headers
            )
            response.raise_for_status()
            data = response.json()
            
            return data.get("items", [])
        except httpx.HTTPError as e:
            logger.warning(f"Failed to fetch YouTube channels: {e}")
            return []


class FacebookOAuthHandler(OAuthHandler):
    """Handler for Facebook OAuth"""
    
    @with_trace_id
    async def exchange_code_for_tokens(self, code: str, redirect_uri: str) -> OAuthProviderTokens:
        """Exchange authorization code for Facebook tokens"""
        token_url = "https://graph.facebook.com/v13.0/oauth/access_token"
        
        params = {
            "code": code,
            "client_id": APP_CONFIG.instagram_client_id,
            "client_secret": APP_CONFIG.instagram_client_secret,
            "redirect_uri": redirect_uri
        }
        
        try:
            response = await self.http_client.get(token_url, params=params)
            response.raise_for_status()
            tokens_data = response.json()
            
            return OAuthProviderTokens(
                access_token=tokens_data["access_token"],
                expires_in=tokens_data.get("expires_in"),
                token_type="bearer",
                scope=None  # Facebook doesn't typically return scope in token response
            )
        except httpx.HTTPError as e:
            logger.error(f"Facebook token exchange error: {e}")
            raise ValueError(f"Failed to exchange code for tokens: {e}")

    @with_trace_id
    async def get_user_profile(self, tokens: OAuthProviderTokens) -> OAuthProviderProfile:
        """Get user profile from Facebook"""
        user_info_url = "https://graph.facebook.com/v13.0/me"
        
        params = {
            "fields": "id,name,email,first_name,last_name,picture.type(large)",
            "access_token": tokens.access_token
        }
        
        try:
            response = await self.http_client.get(user_info_url, params=params)
            response.raise_for_status()
            user_data = response.json()
            
            # Facebook doesn't guarantee email verification status
            # As per requirement, create <EMAIL> if email not provided
            email = user_data.get("email")
            if not email:
                email = f"{user_data['id']}@facebook.com"
                is_email_verified = False
            else:
                is_email_verified = True  # Assuming Facebook emails are verified
            
            return OAuthProviderProfile(
                provider_id=user_data["id"],
                email=email,
                name=user_data.get("name"),
                given_name=user_data.get("first_name"),
                family_name=user_data.get("last_name"),
                picture=user_data.get("picture", {}).get("data", {}).get("url"),
                is_email_verified=is_email_verified
            )
        except httpx.HTTPError as e:
            logger.error(f"Facebook user profile fetch error: {e}")
            raise ValueError(f"Failed to fetch user profile: {e}")


class InstagramOAuthHandler(OAuthHandler):
    """Handler for Instagram OAuth"""
    
    @with_trace_id
    async def exchange_code_for_tokens(self, code: str, redirect_uri: str) -> OAuthProviderTokens:
        """Exchange authorization code for Instagram tokens"""
        token_url = "https://api.instagram.com/oauth/access_token"
        
        data = {
            "code": code,
            "client_id": APP_CONFIG.instagram_client_id,
            "client_secret": APP_CONFIG.instagram_client_secret,
            "redirect_uri": redirect_uri,
            "grant_type": "authorization_code"
        }
        
        try:
            response = await self.http_client.post(token_url, data=data)
            response.raise_for_status()
            tokens_data = response.json()
            
            return OAuthProviderTokens(
                access_token=tokens_data["access_token"],
                token_type="bearer",
                scope=None  # Instagram doesn't typically return scope in token response
            )
        except httpx.HTTPError as e:
            logger.error(f"Instagram token exchange error: {e}")
            raise ValueError(f"Failed to exchange code for tokens: {e}")

    @with_trace_id
    async def get_user_profile(self, tokens: OAuthProviderTokens) -> OAuthProviderProfile:
        """Get user profile from Instagram"""
        user_info_url = "https://graph.instagram.com/me"
        
        params = {
            "fields": "id,username",
            "access_token": tokens.access_token
        }
        
        try:
            response = await self.http_client.get(user_info_url, params=params)
            response.raise_for_status()
            user_data = response.json()
            
            # Instagram doesn't provide email, create a placeholder
            username = user_data.get("username") or user_data["id"]
            
            return OAuthProviderProfile(
                provider_id=user_data["id"],
                email=f"{username}@instagram.com",
                name=username,
                is_email_verified=False  # Instagram emails are placeholders
            )
        except httpx.HTTPError as e:
            logger.error(f"Instagram user profile fetch error: {e}")
            raise ValueError(f"Failed to fetch user profile: {e}")


def get_oauth_handler(provider: str) -> OAuthHandler:
    """Factory function to get the appropriate OAuth handler for a provider"""
    if provider == "google":
        return GoogleOAuthHandler()
    elif provider == "facebook":
        return FacebookOAuthHandler()
    elif provider == "instagram":
        return InstagramOAuthHandler()
    else:
        raise ValueError(f"Unsupported provider: {provider}")
