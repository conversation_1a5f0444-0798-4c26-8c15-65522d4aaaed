"""
OAuth service for CreatorVerse Backend.
Implements user creation/update from OAuth providers.
"""
from datetime import UTC, datetime, timedelta
from typing import Dict, Optional, Tuple, Any
from uuid import UUID

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.enums import UserStatus
from app.core.logger import get_logger
from app.core_helper.async_logger import with_trace_id
from app.models.user_models import (
    UserAuthMethod,
    UserRoleModel,
    OAuthAccount,
    SocialProfile,
    OrgMemberRole, MasterRole
)
from app.schemas.auth import AuthTokenResponse, AuthTokenBrandResponse
from app.schemas.oauth_schemas import OAuthProviderTokens, OAuthProviderProfile
from app.services.session_service import create_user_session
from app.services.user_service import get_user_by_email_cache_aside, create_user_cache_aside
from app.utilities.brand_organization_utils import ensure_org_and_membership
from app.utilities.validation_functions import extract_domain_from_email

logger = get_logger()

# List of consumer domains not allowed for brand users
BLOCKED_CONSUMER_DOMAINS = [
    'gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com',
    'icloud.com', 'aol.com', 'protonmail.com', 'mail.com'
]


class OAuthService:
    """Service for handling OAuth operations"""

    def __init__(self, redis_client):
        """
        Initialize the OAuthService
        
        Args:
            redis_client: Redis client instance
        """
        self.redis_client = redis_client

    @with_trace_id
    async def upsert_oauth_account(
            self,
            session: AsyncSession,
            user_id: UUID,
            provider: str,
            provider_id: str,
            tokens: OAuthProviderTokens
    ) -> OAuthAccount:
        """
        Create or update OAuth account for user
        DEPRECATED: Use oauth_utils.upsert_oauth_account_safe instead
        
        Args:
            session: Database session
            user_id: User ID
            provider: OAuth provider name
            provider_id: Provider's unique identifier for the user
            tokens: OAuth provider tokens
            
        Returns:
            OAuthAccount: The created or updated account
        """
        # Use the new oauth_utils function instead
        from app.utilities.oauth_utils import upsert_oauth_account_safe
        return await upsert_oauth_account_safe(
            session=session,
            user_id=user_id,
            provider=provider,
            provider_id=provider_id,
            tokens=tokens
        )

    @with_trace_id
    async def upsert_user_auth_method(
            self,
            session: AsyncSession,
            user_id: UUID,
            provider: str
    ) -> UserAuthMethod:
        """
        Create or update user authentication method
        DEPRECATED: Use oauth_utils.upsert_user_auth_method_safe instead

        Args:
            session: Database session
            user_id: User ID
            provider: Authentication provider name (e.g., 'google', 'facebook')

        Returns:
            UserAuthMethod: The created or updated auth method
        """
        # Use the new oauth_utils function instead
        from app.utilities.oauth_utils import upsert_user_auth_method_safe
        return await upsert_user_auth_method_safe(
            session=session,
            redis_client=self.redis_client,
            user_id=user_id,
            provider=provider
        )

    @with_trace_id
    async def create_or_update_youtube_profile(
            self,
            session: AsyncSession,
            user_id: UUID,
            channel_data: Dict[str, Any]
    ) -> SocialProfile:
        """
        Create or update user's YouTube profile
        DEPRECATED: Use oauth_utils.create_or_update_youtube_profile_safe instead
        
        Args:
            session: Database session
            user_id: User ID
            channel_data: YouTube channel data
            
        Returns:
            SocialProfile: The created or updated profile
        """
        # Use the new oauth_utils function instead
        from app.utilities.oauth_utils import create_or_update_youtube_profile_safe
        return await create_or_update_youtube_profile_safe(
            session=session,
            user_id=user_id,
            channel_data=channel_data
        )

    @with_trace_id
    async def create_or_update_user_from_oauth(
            self,
            db_conn,
            redis_client,
            provider_tokens: OAuthProviderTokens,
            provider_profile: OAuthProviderProfile,
            role_uuid: str,
            provider: str,
            rbac_service = None
    ) -> Tuple[AuthTokenResponse, Optional[str], bool]:
        """
        Create or update user from OAuth profile data
        
        Args:
            db_conn: Database connection
            redis_client: Redis client
            provider_tokens: OAuth provider tokens
            provider_profile: User profile from provider
            role_uuid: Role UUID (influencer or brand_user)
            provider: OAuth provider name
            
        Returns:
            Tuple[AuthTokenResponse, Optional[str], bool]:
                - Auth token response
                - Redirection URL (if needed)
                - Is new user flag
        """
        email = provider_profile.email
        if not email:
            raise ValueError("Missing email from provider")

        # First get the role information
        is_brand = False
        async with db_conn.get_db() as session:
            role_query = select(MasterRole).where(MasterRole.id == role_uuid)
            role_result = await session.execute(role_query)
            role_obj = role_result.scalar_one_or_none()

            if not role_obj:
                raise ValueError(f"Role not found for ID: {role_uuid}")

            is_brand = role_obj.role_name.startswith("brand")

        # Use the new specialized functions based on user type
        if is_brand:
            # Handle brand user flow with new implementation
            from app.utilities.oauth_utils import handle_brand_oauth_flow
            return await handle_brand_oauth_flow(
                db_conn=db_conn,
                redis_client=redis_client,
                provider_tokens=provider_tokens,
                provider_profile=provider_profile,
                role_uuid=role_uuid,
                provider=provider,
                rbac_service=rbac_service
            )
        else:
            # Handle influencer flow with new implementation
            from app.utilities.oauth_utils import handle_influencer_oauth_flow
            return await handle_influencer_oauth_flow(
                db_conn=db_conn,
                redis_client=redis_client,
                provider_tokens=provider_tokens,
                provider_profile=provider_profile,
                role_uuid=role_uuid,
                provider=provider,
                rbac_service=rbac_service
            )

    @with_trace_id
    async def _handle_brand_oauth_flow(
            self,
            db_conn,
            redis_client,
            provider_tokens: OAuthProviderTokens,
            provider_profile: OAuthProviderProfile,
            role_uuid: str,
            provider: str
    ) -> Tuple[AuthTokenBrandResponse, Optional[str], bool]:
        """
        DEPRECATED: Use handle_brand_oauth_flow from oauth_utils instead.
        This method is kept for backward compatibility but should not be used.
        """
        logger.warning("Using deprecated _handle_brand_oauth_flow method. Please use handle_brand_oauth_flow from oauth_utils instead.")
        
        # Redirect to the new implementation
        from app.utilities.oauth_utils import handle_brand_oauth_flow
        return await handle_brand_oauth_flow(
            db_conn=db_conn,
            redis_client=redis_client,
            provider_tokens=provider_tokens,
            provider_profile=provider_profile,
            role_uuid=role_uuid,
            provider=provider
        )


def get_oauth_service(redis_client):
    """Factory function to get OAuthService instance"""
    return OAuthService(redis_client)
