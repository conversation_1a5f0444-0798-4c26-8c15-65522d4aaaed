import random
import uuid
from datetime import datetime
import json
import os

# Ensure the script runs from the project root
os.chdir(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Sample platforms
PLATFORMS = [
    {"id": "platform_1", "name": "YouTube"},
    {"id": "platform_2", "name": "Instagram"},
    {"id": "platform_3", "name": "TikTok"},
    {"id": "platform_4", "name": "Twitter"},
]

# Sample user data
def generate_users(count=5):
    users = []
    for i in range(count):
        user_id = f"user_{uuid.uuid4().hex[:8]}"
        user = {
            "id": user_id,
            "name": f"Test User {i+1}",
            "email": f"user{i+1}@example.com",
            "status": random.choice(["active", "inactive", "pending"]),
            "platform": random.choice([p["name"] for p in PLATFORMS]) if random.random() > 0.3 else None,
        }
        users.append(user)
    return users

# Sample account data
def generate_accounts(users, count_per_user=2):
    accounts = []
    for user in users:
        platforms = random.sample(PLATFORMS, min(count_per_user, len(PLATFORMS)))
        for platform in platforms:
            account_id = f"account_{uuid.uuid4().hex[:8]}"
            account = {
                "id": account_id,
                "user_id": user["id"],
                "platform_id": platform["id"],
                "status": random.choice(["connected", "disconnected", "expired"]),
                "connected_at": datetime.now().isoformat(),
            }
            accounts.append(account)
    return accounts

# Sample data for accounts
def generate_data(accounts, count_per_account=3):
    all_data = []
    data_types = ["profile", "posts", "engagement", "income"]
    
    for account in accounts:
        for _ in range(count_per_account):
            data_type = random.choice(data_types)
            data_id = f"data_{uuid.uuid4().hex[:8]}"
            
            # Generate content based on data type
            if data_type == "profile":
                content = {
                    "username": f"creator_{random.randint(1000, 9999)}",
                    "followers": random.randint(100, 1000000),
                    "following": random.randint(10, 1000),
                    "bio": f"Sample creator bio {random.randint(1, 100)}"
                }
            elif data_type == "posts":
                content = {
                    "total_posts": random.randint(10, 500),
                    "recent_posts": [
                        {
                            "id": f"post_{i}",
                            "title": f"Sample Post {i}",
                            "likes": random.randint(10, 10000),
                            "views": random.randint(100, 100000)
                        } for i in range(1, 4)
                    ]
                }
            elif data_type == "engagement":
                content = {
                    "avg_views": random.randint(1000, 100000),
                    "avg_likes": random.randint(100, 10000),
                    "avg_comments": random.randint(10, 1000),
                    "engagement_rate": round(random.uniform(0.5, 15), 2)
                }
            else:  # income
                content = {
                    "estimated_earnings": round(random.uniform(100, 10000), 2),
                    "revenue_sources": [
                        {
                            "source": "ads",
                            "percentage": random.randint(20, 80)
                        },
                        {
                            "source": "sponsorships",
                            "percentage": random.randint(10, 70)
                        }
                    ]
                }
                
            data_entry = {
                "id": data_id,
                "account_id": account["id"],
                "data_type": data_type,
                "content": content,
                "created_at": datetime.now().isoformat()
            }
            all_data.append(data_entry)
    
    return all_data

# Generate sample data
def generate_sample_data():
    users = generate_users(5)
    accounts = generate_accounts(users)
    data = generate_data(accounts)
    
    # Save to JSON files for reference
    os.makedirs("sample_data", exist_ok=True)
    
    with open("sample_data/users.json", "w") as f:
        json.dump(users, f, indent=2)
    
    with open("sample_data/accounts.json", "w") as f:
        json.dump(accounts, f, indent=2)
    
    with open("sample_data/data.json", "w") as f:
        json.dump(data, f, indent=2)
    
    print(f"Generated {len(users)} users, {len(accounts)} accounts, and {len(data)} data entries")
    print("Sample data has been saved to the 'sample_data' directory")

if __name__ == "__main__":
    generate_sample_data()
