"""
Influencer List Service for CreatorVerse Backend.
Handles all business logic for managing brand influencer lists and entries.
"""
import json
from datetime import datetime, UTC
from typing import List, Optional, Dict, Any, Tuple
from uuid import UUID

from sqlalchemy import select, update, delete, func, and_, or_
from sqlalchemy.exc import IntegrityError
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload, joinedload

from app.core.logger import get_logger
from app.models.user_models import (\n    BrandInfluencerList, BrandInfluencerListEntry, Brand,\n    BrandInfluencerListStatus, InfluencerStatus, User, BrandMembership\n)\nfrom app.schemas.influencer_schemas import (\n    CreateInfluencerListRequest, UpdateInfluencerListRequest,\n    AddInfluencerToListRequest, UpdateInfluencerInListRequest,\n    InfluencerListFilters, InfluencerEntryFilters\n)\n\nlogger = get_logger()\n\n\n# ──────────────────────────────────────────────────────────────────────────────\n# CACHE KEYS\n# ──────────────────────────────────────────────────────────────────────────────\n\ndef _get_brand_lists_cache_key(brand_id: UUID) -> str:\n    \"\"\"Get cache key for brand's influencer lists\"\"\"\n    return f\"brand_influencer_lists:{brand_id}\"\n\n\ndef _get_list_detail_cache_key(list_id: UUID) -> str:\n    \"\"\"Get cache key for detailed list with entries\"\"\"\n    return f\"influencer_list_detail:{list_id}\"\n\n\ndef _get_list_entries_cache_key(list_id: UUID) -> str:\n    \"\"\"Get cache key for list entries\"\"\"\n    return f\"influencer_list_entries:{list_id}\"\n\n\nasync def _evict_list_caches(redis_client, brand_id: UUID, list_id: UUID = None):\n    \"\"\"Evict influencer list related caches\"\"\"\n    try:\n        # Evict brand lists cache\n        await redis_client.delete(_get_brand_lists_cache_key(brand_id))\n        \n        if list_id:\n            # Evict specific list caches\n            await redis_client.delete(_get_list_detail_cache_key(list_id))\n            await redis_client.delete(_get_list_entries_cache_key(list_id))\n            \n        logger.debug(f\"Evicted influencer list caches for brand {brand_id}\")\n    except Exception as e:\n        logger.warning(f\"Failed to evict list caches: {e}\")\n\n\n# ──────────────────────────────────────────────────────────────────────────────\n# LIST MANAGEMENT FUNCTIONS\n# ──────────────────────────────────────────────────────────────────────────────\n\nasync def create_influencer_list(\n    session: AsyncSession,\n    redis_client,\n    brand_id: UUID,\n    creator_id: UUID,\n    name: str,\n    description: Optional[str] = None\n) -> BrandInfluencerList:\n    \"\"\"\n    Create a new influencer list for a brand.\n    \n    Args:\n        session: Database session\n        redis_client: Redis client for caching\n        brand_id: Brand ID\n        creator_id: User ID who is creating the list\n        name: List name\n        description: Optional description\n        \n    Returns:\n        Created BrandInfluencerList instance\n        \n    Raises:\n        IntegrityError: If list name already exists for the brand\n        ValueError: If brand not found or user doesn't have permission\n    \"\"\"\n    # Verify brand exists and user has permission\n    brand_query = select(Brand).where(\n        Brand.id == brand_id,\n        Brand.is_active == True,\n        Brand.deleted_at.is_(None)\n    )\n    brand_result = await session.execute(brand_query)\n    brand = brand_result.scalar_one_or_none()\n    \n    if not brand:\n        raise ValueError(\"Brand not found or inactive\")\n    \n    # Check if user is a member of the brand\n    membership_query = select(BrandMembership).where(\n        BrandMembership.brand_id == brand_id,\n        BrandMembership.user_id == creator_id,\n        BrandMembership.is_active == True\n    )\n    membership_result = await session.execute(membership_query)\n    membership = membership_result.scalar_one_or_none()\n    \n    if not membership:\n        raise ValueError(\"User is not a member of this brand\")\n    \n    # Create the influencer list\n    new_list = BrandInfluencerList(\n        brand_id=brand_id,\n        name=name,\n        description=description,\n        created_by=creator_id,\n        status=BrandInfluencerListStatus.active\n    )\n    \n    session.add(new_list)\n    await session.flush()  # To get the ID\n    \n    # Evict caches\n    await _evict_list_caches(redis_client, brand_id)\n    \n    logger.info(\n        f\"Created influencer list '{name}' for brand {brand_id}\",\n        extra={\n            \"list_id\": str(new_list.id),\n            \"brand_id\": str(brand_id),\n            \"creator_id\": str(creator_id)\n        }\n    )\n    \n    return new_list\n\n\nasync def get_brand_influencer_lists(\n    db_conn,\n    redis_client,\n    brand_id: UUID,\n    filters: Optional[InfluencerListFilters] = None\n) -> List[Dict[str, Any]]:\n    \"\"\"\n    Get all influencer lists for a brand with optional filtering.\n    \n    Args:\n        db_conn: Database connection manager\n        redis_client: Redis client\n        brand_id: Brand ID\n        filters: Optional filters\n        \n    Returns:\n        List of influencer list dictionaries\n    \"\"\"\n    cache_key = _get_brand_lists_cache_key(brand_id)\n    \n    # Try cache first (only if no filters)\n    if not filters:\n        try:\n            cached_data = await redis_client.get(cache_key)\n            if cached_data:\n                logger.debug(f\"Retrieved brand lists from cache for brand {brand_id}\")\n                return json.loads(cached_data)\n        except Exception as e:\n            logger.warning(f\"Cache retrieval failed: {e}\")\n    \n    async with db_conn.get_db() as session:\n        # Build query\n        query = select(BrandInfluencerList).where(\n            BrandInfluencerList.brand_id == brand_id,\n            BrandInfluencerList.deleted_at.is_(None)\n        ).options(\n            joinedload(BrandInfluencerList.creator)\n        )\n        \n        # Apply filters\n        if filters:\n            if filters.status:\n                query = query.where(BrandInfluencerList.status == filters.status)\n            if filters.created_by:\n                query = query.where(BrandInfluencerList.created_by == filters.created_by)\n            if filters.search:\n                search_term = f\"%{filters.search}%\"\n                query = query.where(\n                    or_(\n                        BrandInfluencerList.name.ilike(search_term),\n                        BrandInfluencerList.description.ilike(search_term)\n                    )\n                )\n        \n        query = query.order_by(BrandInfluencerList.updated_at.desc())\n        \n        result = await session.execute(query)\n        lists = result.scalars().all()\n        \n        # Get entry counts for each list\n        list_data = []\n        for list_obj in lists:\n            count_query = select(func.count(BrandInfluencerListEntry.id)).where(\n                BrandInfluencerListEntry.list_id == list_obj.id\n            )\n            count_result = await session.execute(count_query)\n            entry_count = count_result.scalar() or 0\n            \n            list_data.append({\n                \"id\": str(list_obj.id),\n                \"name\": list_obj.name,\n                \"description\": list_obj.description,\n                \"status\": list_obj.status.value,\n                \"brand_id\": str(list_obj.brand_id),\n                \"created_by\": str(list_obj.created_by),\n                \"created_at\": list_obj.created_at.isoformat(),\n                \"updated_at\": list_obj.updated_at.isoformat(),\n                \"total_influencers\": entry_count\n            })\n        \n        # Cache the result (only if no filters)\n        if not filters:\n            try:\n                await redis_client.setex(\n                    cache_key,\n                    300,  # 5 minutes\n                    json.dumps(list_data, default=str)\n                )\n            except Exception as e:\n                logger.warning(f\"Cache storage failed: {e}\")\n        \n        return list_data\n\n\nasync def get_influencer_list_detail(\n    db_conn,\n    redis_client,\n    list_id: UUID,\n    entry_filters: Optional[InfluencerEntryFilters] = None\n) -> Optional[Dict[str, Any]]:\n    \"\"\"\n    Get detailed influencer list with entries.\n    \n    Args:\n        db_conn: Database connection manager\n        redis_client: Redis client\n        list_id: List ID\n        entry_filters: Optional filters for entries\n        \n    Returns:\n        Detailed list dictionary or None if not found\n    \"\"\"\n    cache_key = _get_list_detail_cache_key(list_id)\n    \n    # Try cache first (only if no filters)\n    if not entry_filters:\n        try:\n            cached_data = await redis_client.get(cache_key)\n            if cached_data:\n                logger.debug(f\"Retrieved list detail from cache for list {list_id}\")\n                return json.loads(cached_data)\n        except Exception as e:\n            logger.warning(f\"Cache retrieval failed: {e}\")\n    \n    async with db_conn.get_db() as session:\n        # Get list details\n        list_query = select(BrandInfluencerList).where(\n            BrandInfluencerList.id == list_id,\n            BrandInfluencerList.deleted_at.is_(None)\n        ).options(\n            joinedload(BrandInfluencerList.creator)\n        )\n        \n        list_result = await session.execute(list_query)\n        list_obj = list_result.scalar_one_or_none()\n        \n        if not list_obj:\n            return None\n        \n        # Get entries with filtering\n        entries_query = select(BrandInfluencerListEntry).where(\n            BrandInfluencerListEntry.list_id == list_id\n        ).options(\n            joinedload(BrandInfluencerListEntry.added_by_user),\n            joinedload(BrandInfluencerListEntry.user)\n        )\n        \n        # Apply entry filters\n        if entry_filters:\n            if entry_filters.status:\n                entries_query = entries_query.where(\n                    BrandInfluencerListEntry.status == entry_filters.status\n                )\n            if entry_filters.campaign:\n                entries_query = entries_query.where(\n                    BrandInfluencerListEntry.campaign.ilike(f\"%{entry_filters.campaign}%\")\n                )\n            if entry_filters.min_audience:\n                entries_query = entries_query.where(\n                    BrandInfluencerListEntry.audience_size >= entry_filters.min_audience\n                )\n            if entry_filters.max_audience:\n                entries_query = entries_query.where(\n                    BrandInfluencerListEntry.audience_size <= entry_filters.max_audience\n                )\n            if entry_filters.min_engagement:\n                entries_query = entries_query.where(\n                    BrandInfluencerListEntry.engagement_rate >= entry_filters.min_engagement\n                )\n            if entry_filters.max_engagement:\n                entries_query = entries_query.where(\n                    BrandInfluencerListEntry.engagement_rate <= entry_filters.max_engagement\n                )\n            if entry_filters.search:\n                search_term = f\"%{entry_filters.search}%\"\n                entries_query = entries_query.where(\n                    or_(\n                        BrandInfluencerListEntry.influencer_name.ilike(search_term),\n                        BrandInfluencerListEntry.influencer_username.ilike(search_term)\n                    )\n                )\n        \n        entries_query = entries_query.order_by(BrandInfluencerListEntry.added_at.desc())\n        \n        entries_result = await session.execute(entries_query)\n        entries = entries_result.scalars().all()\n        \n        # Format entries\n        entry_data = []\n        for entry in entries:\n            entry_data.append({\n                \"id\": str(entry.id),\n                \"influencer_id\": entry.influencer_id,\n                \"influencer_name\": entry.influencer_name,\n                \"influencer_username\": entry.influencer_username,\n                \"influencer_avatar_url\": entry.influencer_avatar_url,\n                \"user_id\": str(entry.user_id) if entry.user_id else None,\n                \"status\": entry.status.value,\n                \"channels\": entry.channels or [],\n                \"audience_size\": entry.audience_size,\n                \"engagement_rate\": entry.engagement_rate,\n                \"campaign\": entry.campaign,\n                \"labels\": entry.labels or [],\n                \"notes\": entry.notes,\n                \"added_by\": str(entry.added_by),\n                \"added_at\": entry.added_at.isoformat(),\n                \"updated_at\": entry.updated_at.isoformat()\n            })\n        \n        # Format response\n        response_data = {\n            \"id\": str(list_obj.id),\n            \"name\": list_obj.name,\n            \"description\": list_obj.description,\n            \"status\": list_obj.status.value,\n            \"brand_id\": str(list_obj.brand_id),\n            \"created_by\": str(list_obj.created_by),\n            \"created_at\": list_obj.created_at.isoformat(),\n            \"updated_at\": list_obj.updated_at.isoformat(),\n            \"influencers\": entry_data,\n            \"total_influencers\": len(entry_data)\n        }\n        \n        # Cache the result (only if no filters)\n        if not entry_filters:\n            try:\n                await redis_client.setex(\n                    cache_key,\n                    300,  # 5 minutes\n                    json.dumps(response_data, default=str)\n                )\n            except Exception as e:\n                logger.warning(f\"Cache storage failed: {e}\")\n        \n        return response_data\n\n\nasync def update_influencer_list(\n    session: AsyncSession,\n    redis_client,\n    list_id: UUID,\n    user_id: UUID,\n    updates: UpdateInfluencerListRequest\n) -> Optional[BrandInfluencerList]:\n    \"\"\"\n    Update an influencer list.\n    \n    Args:\n        session: Database session\n        redis_client: Redis client\n        list_id: List ID to update\n        user_id: User making the update\n        updates: Update data\n        \n    Returns:\n        Updated list or None if not found\n        \n    Raises:\n        ValueError: If user doesn't have permission\n    \"\"\"\n    # Get the list and verify permissions\n    list_query = select(BrandInfluencerList).where(\n        BrandInfluencerList.id == list_id,\n        BrandInfluencerList.deleted_at.is_(None)\n    )\n    \n    list_result = await session.execute(list_query)\n    list_obj = list_result.scalar_one_or_none()\n    \n    if not list_obj:\n        return None\n    \n    # Check if user is a member of the brand\n    membership_query = select(BrandMembership).where(\n        BrandMembership.brand_id == list_obj.brand_id,\n        BrandMembership.user_id == user_id,\n        BrandMembership.is_active == True\n    )\n    membership_result = await session.execute(membership_query)\n    membership = membership_result.scalar_one_or_none()\n    \n    if not membership:\n        raise ValueError(\"User is not a member of this brand\")\n    \n    # Apply updates\n    update_data = {}\n    if updates.name is not None:\n        update_data[\"name\"] = updates.name\n    if updates.description is not None:\n        update_data[\"description\"] = updates.description\n    if updates.status is not None:\n        update_data[\"status\"] = updates.status\n    \n    if update_data:\n        update_data[\"updated_at\"] = datetime.now(UTC)\n        \n        update_query = update(BrandInfluencerList).where(\n            BrandInfluencerList.id == list_id\n        ).values(**update_data)\n        \n        await session.execute(update_query)\n        \n        # Refresh the object\n        await session.refresh(list_obj)\n        \n        # Evict caches\n        await _evict_list_caches(redis_client, list_obj.brand_id, list_id)\n        \n        logger.info(\n            f\"Updated influencer list {list_id}\",\n            extra={\n                \"list_id\": str(list_id),\n                \"user_id\": str(user_id),\n                \"updates\": update_data\n            }\n        )\n    \n    return list_obj\n\n\nasync def delete_influencer_list(\n    session: AsyncSession,\n    redis_client,\n    list_id: UUID,\n    user_id: UUID\n) -> bool:\n    \"\"\"\n    Soft delete an influencer list.\n    \n    Args:\n        session: Database session\n        redis_client: Redis client\n        list_id: List ID to delete\n        user_id: User making the deletion\n        \n    Returns:\n        True if deleted, False if not found\n        \n    Raises:\n        ValueError: If user doesn't have permission\n    \"\"\"\n    # Get the list and verify permissions\n    list_query = select(BrandInfluencerList).where(\n        BrandInfluencerList.id == list_id,\n        BrandInfluencerList.deleted_at.is_(None)\n    )\n    \n    list_result = await session.execute(list_query)\n    list_obj = list_result.scalar_one_or_none()\n    \n    if not list_obj:\n        return False\n    \n    # Check if user is a member of the brand\n    membership_query = select(BrandMembership).where(\n        BrandMembership.brand_id == list_obj.brand_id,\n        BrandMembership.user_id == user_id,\n        BrandMembership.is_active == True\n    )\n    membership_result = await session.execute(membership_query)\n    membership = membership_result.scalar_one_or_none()\n    \n    if not membership:\n        raise ValueError(\"User is not a member of this brand\")\n    \n    # Soft delete\n    update_query = update(BrandInfluencerList).where(\n        BrandInfluencerList.id == list_id\n    ).values(\n        deleted_at=datetime.now(UTC),\n        updated_at=datetime.now(UTC)\n    )\n    \n    await session.execute(update_query)\n    \n    # Evict caches\n    await _evict_list_caches(redis_client, list_obj.brand_id, list_id)\n    \n    logger.info(\n        f\"Deleted influencer list {list_id}\",\n        extra={\n            \"list_id\": str(list_id),\n            \"user_id\": str(user_id)\n        }\n    )\n    \n    return True\n\n\n# ──────────────────────────────────────────────────────────────────────────────\n# INFLUENCER ENTRY MANAGEMENT FUNCTIONS\n# ──────────────────────────────────────────────────────────────────────────────\n\nasync def add_influencer_to_list(\n    session: AsyncSession,\n    redis_client,\n    list_id: UUID,\n    user_id: UUID,\n    influencer_data: AddInfluencerToListRequest\n) -> BrandInfluencerListEntry:\n    \"\"\"\n    Add an influencer to a list.\n    \n    Args:\n        session: Database session\n        redis_client: Redis client\n        list_id: List ID\n        user_id: User adding the influencer\n        influencer_data: Influencer data\n        \n    Returns:\n        Created entry\n        \n    Raises:\n        ValueError: If list not found or user doesn't have permission\n        IntegrityError: If influencer already in list\n    \"\"\"\n    # Verify list exists and user has permission\n    list_query = select(BrandInfluencerList).where(\n        BrandInfluencerList.id == list_id,\n        BrandInfluencerList.deleted_at.is_(None)\n    )\n    \n    list_result = await session.execute(list_query)\n    list_obj = list_result.scalar_one_or_none()\n    \n    if not list_obj:\n        raise ValueError(\"List not found\")\n    \n    # Check if user is a member of the brand\n    membership_query = select(BrandMembership).where(\n        BrandMembership.brand_id == list_obj.brand_id,\n        BrandMembership.user_id == user_id,\n        BrandMembership.is_active == True\n    )\n    membership_result = await session.execute(membership_query)\n    membership = membership_result.scalar_one_or_none()\n    \n    if not membership:\n        raise ValueError(\"User is not a member of this brand\")\n    \n    # Create the entry\n    new_entry = BrandInfluencerListEntry(\n        list_id=list_id,\n        influencer_id=influencer_data.influencer_id,\n        user_id=influencer_data.user_id,\n        influencer_name=influencer_data.influencer_name,\n        influencer_username=influencer_data.influencer_username,\n        influencer_avatar_url=influencer_data.influencer_avatar_url,\n        status=influencer_data.status or InfluencerStatus.shortlisted,\n        channels=influencer_data.channels or [],\n        audience_size=influencer_data.audience_size,\n        engagement_rate=influencer_data.engagement_rate,\n        campaign=influencer_data.campaign,\n        labels=influencer_data.labels or [],\n        notes=influencer_data.notes,\n        added_by=user_id\n    )\n    \n    session.add(new_entry)\n    await session.flush()  # To get the ID\n    \n    # Evict caches\n    await _evict_list_caches(redis_client, list_obj.brand_id, list_id)\n    \n    logger.info(\n        f\"Added influencer {influencer_data.influencer_id} to list {list_id}\",\n        extra={\n            \"list_id\": str(list_id),\n            \"influencer_id\": influencer_data.influencer_id,\n            \"user_id\": str(user_id)\n        }\n    )\n    \n    return new_entry\n\n\nasync def update_influencer_in_list(\n    session: AsyncSession,\n    redis_client,\n    entry_id: UUID,\n    user_id: UUID,\n    updates: UpdateInfluencerInListRequest\n) -> Optional[BrandInfluencerListEntry]:\n    \"\"\"\n    Update an influencer entry in a list.\n    \n    Args:\n        session: Database session\n        redis_client: Redis client\n        entry_id: Entry ID to update\n        user_id: User making the update\n        updates: Update data\n        \n    Returns:\n        Updated entry or None if not found\n        \n    Raises:\n        ValueError: If user doesn't have permission\n    \"\"\"\n    # Get the entry and verify permissions\n    entry_query = select(BrandInfluencerListEntry).where(\n        BrandInfluencerListEntry.id == entry_id\n    ).options(\n        joinedload(BrandInfluencerListEntry.influencer_list)\n    )\n    \n    entry_result = await session.execute(entry_query)\n    entry = entry_result.scalar_one_or_none()\n    \n    if not entry:\n        return None\n    \n    # Check if user is a member of the brand\n    membership_query = select(BrandMembership).where(\n        BrandMembership.brand_id == entry.influencer_list.brand_id,\n        BrandMembership.user_id == user_id,\n        BrandMembership.is_active == True\n    )\n    membership_result = await session.execute(membership_query)\n    membership = membership_result.scalar_one_or_none()\n    \n    if not membership:\n        raise ValueError(\"User is not a member of this brand\")\n    \n    # Apply updates\n    update_data = {}\n    if updates.status is not None:\n        update_data[\"status\"] = updates.status\n    if updates.channels is not None:\n        update_data[\"channels\"] = updates.channels\n    if updates.audience_size is not None:\n        update_data[\"audience_size\"] = updates.audience_size\n    if updates.engagement_rate is not None:\n        update_data[\"engagement_rate\"] = updates.engagement_rate\n    if updates.campaign is not None:\n        update_data[\"campaign\"] = updates.campaign\n    if updates.labels is not None:\n        update_data[\"labels\"] = updates.labels\n    if updates.notes is not None:\n        update_data[\"notes\"] = updates.notes\n    \n    if update_data:\n        update_data[\"updated_at\"] = datetime.now(UTC)\n        \n        update_query = update(BrandInfluencerListEntry).where(\n            BrandInfluencerListEntry.id == entry_id\n        ).values(**update_data)\n        \n        await session.execute(update_query)\n        \n        # Refresh the object\n        await session.refresh(entry)\n        \n        # Evict caches\n        await _evict_list_caches(redis_client, entry.influencer_list.brand_id, entry.list_id)\n        \n        logger.info(\n            f\"Updated influencer entry {entry_id}\",\n            extra={\n                \"entry_id\": str(entry_id),\n                \"user_id\": str(user_id),\n                \"updates\": update_data\n            }\n        )\n    \n    return entry\n\n\nasync def remove_influencer_from_list(\n    session: AsyncSession,\n    redis_client,\n    entry_id: UUID,\n    user_id: UUID\n) -> bool:\n    \"\"\"\n    Remove an influencer from a list.\n    \n    Args:\n        session: Database session\n        redis_client: Redis client\n        entry_id: Entry ID to remove\n        user_id: User making the removal\n        \n    Returns:\n        True if removed, False if not found\n        \n    Raises:\n        ValueError: If user doesn't have permission\n    \"\"\"\n    # Get the entry and verify permissions\n    entry_query = select(BrandInfluencerListEntry).where(\n        BrandInfluencerListEntry.id == entry_id\n    ).options(\n        joinedload(BrandInfluencerListEntry.influencer_list)\n    )\n    \n    entry_result = await session.execute(entry_query)\n    entry = entry_result.scalar_one_or_none()\n    \n    if not entry:\n        return False\n    \n    # Check if user is a member of the brand\n    membership_query = select(BrandMembership).where(\n        BrandMembership.brand_id == entry.influencer_list.brand_id,\n        BrandMembership.user_id == user_id,\n        BrandMembership.is_active == True\n    )\n    membership_result = await session.execute(membership_query)\n    membership = membership_result.scalar_one_or_none()\n    \n    if not membership:\n        raise ValueError(\"User is not a member of this brand\")\n    \n    # Delete the entry\n    delete_query = delete(BrandInfluencerListEntry).where(\n        BrandInfluencerListEntry.id == entry_id\n    )\n    \n    await session.execute(delete_query)\n    \n    # Evict caches\n    await _evict_list_caches(redis_client, entry.influencer_list.brand_id, entry.list_id)\n    \n    logger.info(\n        f\"Removed influencer entry {entry_id}\",\n        extra={\n            \"entry_id\": str(entry_id),\n            \"user_id\": str(user_id)\n        }\n    )\n    \n    return True\n\n\nasync def get_list_options_for_brand(\n    db_conn,\n    redis_client,\n    brand_id: UUID\n) -> List[Dict[str, Any]]:\n    \"\"\"\n    Get simple list options for a brand (for dropdowns/selects).\n    \n    Args:\n        db_conn: Database connection manager\n        redis_client: Redis client\n        brand_id: Brand ID\n        \n    Returns:\n        List of simple list options\n    \"\"\"\n    async with db_conn.get_db() as session:\n        query = select(\n            BrandInfluencerList.id,\n            BrandInfluencerList.name,\n            func.count(BrandInfluencerListEntry.id).label('total_influencers')\n        ).select_from(\n            BrandInfluencerList\n        ).outerjoin(\n            BrandInfluencerListEntry,\n            BrandInfluencerList.id == BrandInfluencerListEntry.list_id\n        ).where(\n            BrandInfluencerList.brand_id == brand_id,\n            BrandInfluencerList.deleted_at.is_(None),\n            BrandInfluencerList.status == BrandInfluencerListStatus.active\n        ).group_by(\n            BrandInfluencerList.id,\n            BrandInfluencerList.name\n        ).order_by(\n            BrandInfluencerList.name\n        )\n        \n        result = await session.execute(query)\n        rows = result.all()\n        \n        return [\n            {\n                \"id\": str(row.id),\n                \"name\": row.name,\n                \"total_influencers\": row.total_influencers or 0\n            }\n            for row in rows\n        ]\n