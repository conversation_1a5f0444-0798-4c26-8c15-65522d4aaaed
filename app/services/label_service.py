"""
Label Service for CreatorVerse Backend.
Handles all business logic for managing global labels and brand-specific label usage.
"""
import json
from datetime import datetime, UTC, timedelta
from typing import List, Optional, Dict, Any, Tuple
from uuid import UUID

from sqlalchemy import select, update, delete, func, and_, or_, text
from sqlalchemy.exc import IntegrityError
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload, joinedload

from app.core.logger import get_logger
from app.models.user_models import (
    GlobalLabel, BrandLabel, BrandInfluencerListEntryLabel,
    BrandInfluencerListEntry, BrandInfluencerList, Brand, BrandMembership, User
)
from app.schemas.label_schemas import (
    CreateLabelRequest, UpdateLabelRequest, AssignLabelsToEntryRequest,
    LabelFilters, BrandLabelFilters
)

logger = get_logger()


# ──────────────────────────────────────────────────────────────────────────────
# CACHE KEYS
# ──────────────────────────────────────────────────────────────────────────────

def _get_global_labels_cache_key() -> str:
    """Get cache key for global labels list"""
    return "global_labels:all"


def _get_brand_labels_cache_key(brand_id: UUID) -> str:
    """Get cache key for brand's labels"""
    return f"brand_labels:{brand_id}"


def _get_label_autocomplete_cache_key(brand_id: UUID, query: str = "") -> str:
    """Get cache key for label autocomplete"""
    return f"label_autocomplete:{brand_id}:{query.lower()}"


async def _evict_label_caches(redis_client, brand_id: UUID = None):
    """Evict label-related caches"""
    try:
        # Always evict global cache
        await redis_client.delete(_get_global_labels_cache_key())
        
        if brand_id:
            # Evict brand-specific caches
            await redis_client.delete(_get_brand_labels_cache_key(brand_id))
            
            # Evict autocomplete caches for this brand (pattern match)
            pattern = f"label_autocomplete:{brand_id}:*"
            keys = await redis_client.keys(pattern)
            if keys:
                await redis_client.delete(*keys)
                
        logger.debug(f"Evicted label caches for brand {brand_id}")\n    except Exception as e:\n        logger.warning(f"Failed to evict label caches: {e}")\n\n\n# ──────────────────────────────────────────────────────────────────────────────\n# GLOBAL LABEL MANAGEMENT FUNCTIONS\n# ──────────────────────────────────────────────────────────────────────────────\n\nasync def create_global_label(\n    session: AsyncSession,\n    redis_client,\n    label_data: CreateLabelRequest\n) -> GlobalLabel:\n    \"\"\"\n    Create a new global label.\n    \n    Args:\n        session: Database session\n        redis_client: Redis client for caching\n        label_data: Label creation data\n        \n    Returns:\n        Created GlobalLabel instance\n        \n    Raises:\n        IntegrityError: If label name already exists\n    \"\"\"\n    # Create the global label\n    new_label = GlobalLabel(\n        name=label_data.name,\n        description=label_data.description,\n        color=label_data.color or '#6B7280',\n        usage_count=0\n    )\n    \n    session.add(new_label)\n    await session.flush()  # To get the ID\n    \n    # Evict caches\n    await _evict_label_caches(redis_client)\n    \n    logger.info(\n        f"Created global label '{label_data.name}'\",\n        extra={\n            \"label_id\": str(new_label.id),\n            \"label_name\": label_data.name\n        }\n    )\n    \n    return new_label\n\n\nasync def get_global_labels(\n    db_conn,\n    redis_client,\n    filters: Optional[LabelFilters] = None\n) -> List[Dict[str, Any]]:\n    \"\"\"\n    Get all global labels with optional filtering.\n    \n    Args:\n        db_conn: Database connection manager\n        redis_client: Redis client\n        filters: Optional filters\n        \n    Returns:\n        List of global label dictionaries\n    \"\"\"\n    cache_key = _get_global_labels_cache_key()\n    \n    # Try cache first (only if no filters)\n    if not filters:\n        try:\n            cached_data = await redis_client.get(cache_key)\n            if cached_data:\n                logger.debug(\"Retrieved global labels from cache\")\n                return json.loads(cached_data)\n        except Exception as e:\n            logger.warning(f\"Cache retrieval failed: {e}\")\n    \n    async with db_conn.get_db() as session:\n        # Build query\n        query = select(GlobalLabel)\n        \n        # Apply filters\n        if filters:\n            if filters.search:\n                search_term = f\"%{filters.search}%\"\n                query = query.where(\n                    or_(\n                        GlobalLabel.name.ilike(search_term),\n                        GlobalLabel.description.ilike(search_term)\n                    )\n                )\n            if filters.color:\n                query = query.where(GlobalLabel.color == filters.color)\n            if filters.min_usage is not None:\n                query = query.where(GlobalLabel.usage_count >= filters.min_usage)\n            if filters.max_usage is not None:\n                query = query.where(GlobalLabel.usage_count <= filters.max_usage)\n        \n        query = query.order_by(GlobalLabel.usage_count.desc(), GlobalLabel.name)\n        \n        result = await session.execute(query)\n        labels = result.scalars().all()\n        \n        # Format response\n        label_data = [\n            {\n                \"id\": str(label.id),\n                \"name\": label.name,\n                \"description\": label.description,\n                \"color\": label.color,\n                \"usage_count\": label.usage_count,\n                \"created_at\": label.created_at.isoformat(),\n                \"updated_at\": label.updated_at.isoformat()\n            }\n            for label in labels\n        ]\n        \n        # Cache the result (only if no filters)\n        if not filters:\n            try:\n                await redis_client.setex(\n                    cache_key,\n                    300,  # 5 minutes\n                    json.dumps(label_data, default=str)\n                )\n            except Exception as e:\n                logger.warning(f\"Cache storage failed: {e}\")\n        \n        return label_data\n\n\nasync def update_global_label(\n    session: AsyncSession,\n    redis_client,\n    label_id: UUID,\n    updates: UpdateLabelRequest\n) -> Optional[GlobalLabel]:\n    \"\"\"\n    Update a global label.\n    \n    Args:\n        session: Database session\n        redis_client: Redis client\n        label_id: Label ID to update\n        updates: Update data\n        \n    Returns:\n        Updated label or None if not found\n    \"\"\"\n    # Get the label\n    label_query = select(GlobalLabel).where(GlobalLabel.id == label_id)\n    label_result = await session.execute(label_query)\n    label = label_result.scalar_one_or_none()\n    \n    if not label:\n        return None\n    \n    # Apply updates\n    update_data = {}\n    if updates.name is not None:\n        update_data[\"name\"] = updates.name\n    if updates.description is not None:\n        update_data[\"description\"] = updates.description\n    if updates.color is not None:\n        update_data[\"color\"] = updates.color\n    \n    if update_data:\n        update_data[\"updated_at\"] = datetime.now(UTC)\n        \n        update_query = update(GlobalLabel).where(\n            GlobalLabel.id == label_id\n        ).values(**update_data)\n        \n        await session.execute(update_query)\n        \n        # Refresh the object\n        await session.refresh(label)\n        \n        # Evict caches\n        await _evict_label_caches(redis_client)\n        \n        logger.info(\n            f\"Updated global label {label_id}\",\n            extra={\n                \"label_id\": str(label_id),\n                \"updates\": update_data\n            }\n        )\n    \n    return label\n\n\nasync def delete_global_label(\n    session: AsyncSession,\n    redis_client,\n    label_id: UUID\n) -> bool:\n    \"\"\"\n    Delete a global label (hard delete).\n    \n    Args:\n        session: Database session\n        redis_client: Redis client\n        label_id: Label ID to delete\n        \n    Returns:\n        True if deleted, False if not found\n    \"\"\"\n    # Check if label exists\n    label_query = select(GlobalLabel).where(GlobalLabel.id == label_id)\n    label_result = await session.execute(label_query)\n    label = label_result.scalar_one_or_none()\n    \n    if not label:\n        return False\n    \n    # Delete the label (cascade will handle related records)\n    delete_query = delete(GlobalLabel).where(GlobalLabel.id == label_id)\n    await session.execute(delete_query)\n    \n    # Evict caches\n    await _evict_label_caches(redis_client)\n    \n    logger.info(\n        f\"Deleted global label {label_id}\",\n        extra={\"label_id\": str(label_id), \"label_name\": label.name}\n    )\n    \n    return True\n\n\n# ──────────────────────────────────────────────────────────────────────────────\n# BRAND LABEL FUNCTIONS\n# ──────────────────────────────────────────────────────────────────────────────\n\nasync def get_brand_labels(\n    db_conn,\n    redis_client,\n    brand_id: UUID,\n    filters: Optional[BrandLabelFilters] = None\n) -> List[Dict[str, Any]]:\n    \"\"\"\n    Get labels used by a specific brand.\n    \n    Args:\n        db_conn: Database connection manager\n        redis_client: Redis client\n        brand_id: Brand ID\n        filters: Optional filters\n        \n    Returns:\n        List of brand label dictionaries\n    \"\"\"\n    cache_key = _get_brand_labels_cache_key(brand_id)\n    \n    # Try cache first (only if no filters)\n    if not filters:\n        try:\n            cached_data = await redis_client.get(cache_key)\n            if cached_data:\n                logger.debug(f\"Retrieved brand labels from cache for brand {brand_id}\")\n                return json.loads(cached_data)\n        except Exception as e:\n            logger.warning(f\"Cache retrieval failed: {e}\")\n    \n    async with db_conn.get_db() as session:\n        # Build query\n        query = select(BrandLabel).where(\n            BrandLabel.brand_id == brand_id\n        ).options(\n            joinedload(BrandLabel.global_label),\n            joinedload(BrandLabel.creator)\n        )\n        \n        # Apply filters\n        if filters:\n            if filters.search:\n                search_term = f\"%{filters.search}%\"\n                query = query.join(GlobalLabel).where(\n                    or_(\n                        GlobalLabel.name.ilike(search_term),\n                        GlobalLabel.description.ilike(search_term)\n                    )\n                )\n            if filters.color:\n                query = query.join(GlobalLabel).where(GlobalLabel.color == filters.color)\n            if filters.min_usage is not None:\n                query = query.where(BrandLabel.usage_count >= filters.min_usage)\n            if filters.max_usage is not None:\n                query = query.where(BrandLabel.usage_count <= filters.max_usage)\n            if filters.created_by:\n                query = query.where(BrandLabel.created_by == filters.created_by)\n        \n        query = query.order_by(BrandLabel.usage_count.desc(), GlobalLabel.name)\n        \n        result = await session.execute(query)\n        brand_labels = result.scalars().all()\n        \n        # Format response\n        label_data = [\n            {\n                \"id\": str(bl.id),\n                \"brand_id\": str(bl.brand_id),\n                \"global_label\": {\n                    \"id\": str(bl.global_label.id),\n                    \"name\": bl.global_label.name,\n                    \"description\": bl.global_label.description,\n                    \"color\": bl.global_label.color,\n                    \"usage_count\": bl.global_label.usage_count,\n                    \"created_at\": bl.global_label.created_at.isoformat(),\n                    \"updated_at\": bl.global_label.updated_at.isoformat()\n                },\n                \"usage_count\": bl.usage_count,\n                \"created_by\": str(bl.created_by),\n                \"created_at\": bl.created_at.isoformat(),\n                \"updated_at\": bl.updated_at.isoformat()\n            }\n            for bl in brand_labels\n        ]\n        \n        # Cache the result (only if no filters)\n        if not filters:\n            try:\n                await redis_client.setex(\n                    cache_key,\n                    300,  # 5 minutes\n                    json.dumps(label_data, default=str)\n                )\n            except Exception as e:\n                logger.warning(f\"Cache storage failed: {e}\")\n        \n        return label_data\n\n\nasync def get_label_autocomplete(\n    db_conn,\n    redis_client,\n    brand_id: UUID,\n    query: str = \"\",\n    limit: int = 10\n) -> List[Dict[str, Any]]:\n    \"\"\"\n    Get label autocomplete suggestions for a brand.\n    \n    Args:\n        db_conn: Database connection manager\n        redis_client: Redis client\n        brand_id: Brand ID\n        query: Search query\n        limit: Maximum number of suggestions\n        \n    Returns:\n        List of label suggestions\n    \"\"\"\n    cache_key = _get_label_autocomplete_cache_key(brand_id, query)\n    \n    # Try cache first\n    try:\n        cached_data = await redis_client.get(cache_key)\n        if cached_data:\n            logger.debug(f\"Retrieved label autocomplete from cache for brand {brand_id}\")\n            return json.loads(cached_data)\n    except Exception as e:\n        logger.warning(f\"Cache retrieval failed: {e}\")\n    \n    async with db_conn.get_db() as session:\n        # Build query - prioritize labels used by this brand, then all global labels\n        brand_labels_query = select(\n            GlobalLabel,\n            BrandLabel.usage_count.label('brand_usage_count')\n        ).select_from(\n            GlobalLabel\n        ).join(\n            BrandLabel,\n            and_(\n                BrandLabel.global_label_id == GlobalLabel.id,\n                BrandLabel.brand_id == brand_id\n            )\n        )\n        \n        global_labels_query = select(\n            GlobalLabel,\n            text('0').label('brand_usage_count')\n        ).where(\n            ~GlobalLabel.id.in_(\n                select(BrandLabel.global_label_id).where(\n                    BrandLabel.brand_id == brand_id\n                )\n            )\n        )\n        \n        # Combine queries\n        combined_query = brand_labels_query.union_all(global_labels_query)\n        \n        # Apply search filter if provided\n        if query:\n            search_term = f\"%{query}%\"\n            combined_query = combined_query.where(\n                GlobalLabel.name.ilike(search_term)\n            )\n        \n        # Order by brand usage first, then global usage, then name\n        combined_query = combined_query.order_by(\n            text('brand_usage_count DESC'),\n            GlobalLabel.usage_count.desc(),\n            GlobalLabel.name\n        ).limit(limit)\n        \n        result = await session.execute(combined_query)\n        rows = result.all()\n        \n        # Format response\n        suggestions = [\n            {\n                \"id\": str(row.GlobalLabel.id),\n                \"name\": row.GlobalLabel.name,\n                \"description\": row.GlobalLabel.description,\n                \"color\": row.GlobalLabel.color,\n                \"usage_count\": row.GlobalLabel.usage_count,\n                \"created_at\": row.GlobalLabel.created_at.isoformat(),\n                \"updated_at\": row.GlobalLabel.updated_at.isoformat()\n            }\n            for row in rows\n        ]\n        \n        # Cache the result\n        try:\n            await redis_client.setex(\n                cache_key,\n                60,  # 1 minute for autocomplete\n                json.dumps(suggestions, default=str)\n            )\n        except Exception as e:\n            logger.warning(f\"Cache storage failed: {e}\")\n        \n        return suggestions\n\n\n# ──────────────────────────────────────────────────────────────────────────────\n# LABEL ASSIGNMENT FUNCTIONS\n# ──────────────────────────────────────────────────────────────────────────────\n\nasync def assign_labels_to_entry(\n    session: AsyncSession,\n    redis_client,\n    entry_id: UUID,\n    user_id: UUID,\n    label_ids: List[UUID]\n) -> Tuple[bool, List[str]]:\n    \"\"\"\n    Assign labels to an influencer entry.\n    \n    Args:\n        session: Database session\n        redis_client: Redis client\n        entry_id: Entry ID\n        user_id: User making the assignment\n        label_ids: List of label IDs to assign\n        \n    Returns:\n        Tuple of (success, list of error messages)\n        \n    Raises:\n        ValueError: If entry not found or user doesn't have permission\n    \"\"\"\n    # Get the entry and verify permissions\n    entry_query = select(BrandInfluencerListEntry).where(\n        BrandInfluencerListEntry.id == entry_id\n    ).options(\n        joinedload(BrandInfluencerListEntry.influencer_list)\n    )\n    \n    entry_result = await session.execute(entry_query)\n    entry = entry_result.scalar_one_or_none()\n    \n    if not entry:\n        raise ValueError(\"Entry not found\")\n    \n    # Check if user is a member of the brand\n    membership_query = select(BrandMembership).where(\n        BrandMembership.brand_id == entry.influencer_list.brand_id,\n        BrandMembership.user_id == user_id,\n        BrandMembership.is_active == True\n    )\n    membership_result = await session.execute(membership_query)\n    membership = membership_result.scalar_one_or_none()\n    \n    if not membership:\n        raise ValueError(\"User is not a member of this brand\")\n    \n    # Get existing assignments\n    existing_query = select(BrandInfluencerListEntryLabel.global_label_id).where(\n        BrandInfluencerListEntryLabel.entry_id == entry_id\n    )\n    existing_result = await session.execute(existing_query)\n    existing_labels = {row[0] for row in existing_result}\n    \n    # Determine new labels to assign\n    new_labels = set(label_ids) - existing_labels\n    errors = []\n    \n    if new_labels:\n        # Verify all labels exist\n        labels_query = select(GlobalLabel.id).where(\n            GlobalLabel.id.in_(new_labels)\n        )\n        labels_result = await session.execute(labels_query)\n        valid_labels = {row[0] for row in labels_result}\n        \n        invalid_labels = new_labels - valid_labels\n        if invalid_labels:\n            errors.extend([f\"Label {label_id} not found\" for label_id in invalid_labels])\n        \n        # Assign valid labels\n        for label_id in valid_labels:\n            assignment = BrandInfluencerListEntryLabel(\n                entry_id=entry_id,\n                global_label_id=label_id,\n                assigned_by=user_id\n            )\n            session.add(assignment)\n        \n        if valid_labels:\n            await session.flush()\n            \n            # Update usage counts\n            await _update_label_usage_counts(session, entry.influencer_list.brand_id, valid_labels)\n            \n            # Evict caches\n            await _evict_label_caches(redis_client, entry.influencer_list.brand_id)\n            \n            logger.info(\n                f\"Assigned {len(valid_labels)} labels to entry {entry_id}\",\n                extra={\n                    \"entry_id\": str(entry_id),\n                    \"user_id\": str(user_id),\n                    \"label_ids\": [str(lid) for lid in valid_labels]\n                }\n            )\n    \n    return len(errors) == 0, errors\n\n\nasync def remove_labels_from_entry(\n    session: AsyncSession,\n    redis_client,\n    entry_id: UUID,\n    user_id: UUID,\n    label_ids: List[UUID]\n) -> Tuple[bool, List[str]]:\n    \"\"\"\n    Remove labels from an influencer entry.\n    \n    Args:\n        session: Database session\n        redis_client: Redis client\n        entry_id: Entry ID\n        user_id: User making the removal\n        label_ids: List of label IDs to remove\n        \n    Returns:\n        Tuple of (success, list of error messages)\n        \n    Raises:\n        ValueError: If entry not found or user doesn't have permission\n    \"\"\"\n    # Get the entry and verify permissions\n    entry_query = select(BrandInfluencerListEntry).where(\n        BrandInfluencerListEntry.id == entry_id\n    ).options(\n        joinedload(BrandInfluencerListEntry.influencer_list)\n    )\n    \n    entry_result = await session.execute(entry_query)\n    entry = entry_result.scalar_one_or_none()\n    \n    if not entry:\n        raise ValueError(\"Entry not found\")\n    \n    # Check if user is a member of the brand\n    membership_query = select(BrandMembership).where(\n        BrandMembership.brand_id == entry.influencer_list.brand_id,\n        BrandMembership.user_id == user_id,\n        BrandMembership.is_active == True\n    )\n    membership_result = await session.execute(membership_query)\n    membership = membership_result.scalar_one_or_none()\n    \n    if not membership:\n        raise ValueError(\"User is not a member of this brand\")\n    \n    # Remove the assignments\n    delete_query = delete(BrandInfluencerListEntryLabel).where(\n        and_(\n            BrandInfluencerListEntryLabel.entry_id == entry_id,\n            BrandInfluencerListEntryLabel.global_label_id.in_(label_ids)\n        )\n    )\n    \n    result = await session.execute(delete_query)\n    removed_count = result.rowcount\n    \n    if removed_count > 0:\n        # Update usage counts (decrease)\n        await _update_label_usage_counts(session, entry.influencer_list.brand_id, label_ids, increment=False)\n        \n        # Evict caches\n        await _evict_label_caches(redis_client, entry.influencer_list.brand_id)\n        \n        logger.info(\n            f\"Removed {removed_count} label assignments from entry {entry_id}\",\n            extra={\n                \"entry_id\": str(entry_id),\n                \"user_id\": str(user_id),\n                \"removed_count\": removed_count\n            }\n        )\n    \n    return True, []\n\n\n# ──────────────────────────────────────────────────────────────────────────────\n# HELPER FUNCTIONS\n# ──────────────────────────────────────────────────────────────────────────────\n\nasync def _update_label_usage_counts(\n    session: AsyncSession,\n    brand_id: UUID,\n    label_ids: List[UUID],\n    increment: bool = True\n):\n    \"\"\"\n    Update usage counts for labels.\n    \n    Args:\n        session: Database session\n        brand_id: Brand ID\n        label_ids: List of label IDs\n        increment: Whether to increment (True) or decrement (False)\n    \"\"\"\n    delta = 1 if increment else -1\n    \n    # Update global usage counts\n    global_update = update(GlobalLabel).where(\n        GlobalLabel.id.in_(label_ids)\n    ).values(\n        usage_count=GlobalLabel.usage_count + delta,\n        updated_at=datetime.now(UTC)\n    )\n    await session.execute(global_update)\n    \n    # Update or create brand usage counts\n    for label_id in label_ids:\n        # Check if brand label exists\n        brand_label_query = select(BrandLabel).where(\n            and_(\n                BrandLabel.brand_id == brand_id,\n                BrandLabel.global_label_id == label_id\n            )\n        )\n        brand_label_result = await session.execute(brand_label_query)\n        brand_label = brand_label_result.scalar_one_or_none()\n        \n        if brand_label:\n            # Update existing\n            brand_update = update(BrandLabel).where(\n                BrandLabel.id == brand_label.id\n            ).values(\n                usage_count=BrandLabel.usage_count + delta,\n                updated_at=datetime.now(UTC)\n            )\n            await session.execute(brand_update)\n        elif increment:  # Only create new if incrementing\n            # Create new brand label\n            new_brand_label = BrandLabel(\n                brand_id=brand_id,\n                global_label_id=label_id,\n                usage_count=1,\n                created_by=None  # This should be set by the caller if needed\n            )\n            session.add(new_brand_label)\n\n\nasync def get_entry_labels(\n    db_conn,\n    entry_id: UUID\n) -> List[Dict[str, Any]]:\n    \"\"\"\n    Get all labels assigned to an entry.\n    \n    Args:\n        db_conn: Database connection manager\n        entry_id: Entry ID\n        \n    Returns:\n        List of label assignment dictionaries\n    \"\"\"\n    async with db_conn.get_db() as session:\n        query = select(BrandInfluencerListEntryLabel).where(\n            BrandInfluencerListEntryLabel.entry_id == entry_id\n        ).options(\n            joinedload(BrandInfluencerListEntryLabel.global_label),\n            joinedload(BrandInfluencerListEntryLabel.assigned_by_user)\n        )\n        \n        result = await session.execute(query)\n        assignments = result.scalars().all()\n        \n        return [\n            {\n                \"id\": str(assignment.id),\n                \"entry_id\": str(assignment.entry_id),\n                \"global_label\": {\n                    \"id\": str(assignment.global_label.id),\n                    \"name\": assignment.global_label.name,\n                    \"description\": assignment.global_label.description,\n                    \"color\": assignment.global_label.color,\n                    \"usage_count\": assignment.global_label.usage_count,\n                    \"created_at\": assignment.global_label.created_at.isoformat(),\n                    \"updated_at\": assignment.global_label.updated_at.isoformat()\n                },\n                \"assigned_by\": str(assignment.assigned_by),\n                \"assigned_at\": assignment.assigned_at.isoformat()\n            }\n            for assignment in assignments\n        ]\n