"""
RBAC utility functions with cache-aside pattern.
"""
from typing import Optional
from uuid import UUID

from sqlalchemy import select

from app.core.logger import get_logger
from app.core.redis_keys import Redis<PERSON><PERSON>s, RedisConfig
from app.core_helper.async_logger import with_trace_id
from app.models.user_models import Master<PERSON><PERSON>, MasterAuthMethod

logger = get_logger()


@with_trace_id
async def get_role_id_by_uuid_cache_aside(db_conn, redis_client, role_uuid: str) -> Optional[str]:
    # Make sure you're using db_conn correctly
    key = RedisKeys.role_id_by_uuid(role_uuid)
    
    # Try to get from cache first
    role_id = await redis_client.get(key)
    if role_id:
        return role_id
        
    try:
        # If not in cache, query from database
        async with db_conn.get_db() as session:
            query = select(MasterRole.id).where(MasterRole.id == role_uuid)
            result = await session.execute(query)
            role_id = result.scalar_one_or_none()
            
            if role_id:
                # Store in cache
                await redis_client.set(key, str(role_id))
                await redis_client.expire(key, RedisConfig.ROLE_TTL)
                return str(role_id)
            return None
    except Exception as e:
        logger.error("Error retrieving role ID from database", error=str(e), exc_info=True)
        return None
    
    # Fetch from database if not in cache
    try:
        async with db_conn.get_db() as session:
            query = select(MasterRole.id).where(MasterRole.id == role_uuid)
            result = await session.execute(query)
            role_id = result.scalar_one_or_none()
            
            if role_id:
                # Cache the result
                try:
                    str_role_id = str(role_id)
                    await redis_client.set(cache_key, str_role_id)
                    await redis_client.expire(cache_key, RedisConfig.RBAC_TTL)
                    logger.debug("Role ID cached", extra={"uuid": role_uuid, "id": str_role_id})
                    return str_role_id
                except Exception as cache_err:
                    logger.warning("Failed to cache role ID", extra={"error": str(cache_err)})
                    return str(role_id)
            
            return None
    except Exception as db_err:
        logger.error("Error retrieving role ID from database", extra={"error": str(db_err)})
        return None


@with_trace_id
async def get_auth_method_id_by_name_cache_aside(db_conn, redis_client, method_name: str) -> Optional[str]:
    """
    Get authentication method ID by name with cache-aside pattern.
    
    Args:
        db_conn: Database connection
        redis_client: Redis client
        method_name: Auth method name (e.g., "email", "mobile", "google")
        
    Returns:
        Auth method ID if found, None otherwise
    """
    logger = get_logger()
    
    # Try to get from cache first
    cache_key = RedisKeys.auth_method_to_id(method_name)
    
    try:
        cached_id = await redis_client.get(cache_key)
        if cached_id:
            logger.debug("Auth method ID cache hit", extra={"name": method_name, "id": cached_id})
            return cached_id
    except Exception as e:
        logger.error("Redis error when retrieving auth method ID", extra={"error": str(e)})
    
    # Fetch from database if not in cache
    try:
        async with db_conn.get_db() as session:
            query = select(MasterAuthMethod.id).where(MasterAuthMethod.method_key == method_name)
            result = await session.execute(query)
            auth_method_id = result.scalar_one_or_none()
            
            if auth_method_id:
                # Cache the result
                try:
                    str_auth_id = str(auth_method_id)
                    await redis_client.set(cache_key, str_auth_id)
                    await redis_client.expire(cache_key, RedisConfig.RBAC_TTL)
                    logger.debug("Auth method ID cached", extra={"name": method_name, "id": str_auth_id})
                    return str_auth_id
                except Exception as cache_err:
                    logger.warning("Failed to cache auth method ID", extra={"error": str(cache_err)})
                    return str(auth_method_id)
            
            return None
    except Exception as db_err:
        logger.error("Error retrieving auth method ID from database", extra={"error": str(db_err)})
        return None
