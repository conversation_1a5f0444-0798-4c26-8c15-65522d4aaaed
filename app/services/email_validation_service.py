"""
Advanced Email Validation Service for CreatorVerse Backend.
Implements SMTP verification, domain validation, and MX record checking with cache-aside pattern.
"""
import smtplib
import socket
from concurrent.futures import Thread<PERSON>oolExecutor, TimeoutError, as_completed
from contextlib import suppress
from typing import List, Tuple

import dns.resolver
from sqlalchemy import select

from app.core.config import APP_CONFIG, get_locobuzz_redis, get_database
from app.core.exceptions import (
    EmailValidationError,
    EmailAlreadyExistsError,
    EmailNotFoundError,
    ServiceUnavailableError,
    create_email_validation_error
)
from app.core.logger import get_logger
from app.core.redis_keys import RedisKeys, RedisConfig
from app.core_helper.async_logger import with_trace_id
from app.models.user_models import User
from app.utilities.bloom_filter import get_bloom_filter_manager
from app.utilities.validation_functions import validate_email_format

# SMTP response codes
SMTP_SUCCESS_CODE = 250
SMTP_PERMANENT_FAILURE_START = 550
SMTP_PERMANENT_FAILURE_END = 554

# Reserved domains that should not be allowed for user registration (RFC 2606)
RESERVED_DOMAINS = {
    'example.com', 'example.net', 'example.org',
    'test.com', 'test.net', 'test.org',
    'localhost', 'localhost.localdomain',
    'invalid', 'local'
}

# Additional blocked domains for testing/placeholder purposes
BLOCKED_TEST_DOMAINS = {
    'test.example', 'user.example', 'mail.example',
    'demo.com', 'sample.com', 'placeholder.com'
}



# All blocked domains combined for easier checking
ALL_BLOCKED_DOMAINS = RESERVED_DOMAINS | BLOCKED_TEST_DOMAINS


class AdvancedEmailValidationService:
    """
    Advanced email validation service with SMTP verification and domain validation.
    Implements cache-aside pattern for performance optimization.
    """

    def __init__(self, redis_client=get_locobuzz_redis(), db_conn=get_database()):
        self.redis_client = redis_client
        self.db_conn = db_conn
        self.logger = get_logger()

    @with_trace_id
    async def validate_email_format_advanced(self, email: str) -> Tuple[str, str]:
        """
        Advanced email format validation with reserved domain blocking.
        
        Args:
            email: Email address to validate
            
        Returns:
            Tuple[str, str]: (local_part, domain)
            
        Raises:
            EmailValidationError: If email format is invalid or uses reserved domains
        """
        # Use existing validation function first
        if not validate_email_format(email):
            raise create_email_validation_error("Invalid email format")

        # Extract parts
        local_part, domain = email.rsplit("@", 1)
        domain_lower = domain.lower()

        # Block reserved domains (RFC 2606)
        if domain_lower in RESERVED_DOMAINS:
            raise create_email_validation_error(
                f"Reserved domain '{domain}' is not allowed for registration (RFC 2606)"
            )

        # Block test domains
        if domain_lower in BLOCKED_TEST_DOMAINS:
            raise create_email_validation_error(
                f"Test domain '{domain}' is not allowed for registration"
            )

        # Block company domains
        # if domain_lower in BLOCKED_COMPANY_DOMAINS:
        #     raise create_email_validation_error(
        #         f"Company domain '{domain}' is not allowed for customer registration"
        #     )

        return local_part, domain

    @with_trace_id
    async def validate_domain_mx_with_cache(self, domain: str, timeout: float = 2.0) -> bool:
        """
        Validate domain MX records with Redis caching for performance.
        Implements cache-aside pattern.
        
        Args:
            domain: Domain to validate
            timeout: DNS lookup timeout
            
        Returns:
            bool: True if domain has valid MX records
        """
        cache_key = RedisKeys.email_domain_mx(domain)

        try:
            # Check cache first (cache-aside pattern)
            cached_result = await self.redis_client.get(cache_key)
            if cached_result is not None:
                self.logger.debug(f"Domain MX validation cache hit for {domain}")
                return cached_result.lower() == "true"
        except Exception as e:
            self.logger.warning(f"Redis cache read failed for domain {domain}: {e}")

        # Cache miss - perform MX lookup
        self.logger.info(f"Cache miss for domain MX validation: {domain}")
        mx_hosts, success = await self._get_mx_hosts_async(domain, timeout)
        has_mx = len(mx_hosts) > 0 and success

        # Update cache
        try:
            await self.redis_client.setex(
                cache_key,
                RedisConfig.EMAIL_DOMAIN_CACHE_TTL,
                "true" if has_mx else "false"
            )
            self.logger.debug(f"Domain MX validation cached for {domain}: {has_mx}")
        except Exception as e:
            self.logger.warning(f"Redis cache write failed for domain {domain}: {e}")

        return has_mx

    async def _get_mx_hosts_async(self, domain: str, timeout: float) -> Tuple[List[str], bool]:
        """
        Async wrapper for MX lookup.
        
        Args:
            domain: Domain to lookup
            timeout: Lookup timeout
            
        Returns:
            Tuple[List[str], bool]: (mx_hosts, success)
        """
        try:
            answers = dns.resolver.resolve(domain, "MX", lifetime=timeout)
            mx_hosts = [
                str(record.exchange).rstrip(".")
                for record in sorted(answers, key=lambda mx: mx.preference)
            ]
            return mx_hosts, True
        except (
                dns.resolver.NoAnswer,
                dns.resolver.NXDOMAIN,
                dns.resolver.Timeout,
                socket.gaierror,
        ) as e:
            self.logger.debug(f"MX lookup failed for domain {domain}: {e}")
            return [], False

    @with_trace_id
    async def verify_email_smtp(
            self,
            email: str,
            from_address: str = "<EMAIL>"
    ) -> Tuple[bool, str]:
        """
        SMTP-based email verification with environment-aware logic.
        
        Args:
            email: Email address to verify
            from_address: From address for SMTP verification
            
        Returns:
            Tuple[bool, str]: (is_valid, message)
            
        Raises:
            EmailValidationError: If validation fails
        """
        # 1. Basic format validation
        local_part, domain = await self.validate_email_format_advanced(email)

        # 2. Environment-based validation strategy
        if not getattr(APP_CONFIG, 'email_smtp_validation_enabled', True):
            # Fast validation for development/test environments
            has_mx = await self.validate_domain_mx_with_cache(domain, 1.0)

            if has_mx:
                self.logger.info(f"Email validation bypassed for development: {email}")
                return True, f"Development mode - format and domain validated for {email}"
            else:
                raise create_email_validation_error(f"Invalid domain: {domain}")

        # 3. Full SMTP validation for production
        return await self._verify_email_smtp_parallel(email, from_address)

    async def _verify_email_smtp_parallel(
            self,
            email: str,
            from_address: str,
            timeout: float = 5.0
    ) -> Tuple[bool, str]:
        """
        Parallel SMTP verification across multiple MX servers.
        
        Args:
            email: Email to verify
            from_address: From address for verification
            timeout: Total timeout for verification
            
        Returns:
            Tuple[bool, str]: (is_valid, message)
        """
        local_part, domain = email.rsplit("@", 1)

        # Get MX hosts
        mx_hosts, success = await self._get_mx_hosts_async(domain, timeout / 2)
        if not mx_hosts or not success:
            raise create_email_validation_error(f"No MX records for domain {domain}")

        # Parallel SMTP probes
        with ThreadPoolExecutor(max_workers=min(len(mx_hosts), 5)) as executor:
            futures = [
                executor.submit(self._probe_mx_smtp, mx, email, from_address, timeout / 2)
                for mx in mx_hosts
            ]

            try:
                for future in as_completed(futures, timeout=timeout):
                    try:
                        success, reason, definitive = future.result()
                        if success or definitive:
                            # Cancel remaining futures
                            for f in futures:
                                f.cancel()
                            if not success and definitive:
                                raise create_email_validation_error(reason)
                            return success, reason
                    except Exception:
                        continue
            except TimeoutError:
                # Cancel all futures on timeout
                for future in futures:
                    future.cancel()
                raise create_email_validation_error("Email verification timed out")

        raise create_email_validation_error("Could not verify email")

    def _probe_mx_smtp(
            self,
            mx: str,
            email: str,
            from_address: str,
            timeout: float
    ) -> Tuple[bool, str, bool]:
        """
        Probe MX server via SMTP.
        
        Args:
            mx: MX server hostname
            email: Email to verify
            from_address: From address
            timeout: Connection timeout
            
        Returns:
            Tuple[bool, str, bool]: (success, reason, definitive)
        """
        smtp = None
        try:
            smtp = smtplib.SMTP(mx, timeout=timeout)
            smtp.ehlo_or_helo_if_needed()
            smtp.mail(from_address)
            code, msg = smtp.rcpt(email)
            text = msg.decode("utf-8", "ignore")

            if code == SMTP_SUCCESS_CODE:
                return True, "Email exists", True
            if SMTP_PERMANENT_FAILURE_START <= code <= SMTP_PERMANENT_FAILURE_END:
                return False, f"Rejected: {text}", True
            return False, f"Unexpected code: {code}", False
        except (OSError, smtplib.SMTPException) as e:
            return False, f"Transient error: {str(e)}", False
        finally:
            if smtp:
                with suppress(OSError, smtplib.SMTPException):
                    smtp.quit()

    @with_trace_id
    async def check_email_exists_in_system(self, user_email: str) -> None:
        """
        Check if email exists in system for login purposes using cache-aside pattern.
        
        Args:
            user_email: Email to check
            
        Raises:
            EmailNotFoundError: If email not found
        """
        bloom_filter_manager = get_bloom_filter_manager(self.redis_client)

        # First check bloom filter (fast negative lookup)
        if bloom_filter_manager.check_email_not_exists(user_email):
            raise EmailNotFoundError(user_email)

        # Bloom filter says it might exist, verify in database
        try:
            async with self.db_conn.get_db() as session:
                stmt = select(User.id).where(User.email == user_email)
                result = await session.execute(stmt)
                user_exists = result.scalar_one_or_none()

                if not user_exists:
                    # Update bloom filter with negative result
                    bloom_filter_manager.add_email(user_email)  # Mark as not existing
                    raise EmailNotFoundError(user_email)

                self.logger.debug(f"Email exists in system: {user_email}")
        except EmailNotFoundError:
            raise
        except Exception as e:
            self.logger.error(f"Database error checking email existence: {e}")
            raise ServiceUnavailableError("Database", "Unable to verify email existence")

    @with_trace_id
    async def check_email_not_in_system(self, user_email: str) -> None:
        """
        Check if email is NOT already in system for registration purposes.
        
        Args:
            user_email: Email to check
            
        Raises:
            EmailAlreadyExistsError: If email already exists
        """
        bloom_filter_manager = get_bloom_filter_manager(self.redis_client)

        # Check bloom filter first (fast positive lookup)
        if bloom_filter_manager.check_email_exists(user_email):
            # Verify in database since bloom filter can have false positives
            try:
                async with self.db_conn.get_db() as session:
                    stmt = select(User.id).where(User.email == user_email)
                    result = await session.execute(stmt)
                    existing_user = result.scalar_one_or_none()

                    if existing_user:
                        raise EmailAlreadyExistsError(user_email)
                    else:
                        # False positive in bloom filter - this is expected
                        self.logger.debug(f"Bloom filter false positive for email: {user_email}")
            except EmailAlreadyExistsError:
                raise
            except Exception as e:
                self.logger.error(f"Database error checking email uniqueness: {e}")
                raise ServiceUnavailableError("Database", "Unable to verify email uniqueness")

        # Email is not in system, safe to proceed
        self.logger.debug(f"Email not in system: {user_email}")


# Factory function with caching
from functools import lru_cache


@lru_cache(maxsize=1)
def get_email_validation_service(
        redis_client=get_locobuzz_redis(),
        db_conn=get_database()
) -> AdvancedEmailValidationService:
    """
    Factory function to get email validation service instance.
    
    Returns:
        AdvancedEmailValidationService: Configured service instance
    """
    return AdvancedEmailValidationService(redis_client, db_conn)


# Convenience functions for backward compatibility
@with_trace_id
async def validate_email_not_reserved(email: str) -> None:
    """
    Validate that email domain is not a reserved or test domain.
    
    Args:
        email: Email address to validate
        
    Raises:
        EmailValidationError: If email uses reserved or test domain
    """
    if not email or "@" not in email:
        return

    domain = email.split("@")[-1].lower()

    if domain in RESERVED_DOMAINS:
        raise create_email_validation_error(
            f"Reserved domain '{domain}' is not allowed for registration. "
            f"Please use a real email address."
        )

    if domain in BLOCKED_TEST_DOMAINS:
        raise create_email_validation_error(
            f"Test domain '{domain}' is not allowed for registration. "
            f"Please use a real email address."
        )

    # if domain in BLOCKED_COMPANY_DOMAINS:
    #     raise create_email_validation_error(
    #         f"Company domain '{domain}' is not allowed for customer registration. "
    #         f"Please use a personal or business email address."
    #     )


@with_trace_id
async def verify_email_exists_optimized(
        email: str,
        redis_client=get_locobuzz_redis(),
        from_address: str = "<EMAIL>"
) -> Tuple[bool, str]:
    """
    Convenience function for optimized email verification.
    
    Args:
        email: Email to verify
        redis_client: Redis client instance
        from_address: From address for SMTP verification
        
    Returns:
        Tuple[bool, str]: (is_valid, message)
    """
    service = get_email_validation_service(redis_client)
    return await service.verify_email_smtp(email, from_address)


@with_trace_id
async def check_email_exists_in_system(
        user_email: str,
        redis_client=get_locobuzz_redis(),
        db_conn=get_database()
) -> None:
    """
    Convenience function to check if email exists in system.
    
    Args:
        user_email: Email to check
        redis_client: Redis client instance
        db_conn: Database connection
    """
    service = get_email_validation_service(redis_client, db_conn)
    await service.check_email_exists_in_system(user_email)


@with_trace_id
async def check_email_not_in_system(
        user_email: str,
        redis_client=get_locobuzz_redis(),
        db_conn=get_database()
) -> None:
    """
    Convenience function to check if email is not in system.
    
    Args:
        user_email: Email to check
        redis_client: Redis client instance
        db_conn: Database connection
    """
    service = get_email_validation_service(redis_client, db_conn)
    await service.check_email_not_in_system(user_email)
