"""
Email service for sending emails using Mailgun API with async support.
"""
from functools import lru_cache
from typing import Optional

import httpx
from pydantic import BaseModel

from app.core.config import APP_CONFIG, get_locobuzz_redis
from app.core.logger import get_logger
from app.templates.email_templates import (
    get_influencer_registration_otp_email_template,
    get_influencer_login_otp_email_template,
    get_influencer_welcome_email_template,
    get_brand_registration_otp_email_template,
    get_brand_login_otp_email_template,
    get_brand_welcome_email_template,
)


class EmailConfig(BaseModel):
    """Email configuration settings"""
    mailgun_domain: str
    mailgun_secret: str
    sender_name: str = "CreatorVerse"


class EmailService:
    """Async email service using Mailgun API with connection reuse"""

    # Class-level httpx client for connection pooling
    _http_client: Optional[httpx.AsyncClient] = None

    def __init__(self, config: EmailConfig):
        """
        Initialize email service with configuration.

        Args:
            config: Email configuration settings
        """
        self.config = config
        self.logger =  get_logger()  
        self.base_url = f"https://api.mailgun.net/v3/{self.config.mailgun_domain}"
        self.auth = ("api", self.config.mailgun_secret)

    async def get_http_client(self) -> httpx.AsyncClient:
        """
        Get or create the shared HTTP client with connection pooling.
        
        Returns:
            httpx.AsyncClient: Shared HTTP client
        """
        if EmailService._http_client is None or EmailService._http_client.is_closed:
            # Create new client with connection pooling
            EmailService._http_client = httpx.AsyncClient(
                timeout=30.0,
                limits=httpx.Limits(max_keepalive_connections=5, max_connections=10),
            )
            self.logger.info("Created new Mailgun HTTP client")
        return EmailService._http_client

    async def send_email(
            self,
            text: str,
            receiver: str,
            subject: str,
            sender: Optional[str] = None,
            report_name: Optional[str] = None,
    ) -> bool:
        """
        Send email using Mailgun API asynchronously with connection reuse.

        Args:
            text: HTML content of the email
            receiver: Recipient email address
            subject: Email subject
            sender: Sender name (optional, defaults to config sender_name)
            report_name: Report name for logging (optional)

        Returns:
            bool: True if email sent successfully, False otherwise
        """
        sender_name = sender or self.config.sender_name

        email_data = {
            "from": f"{sender_name} <no-reply@{self.config.mailgun_domain}>",
            "to": receiver,
            "subject": subject,
            "html": text,
        }

        try:
            client = await self.get_http_client()
            response = await client.post(
                f"{self.base_url}/messages",
                auth=self.auth,
                data=email_data,
            )

            if response.status_code == 200:
                self.logger.info(f"Email sent successfully to {receiver}")
                return True

            self.logger.error(
                f"Failed to send email to {receiver}. "
                f"Status: {response.status_code}, Response: {response.text}"
            )
            return False

        except httpx.TimeoutException:
            self.logger.error(f"Timeout while sending email to {receiver}")
            return False

        except httpx.RequestError as e:
            self.logger.error(f"Network error while sending email to {receiver}: {str(e)}")
            return False

        except Exception as e:
            self.logger.error(f"Unexpected error while sending email to {receiver}: {str(e)}")
            return False

    async def send_registration_otp_email(self, email: str, otp: str, user_role: str = "influencer") -> bool:
        """
        Send OTP verification email for registration.

        Args:
            email: Recipient email address
            otp: One-time password
            user_role: User role (influencer/brand) to determine template

        Returns:
            bool: True if email sent successfully, False otherwise
        """
        if user_role.lower() == "brand":
            subject = "CreatorVerse Business - Welcome Partner! Verify Your Business Email"
            html_content = get_brand_registration_otp_email_template(otp, email)
            self.logger.info(f"Sending brand registration OTP email to {email}")
        else:
            subject = "CreatorVerse - Welcome! Verify Your Email"
            html_content = get_influencer_registration_otp_email_template(otp, email)
            self.logger.info(f"Sending influencer registration OTP email to {email}")

        return await self.send_email(
            text=html_content,
            receiver=email,
            subject=subject,
            report_name=f"{user_role}_registration_otp_verification",
        )

    async def send_login_otp_email(self, email: str, otp: str, user_role: str = "influencer") -> bool:
        """
        Send OTP verification email for login.

        Args:
            email: Recipient email address
            otp: One-time password
            user_role: User role (influencer/brand) to determine template

        Returns:
            bool: True if email sent successfully, False otherwise
        """
        if user_role.lower() == "brand":
            subject = "CreatorVerse Business - Business Account Login Verification"
            html_content = get_brand_login_otp_email_template(otp, email)
            self.logger.info(f"Sending brand login OTP email to {email}")
        else:
            subject = "CreatorVerse - Login Verification Code"
            html_content = get_influencer_login_otp_email_template(otp, email)
            self.logger.info(f"Sending influencer login OTP email to {email}")

        return await self.send_email(
            text=html_content,
            receiver=email,
            subject=subject,
            report_name=f"{user_role}_login_otp_verification",
        )

    async def send_welcome_email(self, email: str, user_role: str = "influencer") -> bool:
        """
        Send welcome email after successful registration.

        Args:
            email: Recipient email address
            user_role: User role (influencer/brand) to determine template

        Returns:
            bool: True if email sent successfully, False otherwise
        """
        if user_role.lower() == "brand":
            subject = "🎉 Welcome to CreatorVerse Business - Let's Elevate Your Brand!"
            html_content = get_brand_welcome_email_template(email)
            self.logger.info(f"Sending brand welcome email to {email}")
        else:
            subject = "🎉 Welcome to CreatorVerse - Your Creator Journey Starts Now!"
            html_content = get_influencer_welcome_email_template(email)
            self.logger.info(f"Sending influencer welcome email to {email}")

        return await self.send_email(
            text=html_content,
            receiver=email,
            subject=subject,
            report_name=f"{user_role}_welcome_email",
        )

    @classmethod
    async def close(cls) -> None:
        """Close the shared HTTP client when the application shuts down."""
        if cls._http_client and not cls._http_client.is_closed:
            await cls._http_client.aclose()
            cls._http_client = None


@lru_cache(maxsize=1)
def get_email_service() -> EmailService:
    """
    Factory function to create EmailService instance with config-based settings.
    Uses caching to return the same instance for multiple calls.

    Returns:
        EmailService: Configured email service
    """
    config = EmailConfig(
        mailgun_domain=APP_CONFIG.mailgun_domain if hasattr(APP_CONFIG, 'mailgun_domain') else "mail.creatorverse.com",
        mailgun_secret=APP_CONFIG.mailgun_secret if hasattr(APP_CONFIG, 'mailgun_secret') else "",
        sender_name=APP_CONFIG.email_sender_name if hasattr(APP_CONFIG, 'email_sender_name') else "CreatorVerse",
    )

    return EmailService(config)


# Add this to your application startup/shutdown handlers
async def close_email_service() -> None:
    """Close email service connections on application shutdown."""
    await EmailService.close()
