"""
Bulk insert service for profile analytics data.
Refactored from bulk_insert_analytics.py to be reusable as a service.
"""

import json
import uuid
import random
import psycopg2
from datetime import datetime
from typing import Dict, List, Any, Optional
from ..config import settings

# Enum values for post_type
POST_TYPES = ['REEL', 'STORY', 'STATIC_POST', 'CAROUSEL']

class BulkInsertService:
    """Service for bulk inserting profile analytics data into PostgreSQL."""
    
    def __init__(self, db_uri: Optional[str] = None):
        self.db_uri = db_uri or settings.DATABASE_URL
        self.batch_size = settings.BULK_INSERT_BATCH_SIZE
        self.max_profiles = settings.BULK_INSERT_MAX_PROFILES
    
    def connect_db(self):
        """Establish database connection"""
        try:
            conn = psycopg2.connect(self.db_uri)
            return conn
        except Exception as e:
            raise Exception(f"Error connecting to database: {e}")
    
    def load_profiles(self, filepath: str = 'generated_profile_analytics.json', limit: Optional[int] = None) -> List[Dict]:
        """Load profile data from JSON file"""
        try:
            with open(filepath, 'r') as f:
                data = json.load(f)
                limit = limit or self.max_profiles
                return data[:limit]
        except Exception as e:
            raise Exception(f"Error loading profiles from {filepath}: {e}")
    
    def ensure_master_data(self, conn, profiles: List[Dict]) -> None:
        """
        Ensure master data exists in api_provider schema.
        This includes platforms, interests, brands, etc.
        """
        cur = conn.cursor()
        platforms = {}
        interests = set()
        brands = set()
        
        # Extract unique platforms, interests, and brands
        for profile in profiles:
            # Add platform
            platform = profile.get('work_platform', {})
            if platform and 'name' in platform:
                platforms[platform.get('id')] = {
                    'name': platform.get('name'),
                    'logo_url': platform.get('logo_url')
                }
            
            # Add interests from top_interests
            profile_data = profile.get('profile', {})
            top_interests = profile_data.get('top_interests', [])
            for interest in top_interests:
                if 'name' in interest:
                    interests.add(interest['name'])
            
            # Add brands from top_mentions
            top_mentions = profile_data.get('top_mentions', [])
            for mention in top_mentions:
                if 'name' in mention:
                    brands.add(mention['name'])
        
        # Insert platforms if they don't exist
        for platform_id, platform_data in platforms.items():
            try:
                cur.execute("""
                    INSERT INTO api_provider.master_work_platform (id, name, logo_url, status)
                    VALUES (%s, %s, %s, 'ACTIVE')
                    ON CONFLICT (id) DO UPDATE 
                    SET name = EXCLUDED.name, logo_url = EXCLUDED.logo_url
                """, (platform_id, platform_data['name'], platform_data['logo_url']))
            except Exception as e:
                print(f"Error inserting platform {platform_id}: {e}")
        
        # Insert interests if they don't exist
        for interest in interests:
            try:
                cur.execute("""
                    INSERT INTO api_provider.master_interest (name)
                    VALUES (%s)
                    ON CONFLICT (name) DO NOTHING
                """, (interest,))
            except Exception as e:
                print(f"Error inserting interest {interest}: {e}")
        
        # Insert brands if they don't exist
        for brand in brands:
            try:
                cur.execute("""
                    INSERT INTO api_provider.master_brand (id, name)
                    VALUES (gen_random_uuid(), %s)
                    ON CONFLICT DO NOTHING
                """, (brand,))
            except Exception as e:
                print(f"Error inserting brand {brand}: {e}")
        
        conn.commit()
        cur.close()
        print(f"Inserted {len(platforms)} platforms, {len(interests)} interests, and {len(brands)} brands")
    
    def get_interest_ids(self, conn) -> Dict[str, str]:
        """Get mapping of interest names to IDs"""
        interest_map = {}
        cur = conn.cursor()
        try:
            cur.execute("SELECT id, name FROM api_provider.master_interest")
            for row in cur.fetchall():
                interest_map[row[1]] = row[0]
        except Exception as e:
            print(f"Error getting interest IDs: {e}")
        finally:
            cur.close()
        return interest_map
    
    def get_brand_ids(self, conn) -> Dict[str, str]:
        """Get mapping of brand names to IDs"""
        brand_map = {}
        cur = conn.cursor()
        try:
            cur.execute("SELECT id, name FROM api_provider.master_brand")
            for row in cur.fetchall():
                brand_map[row[1]] = row[0]
        except Exception as e:
            print(f"Error getting brand IDs: {e}")
        finally:
            cur.close()
        return brand_map
    
    def insert_profiles(self, conn, profiles: List[Dict]) -> int:
        """Insert profile data into profile_analytics.profile"""
        cur = conn.cursor()
        profile_count = 0
        
        try:
            # Prepare data for batch insert
            profile_values = []
            history_values = []
            
            for profile_data in profiles:
                profile_id = profile_data.get('id')
                work_platform = profile_data.get('work_platform', {})
                work_platform_id = work_platform.get('id')
                profile = profile_data.get('profile', {})
                
                # Add profile data
                profile_values.append((
                    profile_id, work_platform_id, profile.get('external_id'),
                    profile.get('platform_username'), profile.get('url'),
                    profile.get('image_url'), profile.get('full_name'),
                    profile.get('introduction'), profile.get('content_count'),
                    profile.get('is_verified'), profile.get('platform_account_type'),
                    profile.get('gender'), profile.get('age_group'),
                    profile.get('language'), profile.get('follower_count'),
                    profile.get('subscriber_count'), profile.get('average_likes'),
                    profile.get('average_comments'), profile.get('average_views'),
                    profile.get('average_reels_views'), profile.get('engagement_rate'),
                    None,  # credibility_score
                    datetime.now(), datetime.now()
                ))
                
                # Add reputation history data
                for history in profile.get('reputation_history', []):
                    history_values.append((
                        profile_id, f"{history.get('month')}-01",
                        history.get('follower_count'), history.get('subscriber_count'),
                        history.get('following_count'), history.get('average_likes'),
                        history.get('average_views'), history.get('average_comments'),
                        history.get('total_views'), history.get('total_likes')
                    ))
            
            # Batch insert profiles
            if profile_values:
                args_str = ','.join(cur.mogrify("(%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s)", x).decode('utf-8') for x in profile_values)
                cur.execute("""
                    INSERT INTO profile_analytics.profile (
                        id, work_platform_id, external_id, platform_username, url,
                        image_url, full_name, introduction, content_count,
                        is_verified, platform_account_type, gender, age_group,
                        language, follower_count, subscriber_count,
                        average_likes, average_comments, average_views, average_reels_views,
                        engagement_rate, credibility_score, created_at, updated_at
                    ) VALUES """ + args_str + " ON CONFLICT (id) DO NOTHING")
                profile_count = len(profile_values)
            
            # Batch insert reputation history
            if history_values:
                args_str = ','.join(cur.mogrify("(%s,%s,%s,%s,%s,%s,%s,%s,%s,%s)", x).decode('utf-8') for x in history_values)
                cur.execute("""
                    INSERT INTO profile_analytics.reputation_history (
                        profile_id, month, follower_count, subscriber_count,
                        following_count, average_likes, average_views, average_comments,
                        total_views, total_likes
                    ) VALUES """ + args_str + " ON CONFLICT (profile_id, month) DO NOTHING")
            
            conn.commit()
            
        except Exception as e:
            print(f"Error in batch insert: {str(e)[:200]}")
            conn.rollback()
            raise
        finally:
            cur.close()
        
        return profile_count
    
    def insert_single_profile(self, conn, profile_data: Dict, interest_map: Dict, brand_map: Dict) -> Dict[str, int]:
        """Insert a single profile with all related data"""
        try:
            # Insert core profile data
            self.insert_profiles(conn, [profile_data])
            
            # Insert related data
            related_counts = self.insert_related_data(conn, [profile_data], interest_map, brand_map)
            
            # Insert audience data
            self.insert_audience_data(conn, profile_data)
            
            # Insert contact details
            self.insert_contact_details(conn, profile_data)
            
            # Insert profile stats
            self.insert_profile_stats(conn, profile_data)
            
            return related_counts
            
        except Exception as e:
            conn.rollback()
            raise Exception(f"Error processing profile {profile_data.get('id')}: {e}")
    
    def validate_inserts(self, conn, expected_count: int) -> Dict[str, Any]:
        """Validate the number of records inserted"""
        cur = conn.cursor()
        results = {}
        
        tables = [
            "profile_analytics.profile",
            "profile_analytics.reputation_history",
            "profile_analytics.profile_hashtag",
            "profile_analytics.profile_interest",
            "profile_analytics.profile_brand_affinity",
            "profile_analytics.content",
            "profile_analytics.pricing",
            "profile_analytics.audience_ethnicity",
            "profile_analytics.audience_gender_age",
            "profile_analytics.audience_language",
            "profile_analytics.audience_location",
            "profile_analytics.audience_interest",
            "profile_analytics.audience_income",
            "profile_analytics.audience_device",
            "profile_analytics.contact_detail",
            "profile_analytics.profile_stats_current"
        ]
        
        for table in tables:
            try:
                cur.execute(f"SELECT COUNT(*) FROM {table}")
                count = cur.fetchone()[0]
                results[table.split('.')[1]] = count
            except Exception as e:
                print(f"Error counting records in {table}: {e}")
                results[table.split('.')[1]] = "Error"
        
        cur.close()
        return results

    def insert_related_data(self, conn, profiles: List[Dict], interest_map: Dict, brand_map: Dict) -> Dict[str, int]:
        """Insert related data for each profile"""
        cur = conn.cursor()

        hashtag_count = 0
        interest_count = 0
        brand_count = 0
        content_count = 0
        pricing_count = 0

        for profile_data in profiles:
            profile_id = profile_data.get('id')
            profile = profile_data.get('profile', {})

            # Insert hashtags/interests
            top_hashtags = profile.get('top_hashtags', [])
            for hashtag_data in top_hashtags:
                hashtag_name = hashtag_data.get('name')
                weight = hashtag_data.get('value', 1.0)

                if hashtag_name and hashtag_name in interest_map:
                    try:
                        cur.execute("""
                            INSERT INTO profile_analytics.profile_hashtag (
                                profile_id, hashtag_id, weight
                            ) VALUES (%s, %s, %s)
                            ON CONFLICT (profile_id, hashtag_id) DO NOTHING
                        """, (
                            profile_id, interest_map[hashtag_name], weight
                        ))
                        hashtag_count += 1
                    except Exception as e:
                        print(f"Error inserting hashtag {hashtag_name} for profile {profile_id}: {e}")

            # Insert interests
            top_interests = profile.get('top_interests', [])
            for interest_data in top_interests:
                interest_name = interest_data.get('name')

                if interest_name and interest_name in interest_map:
                    try:
                        cur.execute("""
                            INSERT INTO profile_analytics.profile_interest (
                                profile_id, interest_id, weight
                            ) VALUES (%s, %s, %s)
                            ON CONFLICT (profile_id, interest_id) DO NOTHING
                        """, (
                            profile_id, interest_map[interest_name], 1.0
                        ))
                        interest_count += 1
                    except Exception as e:
                        print(f"Error inserting interest {interest_name} for profile {profile_id}: {e}")

            # Insert brand affiliations
            top_mentions = profile.get('top_mentions', [])
            for mention_data in top_mentions:
                brand_name = mention_data.get('name')

                if brand_name and brand_name in brand_map:
                    try:
                        cur.execute("""
                            INSERT INTO profile_analytics.profile_brand_affinity (
                                profile_id, brand_id, weight
                            ) VALUES (%s, %s, %s)
                            ON CONFLICT (profile_id, brand_id) DO NOTHING
                        """, (
                            profile_id, brand_map[brand_name], 1.0
                        ))
                        brand_count += 1
                    except Exception as e:
                        print(f"Error inserting brand affinity {brand_name} for profile {profile_id}: {e}")

            # Insert content samples (up to 5 per profile)
            top_contents = profile.get('top_contents', [])
            content_limit = min(5, len(top_contents))

            for i in range(content_limit):
                content_data = top_contents[i] if i < len(top_contents) else {}
                content_type = content_data.get('type', 'IMAGE')

                if content_type:
                    try:
                        content_id = str(uuid.uuid4())
                        engagement = content_data.get('engagement', {})

                        cur.execute("""
                            INSERT INTO profile_analytics.content (
                                id, profile_id, group_type, kind, is_sponsored,
                                url, thumbnail_url, title, description, posted_at,
                                like_count, comment_count, view_count, play_count
                            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                            ON CONFLICT (id) DO NOTHING
                        """, (
                            content_id, profile_id, 'ALL', content_type, False,
                            content_data.get('url', f"https://example.com/{content_id}"),
                            content_data.get('thumbnail_url'),
                            content_data.get('title'),
                            content_data.get('description'),
                            datetime.now(),
                            engagement.get('like_count'),
                            engagement.get('comment_count'),
                            engagement.get('view_count'),
                            engagement.get('play_count')
                        ))
                        content_count += 1
                    except Exception as e:
                        print(f"Error inserting content for profile {profile_id}: {e}")

            # Insert pricing data
            pricing = profile_data.get('pricing', {})
            post_type_pricing = pricing.get('post_type', {})

            for post_type in POST_TYPES:
                price_data = post_type_pricing.get(post_type.lower(), {})
                if not price_data:
                    # Generate sample pricing if not available
                    price_range = {
                        'min': random.randint(1000, 5000),
                        'max': random.randint(5000, 10000)
                    }
                else:
                    price_range = price_data.get('price_range', {})

                try:
                    cur.execute("""
                        INSERT INTO profile_analytics.pricing (
                            profile_id, post_type, currency, price_min, price_max
                        ) VALUES (%s, %s, %s, %s, %s)
                        ON CONFLICT (profile_id, post_type) DO NOTHING
                    """, (
                        profile_id, post_type, 'USD',
                        price_range.get('min'), price_range.get('max')
                    ))
                    pricing_count += 1
                except Exception as e:
                    print(f"Error inserting pricing for profile {profile_id}, post type {post_type}: {e}")

            # Commit after each profile to avoid large transactions
            conn.commit()

        cur.close()
        return {
            'hashtags': hashtag_count,
            'interests': interest_count,
            'brands': brand_count,
            'contents': content_count,
            'pricing': pricing_count
        }

    def insert_audience_data(self, conn, profile_data: Dict) -> None:
        """Insert audience-related data for a profile"""
        cur = conn.cursor()
        try:
            profile_id = profile_data.get('id')

            # Insert audience gender age data
            audience = profile_data.get('audience', {})
            gender_age = audience.get('gender_age', {})
            for gender in ['MALE', 'FEMALE']:
                for age_range in ['13-17', '18-24', '25-34', '35-44', '45-54', '55+']:
                    value = gender_age.get(f"{gender.lower()}_{age_range}", random.uniform(0, 100))
                    try:
                        cur.execute("""
                            INSERT INTO profile_analytics.audience_gender_age (
                                profile_id, gender, age_range, value
                            ) VALUES (%s, %s, %s, %s)
                            ON CONFLICT (profile_id, gender, age_range) DO NOTHING
                        """, (profile_id, gender, age_range, value))
                    except Exception as e:
                        print(f"Error inserting audience gender age for {profile_id}: {e}")

            # Insert audience language data
            languages = audience.get('languages', [{'code': 'en', 'value': 70}, {'code': 'es', 'value': 30}])
            for lang in languages:
                try:
                    cur.execute("""
                        INSERT INTO profile_analytics.audience_language (
                            profile_id, language_code, value
                        ) VALUES (%s, %s, %s)
                        ON CONFLICT (profile_id, language_code) DO NOTHING
                    """, (profile_id, lang.get('code', 'en'), lang.get('value', 100)))
                except Exception as e:
                    print(f"Error inserting audience language for {profile_id}: {e}")

            # Insert audience location data
            try:
                audience_locations = audience.get('locations', [])
                if audience_locations:
                    # Use the generated location data
                    for location_data in audience_locations:
                        country_name = location_data.get('country')
                        value = location_data.get('value', 0)

                        # Try to find or create location in master_location
                        cur.execute("""
                            INSERT INTO api_provider.master_location (name, type, is_deleted)
                            VALUES (%s, 'COUNTRY', false)
                            ON CONFLICT (name) DO NOTHING
                        """, (country_name,))

                        # Get the location ID
                        cur.execute("""
                            SELECT id FROM api_provider.master_location
                            WHERE name = %s AND type = 'COUNTRY' AND is_deleted = false
                        """, (country_name,))
                        location_result = cur.fetchone()

                        if location_result:
                            location_id = location_result[0]
                            cur.execute("""
                                INSERT INTO profile_analytics.audience_location (
                                    profile_id, geo_id, value
                                ) VALUES (%s, %s, %s)
                                ON CONFLICT (profile_id, geo_id) DO NOTHING
                            """, (profile_id, location_id, value))
                else:
                    # Fallback to random locations if no data provided
                    cur.execute("SELECT id FROM api_provider.master_location WHERE type = 'COUNTRY' AND is_deleted = false LIMIT 5")
                    location_ids = [row[0] for row in cur.fetchall()]

                    remaining_percentage = 100.0
                    for i, location_id in enumerate(location_ids):
                        if i == len(location_ids) - 1:
                            value = remaining_percentage
                        else:
                            value = round(random.uniform(5, min(35, remaining_percentage)), 2)
                            remaining_percentage -= value

                        cur.execute("""
                            INSERT INTO profile_analytics.audience_location (
                                profile_id, geo_id, value
                            ) VALUES (%s, %s, %s)
                            ON CONFLICT (profile_id, geo_id) DO NOTHING
                        """, (profile_id, location_id, value))
            except Exception as e:
                print(f"Error inserting audience location for {profile_id}: {e}")

            # Insert audience interest data
            try:
                cur.execute("SELECT id FROM api_provider.master_interest LIMIT 5")
                interest_ids = [row[0] for row in cur.fetchall()]

                remaining_percentage = 100.0
                for i, interest_id in enumerate(interest_ids):
                    if i == len(interest_ids) - 1:
                        value = remaining_percentage
                    else:
                        value = round(random.uniform(5, min(35, remaining_percentage)), 2)
                        remaining_percentage -= value

                    cur.execute("""
                        INSERT INTO profile_analytics.audience_interest (
                            profile_id, interest_id, value
                        ) VALUES (%s, %s, %s)
                        ON CONFLICT (profile_id, interest_id) DO NOTHING
                    """, (profile_id, interest_id, value))
            except Exception as e:
                print(f"Error inserting audience interest for {profile_id}: {e}")

            # Insert audience income data
            try:
                audience_income = audience.get('income', [])
                if audience_income:
                    # Use the generated income data
                    for income_data in audience_income:
                        income_range = income_data.get('range')
                        value = income_data.get('value', 0)

                        cur.execute("""
                            INSERT INTO profile_analytics.audience_income (
                                profile_id, income_range, value
                            ) VALUES (%s, %s, %s)
                            ON CONFLICT (profile_id, income_range) DO NOTHING
                        """, (profile_id, income_range, value))
                else:
                    # Fallback to random income distribution
                    income_ranges = ['0-25000', '25001-50000', '50001-75000', '75001-100000', '100001+']
                    remaining_percentage = 100.0
                    for i, income_range in enumerate(income_ranges):
                        if i == len(income_ranges) - 1:
                            value = remaining_percentage
                        else:
                            value = round(random.uniform(5, min(35, remaining_percentage)), 2)
                            remaining_percentage -= value

                        cur.execute("""
                            INSERT INTO profile_analytics.audience_income (
                                profile_id, income_range, value
                            ) VALUES (%s, %s, %s)
                            ON CONFLICT (profile_id, income_range) DO NOTHING
                        """, (profile_id, income_range, value))
            except Exception as e:
                print(f"Error inserting audience income for {profile_id}: {e}")

            # Insert audience device data
            try:
                audience_devices = audience.get('devices', [])
                if audience_devices:
                    # Use the generated device data
                    for device_data in audience_devices:
                        device_type = device_data.get('type')
                        value = device_data.get('value', 0)

                        cur.execute("""
                            INSERT INTO profile_analytics.audience_device (
                                profile_id, device_type, value
                            ) VALUES (%s, %s, %s)
                            ON CONFLICT (profile_id, device_type) DO NOTHING
                        """, (profile_id, device_type, value))
                else:
                    # Fallback to random device distribution
                    device_types = ['MOBILE', 'DESKTOP', 'TABLET']
                    remaining_percentage = 100.0

                    for i, device_type in enumerate(device_types):
                        if i == len(device_types) - 1:
                            value = remaining_percentage
                        else:
                            value = round(random.uniform(20, min(60, remaining_percentage)), 2)
                            remaining_percentage -= value

                        cur.execute("""
                            INSERT INTO profile_analytics.audience_device (
                                profile_id, device_type, value
                            ) VALUES (%s, %s, %s)
                            ON CONFLICT (profile_id, device_type) DO NOTHING
                        """, (profile_id, device_type, value))
            except Exception as e:
                print(f"Error inserting audience device for {profile_id}: {e}")

            # Insert ethnicity data
            try:
                cur.execute("SELECT id FROM api_provider.master_brand LIMIT 4")
                ethnicity_ids = [row[0] for row in cur.fetchall()]

                for ethnicity_id in ethnicity_ids:
                    try:
                        cur.execute("""
                            INSERT INTO profile_analytics.audience_ethnicity (
                                profile_id, ethnicity_id, value
                            ) VALUES (%s, %s, %s)
                            ON CONFLICT (profile_id, ethnicity_id) DO NOTHING
                        """, (profile_id, ethnicity_id, random.uniform(0, 100)))
                    except Exception as e:
                        print(f"Error inserting audience ethnicity for profile {profile_id}: {e}")

                conn.commit()
            except Exception as e:
                print(f"Error getting ethnicity IDs: {e}")
                conn.rollback()
        except Exception as e:
            print(f"Error in insert_audience_data: {e}")
            conn.rollback()
        finally:
            cur.close()

    def insert_contact_details(self, conn, profile_data: Dict) -> None:
        """Insert contact details for a profile"""
        cur = conn.cursor()
        try:
            profile_id = profile_data.get('id')

            # Sample contact details
            contacts = [
                ('EMAIL', '<EMAIL>'),
                ('PHONE', '+1234567890'),
                ('LINKTREE', 'https://example.com/creator')
            ]

            for contact_type, value in contacts:
                try:
                    cur.execute("""
                        INSERT INTO profile_analytics.contact_detail (
                            id, profile_id, type, value
                        ) VALUES (gen_random_uuid(), %s, %s, %s)
                        ON CONFLICT DO NOTHING
                    """, (profile_id, contact_type, value))
                except Exception as e:
                    print(f"Error inserting contact detail for {profile_id}: {e}")

            conn.commit()
        except Exception as e:
            print(f"Error in insert_contact_details: {e}")
            conn.rollback()
        finally:
            cur.close()

    def insert_profile_stats(self, conn, profile_data: Dict) -> None:
        """Insert current profile stats"""
        cur = conn.cursor()
        try:
            profile_id = profile_data.get('id')
            profile = profile_data.get('profile', {})

            cur.execute("""
                INSERT INTO profile_analytics.profile_stats_current (
                    profile_id, follower_count, following_count,
                    average_likes, average_views, average_comments,
                    engagement_rate, updated_at
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                ON CONFLICT (profile_id) DO NOTHING
            """, (
                profile_id,
                profile.get('follower_count'),
                profile.get('following_count'),
                profile.get('average_likes'),
                profile.get('average_views'),
                profile.get('average_comments'),
                profile.get('engagement_rate'),
                datetime.now()
            ))

            conn.commit()
        except Exception as e:
            print(f"Error in insert_profile_stats: {e}")
            conn.rollback()
        finally:
            cur.close()

    def bulk_insert_profiles(self, filepath: str = 'generated_profile_analytics.json',
                           limit: Optional[int] = None) -> Dict[str, Any]:
        """
        Main method to perform bulk insert of profile analytics data.
        Returns statistics about the insertion process.
        """
        print("Starting bulk insert of profile analytics data...")

        # Connect to database
        conn = self.connect_db()
        print("Connected to database")

        try:
            # Load all profiles
            print("Loading profile data...")
            all_profiles = self.load_profiles(filepath, limit)
            total_profiles = len(all_profiles)
            print(f"Loaded {total_profiles} profiles")

            # Track statistics
            stats = {
                'processed': 0,
                'successful': 0,
                'failed': 0,
                'start_time': datetime.now(),
                'errors': []
            }

            # Process in batches
            for batch_start in range(0, total_profiles, self.batch_size):
                batch_end = min(batch_start + self.batch_size, total_profiles)
                batch = all_profiles[batch_start:batch_end]

                print(f"\nProcessing batch {batch_start//self.batch_size + 1} ({batch_start+1} to {batch_end} of {total_profiles})")

                try:
                    # Ensure master data for this batch
                    print("Ensuring master data...")
                    self.ensure_master_data(conn, batch)

                    # Get mappings for foreign keys
                    print("Getting foreign key mappings...")
                    interest_map = self.get_interest_ids(conn)
                    brand_map = self.get_brand_ids(conn)

                    for profile_data in batch:
                        try:
                            profile_id = profile_data.get('id')
                            print(f"Processing profile {profile_id}")

                            # Insert single profile with all related data
                            related_counts = self.insert_single_profile(conn, profile_data, interest_map, brand_map)
                            print(f"  Related data counts: {related_counts}")

                            stats['successful'] += 1

                        except Exception as e:
                            stats['failed'] += 1
                            error_msg = f"Error processing profile {profile_id}: {str(e)}"
                            print(f"ERROR: {error_msg}")
                            stats['errors'].append(error_msg)
                            conn.rollback()
                            continue

                        stats['processed'] += 1

                    # Print batch progress
                    elapsed_time = datetime.now() - stats['start_time']
                    avg_time_per_profile = elapsed_time / stats['processed'] if stats['processed'] > 0 else 0
                    remaining_profiles = total_profiles - stats['processed']
                    estimated_time_remaining = avg_time_per_profile * remaining_profiles

                    print(f"\nBatch Statistics:")
                    print(f"Processed: {stats['processed']} / {total_profiles} ({(stats['processed']/total_profiles*100):.1f}%)")
                    print(f"Successful: {stats['successful']}")
                    print(f"Failed: {stats['failed']}")
                    print(f"Elapsed Time: {elapsed_time}")
                    print(f"Estimated Time Remaining: {estimated_time_remaining}")

                except Exception as e:
                    error_msg = f"Error processing batch: {str(e)}"
                    print(error_msg)
                    stats['errors'].append(error_msg)
                    conn.rollback()
                    continue

            print("\nProcessing completed!")
            print("\nFinal Statistics:")
            print(f"Total Profiles Processed: {stats['processed']}")
            print(f"Successful: {stats['successful']}")
            print(f"Failed: {stats['failed']}")
            print(f"Total Time: {datetime.now() - stats['start_time']}")

            # Final validation
            print("\nRunning final validation...")
            final_validation = self.validate_inserts(conn, total_profiles)
            stats['final_counts'] = final_validation

            print("\nFinal Record Counts:")
            print("=" * 50)
            print(f"{'Table':<30} {'Count':>10}")
            print("-" * 50)
            for table, count in final_validation.items():
                print(f"{table:<30} {count:>10}")
            print("=" * 50)

            return stats

        except Exception as e:
            error_msg = f"Fatal error: {str(e)}"
            print(error_msg)
            conn.rollback()
            raise Exception(error_msg)
        finally:
            conn.close()
            print("\nDatabase connection closed")
