"""
User service for CreatorVerse Backend.
Implements user-related operations with cache-aside pattern.
"""
import json
from datetime import datetime, UTC
from typing import Any, Dict, List, Optional, Tuple
from uuid import UUID

from sqlalchemy import func, select

from app.core.config import get_locobuzz_redis
from app.core.logger import get_logger
from app.core.redis_keys import RedisKeys, RedisConfig
from app.core_helper.async_logger import with_trace_id
from app.core_helper.redis_client import RedisClient
from app.models.user_models import (
    Brand,
    BrandMembership,
    BrandMembershipStatus,
    Organization,
    OrganizationMembership,
    User,
    UserAuthMethod,
    UserRoleModel
)
from app.utilities.bloom_filter import CreatorBloomFilter
from app.utilities.otp_manager import get_optimized_otp_manager
from app.utilities.validation_functions import extract_domain_from_email

logger = get_logger()


@with_trace_id
async def get_user_by_email_cache_aside(db_conn, redis_client, email: str, is_user: bool = False) -> Optional[
    Dict[str, Any]]:
    """
    Get user by email with cache-aside pattern.
    Returns a dictionary instead of a User entity to avoid session issues.
    
    Args:
        db_conn: Database connection
        redis_client: Redis client
        email: User email address
        is_user: If True, return User entity instead of dictionary
        
    Returns:
        Dictionary with user data or None if not found
    """
    # 1. Try Redis Cache First
    cache_key = RedisKeys.user_by_email(email)
    try:
        cached_user = await redis_client.hgetall(cache_key)
        if cached_user:
            logger.info("User cache hit", extra={"email": email})
            if is_user:
                return User(**cached_user)
            return cached_user  # Return dictionary, not entity
    except Exception as e:
        logger.warning("Redis error when retrieving user", extra={
            "email": email,
            "error": str(e)
        })

    # 2. Fetch from Database
    try:
        async with db_conn.get_db() as session:
            query = select(User).where(User.email == email)
            result = await session.execute(query)
            user = result.scalar_one_or_none()

            if not user:
                return None

            # 3. Convert to Dictionary (detach from session)
            user_dict = {
                "id": str(user.id),
                "email": user.email,
                "name": user.name or "",
                "status": user.status,
                "is_active": str(user.is_active),
                "is_email_verified": str(user.is_email_verified),
                "phone_number": user.phone_number or "",
                "profile_image": user.profile_image or "",
                "created_at": str(user.created_at),
                "updated_at": str(user.updated_at)
            }

            # 4. Update Cache
            try:
                pipe = await redis_client.pipeline()
                await pipe.hset(cache_key, mapping=user_dict)
                await pipe.expire(cache_key, RedisConfig.USER_TTL)
                await pipe.execute()
                logger.debug("User cached successfully", extra={"email": email})
            except Exception as cache_err:
                logger.warning("Failed to cache user", extra={
                    "email": email,
                    "error": str(cache_err)
                })
            if is_user:
                return User(**user_dict)
            return user_dict
    except Exception as db_err:
        logger.error("Database error when retrieving user", extra={
            "email": email,
            "error": str(db_err)
        })
        return None


@with_trace_id
async def get_user_by_id_cache_aside(db_conn, redis_client, user_id: UUID) -> Optional[Dict[str, Any]]:
    """
    Get user by ID with cache-aside pattern.
    Returns a dictionary instead of a User entity to avoid session issues.
    
    Args:
        db_conn: Database connection
        redis_client: Redis client
        user_id: User ID
        
    Returns:
        Dictionary with user data or None if not found
    """
    str_user_id = str(user_id)
    cache_key = f"CreatorVerse:user:id:{str_user_id}"

    # 1. Try Redis Cache First
    try:
        cached_user = await redis_client.hgetall(cache_key)
        if cached_user:
            logger.info("User cache hit", extra={"user_id": str_user_id})
            return cached_user
    except Exception as e:
        logger.warning("Redis error when retrieving user", extra={
            "user_id": str_user_id,
            "error": str(e)
        })

    # 2. Fetch from Database
    try:
        async with db_conn.get_db() as session:
            query = select(User).where(User.id == user_id)
            result = await session.execute(query)
            user = result.scalar_one_or_none()

            if not user:
                return None

            # 3. Convert to Dictionary (detach from session)
            user_dict = {
                "id": str(user.id),
                "email": user.email,
                "name": user.name or "",
                "status": user.status,
                "is_active": str(user.is_active),
                "is_email_verified": str(user.is_email_verified),
                "phone_number": user.phone_number or "",
                "profile_image": user.profile_image or "",
                "created_at": str(user.created_at),
                "updated_at": str(user.updated_at)
            }

            # 4. Update both caches (by ID and email)
            try:
                # Cache by ID
                pipe = await redis_client.pipeline()
                await pipe.hset(cache_key, mapping=user_dict)
                await pipe.expire(cache_key, RedisConfig.USER_TTL)

                # Also update email cache
                email_cache_key = RedisKeys.user_by_email(user.email)
                await pipe.hset(email_cache_key, mapping=user_dict)
                await pipe.expire(email_cache_key, RedisConfig.USER_TTL)

                await pipe.execute()
                logger.debug("User cached successfully", extra={"user_id": str_user_id})
            except Exception as cache_err:
                logger.warning("Failed to cache user", extra={
                    "user_id": str_user_id,
                    "error": str(cache_err)
                })

            return user_dict
    except Exception as db_err:
        logger.error("Database error when retrieving user", extra={
            "user_id": str_user_id,
            "error": str(db_err)
        })
        return None


@with_trace_id
async def create_user_cache_aside(db_conn, redis_client, user_data: Dict[str, Any]) -> Tuple[
    bool, Optional[Dict[str, Any]], str]:
    """
    Create a new user with cache-aside pattern.
    
    Args:
        db_conn: Database connection
        redis_client: Redis client
        user_data: User data to create
        
    Returns:
        Tuple of (success, user_dict, message)
    """
    email = user_data.get("email")
    if not email:
        return False, None, "Email is required"

    # 1. Check if email already exists using bloom filter
    try:
        bloom_filter = CreatorBloomFilter(
            redis_client,
            filter_name=RedisKeys.get_email_bloom_filter_key(),
        )

        if await bloom_filter.exists(email):
            # Double-check in the database because bloom filter may have false positives
            existing_user = await get_user_by_email_cache_aside(db_conn, redis_client, email)
            if existing_user:
                return False, None, "User with this email already exists"
    except Exception as bloom_err:
        logger.warning("Error checking bloom filter", extra={"error": str(bloom_err)})
        # Continue with database check if bloom filter fails

    # 2. Create user in database
    try:
        async with db_conn.get_db() as session:
            async with session.begin():
                # Create the user
                new_user = User(
                    email=email,
                    name=user_data.get("name", ""),
                    status=user_data.get("status", "requested"),
                    is_email_verified=user_data.get("is_email_verified", False),
                    is_phone_verified=user_data.get("is_phone_verified", False),
                    is_active=user_data.get("is_active", True),
                    phone_number=user_data.get("phone_number", None),
                    country_code=user_data.get("country_code", None),
                    profile_image=user_data.get("profile_image", None),
                    created_at=datetime.now(UTC),
                    updated_at=datetime.now(UTC)
                )

                session.add(new_user)
                await session.flush()  # Get the ID without committing yet

                # 3. Convert to Dictionary (detach from session)
                user_dict = {
                    "id": str(new_user.id),
                    "email": new_user.email,
                    "name": new_user.name or "",
                    "status": new_user.status,
                    "is_active": str(new_user.is_active),
                    "is_email_verified": str(new_user.is_email_verified),
                    "phone_number": new_user.phone_number or "",
                    "profile_image": new_user.profile_image or "",
                    "created_at": str(new_user.created_at),
                    "updated_at": str(new_user.updated_at)
                }

                # 4. Add to bloom filter
                try:
                    bloom_filter = CreatorBloomFilter(
                        redis_client,
                        filter_name=RedisKeys.get_email_bloom_filter_key(),
                    )
                    await bloom_filter.add(email)  # ✅ FIXED: Added await
                except Exception as bloom_err:
                    logger.warning("Failed to update bloom filter", extra={
                        "email": email,
                        "error": str(bloom_err)
                    })

                # 5. Update Cache
                try:
                    # Cache by email
                    email_cache_key = RedisKeys.user_by_email(email)
                    id_cache_key = f"CreatorVerse:user:id:{str(new_user.id)}"

                    pipe = await redis_client.pipeline()
                    await pipe.hset(email_cache_key, mapping=user_dict)
                    await pipe.expire(email_cache_key, RedisConfig.USER_TTL)

                    await pipe.hset(id_cache_key, mapping=user_dict)
                    await pipe.expire(id_cache_key, RedisConfig.USER_TTL)

                    await pipe.execute()
                    logger.debug("User cached successfully", extra={"email": email})
                except Exception as cache_err:
                    logger.warning("Failed to cache user", extra={
                        "email": email,
                        "error": str(cache_err)
                    })

                # Commit transaction
                await session.commit()

                logger.info("User created successfully", extra={"user_id": str(new_user.id)})
                return True, user_dict, "User created successfully"

    except Exception as e:
        logger.error("Error creating user", extra={
            "email": email,
            "error": str(e)
        })
        return False, None, f"Error creating user: {str(e)}"


@with_trace_id
async def update_user_cache_aside(
        db_conn,
        redis_client,
        user_id: UUID,
        update_data: Dict[str, Any]
) -> Tuple[bool, Optional[Dict[str, Any]], str]:
    """
    Update user data with cache-aside pattern.
    
    Args:
        db_conn: Database connection
        redis_client: Redis client
        user_id: User ID
        update_data: Fields to update
        
    Returns:
        Tuple of (success, updated_user_dict, message)
    """
    if not update_data:
        return False, None, "No data to update"

    str_user_id = str(user_id)

    try:
        async with db_conn.get_db() as session:
            async with session.begin():
                # 1. Get user
                query = select(User).where(User.id == user_id)
                result = await session.execute(query)
                user = result.scalar_one_or_none()

                if not user:
                    return False, None, "User not found"

                # 2. Update fields (only allowed fields)
                allowed_fields = [
                    "name", "status", "phone_number", "country_code",
                    "profile_image", "is_active", "metadata_json"
                ]

                for field, value in update_data.items():
                    if field in allowed_fields and hasattr(user, field):
                        setattr(user, field, value)

                # Add updated_at timestamp
                user.updated_at = func.now()

                # Get email for cache key
                email = user.email

                await session.flush()  # Ensure we have all updated data

                # 3. Convert to Dictionary (detach from session)
                user_dict = {
                    "id": str(user.id),
                    "email": user.email,
                    "name": user.name or "",
                    "status": user.status,
                    "is_active": str(user.is_active),
                    "is_email_verified": str(user.is_email_verified),
                    "phone_number": user.phone_number or "",
                    "profile_image": user.profile_image or "",
                    "created_at": str(user.created_at),
                    "updated_at": str(user.updated_at)
                }

                # 4. Update Cache
                try:
                    # Cache by email and ID
                    email_cache_key = RedisKeys.user_by_email(email)
                    id_cache_key = f"CreatorVerse:user:id:{str_user_id}"

                    pipe = await redis_client.pipeline()
                    # Delete first then set to ensure all fields are updated
                    await pipe.delete(email_cache_key)
                    await pipe.delete(id_cache_key)

                    await pipe.hset(email_cache_key, mapping=user_dict)
                    await pipe.expire(email_cache_key, RedisConfig.USER_TTL)

                    await pipe.hset(id_cache_key, mapping=user_dict)
                    await pipe.expire(id_cache_key, RedisConfig.USER_TTL)

                    await pipe.execute()
                    logger.debug("User cache updated", extra={"user_id": str_user_id})
                except Exception as cache_err:
                    logger.warning("Failed to update user cache", extra={
                        "user_id": str_user_id,
                        "error": str(cache_err)
                    })

                # Commit transaction
                await session.commit()

                logger.info("User updated successfully", extra={"user_id": str_user_id})
                return True, user_dict, "User updated successfully"

    except Exception as e:
        logger.error("Error updating user", extra={
            "user_id": str_user_id,
            "error": str(e)
        })
        return False, None, f"Error updating user: {str(e)}"


@with_trace_id
async def verify_user_email_cache_aside(
        db_conn,
        redis_client,
        user_id: UUID
) -> Tuple[bool, Optional[Dict[str, Any]], str]:
    """
    Verify user email and update cache with cache-aside pattern.
    
    Args:
        db_conn: Database connection
        redis_client: Redis client
        user_id: User ID to verify
        
    Returns:
        Tuple of (success, updated_user_dict, message)
    """
    str_user_id = str(user_id)

    try:
        async with db_conn.get_db() as session:
            async with session.begin():
                # 1. Get user
                query = select(User).where(User.id == user_id)
                result = await session.execute(query)
                user = result.scalar_one_or_none()

                if not user:
                    return False, None, "User not found"

                # Already verified
                if user.is_email_verified:
                    return True, None, "Email already verified"

                # 2. Update email verification status
                user.is_email_verified = True
                user.updated_at = func.now()

                # Get email for cache key
                email = user.email

                await session.flush()  # Ensure we have all updated data

                # 3. Convert to Dictionary (detach from session)
                user_dict = {
                    "id": str(user.id),
                    "email": user.email,
                    "name": user.name or "",
                    "status": user.status,
                    "is_active": str(user.is_active),
                    "is_email_verified": "True",  # Now verified
                    "phone_number": user.phone_number or "",
                    "profile_image": user.profile_image or "",
                    "created_at": str(user.created_at),
                    "updated_at": str(user.updated_at)
                }

                # 4. Update Cache
                try:
                    # Cache by email and ID
                    email_cache_key = RedisKeys.user_by_email(email)
                    id_cache_key = f"CreatorVerse:user:id:{str_user_id}"

                    pipe = await redis_client.pipeline()
                    # Delete first then set to ensure all fields are updated
                    await pipe.delete(email_cache_key)
                    await pipe.delete(id_cache_key)

                    await pipe.hset(email_cache_key, mapping=user_dict)
                    await pipe.expire(email_cache_key, RedisConfig.USER_TTL)

                    await pipe.hset(id_cache_key, mapping=user_dict)
                    await pipe.expire(id_cache_key, RedisConfig.USER_TTL)

                    await pipe.execute()
                    logger.debug("User cache updated", extra={"user_id": str_user_id})
                except Exception as cache_err:
                    logger.warning("Failed to update user cache", extra={
                        "user_id": str_user_id,
                        "error": str(cache_err)
                    })

                # Commit transaction
                await session.commit()

                logger.info("User email verified successfully", extra={"user_id": str_user_id})
                return True, user_dict, "Email verified successfully"

    except Exception as e:
        logger.error("Error verifying user email", extra={
            "user_id": str_user_id,
            "error": str(e)
        })
        return False, None, f"Error verifying user email: {str(e)}"


@with_trace_id
async def verify_login_otp_cache_aside(
        db_conn,
        redis_client,
        email: str,
        otp: str
) -> Tuple[bool, Optional[Dict[str, Any]], str]:
    """
    Verify OTP and return user data if valid.
    Returns a tuple of (success, user_dict, message).
    User is returned as dictionary to avoid session issues.
    
    Args:
        db_conn: Database connection
        redis_client: Redis client
        email: User email address
        otp: OTP code to verify
        
    Returns:
        Tuple of (success, user_dict, message)
    """
    otp_manager = get_optimized_otp_manager(redis_client)

    # 1. Verify OTP
    try:
        is_valid, lockout_time = await otp_manager.verify_otp(email, otp)

        if lockout_time > 0:
            return False, None, f"Too many failed attempts. Try again in {lockout_time} seconds."

        if not is_valid:
            return False, None, "Invalid or expired OTP"

    except Exception as e:
        logger.error("Error verifying OTP", extra={
            "email": email,
            "error": str(e)
        })
        return False, None, f"Error verifying OTP: {str(e)}"

    # 2. Get user within this function's transaction
    try:
        async with db_conn.get_db() as session:
            async with session.begin():
                # Get user
                query = select(User).where(User.email == email)
                result = await session.execute(query)
                user = result.scalar_one_or_none()

                if not user:
                    return False, None, "User not found"

                if not user.is_active:
                    return False, None, "User account is not active"

                # Update last login time
                user.last_login_at = datetime.now()
                await session.flush()

                # Convert to dictionary (detach from session)
                user_dict = {
                    "id": str(user.id),
                    "email": user.email,
                    "name": user.name or "",
                    "status": user.status,
                    "is_active": str(user.is_active),
                    "is_email_verified": str(user.is_email_verified),
                    "phone_number": user.phone_number or "",
                    "profile_image": user.profile_image or "",
                    "last_login_at": str(user.last_login_at),
                    "created_at": str(user.created_at),
                    "updated_at": str(user.updated_at),
                }

                # Commit transaction
                await session.commit()

                # Refresh cache
                cache_key = RedisKeys.user_by_email(email)
                id_cache_key = f"CreatorVerse:user:id:{str(user.id)}"

                try:
                    pipe = await redis_client.pipeline()
                    await pipe.delete(cache_key)
                    await pipe.delete(id_cache_key)

                    await pipe.hset(cache_key, mapping=user_dict)
                    await pipe.expire(cache_key, RedisConfig.USER_TTL)

                    await pipe.hset(id_cache_key, mapping=user_dict)
                    await pipe.expire(id_cache_key, RedisConfig.USER_TTL)

                    await pipe.execute()

                except Exception as cache_err:
                    logger.warning("Failed to update user cache", extra={
                        "email": email,
                        "error": str(cache_err)
                    })

                logger.info("OTP verified successfully", extra={"email": email})
                return True, user_dict, "OTP verified successfully"

    except Exception as e:
        logger.error("Error during OTP verification", extra={"email": email, "error": str(e)})
        return False, None, f"Error verifying OTP: {str(e)}"


@with_trace_id
async def update_user_last_login_cache_aside(
        db_conn,
        redis_client,
        user_id: UUID
) -> Tuple[bool, Optional[Dict[str, Any]], str]:
    """
    Update user's last login timestamp and refresh cache.
    
    Args:
        db_conn: Database connection
        redis_client: Redis client
        user_id: User ID
        
    Returns:
        Tuple of (success, updated_user_dict, message)
    """
    str_user_id = str(user_id)

    try:
        async with db_conn.get_db() as session:
            async with session.begin():
                # Get user
                query = select(User).where(User.id == user_id)
                result = await session.execute(query)
                user = result.scalar_one_or_none()

                if not user:
                    return False, None, "User not found"

                # Update last login time
                user.last_login_at = datetime.now()
                await session.flush()

                # Get email for cache key
                email = user.email

                # Convert to dictionary (detach from session)
                user_dict = {
                    "id": str(user.id),
                    "email": user.email,
                    "name": user.name or "",
                    "status": user.status,
                    "is_active": str(user.is_active),
                    "is_email_verified": str(user.is_email_verified),
                    "phone_number": user.phone_number or "",
                    "profile_image": user.profile_image or "",
                    "last_login_at": str(user.last_login_at),
                    "created_at": str(user.created_at),
                    "updated_at": str(user.updated_at),
                }

                # Commit transaction
                await session.commit()

                # Update cache
                try:
                    email_cache_key = RedisKeys.user_by_email(email)
                    id_cache_key = f"CreatorVerse:user:id:{str_user_id}"

                    pipe = await redis_client.pipeline()
                    await pipe.delete(email_cache_key)
                    await pipe.delete(id_cache_key)

                    await pipe.hset(email_cache_key, mapping=user_dict)
                    await pipe.expire(email_cache_key, RedisConfig.USER_TTL)

                    await pipe.hset(id_cache_key, mapping=user_dict)
                    await pipe.expire(id_cache_key, RedisConfig.USER_TTL)

                    await pipe.execute()

                except Exception as cache_err:
                    logger.warning("Failed to update user cache", extra={
                        "user_id": str_user_id,
                        "error": str(cache_err)
                    })

                logger.debug("User last login updated", extra={"user_id": str_user_id})
                return True, user_dict, "Last login updated successfully"

    except Exception as e:
        logger.error("Error updating user last login", extra={
            "user_id": str_user_id,
            "error": str(e)
        })
        return False, None, f"Error updating last login: {str(e)}"


@with_trace_id
async def get_organization_by_domain_cache_aside(
        db_conn,
        redis_client,
        domain: str
) -> Optional[Dict[str, Any]]:
    """
    Get organization by domain with cache-aside pattern.
    
    Args:
        db_conn: Database connection
        redis_client: Redis client
        domain: Organization domain
        
    Returns:
        Dictionary with organization data or None if not found
    """
    # 1. Try Redis Cache First
    cache_key = RedisKeys.organization_key(domain)
    try:
        cached_org = await redis_client.hgetall(cache_key)
        if cached_org:
            logger.info("Organization cache hit", extra={"domain": domain})
            return cached_org
    except Exception as e:
        logger.warning("Redis error when retrieving organization", extra={
            "domain": domain,
            "error": str(e)
        })

    # 2. Fetch from Database
    try:
        async with db_conn.get_db() as session:
            query = select(Organization).where(Organization.domain == domain)
            result = await session.execute(query)
            org = result.scalar_one_or_none()

            if not org:
                return None

            # 3. Convert to Dictionary (detach from session)
            org_dict = {
                "id": str(org.id),
                "name": org.name,
                "domain": org.domain,
                "organization_code": org.organization_code,
                "description": org.description or "",
                "logo_url": org.logo_url or "",
                "contact_email": org.contact_email or "",
                "total_members": str(org.total_members),
                "is_active": str(org.is_active),
                "created_at": str(org.created_at),
                "updated_at": str(org.updated_at)
            }

            # 4. Update Cache
            try:
                await redis_client.hset(cache_key, mapping=org_dict)
                await redis_client.expire(cache_key, RedisConfig.ORGANIZATION_TTL)
                logger.debug("Organization cached successfully", extra={"domain": domain})
            except Exception as cache_err:
                logger.warning("Failed to cache organization", extra={
                    "domain": domain,
                    "error": str(cache_err)
                })

            return org_dict
    except Exception as db_err:
        logger.error("Database error when retrieving organization", extra={
            "domain": domain,
            "error": str(db_err)
        })
        return None


@with_trace_id
async def register_brand_with_organization_cache_aside(
        db_conn,
        redis_client,
        email: str,
        brand_data: Dict[str, Any]
) -> Tuple[bool, Optional[Dict[str, Any]], str]:
    """
    Register a brand and create organization if needed.
    Follows instruction #14 for brand registration from a new domain.
    
    Args:
        db_conn: Database connection
        redis_client: Redis client
        email: User email
        brand_data: Brand data to create
        
    Returns:
        Tuple of (success, brand_dict, message)
    """
    domain = extract_domain_from_email(email)
    if not domain:
        return False, None, "Invalid email domain"

    try:
        async with db_conn.get_db() as session:
            async with session.begin():
                # Check if organization exists for domain
                org_query = select(Organization).where(Organization.domain == domain)
                org_result = await session.execute(org_query)
                organization = org_result.scalar_one_or_none()

                # Get user
                user_query = select(User).where(User.email == email)
                user_result = await session.execute(user_query)
                user = user_result.scalar_one_or_none()

                if not user:
                    return False, None, "User not found"

                # Create organization if it doesn't exist (first from domain)
                if not organization:
                    logger.info(f"Creating new organization for domain: {domain}")
                    organization = Organization(
                        domain=domain,
                        name=brand_data.get("org_name", f"{domain} Organization"),
                        contact_email=email
                    )
                    session.add(organization)
                    await session.flush()  # Get ID without committing

                    # Add user as organization member with brand-admin role
                    membership = OrganizationMembership(
                        organization_id=organization.id,
                        user_id=user.id,
                        role="brand-admin",
                        status="active"
                    )
                    session.add(membership)

                # Create the brand
                brand = Brand(
                    organization_id=organization.id,
                    name=brand_data.get("name"),
                    description=brand_data.get("description", ""),
                    website_url=brand_data.get("website_url", ""),
                    contact_email=email,
                    created_by=user.id
                )
                session.add(brand)
                await session.flush()

                # Add user as brand member with appropriate role
                brand_membership = BrandMembership(
                    brand_id=brand.id,
                    user_id=user.id,
                    role="brand-admin",
                    status=BrandMembershipStatus.active
                )
                session.add(brand_membership)

                # Prepare dictionaries for cache and return
                org_dict = {
                    "id": str(organization.id),
                    "name": organization.name,
                    "domain": organization.domain,
                    "contact_email": organization.contact_email or "",
                    "is_active": str(organization.is_active),
                }

                brand_dict = {
                    "id": str(brand.id),
                    "name": brand.name,
                    "description": brand.description or "",
                    "website_url": brand.website_url or "",
                    "contact_email": brand.contact_email or "",
                    "organization_id": str(organization.id),
                    "organization_name": organization.name,
                    "created_by": str(user.id),
                    "is_active": str(brand.is_active)
                }

                # Update organization cache
                try:
                    org_key = RedisKeys.organization_key(domain)
                    await redis_client.delete(org_key)
                    await redis_client.hset(org_key, mapping=org_dict)
                    await redis_client.expire(org_key, RedisConfig.ORGANIZATION_TTL)
                except Exception as cache_err:
                    logger.warning("Failed to cache organization", extra={
                        "domain": domain,
                        "error": str(cache_err)
                    })

                # Update brand cache
                try:
                    brand_key = f"CreatorVerse:brand:id:{str(brand.id)}"
                    await redis_client.hset(brand_key, mapping=brand_dict)
                    await redis_client.expire(brand_key, RedisConfig.ORGANIZATION_TTL)
                except Exception as cache_err:
                    logger.warning("Failed to cache brand", extra={
                        "brand_id": str(brand.id),
                        "error": str(cache_err)
                    })

                # Commit transaction
                await session.commit()

                logger.info("Brand registered successfully", extra={
                    "brand_id": str(brand.id),
                    "organization_id": str(organization.id),
                    "user_id": str(user.id)
                })

                return True, brand_dict, "Brand registered successfully"

    except Exception as e:
        logger.error("Error registering brand", extra={
            "email": email,
            "error": str(e)
        })
        return False, None, f"Error registering brand: {str(e)}"


@with_trace_id
async def get_user_organizations_cache_aside(
        db_conn,
        redis_client,
        user_id: UUID
) -> List[Dict[str, Any]]:
    """
    Get organizations where user is a member with cache-aside pattern.
    
    Args:
        db_conn: Database connection
        redis_client: Redis client
        user_id: User ID
        
    Returns:
        List of organization dictionaries
    """
    str_user_id = str(user_id)
    cache_key = f"CreatorVerse:user:organizations:{str_user_id}"

    # 1. Try Redis Cache First
    try:
        cached_orgs = await redis_client.get(cache_key)
        if cached_orgs:
            logger.debug("User organizations cache hit", extra={"user_id": str_user_id})
            return json.loads(cached_orgs)
    except Exception as e:
        logger.warning("Redis error when retrieving organizations", extra={
            "user_id": str_user_id,
            "error": str(e)
        })

    # 2. Fetch from Database
    try:
        async with db_conn.get_db() as session:
            # Get memberships with organization data
            query = (
                select(
                    OrganizationMembership,
                    Organization
                )
                .join(Organization, OrganizationMembership.organization_id == Organization.id)
                .where(OrganizationMembership.user_id == user_id)
                .where(OrganizationMembership.status == "active")
                .where(Organization.is_active == True)
            )

            result = await session.execute(query)
            rows = result.all()

            # 3. Convert to List of Dictionaries (detach from session)
            org_list = []

            for membership, organization in rows:
                org_dict = {
                    "id": str(organization.id),
                    "name": organization.name,
                    "domain": organization.domain,
                    "organization_code": organization.organization_code,
                    "description": organization.description or "",
                    "logo_url": organization.logo_url or "",
                    "role": membership.role,
                    "joined_at": str(membership.joined_at)
                }
                org_list.append(org_dict)

            # 4. Update Cache
            try:
                # Convert list to JSON string for Redis storage
                orgs_json = json.dumps(org_list)
                await redis_client.set(cache_key, orgs_json)
                await redis_client.expire(cache_key, RedisConfig.ORGANIZATION_TTL)
                logger.debug("User organizations cached", extra={"user_id": str_user_id})
            except Exception as cache_err:
                logger.warning("Failed to cache user organizations", extra={
                    "user_id": str_user_id,
                    "error": str(cache_err)
                })

            return org_list
    except Exception as db_err:
        logger.error("Database error when retrieving organizations", extra={
            "user_id": str_user_id,
            "error": str(db_err)
        })
        return []


@with_trace_id
async def get_user_brands_cache_aside(
        db_conn,
        redis_client,
        user_id: UUID
) -> List[Dict[str, Any]]:
    """
    Get brands where user is a member with cache-aside pattern.
    
    Args:
        db_conn: Database connection
        redis_client: Redis client
        user_id: User ID
        
    Returns:
        List of brand dictionaries
    """
    str_user_id = str(user_id)
    cache_key = f"CreatorVerse:user:brands:{str_user_id}"

    # 1. Try Redis Cache First
    try:
        cached_brands = await redis_client.get(cache_key)
        if cached_brands:
            logger.debug("User brands cache hit", extra={"user_id": str_user_id})
            return json.loads(cached_brands)
    except Exception as e:
        logger.warning("Redis error when retrieving brands", extra={
            "user_id": str_user_id,
            "error": str(e)
        })

    # 2. Fetch from Database
    try:
        async with db_conn.get_db() as session:
            # Get memberships with brand data
            query = (
                select(
                    BrandMembership,
                    Brand,
                    Organization
                )
                .join(Brand, BrandMembership.brand_id == Brand.id)
                .join(Organization, Brand.organization_id == Organization.id)
                .where(BrandMembership.user_id == user_id)
                .where(BrandMembership.status == BrandMembershipStatus.active)
                .where(Brand.is_active == True)
                .where(Organization.is_active == True)
            )

            result = await session.execute(query)
            rows = result.all()

            # 3. Convert to List of Dictionaries (detach from session)
            brand_list = []

            for membership, brand, organization in rows:
                brand_dict = {
                    "id": str(brand.id),
                    "name": brand.name,
                    "description": brand.description or "",
                    "logo_url": brand.logo_url or "",
                    "website_url": brand.website_url or "",
                    "organization_id": str(organization.id),
                    "organization_name": organization.name,
                    "role": membership.role,
                    "joined_at": str(membership.joined_at)
                }
                brand_list.append(brand_dict)

            # 4. Update Cache
            try:
                # Convert list to JSON string for Redis storage
                brands_json = json.dumps(brand_list)
                await redis_client.set(cache_key, brands_json)
                await redis_client.expire(cache_key, RedisConfig.ORGANIZATION_TTL)
                logger.debug("User brands cached", extra={"user_id": str_user_id})
            except Exception as cache_err:
                logger.warning("Failed to cache user brands", extra={
                    "user_id": str_user_id,
                    "error": str(cache_err)
                })

            return brand_list
    except Exception as db_err:
        logger.error("Database error when retrieving brands", extra={
            "user_id": str_user_id,
            "error": str(db_err)
        })
        return []


@with_trace_id
async def get_user_roles_cache_aside(
        db_conn,
        redis_client,
        user_id: UUID
) -> List[Dict[str, Any]]:
    """
    Get user roles with cache-aside pattern.
    
    Args:
        db_conn: Database connection
        redis_client: Redis client
        user_id: User ID
        
    Returns:
        List of role dictionaries
    """
    str_user_id = str(user_id)
    cache_key = RedisKeys.rbac_user_roles(user_id)

    # 1. Try Redis Cache First
    try:
        # Get from cache as set of role IDs
        cached_role_ids = await redis_client.smembers(cache_key)

        if cached_role_ids:
            logger.debug("User roles cache hit", extra={"user_id": str_user_id})

            # Get role details from master roles cache
            roles_cache_key = RedisKeys.rbac_roles()
            roles_dict = await redis_client.hgetall(roles_cache_key)

            if roles_dict:
                result = []
                for role_id in cached_role_ids:
                    if role_id in roles_dict:
                        try:
                            role_data = json.loads(roles_dict[role_id])
                            result.append({
                                "id": role_id,
                                "role_name": role_data.get("name"),
                                "description": role_data.get("description")
                            })
                        except:
                            continue

                return result
    except Exception as e:
        logger.warning("Redis error when retrieving user roles", extra={
            "user_id": str_user_id,
            "error": str(e)
        })

    # 2. Fetch from Database
    try:
        async with db_conn.get_db() as session:
            from app.models.user_models import MasterRole

            # Get user roles with role data
            query = (
                select(
                    UserRoleModel,
                    MasterRole
                )
                .join(MasterRole, UserRoleModel.role_id == MasterRole.id)
                .where(UserRoleModel.user_id == user_id)
            )

            result = await session.execute(query)
            rows = result.all()

            # 3. Convert to List of Dictionaries (detach from session)
            role_list = []
            role_ids = set()

            for user_role, master_role in rows:
                role_dict = {
                    "id": str(master_role.id),
                    "role_name": master_role.role_name,
                    "description": master_role.description or ""
                }
                role_list.append(role_dict)
                role_ids.add(str(master_role.id))

            # 4. Update Cache
            try:
                # Store role IDs in set for this user
                pipe = await redis_client.pipeline()

                # Delete existing cache
                await pipe.delete(cache_key)

                # Add each role ID to the set
                if role_ids:
                    await pipe.sadd(cache_key, *list(role_ids))
                    await pipe.expire(cache_key, RedisConfig.RBAC_TTL)

                # Also ensure each role is cached in the master roles cache
                roles_cache_key = RedisKeys.rbac_roles()
                for role in role_list:
                    role_data = {
                        "name": role["role_name"],
                        "description": role["description"]
                    }
                    await pipe.hset(
                        roles_cache_key,
                        role["id"],
                        json.dumps(role_data)
                    )

                await pipe.expire(roles_cache_key, RedisConfig.RBAC_TTL)
                await pipe.execute()

                logger.debug("User roles cached", extra={"user_id": str_user_id})
            except Exception as cache_err:
                logger.warning("Failed to cache user roles", extra={
                    "user_id": str_user_id,
                    "error": str(cache_err)
                })

            return role_list
    except Exception as db_err:
        logger.error("Database error when retrieving user roles", extra={
            "user_id": str_user_id,
            "error": str(db_err)
        })
        return []


@with_trace_id
async def get_user_auth_methods_cache_aside(
        db_conn,
        redis_client,
        user_id: UUID
) -> List[Dict[str, Any]]:
    """
    Get user authentication methods with cache-aside pattern.
    
    Args:
        db_conn: Database connection
        redis_client: Redis client
        user_id: User ID
        
    Returns:
        List of auth method dictionaries
    """
    str_user_id = str(user_id)
    cache_key = f"CreatorVerse:user:auth_methods:{str_user_id}"

    # 1. Try Redis Cache First
    try:
        cached_methods = await redis_client.get(cache_key)
        if cached_methods:
            logger.debug("User auth methods cache hit", extra={"user_id": str_user_id})
            return json.loads(cached_methods)
    except Exception as e:
        logger.warning("Redis error when retrieving auth methods", extra={
            "user_id": str_user_id,
            "error": str(e)
        })

    # 2. Fetch from Database
    try:
        async with db_conn.get_db() as session:
            from app.models.user_models import MasterAuthMethod

            # Get user auth methods with master data
            query = (
                select(
                    UserAuthMethod,
                    MasterAuthMethod
                )
                .join(MasterAuthMethod, UserAuthMethod.auth_method_id == MasterAuthMethod.id)
                .where(UserAuthMethod.user_id == user_id)
                .where(UserAuthMethod.is_enabled == True)
            )

            result = await session.execute(query)
            rows = result.all()

            # 3. Convert to List of Dictionaries (detach from session)
            methods_list = []

            for user_method, master_method in rows:
                method_dict = {
                    "id": str(master_method.id),
                    "method_key": master_method.method_key,
                    "description": master_method.description or "",
                    "enabled_at": str(user_method.enabled_at) if user_method.enabled_at else None
                }
                methods_list.append(method_dict)

            # 4. Update Cache
            try:
                # Convert list to JSON string for Redis storage
                methods_json = json.dumps(methods_list)
                await redis_client.set(cache_key, methods_json)
                await redis_client.expire(cache_key, RedisConfig.USER_TTL)
                logger.debug("User auth methods cached", extra={"user_id": str_user_id})
            except Exception as cache_err:
                logger.warning("Failed to cache user auth methods", extra={
                    "user_id": str_user_id,
                    "error": str(cache_err)
                })

            return methods_list
    except Exception as db_err:
        logger.error("Database error when retrieving auth methods", extra={
            "user_id": str_user_id,
            "error": str(db_err)
        })
        return []


@with_trace_id
async def get_user_by_mobile_cache_aside(db_conn, redis_client, mobile: str) -> Optional[Dict[str, Any]]:
    """
    Get user by mobile number with cache-aside pattern.
    Returns a dictionary instead of a User entity to avoid session issues.
    
    Args:
        db_conn: Database connection
        redis_client: Redis client
        mobile: User mobile number in E.164 format
        
    Returns:
        Dictionary with user data or None if not found
    """
    # 1. Try Redis Cache First
    cache_key = RedisKeys.user_by_mobile(mobile)
    try:
        cached_user = await redis_client.hgetall(cache_key)
        if cached_user:
            logger.info("User cache hit by mobile", extra={"mobile": mobile})
            return cached_user  # Return dictionary, not entity
    except Exception as e:
        logger.warning("Redis error when retrieving user by mobile", extra={
            "mobile": mobile,
            "error": str(e)
        })

    # 2. Fetch from Database
    try:
        async with db_conn.get_db() as session:
            query = select(User).where(User.phone_number == mobile)
            result = await session.execute(query)
            user = result.scalar_one_or_none()

            if not user:
                return None

            # 3. Convert to Dictionary (detach from session)
            user_dict = {
                "id": str(user.id),
                "email": user.email or "",
                "name": user.name or "",
                "status": user.status,
                "is_active": str(user.is_active),
                "is_email_verified": str(user.is_email_verified),
                "is_mobile_verified": str(user.is_mobile_verified),
                "phone_number": user.phone_number or "",
                "profile_image": user.profile_image or "",
                "created_at": str(user.created_at),
                "updated_at": str(user.updated_at)
            }

            # 4. Update Cache
            try:
                pipe = await redis_client.pipeline()
                await pipe.hset(cache_key, mapping=user_dict)
                await pipe.expire(cache_key, RedisConfig.USER_TTL)
                await pipe.execute()
                logger.debug("User cached successfully by mobile", extra={"mobile": mobile})
            except Exception as cache_err:
                logger.warning("Failed to cache user by mobile", extra={
                    "mobile": mobile,
                    "error": str(cache_err)
                })

            return user_dict

    except Exception as e:
        logger.error("Error retrieving user by mobile from database", extra={
            "mobile": mobile,
            "error": str(e)
        })
        return None


async def get_role_name_from_cached_from_role_uuid(role_id: str, redis_client_obj: RedisClient = get_locobuzz_redis()) -> Optional[str]:
    """
    Get role name from cached role UUID.

    Args:
        role_id: Role UUID
        redis_client_obj: Redis client instance

    Returns:
        Role name if found, None otherwise
    """

    # Try to get from cache first
    await redis_client_obj.initialize()
    cache_key = RedisKeys.rbac_roles()
    try:
        role_data: dict = await redis_client_obj.hgetall(cache_key)
        if role_data:
            for key, value in role_data.items():
                if key == role_id:
                    return value
        return ""
    except Exception as e:
        logger.warning("Redis error when retrieving role name", extra={"role_id": role_id, "error": str(e)})

    return ""


if __name__ == "__main__":
    # Example usage
    import asyncio
    # Example call to get user by mobile
    print(asyncio.run(get_role_name_from_cached_from_role_uuid("11239913-1b15-4724-b176-270dff840ef7")))
