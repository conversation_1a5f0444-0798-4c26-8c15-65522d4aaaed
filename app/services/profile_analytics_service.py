"""
Profile Analytics Service - Integration with Phyllo API and database operations
"""
import json
import httpx
import hashlib
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, desc, asc, func, text
from sqlalchemy.orm import selectinload

from app.core.config import APP_CONFIG, get_database, get_discovery_redis
from app.core.exceptions import DiscoveryError, ProfileNotFoundError, ExternalAPIError
from app.models.profile_models import Profile
from app.schemas.profile_analytics_schemas import (
    ProfileAnalyticsRequest, BasicProfileAnalyticsResponse, 
    AudienceDemographicsResponse, AudienceInsightsResponse,
    SponsoredContentResponse, SimilarCreatorsResponse,
    PerformanceMetrics, ReputationHistoryItem, ContentItem,
    AudienceDemographics, AudienceInsights, SponsoredContentAnalysis
)
from app.schemas.filter_schemas import PlatformEnum
from app.core_helper.async_logger import with_trace_id
from app.utilities.profile_analytics_transformer import ProfileAnalyticsTransformer


class ProfileAnalyticsService:
    """
    Service for handling profile analytics operations
    """
    
    def __init__(self):
        self.logger = APP_CONFIG.logger or APP_CONFIG.initialize_logger()
        self.redis = get_discovery_redis()
        self.phyllo_base_url = "http://127.0.0.1:8001"  # Phyllo dummy service
        self.transformer = ProfileAnalyticsTransformer()

        # Cache TTL settings (in seconds)
        self.cache_ttl = {
            'basic_metrics': 24 * 3600,  # 24 hours
            'audience_demographics': 7 * 24 * 3600,  # 7 days
            'audience_insights': 7 * 24 * 3600,  # 7 days
            'sponsored_content': 24 * 3600,  # 24 hours
            'similar_creators': 3 * 24 * 3600  # 3 days
        }
    
    @with_trace_id
    async def get_basic_profile_analytics(
        self,
        profile_id: str,
        request: ProfileAnalyticsRequest
    ) -> BasicProfileAnalyticsResponse:
        """
        Get basic profile analytics including performance metrics and content analysis
        """
        try:
            self.logger.info(f"Getting basic analytics for profile: {profile_id}")
            
            # Check cache first
            cache_key = f"profile_analytics:basic:{profile_id}"
            if not request.refresh_external:
                cached_data = await self._get_from_cache(cache_key)
                if cached_data:
                    self.logger.info(f"Cache hit for basic analytics: {profile_id}")
                    return BasicProfileAnalyticsResponse(**cached_data)
            
            # Get profile from database
            db_conn = get_database()
            async with db_conn.get_db() as db:
                profile = await self._get_profile_by_id(db, profile_id)
                if not profile:
                    raise ProfileNotFoundError(f"Profile not found: {profile_id}")

                # Check if we need to fetch from external API
                needs_external_update = (
                    request.refresh_external or
                    not profile.cache_expires_at or
                    datetime.utcnow() > profile.cache_expires_at
                )

                if needs_external_update:
                    # Fetch from Phyllo API
                    external_data = await self._fetch_phyllo_analytics(profile.external_id, profile.platform)

                    # Update profile with new data
                    await self._update_profile_with_analytics(db, profile, external_data)
                    await db.commit()

                # Build response
                response_data = await self._build_basic_analytics_response(profile)

                # Cache the response
                await self._cache_result(cache_key, response_data.dict(), self.cache_ttl['basic_metrics'])

                return response_data
                
        except Exception as e:
            self.logger.error(f"Failed to get basic analytics for {profile_id}: {str(e)}")
            raise DiscoveryError(f"Failed to get profile analytics: {str(e)}")
    
    @with_trace_id
    async def get_audience_demographics(
        self,
        profile_id: str,
        request: ProfileAnalyticsRequest
    ) -> AudienceDemographicsResponse:
        """
        Get detailed audience demographics
        """
        try:
            self.logger.info(f"Getting audience demographics for profile: {profile_id}")
            
            # Check cache first
            cache_key = f"profile_analytics:demographics:{profile_id}"
            if not request.refresh_external:
                cached_data = await self._get_from_cache(cache_key)
                if cached_data:
                    return AudienceDemographicsResponse(**cached_data)
            
            db_conn = get_database()
            async with db_conn.get_db() as db:
                profile = await self._get_profile_by_id(db, profile_id)
                if not profile:
                    raise ProfileNotFoundError(f"Profile not found: {profile_id}")

                # Ensure we have fresh audience data
                if (request.refresh_external or
                    not profile.audience_countries or
                    not profile.cache_expires_at or
                    datetime.utcnow() > profile.cache_expires_at):

                    external_data = await self._fetch_phyllo_analytics(profile.external_id, profile.platform)
                    await self._update_profile_with_analytics(db, profile, external_data)
                    await db.commit()

                response_data = await self._build_demographics_response(profile)
                await self._cache_result(cache_key, response_data.dict(), self.cache_ttl['audience_demographics'])

                return response_data
                
        except Exception as e:
            self.logger.error(f"Failed to get demographics for {profile_id}: {str(e)}")
            raise DiscoveryError(f"Failed to get audience demographics: {str(e)}")
    
    @with_trace_id
    async def get_audience_insights(
        self,
        profile_id: str,
        request: ProfileAnalyticsRequest
    ) -> AudienceInsightsResponse:
        """
        Get audience behavioral insights
        """
        try:
            self.logger.info(f"Getting audience insights for profile: {profile_id}")
            
            cache_key = f"profile_analytics:insights:{profile_id}"
            if not request.refresh_external:
                cached_data = await self._get_from_cache(cache_key)
                if cached_data:
                    return AudienceInsightsResponse(**cached_data)
            
            db_conn = get_database()
            async with db_conn.get_db() as db:
                profile = await self._get_profile_by_id(db, profile_id)
                if not profile:
                    raise ProfileNotFoundError(f"Profile not found: {profile_id}")

                # Ensure fresh insights data
                if (request.refresh_external or
                    not profile.audience_interests_detailed or
                    not profile.cache_expires_at or
                    datetime.utcnow() > profile.cache_expires_at):

                    external_data = await self._fetch_phyllo_analytics(profile.external_id, profile.platform)
                    await self._update_profile_with_analytics(db, profile, external_data)
                    await db.commit()

                response_data = await self._build_insights_response(profile)
                await self._cache_result(cache_key, response_data.dict(), self.cache_ttl['audience_insights'])

                return response_data
                
        except Exception as e:
            self.logger.error(f"Failed to get insights for {profile_id}: {str(e)}")
            raise DiscoveryError(f"Failed to get audience insights: {str(e)}")
    
    @with_trace_id
    async def get_sponsored_content_analysis(
        self,
        profile_id: str,
        request: ProfileAnalyticsRequest
    ) -> SponsoredContentResponse:
        """
        Get sponsored content analysis
        """
        try:
            self.logger.info(f"Getting sponsored content analysis for profile: {profile_id}")
            
            cache_key = f"profile_analytics:sponsored:{profile_id}"
            if not request.refresh_external:
                cached_data = await self._get_from_cache(cache_key)
                if cached_data:
                    return SponsoredContentResponse(**cached_data)
            
            db_conn = get_database()
            async with db_conn.get_db() as db:
                profile = await self._get_profile_by_id(db, profile_id)
                if not profile:
                    raise ProfileNotFoundError(f"Profile not found: {profile_id}")

                # Ensure fresh sponsored content data
                if (request.refresh_external or
                    not profile.sponsored_contents or
                    not profile.cache_expires_at or
                    datetime.utcnow() > profile.cache_expires_at):

                    external_data = await self._fetch_phyllo_analytics(profile.external_id, profile.platform)
                    await self._update_profile_with_analytics(db, profile, external_data)
                    await db.commit()

                response_data = await self._build_sponsored_content_response(profile)
                await self._cache_result(cache_key, response_data.dict(), self.cache_ttl['sponsored_content'])

                return response_data
                
        except Exception as e:
            self.logger.error(f"Failed to get sponsored content for {profile_id}: {str(e)}")
            raise DiscoveryError(f"Failed to get sponsored content analysis: {str(e)}")
    
    @with_trace_id
    async def get_similar_creators(
        self,
        profile_id: str,
        limit: int = 10
    ) -> SimilarCreatorsResponse:
        """
        Get similar creators recommendations
        """
        try:
            self.logger.info(f"Getting similar creators for profile: {profile_id}")
            
            cache_key = f"profile_analytics:similar:{profile_id}:{limit}"
            cached_data = await self._get_from_cache(cache_key)
            if cached_data:
                return SimilarCreatorsResponse(**cached_data)
            
            db_conn = get_database()
            async with db_conn.get_db() as db:
                profile = await self._get_profile_by_id(db, profile_id)
                if not profile:
                    raise ProfileNotFoundError(f"Profile not found: {profile_id}")

                # Find similar creators based on follower count, engagement rate, and category
                similar_creators = await self._find_similar_creators(db, profile, limit)

                response_data = SimilarCreatorsResponse(
                    profile_id=profile_id,
                    similar_creators=similar_creators,
                    total_count=len(similar_creators),
                    last_updated=datetime.utcnow()
                )

                await self._cache_result(cache_key, response_data.dict(), self.cache_ttl['similar_creators'])

                return response_data
                
        except Exception as e:
            self.logger.error(f"Failed to get similar creators for {profile_id}: {str(e)}")
            raise DiscoveryError(f"Failed to get similar creators: {str(e)}")
    
    async def _fetch_phyllo_analytics(self, external_id: str, platform: str) -> Dict[str, Any]:
        """
        Fetch analytics data from Phyllo dummy API
        """
        try:
            async with httpx.AsyncClient() as client:
                self.logger.info(f"Calling Phyllo API: {self.phyllo_base_url}/v1/social/creator/profile/analytics")
                self.logger.info(f"Request payload: profile_id={external_id}")

                response = await client.post(
                    f"{self.phyllo_base_url}/v1/social/creator/profile/analytics",
                    json={
                        "profile_id": external_id,
                        "include_audience": True,
                        "include_content": True,
                        "include_pricing": True
                    },
                    timeout=30.0
                )
                
                if response.status_code != 200:
                    raise ExternalAPIError(f"Phyllo API error: {response.status_code} - {response.text}")
                
                return response.json()
                
        except httpx.TimeoutException:
            raise ExternalAPIError("Phyllo API request timed out")
        except Exception as e:
            self.logger.error(f"Error fetching from Phyllo API: {str(e)}")
            raise ExternalAPIError(f"Failed to fetch from Phyllo API: {str(e)}")
    
    async def _get_profile_by_id(self, db: AsyncSession, profile_id: str) -> Optional[Profile]:
        """Get profile by ID from database"""
        try:
            self.logger.info(f"Searching for profile with ID: {profile_id}")
            self.logger.info(f"Using Profile model: {Profile.__table__.name}")

            result = await db.execute(
                select(Profile).where(Profile.id == profile_id)
            )
            profile = result.scalar_one_or_none()

            if profile:
                self.logger.info(f"Found profile: {profile.platform_username} on {profile.platform}")
            else:
                self.logger.warning(f"No profile found with ID: {profile_id}")

            return profile
        except Exception as e:
            self.logger.error(f"Database error getting profile {profile_id}: {str(e)}")
            return None

    async def _update_profile_with_analytics(self, db: AsyncSession, profile: Profile, analytics_data: Dict[str, Any]):
        """Update profile with analytics data from Phyllo API using transformer"""
        try:
            # Transform the analytics data using our transformer
            transformed_data = self.transformer.transform_phyllo_response(analytics_data)

            # Update profile fields with transformed data
            for field_name, field_value in transformed_data.items():
                if hasattr(profile, field_name):
                    setattr(profile, field_name, field_value)
                else:
                    self.logger.warning(f"Profile model does not have field: {field_name}")

            # Always update the updated_at timestamp
            profile.updated_at = datetime.utcnow()

        except Exception as e:
            self.logger.error(f"Error updating profile with analytics: {str(e)}")
            raise

    async def _build_basic_analytics_response(self, profile: Profile) -> BasicProfileAnalyticsResponse:
        """Build basic analytics response from profile data"""
        try:
            # Parse JSON fields
            reputation_history = []
            if profile.reputation_history:
                reputation_data = json.loads(profile.reputation_history)
                reputation_history = [ReputationHistoryItem(**item) for item in reputation_data]

            top_contents = []
            if profile.top_contents:
                content_data = json.loads(profile.top_contents)
                top_contents = [ContentItem(**item) for item in content_data[:10]]  # Limit to top 10

            top_hashtags = []
            if profile.top_hashtags:
                top_hashtags = json.loads(profile.top_hashtags)

            top_mentions = []
            if profile.top_mentions:
                top_mentions = json.loads(profile.top_mentions)

            # Build performance metrics
            performance_metrics = PerformanceMetrics(
                followers=profile.follower_count or 0,
                follower_growth=profile.follower_growth_percentage or 0.0,
                average_impressions=profile.average_reach or 0,
                average_likes=profile.average_likes or 0.0,
                average_comments=profile.average_comments or 0.0,
                average_views=profile.average_views or 0.0,
                average_reach=profile.average_reach or 0,
                engagement_rate=profile.engagement_rate or 0.0
            )

            return BasicProfileAnalyticsResponse(
                profile_id=str(profile.id),
                platform=profile.platform or "instagram",
                platform_username=profile.platform_username,
                full_name=profile.full_name,
                image_url=profile.image_url,
                is_verified=profile.is_verified or False,
                performance_metrics=performance_metrics,
                reputation_history=reputation_history,
                top_contents=top_contents,
                top_hashtags=top_hashtags,
                top_mentions=top_mentions,
                content_performance_score=profile.content_performance_score or 0.0,
                last_updated=profile.last_updated_external or profile.updated_at,
                data_source=profile.data_source or "internal"
            )

        except Exception as e:
            self.logger.error(f"Error building basic analytics response: {str(e)}")
            raise

    async def _build_demographics_response(self, profile: Profile) -> AudienceDemographicsResponse:
        """Build audience demographics response"""
        try:
            demographics_data = {}

            # Parse audience data
            if profile.audience_countries:
                demographics_data['countries'] = json.loads(profile.audience_countries)
            if profile.audience_states:
                demographics_data['states'] = json.loads(profile.audience_states)
            if profile.audience_cities:
                demographics_data['cities'] = json.loads(profile.audience_cities)
            if profile.audience_age_distribution:
                age_data = json.loads(profile.audience_age_distribution)
                demographics_data['age_groups'] = age_data
            if profile.audience_gender_distribution:
                gender_data = json.loads(profile.audience_gender_distribution)
                demographics_data['gender_distribution'] = gender_data
            if profile.audience_languages:
                demographics_data['languages'] = json.loads(profile.audience_languages)

            demographics_data['credibility_score'] = profile.audience_credibility_score or 0.0

            demographics = AudienceDemographics(**demographics_data)

            return AudienceDemographicsResponse(
                profile_id=str(profile.id),
                demographics=demographics,
                last_updated=profile.last_updated_external or profile.updated_at
            )

        except Exception as e:
            self.logger.error(f"Error building demographics response: {str(e)}")
            raise

    async def _build_insights_response(self, profile: Profile) -> AudienceInsightsResponse:
        """Build audience insights response"""
        try:
            insights_data = {}

            if profile.audience_interests_detailed:
                insights_data['interests'] = json.loads(profile.audience_interests_detailed)
            if profile.audience_brand_affinity_detailed:
                insights_data['brand_affinity'] = json.loads(profile.audience_brand_affinity_detailed)

            insights = AudienceInsights(**insights_data)

            return AudienceInsightsResponse(
                profile_id=str(profile.id),
                insights=insights,
                last_updated=profile.last_updated_external or profile.updated_at
            )

        except Exception as e:
            self.logger.error(f"Error building insights response: {str(e)}")
            raise

    async def _build_sponsored_content_response(self, profile: Profile) -> SponsoredContentResponse:
        """Build sponsored content analysis response"""
        try:
            analysis_data = {
                'sponsored_posts_performance': profile.sponsored_posts_performance or 0.0
            }

            if profile.sponsored_contents:
                content_data = json.loads(profile.sponsored_contents)
                analysis_data['sponsored_contents'] = [ContentItem(**item) for item in content_data]

            if profile.estimated_pricing:
                analysis_data['estimated_pricing'] = json.loads(profile.estimated_pricing)

            if profile.price_explanations:
                analysis_data['price_explanations'] = json.loads(profile.price_explanations)

            analysis = SponsoredContentAnalysis(**analysis_data)

            return SponsoredContentResponse(
                profile_id=str(profile.id),
                analysis=analysis,
                last_updated=profile.last_updated_external or profile.updated_at
            )

        except Exception as e:
            self.logger.error(f"Error building sponsored content response: {str(e)}")
            raise

    async def _find_similar_creators(self, db: AsyncSession, profile: Profile, limit: int) -> List[Dict[str, Any]]:
        """Find similar creators based on profile characteristics"""
        try:
            # Define similarity criteria
            follower_range = 0.3  # 30% range
            engagement_range = 0.5  # 50% range

            min_followers = int((profile.follower_count or 0) * (1 - follower_range))
            max_followers = int((profile.follower_count or 0) * (1 + follower_range))

            min_engagement = (profile.engagement_rate or 0) * (1 - engagement_range)
            max_engagement = (profile.engagement_rate or 0) * (1 + engagement_range)

            # Query for similar profiles
            query = select(Profile).where(
                and_(
                    Profile.id != profile.id,  # Exclude self
                    Profile.platform == profile.platform,  # Same platform
                    Profile.follower_count.between(min_followers, max_followers),
                    Profile.engagement_rate.between(min_engagement, max_engagement),
                    Profile.profile_status == 'active'
                )
            ).limit(limit)

            result = await db.execute(query)
            similar_profiles = result.scalars().all()

            # Build response
            similar_creators = []
            for similar_profile in similar_profiles:
                # Calculate similarity score (simplified)
                follower_similarity = 1 - abs((similar_profile.follower_count or 0) - (profile.follower_count or 0)) / max(profile.follower_count or 1, 1)
                engagement_similarity = 1 - abs((similar_profile.engagement_rate or 0) - (profile.engagement_rate or 0)) / max(profile.engagement_rate or 0.1, 0.1)
                similarity_score = (follower_similarity + engagement_similarity) / 2

                similar_creators.append({
                    'id': str(similar_profile.id),
                    'platform_username': similar_profile.platform_username,
                    'full_name': similar_profile.full_name,
                    'follower_count': similar_profile.follower_count or 0,
                    'engagement_rate': similar_profile.engagement_rate or 0.0,
                    'similarity_score': min(similarity_score, 1.0),
                    'image_url': similar_profile.image_url,
                    'platform': similar_profile.platform or 'instagram'
                })

            # Sort by similarity score
            similar_creators.sort(key=lambda x: x['similarity_score'], reverse=True)

            return similar_creators

        except Exception as e:
            self.logger.error(f"Error finding similar creators: {str(e)}")
            return []

    async def _get_from_cache(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """Get data from Redis cache"""
        try:
            cached_data = await self.redis.get(cache_key)
            if cached_data:
                return json.loads(cached_data)
        except Exception as e:
            self.logger.warning(f"Cache retrieval failed for {cache_key}: {e}")
        return None

    async def _cache_result(self, cache_key: str, data: Dict[str, Any], ttl: int):
        """Cache result in Redis"""
        try:
            await self.redis.set(cache_key, json.dumps(data, default=str), expire=ttl)
        except Exception as e:
            self.logger.warning(f"Cache storage failed for {cache_key}: {e}")
