"""
RBAC Service - Role-Based Access Control with Redis caching
"""
from functools import lru_cache
from typing import Set, List, List
from uuid import UUID

from sqlalchemy import select

from app.core.config import APP_CONFIG, get_database, get_locobuzz_redis
from app.core.logger import get_logger
from app.core.redis_keys import RedisKeys, RedisConfig
from app.core_helper.async_logger import with_trace_id
from app.models.user_models import (
    MasterRole,
    MasterPermission,
    RolePermission,
    UserRoleModel,
    MasterAuthMethod,
)


class RBACService:
    """Service for Role-Based Access Control and Auth-Method operations with Redis caching."""
    
    def __init__(self, redis_client, db_conn):
        self.redis_client = redis_client
        self.db_conn = db_conn
        self.logger = get_logger()

    @with_trace_id
    async def load_rbac_data(self) -> None:
        """Load roles, permissions, role→permission mappings, and auth-methods into Redis."""
        try:
            await self._load_roles()
            await self._load_permissions()
            await self._load_role_permissions()
            await self._load_auth_methods()
            self.logger.info("Global RBAC and AuthMethod data loaded into Redis")
        except Exception as e:
            self.logger.error("Failed to load RBAC/AuthMethod data", extra={"error": str(e)})
            raise

    @with_trace_id
    async def _load_roles(self) -> None:
        """Load roles with cache-aside pattern."""
        key = RedisKeys.rbac_roles()
        
        # Check if already cached
        existing = await self.redis_client.hgetall(key)
        if existing:
            return
        
        # Cache miss, load from database
        self.logger.info("Loading roles from database")
        async with self.db_conn.get_db() as session:
            result = await session.execute(select(MasterRole.id, MasterRole.role_name))
            rows = result.all()
            
            # Need str() for UUID->string, role_name is already string
            mapping = {str(r.id): r.role_name for r in rows}
            
            if mapping:
                await self.redis_client.hmset(key, mapping)
                await self.redis_client.expire(key, RedisConfig.RBAC_TTL)
                self.logger.info(f"Cached {len(mapping)} roles in Redis")

    @with_trace_id
    async def _load_permissions(self) -> None:
        """Load permissions with both forward and reverse lookup caches."""
        forward_key = RedisKeys.rbac_permissions()
        reverse_key = RedisKeys.rbac_permissions_reverse()
        
        # Check if already cached
        existing = await self.redis_client.hgetall(forward_key)
        if existing:
            return
            
        # Cache miss, load from database
        self.logger.info("Loading permissions from database")
        async with self.db_conn.get_db() as session:
            result = await session.execute(select(MasterPermission.id, MasterPermission.permission_key))
            rows = result.all()
            
            # Need str() for UUID->string, permission_key is already string
            forward_mapping = {str(r.id): r.permission_key for r in rows}
            reverse_mapping = {r.permission_key: str(r.id) for r in rows}
            
            if forward_mapping:
                try:
                    pipe = self.redis_client.pipeline()
                    pipe.hmset(forward_key, forward_mapping)
                    pipe.hmset(reverse_key, reverse_mapping)
                    pipe.expire(forward_key, RedisConfig.RBAC_TTL)
                    pipe.expire(reverse_key, RedisConfig.RBAC_TTL)
                    await pipe.execute()
                    self.logger.info(f"Cached {len(forward_mapping)} permissions in Redis")
                except Exception as redis_err:
                    self.logger.error("Redis pipeline error in permissions loading", extra={"error": str(redis_err)})
                    # Fallback to individual operations
                    try:
                        await self.redis_client.hmset(forward_key, forward_mapping)
                        await self.redis_client.expire(forward_key, RedisConfig.RBAC_TTL)
                        await self.redis_client.hmset(reverse_key, reverse_mapping)
                        await self.redis_client.expire(reverse_key, RedisConfig.RBAC_TTL)
                        self.logger.info(f"Cached {len(forward_mapping)} permissions in Redis (fallback)")
                    except Exception as fallback_err:
                        self.logger.error("Failed to cache permissions even with fallback", extra={"error": str(fallback_err)})

    @with_trace_id
    async def _load_role_permissions(self) -> None:
        """Load role permissions mapping."""
        self.logger.info("Loading role-permission mappings from database")
        async with self.db_conn.get_db() as session:
            query = (
                select(MasterRole.id.label("role_id"), RolePermission.permission_id)
                .join(RolePermission, MasterRole.id == RolePermission.role_id)
            )
            result = await session.execute(query)
            rows = result.all()
            
            perms = {}
            for row in rows:
                rid = str(row.role_id)
                perms.setdefault(rid, []).append(str(row.permission_id))
            
            # Cache each role's permissions separately
            for rid, pids in perms.items():
                key = RedisKeys.rbac_role_permissions(UUID(rid))
                # Check if already cached
                existing = await self.redis_client.smembers(key)
                if not existing:
                    # pids are already strings from str() conversion above
                    await self.redis_client.sadd(key, *pids)
                    await self.redis_client.expire(key, RedisConfig.RBAC_TTL)
            
            self.logger.info(f"Cached permission sets for {len(perms)} roles")

    @with_trace_id
    async def _load_auth_methods(self) -> None:
        """Load MasterAuthMethod.method_key→id into a single Redis hash."""
        key = RedisKeys.master_auth_methods()

        # If already loaded, do nothing
        existing = await self.redis_client.hgetall(key)
        if existing:
            return

        # Fetch from DB
        self.logger.info("Loading auth methods from database")
        async with self.db_conn.get_db() as session:
            result = await session.execute(select(MasterAuthMethod.id, MasterAuthMethod.method_key))
            rows = result.all()
            
            if not rows:
                return

            # field=method_key, value=id - need str() for UUID->string
            mapping = {r.method_key: str(r.id) for r in rows}
            await self.redis_client.hmset(key, mapping)
            await self.redis_client.expire(key, RedisConfig.RBAC_TTL)
            self.logger.info(f"Cached {len(mapping)} auth methods in Redis")

    @with_trace_id
    async def get_auth_method_id(self, method_key: str) -> UUID | None:
        """
        Retrieve the UUID for a given method_key via Redis.
        Falls back to DB on cache miss and writes back.
        """
        key = RedisKeys.master_auth_methods()

        # try cache - cached is already a string with decode_responses=True
        cached = await self.redis_client.hget(key, method_key)
        if cached:
            return UUID(cached)

        # cache miss → query DB
        self.logger.info(f"Cache miss for auth method: {method_key}, loading from database")
        async with self.db_conn.get_db() as session:
            stmt = select(MasterAuthMethod.id).where(MasterAuthMethod.method_key == method_key)
            result = await session.execute(stmt)
            master_id = result.scalar_one_or_none()
            
            if master_id is None:
                return None

            # write back for future - need str() for UUID->string
            await self.redis_client.hset(key, method_key, str(master_id))
            await self.redis_client.expire(key, RedisConfig.RBAC_TTL)
            return master_id

    @with_trace_id
    async def get_auth_method_key(self, method_id: UUID) -> str | None:
        """
        Retrieve the method_key for a given UUID via Redis.
        Falls back to DB on cache miss and writes back.
        """
        key = RedisKeys.master_auth_methods()
        str_id = str(method_id)

        # fetch entire hash and search - values are already strings
        mapping = await self.redis_client.hgetall(key)
        for mkey, mid in mapping.items():
            if mid == str_id:
                return mkey

        # cache miss → query DB
        self.logger.info(f"Cache miss for auth method ID: {method_id}, loading from database")
        async with self.db_conn.get_db() as session:
            stmt = select(MasterAuthMethod.method_key).where(MasterAuthMethod.id == method_id)
            result = await session.execute(stmt)
            method_key = result.scalar_one_or_none()
            
            if method_key is None:
                return None

            # write back mapping
            await self.redis_client.hset(key, method_key, str_id)
            await self.redis_client.expire(key, RedisConfig.RBAC_TTL)
            return method_key

    @with_trace_id
    async def get_user_roles(self, user_id: UUID) -> Set[str]:
        """Get roles for a user using cache-aside pattern."""
        redis_key = RedisKeys.rbac_user_roles(user_id)
        
        try:
            cached = await self.redis_client.smembers(redis_key)
            if cached:
                # cached already contains strings with decode_responses=True
                return cached
        except Exception as e:
            self.logger.warning("Redis error; falling back to DB for user roles", extra={"error": str(e)})

        self.logger.info(f"Cache miss for user roles, loading from database for user {user_id}")
        async with self.db_conn.get_db() as session:
            query = select(UserRoleModel.role_id).where(UserRoleModel.user_id == user_id)
            result = await session.execute(query)
            rows = result.all()
            roles = {str(r.role_id) for r in rows}  # Need str() for UUID->string conversion

            if roles:
                try:
                    await self.redis_client.sadd(redis_key, *roles)
                    await self.redis_client.expire(redis_key, RedisConfig.RBAC_TTL)
                except Exception as e:
                    self.logger.warning("Failed to cache user roles", extra={"error": str(e)})

        return roles

    @with_trace_id
    async def get_role_permissions(self, role_id: UUID) -> Set[str]:
        """Get permissions for a role using cache-aside pattern."""
        redis_key = RedisKeys.rbac_role_permissions(role_id)
        
        try:
            cached = await self.redis_client.smembers(redis_key)
            if cached:
                # cached already contains strings with decode_responses=True
                return cached
        except Exception as e:
            self.logger.warning("Redis error; falling back to DB for role permissions", extra={"error": str(e)})

        # Cache miss, load from database
        self.logger.info(f"Cache miss for role permissions: {role_id}, loading from database")
        async with self.db_conn.get_db() as session:
            query = select(RolePermission.permission_id).where(RolePermission.role_id == role_id)
            result = await session.execute(query)
            rows = result.all()
            permissions = {str(r.permission_id) for r in rows}  # Need str() for UUID->string conversion

            if permissions:
                try:
                    await self.redis_client.sadd(redis_key, *permissions)
                    await self.redis_client.expire(redis_key, RedisConfig.RBAC_TTL)
                except Exception as e:
                    self.logger.warning(f"Failed to cache permissions for role {role_id}", extra={"error": str(e)})

            return permissions

    @with_trace_id
    async def check_user_permission(self, user_id: UUID, permission_key: str) -> bool:
        """Check if user has a given permission using optimized reverse lookup."""
        try:
            roles = await self.get_user_roles(user_id)
            if not roles:
                return False

            # Try optimized reverse lookup first
            reverse_key = RedisKeys.rbac_permissions_reverse()
            perm_id = await self.redis_client.hget(reverse_key, permission_key)
            
            if not perm_id:
                # Fallback to forward lookup if reverse cache miss
                all_perms = await self.redis_client.hgetall(RedisKeys.rbac_permissions())
                if not all_perms:
                    await self._load_permissions()
                    all_perms = await self.redis_client.hgetall(RedisKeys.rbac_permissions())

                # Find permission ID - no .decode() needed with decode_responses=True
                perm_id = next(
                    (pid for pid, key in all_perms.items() if key == permission_key),
                    None
                )
                
                if not perm_id:
                    return False

            # Check if user has this permission through any role
            user_perms: Set[str] = set()
            for rid in roles:
                user_perms |= await self.get_role_permissions(UUID(rid))

            return perm_id in user_perms

        except Exception as e:
            # Fallback to database on Redis failure
            self.logger.warning("Redis error during permission check, falling back to DB", extra={"error": str(e)})
            return await self._check_permission_from_db(user_id, permission_key)

    @with_trace_id
    async def _check_permission_from_db(self, user_id: UUID, permission_key: str) -> bool:
        """Fallback permission check using database only."""
        try:
            async with self.db_conn.get_db() as session:
                # Get permission ID
                stmt = select(MasterPermission.id).where(
                    MasterPermission.permission_key == permission_key
                )
                result = await session.execute(stmt)
                perm_id = result.scalar_one_or_none()
                if not perm_id:
                    return False

                # Check if user has this permission through any role
                stmt = select(UserRoleModel.role_id).where(UserRoleModel.user_id == user_id)
                result = await session.execute(stmt)
                user_roles = result.all()
                
                for role in user_roles:
                    stmt = select(RolePermission.permission_id).where(
                        RolePermission.role_id == role.role_id,
                        RolePermission.permission_id == perm_id
                    )
                    result = await session.execute(stmt)
                    if result.first():
                        return True
                
                return False
        except Exception as e:
            self.logger.error("Error in database fallback permission check", extra={"error": str(e)})
            return False

    @with_trace_id
    async def remove_role_from_user(self, user_id: UUID, role_id: UUID) -> bool:
        """Remove a role from a user (DB + Redis) with cache invalidation."""
        try:
            async with self.db_conn.get_db() as session:
                stmt = select(UserRoleModel).filter_by(user_id=user_id, role_id=role_id)
                result = await session.execute(stmt)
                ur = result.scalar_one_or_none()
                
                if ur:
                    await session.delete(ur)
                    await session.commit()
                    
            try:
                await self.redis_client.srem(RedisKeys.rbac_user_roles(user_id), str(role_id))
            except Exception as e:
                self.logger.warning("Failed to update user roles cache after removal", extra={"error": str(e)})
            return True
        except Exception as e:
            self.logger.error("Failed to remove role from user", extra={"error": str(e)})
            return False

    @with_trace_id
    async def add_role_to_user(self, user_id: UUID, role_id: UUID) -> bool:
        """Add a role to a user (DB + Redis) with cache update."""
        try:
            # Validate inputs
            if not isinstance(user_id, UUID) or not isinstance(role_id, UUID):
                raise ValueError("user_id and role_id must be UUID objects")
            
            # Check if already assigned (idempotent operation)
            user_roles = await self.get_user_roles(user_id)
            if str(role_id) in user_roles:
                self.logger.info(f"Role {role_id} already assigned to user {user_id}")
                return True

            async with self.db_conn.get_db() as session:
                session.add(UserRoleModel(user_id=user_id, role_id=role_id))
                await session.commit()
            
            try:
                redis_key = RedisKeys.rbac_user_roles(user_id)
                await self.redis_client.sadd(redis_key, str(role_id))
                await self.redis_client.expire(redis_key, RedisConfig.RBAC_TTL)
            except Exception as e:
                # Cache update failed - invalidate to prevent stale data
                await self.redis_client.delete(RedisKeys.rbac_user_roles(user_id))
                self.logger.warning(f"Cache update failed for user {user_id}, invalidated cache", extra={"error": str(e)})
            
            return True
        except Exception as e:
            self.logger.error("Failed to add role to user", extra={"error": str(e)})
            return False

    @with_trace_id
    async def invalidate_user_roles_cache(self, user_id: UUID) -> bool:
        """Invalidate user roles cache."""
        try:
            await self.redis_client.delete(RedisKeys.rbac_user_roles(user_id))
            self.logger.info(f"Invalidated user roles cache for user {user_id}")
            return True
        except Exception as e:
            self.logger.warning(f"Failed to invalidate user roles cache for user {user_id}", extra={"error": str(e)})
            return False

    @with_trace_id
    async def refresh_rbac_cache(self) -> bool:
        """Force refresh all RBAC cache from database."""
        try:
            self.logger.info("Force refreshing RBAC cache")
            try:
                # Delete all RBAC-related cache keys
                await self.redis_client.delete(RedisKeys.rbac_roles())
                await self.redis_client.delete(RedisKeys.rbac_permissions())
                await self.redis_client.delete(RedisKeys.rbac_permissions_reverse())
                await self.redis_client.delete(RedisKeys.master_auth_methods())
            except Exception as e:
                self.logger.warning("Error deleting cache keys during refresh", extra={"error": str(e)})
            
            await self.load_rbac_data()
            return True
        except Exception as e:
            self.logger.error(f"Failed to refresh RBAC cache: {e}", extra={"error": str(e)})
            return False

    @with_trace_id
    async def get_role_id_by_name(self, role_name: str) -> UUID | None:
        """Get role ID by role name from Redis cache."""
        roles_key = RedisKeys.rbac_roles()
        cached_roles = await self.redis_client.hgetall(roles_key)
        
        # Search through cached roles - values are already strings
        for role_id, name in cached_roles.items():
            if name == role_name:
                return UUID(role_id)
        
        # Cache miss - load from database
        self.logger.info(f"Cache miss for role name: {role_name}, loading from database")
        async with self.db_conn.get_db() as session:
            stmt = select(MasterRole.id).where(MasterRole.role_name == role_name)
            result = await session.execute(stmt)
            role_id = result.scalar_one_or_none()
            
            if role_id:
                # Update cache
                await self.redis_client.hset(roles_key, str(role_id), role_name)
                await self.redis_client.expire(roles_key, RedisConfig.RBAC_TTL)
                
        return role_id

    @with_trace_id
    async def get_role_name_by_id(self, role_id: UUID) -> str | None:
        """Get role name by role ID from Redis cache."""
        roles_key = RedisKeys.rbac_roles()
        cached_roles = await self.redis_client.hgetall(roles_key)
        
        # Search through cached roles - keys are role IDs, values are role names
        role_name = cached_roles.get(str(role_id))
        if role_name:
            return role_name
        
        # Cache miss - load from database
        self.logger.info(f"Cache miss for role ID: {role_id}, loading from database")
        async with self.db_conn.get_db() as session:
            stmt = select(MasterRole.role_name).where(MasterRole.id == role_id)
            result = await session.execute(stmt)
            role_name = result.scalar_one_or_none()
            
            if role_name:
                # Update cache
                await self.redis_client.hset(roles_key, str(role_id), role_name)
                await self.redis_client.expire(roles_key, RedisConfig.RBAC_TTL)
                
        return role_name

    @with_trace_id
    async def batch_check_permissions(self, user_id: UUID, permission_keys: List[str]) -> dict[str, bool]:
        """Check multiple permissions at once to reduce Redis round trips."""
        try:
            # Get user roles once
            user_roles = await self.get_user_roles(user_id)
            if not user_roles:
                return {key: False for key in permission_keys}

            # Get all user permissions (aggregate from all roles)
            user_perms: Set[str] = set()
            for role_id in user_roles:
                role_perms = await self.get_role_permissions(UUID(role_id))
                user_perms.update(role_perms)

            # Get permission IDs for all requested permissions
            reverse_key = RedisKeys.rbac_permissions_reverse()
            permission_ids = []
            for key in permission_keys:
                perm_id = await self.redis_client.hget(reverse_key, key)
                permission_ids.append(perm_id)
            
            # Check each permission
            results = {}
            for i, permission_key in enumerate(permission_keys):
                perm_id = permission_ids[i]
                if perm_id:
                    results[permission_key] = perm_id in user_perms
                else:                    # Fallback to database for missing permission
                    results[permission_key] = await self._check_permission_from_db(user_id, permission_key)
            
            return results
            
        except Exception as e:
            logger = APP_CONFIG.logger or APP_CONFIG.initialize_logger()
            logger.error("Error in batch permission check", extra={"error": str(e)})
            return {key: False for key in permission_keys}

    @with_trace_id
    async def get_role_id_by_uuid_cache_aside(db_conn, redis_client, role_uuid: str) -> str:
        """
        Get role ID by UUID with cache-aside pattern.
        
        Args:
            db_conn: Database connection
            redis_client: Redis client
            role_uuid: Role UUID
            
        Returns:
            Role ID if found, None otherwise
        """
        logger = get_logger()
        
        # Try to get from cache first
        cache_key = f"CreatorVerse:rbac:role_uuid_to_id:{role_uuid}"
        
        try:
            cached_id = await redis_client.get(cache_key)
            if cached_id:
                logger.debug("Role ID cache hit", extra={"uuid": role_uuid, "id": cached_id})
                return cached_id
        except Exception as e:
            logger.error("Redis error when retrieving role ID", extra={"error": str(e)})
        
        # Fetch from database if not in cache
        try:
            async with db_conn.get_db() as session:
                query = select(MasterRole.id).where(MasterRole.id == role_uuid)
                result = await session.execute(query)
                role_id = result.scalar_one_or_none()
                
                if role_id:
                    # Cache the result
                    try:
                        await redis_client.set(cache_key, str(role_id))
                        await redis_client.expire(cache_key, RedisConfig.RBAC_TTL)
                        logger.debug("Role ID cached", extra={"uuid": role_uuid, "id": str(role_id)})
                    except Exception as cache_err:
                        logger.warning("Failed to cache role ID", extra={"error": str(cache_err)})
                    
                    return str(role_id)
                
                return None
        except Exception as db_err:
            logger.error("Error retrieving role ID from database", extra={"error": str(db_err)})
            return None

    @with_trace_id
    async def get_auth_method_id_by_name_cache_aside(db_conn, redis_client, method_name: str) -> str:
        """
        Get authentication method ID by name with cache-aside pattern.
        
        Args:
            db_conn: Database connection
            redis_client: Redis client
            method_name: Auth method name (e.g., "email", "mobile", "google")
            
        Returns:
            Auth method ID if found, None otherwise
        """
        logger = get_logger()
        
        # Try to get from cache first
        cache_key = f"CreatorVerse:rbac:auth_method_name_to_id:{method_name}"
        
        try:
            cached_id = await redis_client.get(cache_key)
            if cached_id:
                logger.debug("Auth method ID cache hit", extra={"name": method_name, "id": cached_id})
                return cached_id
        except Exception as e:
            logger.error("Redis error when retrieving auth method ID", extra={"error": str(e)})
        
        # Fetch from database if not in cache
        try:
            async with db_conn.get_db() as session:
                query = select(MasterAuthMethod.id).where(MasterAuthMethod.method_key == method_name)
                result = await session.execute(query)
                auth_method_id = result.scalar_one_or_none()
                
                if auth_method_id:
                    # Cache the result
                    try:
                        await redis_client.set(cache_key, str(auth_method_id))
                        await redis_client.expire(cache_key, RedisConfig.RBAC_TTL)
                        logger.debug("Auth method ID cached", extra={"name": method_name, "id": str(auth_method_id)})

                    except Exception as cache_err:
                        logger.warning("Failed to cache auth method ID", extra={"error": str(cache_err)})
                    
                    return str(auth_method_id)
                
                return None
        except Exception as db_err:
            logger.error("Error retrieving auth method ID from database", extra={"error": str(db_err)})
            return None


def get_rbac_service() -> RBACService:
    """
    Get the RBAC service instance.

    Returns:
        RBACService: The RBAC service instance
    """

    db_conn = get_database()
    redis_client = get_locobuzz_redis()

    return RBACService(redis_client, db_conn)