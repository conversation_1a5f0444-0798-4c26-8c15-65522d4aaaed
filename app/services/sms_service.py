"""
SMS service for sending text messages with OTP codes and notifications.
Provides a provider-agnostic interface with <PERSON><PERSON><PERSON> as the default implementation.
Implements cache-aside pattern for tracking SMS sending status and rate limiting.
"""
import json
import time
from functools import lru_cache
from typing import Dict, Optional, Union

import httpx
from pydantic import BaseModel

from app.core.config import APP_CONFIG, get_locobuzz_redis
from app.core.logger import get_logger
from app.core.redis_keys import RedisKeys
from app.core_helper.async_logger import with_trace_id


class SMSConfig(BaseModel):
    """SMS configuration settings"""
    twilio_account_sid: str
    twilio_auth_token: str
    twilio_phone_number: str
    use_twilio: bool = True
    sms_enabled: bool = True
    sms_rate_limit_hourly: int = 5  # Max SMS per hour per user
    sms_cooldown_seconds: int = 60  # Cooldown between SMS sends


class SMSService:
    """Async SMS service with provider-agnostic interface and cache-aside pattern"""

    def __init__(self, config: SMSConfig):
        """Initialize the SMS service"""
        self.config = config
        self.logger = get_logger()
        self.twilio_api_url = f"https://api.twilio.com/2010-04-01/Accounts/{config.twilio_account_sid}/Messages.json"
        self.auth = (config.twilio_account_sid, config.twilio_auth_token)

    @with_trace_id
    async def _check_rate_limit(self, phone_number: str, redis_client) -> Dict[str, Union[bool, int, str]]:
        """
        Check if the phone number has exceeded rate limits
        
        Args:
            phone_number: The phone number to check
            redis_client: Redis client instance
            
        Returns:
            Dict containing allowed status, cooldown seconds, and message
        """
        # Normalize phone number for consistent keys
        normalized_phone = phone_number.replace('+', '')
        
        # Check cooldown period
        cooldown_key = RedisKeys.sms_cooldown_key(normalized_phone)
        cooldown_exists = await redis_client.exists(cooldown_key)
        
        if cooldown_exists:
            ttl = await redis_client.ttl(cooldown_key)
            return {
                "allowed": False,
                "cooldown_seconds": ttl if ttl > 0 else self.config.sms_cooldown_seconds,
                "message": f"Please wait {ttl} seconds before requesting another SMS"
            }
        
        # Check hourly rate limit
        rate_limit_key = RedisKeys.sms_rate_limit_key(normalized_phone)
        count_str = await redis_client.get(rate_limit_key)
        count = int(count_str) if count_str else 0
        
        if count >= self.config.sms_rate_limit_hourly:
            ttl = await redis_client.ttl(rate_limit_key)
            return {
                "allowed": False,
                "cooldown_seconds": ttl if ttl > 0 else 3600,
                "message": f"SMS rate limit reached. Try again later ({ttl} seconds)"
            }
            
        return {
            "allowed": True,
            "cooldown_seconds": 0,
            "message": "Rate limit check passed"
        }
    
    @with_trace_id
    async def _update_rate_limit(self, phone_number: str, redis_client) -> None:
        """
        Update rate limit counters after sending SMS
        
        Args:
            phone_number: The phone number that received SMS
            redis_client: Redis client instance
        """
        # Normalize phone number for consistent keys
        normalized_phone = phone_number.replace('+', '')
        
        # Set cooldown period
        cooldown_key = RedisKeys.sms_cooldown_key(normalized_phone)
        await redis_client.setex(cooldown_key, self.config.sms_cooldown_seconds, "1")
        
        # Update hourly rate limit
        rate_limit_key = RedisKeys.sms_rate_limit_key(normalized_phone)
        count = await redis_client.incr(rate_limit_key)
        if count == 1:  # First SMS in this period
            await redis_client.expire(rate_limit_key, 3600)  # Set 1-hour expiry

    @with_trace_id
    async def send_sms(self, phone_number: str, message: str) -> bool:
        """
        Send an SMS to the specified phone number with rate limiting.
        
        Args:
            phone_number: The recipient's phone number in E.164 format
            message: The message to send
            
        Returns:
            bool: True if SMS was sent successfully, False otherwise
        """
        if not self.config.sms_enabled:
            self.logger.warning("SMS sending is disabled", extra={"phone_number": phone_number})
            return False
            
        redis_client = get_locobuzz_redis()
        
        # Check rate limits
        rate_check = await self._check_rate_limit(phone_number, redis_client)
        if not rate_check["allowed"]:
            self.logger.warning(
                f"SMS rate limit: {rate_check['message']}", 
                extra={"phone_number": phone_number, "cooldown": rate_check["cooldown_seconds"]}
            )
            return False
        
        # Track SMS sending attempt in cache
        timestamp = int(time.time())
        tracking_key = RedisKeys.sms_tracking_key(phone_number)
        tracking_data = {
            "status": "sending",
            "timestamp": timestamp,
            "message_type": "generic"
        }
        await redis_client.setex(tracking_key, 300, json.dumps(tracking_data))  # 5 minute TTL for tracking
        
        success = False
        if self.config.use_twilio:
            success = await self._send_twilio_sms(phone_number, message)
        else:
            self.logger.error("No SMS provider configured", extra={"phone_number": phone_number})
            
        # Update tracking status
        if success:
            tracking_data["status"] = "delivered"
            await redis_client.setex(tracking_key, 3600, json.dumps(tracking_data))  # 1 hour TTL for successful delivery
            # Update rate limit counters
            await self._update_rate_limit(phone_number, redis_client)
        else:
            tracking_data["status"] = "failed"
            await redis_client.setex(tracking_key, 300, json.dumps(tracking_data))  # 5 minute TTL for failed attempts
            
        return success

    @with_trace_id
    async def _send_twilio_sms(self, phone_number: str, message: str) -> bool:
        """Send SMS using Twilio API"""
        try:
            payload = {
                "To": phone_number,
                "From": self.config.twilio_phone_number,
                "Body": message
            }

            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.post(
                    self.twilio_api_url,
                    data=payload,
                    auth=self.auth
                )

            if response.status_code in range(200, 300):
                self.logger.info("SMS sent successfully", extra={"phone_number": phone_number})
                return True
            else:
                self.logger.error(
                    "Failed to send SMS",
                    extra={
                        "phone_number": phone_number,
                        "status_code": response.status_code,
                        "response": response.text
                    }
                )
                return False
        except Exception as e:
            self.logger.error(
                "Error sending SMS",
                extra={"phone_number": phone_number, "error": str(e)}
            )
            return False
    
    @with_trace_id
    async def send_template_sms(
        self, 
        phone_number: str, 
        template_name: str, 
        params: Optional[Dict[str, str]] = None
    ) -> bool:
        """
        Send a templated SMS message.
        
        Args:
            phone_number: The recipient's phone number in E.164 format
            template_name: Name of the template to use
            params: Dictionary of parameters to fill in the template
            
        Returns:
            bool: True if SMS was sent successfully, False otherwise
        """
        redis_client = get_locobuzz_redis()
        params = params or {}
        
        # Define templates
        templates = {
            "influencer_otp": "Your CreatorVerse verification code is: {otp}. This code will expire in 10 minutes.",
            "brand_otp": "Your CreatorVerse brand verification code is: {otp}. This code will expire in 10 minutes.",
            "brand_approval": "Your request to join {brand_name} has been approved. Log in to CreatorVerse to access your brand dashboard.",
            "brand_invite": "You've been invited to join {brand_name} on CreatorVerse. Log in to accept the invitation.",
            "brand_role_change": "Your role in {brand_name} has been updated. Log in to CreatorVerse for details.",
            "brand_join_request": "New join request: {requester_name} wants to join {brand_name} on CreatorVerse. Log in to review this request."
        }
        
        # Get the template or use a default
        template = templates.get(template_name, "You have a new notification from CreatorVerse.")
        
        # Fill in the template with params
        try:
            message = template.format(**params)
        except KeyError as e:
            self.logger.error(
                f"Missing template parameter: {str(e)}",
                extra={"template": template_name, "phone_number": phone_number}
            )
            # Fallback message
            message = f"You have a new notification from CreatorVerse. Please check your account."
        
        # Track SMS with template info
        tracking_key = RedisKeys.sms_tracking_key(phone_number)
        tracking_data = {
            "status": "sending",
            "timestamp": int(time.time()),
            "template": template_name,
            "params": params
        }
        await redis_client.setex(tracking_key, 300, json.dumps(tracking_data))
        
        return await self.send_sms(phone_number, message)
    
    @with_trace_id
    async def send_otp_sms(self, phone_number: str, otp: str, user_role: str = "influencer") -> bool:
        """
        Send an OTP code via SMS.
        
        Args:
            phone_number: The recipient's phone number in E.164 format
            otp: The OTP code to send
            user_role: The user role (influencer or brand)
            
        Returns:
            bool: True if SMS was sent successfully, False otherwise
        """
        template_name = "brand_otp" if user_role.lower() != "influencer" else "influencer_otp"
        
        return await self.send_template_sms(
            phone_number=phone_number,
            template_name=template_name,
            params={"otp": otp}
        )
        
    @with_trace_id
    async def send_brand_notification_sms(self, phone_number: str, brand_name: str, notification_type: str) -> bool:
        """
        Send brand-specific notification via SMS.
        
        Args:
            phone_number: The recipient's phone number in E.164 format
            brand_name: The name of the brand
            notification_type: Type of notification ('approval', 'invite', 'role_change')
            
        Returns:
            bool: True if SMS was sent successfully, False otherwise
        """
        template_name = f"brand_{notification_type}"
        
        return await self.send_template_sms(
            phone_number=phone_number,
            template_name=template_name,
            params={"brand_name": brand_name}
        )
        
    @with_trace_id
    async def send_brand_join_request_sms(self, phone_number: str, brand_name: str, requester_name: str) -> bool:
        """
        Send notification to brand admin about new join request.
        
        Args:
            phone_number: The brand admin's phone number in E.164 format
            brand_name: The name of the brand
            requester_name: Name of the user requesting to join
            
        Returns:
            bool: True if SMS was sent successfully, False otherwise
        """
        return await self.send_template_sms(
            phone_number=phone_number,
            template_name="brand_join_request",
            params={
                "brand_name": brand_name,
                "requester_name": requester_name
            }
        )

    @with_trace_id
    async def check_sms_status(self, phone_number: str) -> Dict[str, Union[str, int]]:
        """
        Check the status of the most recent SMS sent to a phone number.
        
        Args:
            phone_number: The phone number to check
            
        Returns:
            Dict with status information or None if no recent SMS found
        """
        redis_client = get_locobuzz_redis()
        tracking_key = RedisKeys.sms_tracking_key(phone_number)
        
        # Get tracking data from Redis
        tracking_json = await redis_client.get(tracking_key)
        if not tracking_json:
            return {
                "status": "unknown",
                "message": "No recent SMS found",
                "timestamp": 0
            }
            
        try:
            tracking_data = json.loads(tracking_json)
            return tracking_data
        except Exception as e:
            self.logger.error(
                "Error parsing SMS tracking data",
                extra={"phone_number": phone_number, "error": str(e)}
            )
            return {
                "status": "error",
                "message": "Error retrieving SMS status",
                "timestamp": 0
            }


@lru_cache()
def get_sms_service() -> SMSService:
    """Get or create a singleton SMS service instance using cache-aside pattern"""
    # Get SMS settings from APP_CONFIG
    sms_settings = getattr(APP_CONFIG, "sms", {})
    config = SMSConfig(
        twilio_account_sid=sms_settings.get("TWILIO_ACCOUNT_SID", ""),
        twilio_auth_token=sms_settings.get("TWILIO_AUTH_TOKEN", ""),
        twilio_phone_number=sms_settings.get("TWILIO_PHONE_NUMBER", ""),
        use_twilio=sms_settings.get("USE_TWILIO", True),
        sms_enabled=sms_settings.get("SMS_ENABLED", True),
        sms_rate_limit_hourly=sms_settings.get("SMS_RATE_LIMIT_HOURLY", 5),
        sms_cooldown_seconds=sms_settings.get("SMS_COOLDOWN_SECONDS", 60)
    )
    return SMSService(config)
