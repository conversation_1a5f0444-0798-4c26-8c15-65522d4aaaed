"""
Enhanced Filter Service for multi-table filtering and advanced search capabilities
"""
import json
from typing import List, Dict, Any, Optional, Tu<PERSON>
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, desc, asc, func, text
from sqlalchemy.orm import selectinload, joinedload

from app.core.config import APP_CONFIG, get_database
from app.core.exceptions import FilterValidationError, DatabaseError
from app.models.enhanced_profile_models import EnhancedProfile, ContentItem, ProfileAnalyticsCache
from app.models.filter_models import FilterGroup, FilterDefinition, LocationHierarchy
from app.schemas.filter_schemas import DiscoveryFilters, RangeFilter
from app.core_helper.async_logger import with_trace_id


class EnhancedFilterService:
    """Enhanced filter service supporting multi-table queries and complex filtering"""
    
    def __init__(self):
        self.logger = APP_CONFIG.logger or APP_CONFIG.initialize_logger()
        self.db = get_database()
    
    @with_trace_id
    async def apply_advanced_filters(
        self,
        filters: DiscoveryFilters,
        page: int = 1,
        page_size: int = 20,
        sort_by: Optional[List[Dict[str, str]]] = None
    ) -> Tuple[List[EnhancedProfile], int, Dict[str, Any]]:
        """
        Apply advanced filters across multiple tables with performance optimization
        """
        try:
            async with self.db.get_db() as session:
                # Build base query with joins
                query = select(EnhancedProfile)
                count_query = select(func.count(EnhancedProfile.id))
                
                # Track which tables we need to join
                needs_content_join = False
                needs_location_join = False
                
                # Build filter conditions
                conditions = []
                filter_metadata = {}
                
                # 1. Profile-level filters (demographic, performance)
                profile_conditions = await self._build_profile_conditions(filters)
                conditions.extend(profile_conditions)
                filter_metadata['profile_filters'] = len(profile_conditions)
                
                # 2. Content-based filters (hashtags, mentions, content performance)
                content_conditions, content_join_needed = await self._build_content_conditions(filters)
                if content_conditions:
                    conditions.extend(content_conditions)
                    needs_content_join = content_join_needed
                    filter_metadata['content_filters'] = len(content_conditions)
                
                # 3. Location-based filters (hierarchical location filtering)
                location_conditions, location_join_needed = await self._build_location_conditions(filters)
                if location_conditions:
                    conditions.extend(location_conditions)
                    needs_location_join = location_join_needed
                    filter_metadata['location_filters'] = len(location_conditions)
                
                # 4. Audience-based filters (audience demographics, interests)
                audience_conditions = await self._build_audience_conditions(filters)
                conditions.extend(audience_conditions)
                filter_metadata['audience_filters'] = len(audience_conditions)
                
                # Apply joins if needed
                if needs_content_join:
                    query = query.join(ContentItem, EnhancedProfile.id == ContentItem.profile_id)
                    count_query = count_query.join(ContentItem, EnhancedProfile.id == ContentItem.profile_id)
                
                # Apply all conditions
                if conditions:
                    where_clause = and_(*conditions)
                    query = query.where(where_clause)
                    count_query = count_query.where(where_clause)
                
                # Get total count
                count_result = await session.execute(count_query)
                total_count = count_result.scalar()
                
                # Apply sorting
                query = self._apply_sorting(query, sort_by)
                
                # Apply pagination
                offset = (page - 1) * page_size
                query = query.offset(offset).limit(page_size)
                
                # Execute query with optimized loading
                query = query.options(
                    selectinload(EnhancedProfile.content_items)
                )
                
                result = await session.execute(query)
                profiles = result.scalars().all()
                
                # Build metadata
                metadata = {
                    'total_count': total_count,
                    'page': page,
                    'page_size': page_size,
                    'total_pages': (total_count + page_size - 1) // page_size,
                    'filters_applied': filter_metadata,
                    'joins_used': {
                        'content': needs_content_join,
                        'location': needs_location_join
                    }
                }
                
                return list(profiles), total_count, metadata
                
        except Exception as e:
            self.logger.error(f"Advanced filtering failed: {str(e)}")
            raise DatabaseError(f"Advanced filtering failed: {str(e)}")
    
    async def _build_profile_conditions(self, filters: DiscoveryFilters) -> List:
        """Build conditions for profile-level filters"""
        conditions = []
        
        if filters.demographic:
            demo = filters.demographic
            
            if demo.gender:
                conditions.append(EnhancedProfile.gender.in_(demo.gender))
            
            if demo.age_group:
                conditions.append(EnhancedProfile.age_group.in_(demo.age_group))
            
            if demo.language:
                conditions.append(EnhancedProfile.language.in_(demo.language))
            
            if demo.is_verified is not None:
                conditions.append(EnhancedProfile.is_verified == demo.is_verified)
        
        if filters.performance:
            perf = filters.performance
            
            if perf.follower_count:
                conditions.extend(self._build_range_conditions(
                    EnhancedProfile.follower_count, perf.follower_count
                ))
            
            if perf.engagement_rate:
                conditions.extend(self._build_range_conditions(
                    EnhancedProfile.engagement_rate, perf.engagement_rate
                ))
            
            if perf.sponsored_posts_performance:
                conditions.extend(self._build_range_conditions(
                    EnhancedProfile.sponsored_posts_performance, perf.sponsored_posts_performance
                ))
        
        return conditions
    
    async def _build_content_conditions(self, filters: DiscoveryFilters) -> Tuple[List, bool]:
        """Build conditions for content-based filters"""
        conditions = []
        needs_join = False
        
        if filters.content:
            content = filters.content
            
            # Hashtag filtering (JSON contains)
            if content.hashtags:
                for hashtag in content.hashtags:
                    conditions.append(
                        func.jsonb_path_exists(
                            EnhancedProfile.top_hashtags,
                            f'$[*] ? (@.name == "{hashtag}")'
                        )
                    )
            
            # Mention filtering
            if content.mentions:
                for mention in content.mentions:
                    conditions.append(
                        func.jsonb_path_exists(
                            EnhancedProfile.top_mentions,
                            f'$[*] ? (@.name == "{mention}")'
                        )
                    )
            
            # Interest filtering
            if content.interests:
                for interest in content.interests:
                    conditions.append(
                        func.jsonb_path_exists(
                            EnhancedProfile.top_interests,
                            f'$[*] ? (@.name == "{interest}")'
                        )
                    )
            
            # Content performance filters (requires join)
            if content.min_content_engagement or content.content_types:
                needs_join = True
                
                if content.min_content_engagement:
                    conditions.append(
                        ContentItem.like_count >= content.min_content_engagement
                    )
                
                if content.content_types:
                    conditions.append(
                        ContentItem.content_type.in_(content.content_types)
                    )
        
        return conditions, needs_join
    
    async def _build_location_conditions(self, filters: DiscoveryFilters) -> Tuple[List, bool]:
        """Build conditions for location-based filters"""
        conditions = []
        needs_join = False
        
        if filters.demographic and filters.demographic.location:
            location_filter = filters.demographic.location
            
            # City filtering
            if location_filter.cities:
                conditions.append(
                    func.jsonb_extract_path_text(
                        EnhancedProfile.location_data, 'city'
                    ).in_(location_filter.cities)
                )
            
            # Country filtering
            if location_filter.countries:
                conditions.append(
                    func.jsonb_extract_path_text(
                        EnhancedProfile.location_data, 'country'
                    ).in_(location_filter.countries)
                )
            
            # State filtering
            if location_filter.states:
                conditions.append(
                    func.jsonb_extract_path_text(
                        EnhancedProfile.location_data, 'state'
                    ).in_(location_filter.states)
                )
        
        return conditions, needs_join
    
    async def _build_audience_conditions(self, filters: DiscoveryFilters) -> List:
        """Build conditions for audience-based filters"""
        conditions = []
        
        if filters.audience:
            audience = filters.audience
            
            # Audience age group filtering
            if audience.age_groups:
                for age_group in audience.age_groups:
                    conditions.append(
                        func.jsonb_path_exists(
                            EnhancedProfile.audience_demographics,
                            f'$.age_distribution[*] ? (@.age_range == "{age_group}" && @.percentage > 20)'
                        )
                    )
            
            # Audience gender filtering
            if audience.gender_distribution:
                for gender, min_percentage in audience.gender_distribution.items():
                    conditions.append(
                        func.jsonb_path_exists(
                            EnhancedProfile.audience_demographics,
                            f'$.gender_distribution[*] ? (@.gender == "{gender}" && @.percentage >= {min_percentage})'
                        )
                    )
            
            # Brand affinity filtering
            if audience.brand_affinities:
                for brand in audience.brand_affinities:
                    conditions.append(
                        func.jsonb_path_exists(
                            EnhancedProfile.brand_affinities,
                            f'$[*] ? (@.brand == "{brand}" && @.affinity_score > 5.0)'
                        )
                    )
        
        return conditions
    
    def _build_range_conditions(self, column, range_filter: RangeFilter) -> List:
        """Build range conditions for a column"""
        conditions = []
        
        if range_filter.min_value is not None:
            conditions.append(column >= range_filter.min_value)
        
        if range_filter.max_value is not None:
            conditions.append(column <= range_filter.max_value)
        
        return conditions
    
    def _apply_sorting(self, query, sort_by: Optional[List[Dict[str, str]]]):
        """Apply sorting to query"""
        if not sort_by:
            # Default sort by follower count descending
            return query.order_by(desc(EnhancedProfile.follower_count))
        
        for sort_criteria in sort_by:
            field = sort_criteria.get('field', 'follower_count')
            order = sort_criteria.get('order', 'desc')
            
            column = self._get_sort_column(field)
            if order.lower() == 'desc':
                query = query.order_by(desc(column))
            else:
                query = query.order_by(asc(column))
        
        return query
    
    def _get_sort_column(self, field: str):
        """Get SQLAlchemy column for sorting"""
        field_mapping = {
            'follower_count': EnhancedProfile.follower_count,
            'engagement_rate': EnhancedProfile.engagement_rate,
            'sponsored_posts_performance': EnhancedProfile.sponsored_posts_performance,
            'credibility_score': EnhancedProfile.credibility_score,
            'average_likes': EnhancedProfile.average_likes,
            'average_views': EnhancedProfile.average_views,
            'created_at': EnhancedProfile.created_at,
            'updated_at': EnhancedProfile.updated_at,
            'platform_username': EnhancedProfile.platform_username,
            'full_name': EnhancedProfile.full_name,
        }
        
        return field_mapping.get(field, EnhancedProfile.follower_count)
