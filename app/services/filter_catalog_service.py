"""
Filter Catalog Service for CreatorVerse Discovery & Profile Analytics

This service handles the transformation of database filter data into the exact
frontend JSON format required by the UI components.
"""

import json
from typing import List, Dict, Any, Optional
from sqlalchemy import select
from sqlalchemy.orm import selectinload

from app.core.config import get_database, get_locobuzz_redis
from app.core.exceptions import CreatorVerseError
from app.models.filter_models import FilterGroup, FilterDefinition, LocationHierarchy
from app.models.filter_models import PlatformEnum, OptionForTypeEnum
from app.core_helper.async_logger import with_trace_id


class FilterCatalogService:
    """Service for managing filter catalog and transforming data for frontend"""
    
    def __init__(self):
        self.db = get_database()
        self.redis = get_locobuzz_redis()
        self.cache_ttl = 3600  # 1 hour cache
    
    @with_trace_id
    async def get_filters_for_frontend(
        self,
        channel: PlatformEnum,
        option_for: OptionForTypeEnum,
        use_cache: bool = True
    ) -> List[Dict[str, Any]]:
        """
        Get filters in the exact format expected by the frontend
        
        Args:
            channel: Platform type (instagram, youtube, tiktok)
            option_for: Target type (creator, audience)
            use_cache: Whether to use Redis cache
            
        Returns:
            List of filter groups in frontend format
        """
        try:
            # Generate cache key
            cache_key = f"filter_catalog:{channel.value}:{option_for.value}"
            
            # Try cache first
            if use_cache:
                cached_result = await self.redis.get(cache_key)
                if cached_result:
                    return json.loads(cached_result)
            
            # Query database
            async with self.db.get_db() as session:
                result = await session.execute(
                    select(FilterGroup)
                    .options(selectinload(FilterGroup.filter_definitions))
                    .where(
                        FilterGroup.channel == channel,
                        FilterGroup.option_for == option_for,
                        FilterGroup.is_active == True
                    )
                    .order_by(FilterGroup.sort_order, FilterGroup.name)
                )
                filter_groups = result.scalars().all()
            
            # Transform to frontend format
            frontend_filters = []
            for group in filter_groups:
                group_data = await self._transform_group_to_frontend(group)
                if group_data['filters']:  # Only include groups with active filters
                    frontend_filters.append(group_data)
            
            # Cache the result
            if use_cache:
                await self.redis.setex(
                    cache_key,
                    self.cache_ttl,
                    json.dumps(frontend_filters)
                )
            
            return frontend_filters
            
        except Exception as e:
            raise CreatorVerseError(f"Failed to get filters: {str(e)}")
    
    async def _transform_group_to_frontend(self, group: FilterGroup) -> Dict[str, Any]:
        """Transform a FilterGroup to frontend format"""
        filters = []
        
        for definition in group.filter_definitions:
            if definition.is_active:
                filter_data = await self._transform_definition_to_frontend(definition)
                filters.append(filter_data)
        
        return {
            "optionName": group.name,
            "optionFor": group.option_for.value,
            "channel": group.channel.value,
            "filters": sorted(filters, key=lambda x: x.get('sort_order', 0))
        }
    
    async def _transform_definition_to_frontend(self, definition: FilterDefinition) -> Dict[str, Any]:
        """Transform a FilterDefinition to frontend format"""
        # Transform filter type (underscore to hyphen)
        frontend_type = definition.filter_type.value.replace('_', '-')
        
        # Base filter structure
        filter_data = {
            "name": definition.name,
            "type": frontend_type,
            "icon": definition.icon,
            "minmax": definition.has_minmax,
            "enterValue": definition.has_enter_value,
            "placeholder": definition.placeholder,
            "sort_order": definition.sort_order
        }
        
        # Add searchBox if applicable
        if definition.has_search_box:
            filter_data["searchBox"] = True
        
        # Transform options based on filter type
        if definition.filter_type.value == 'multilevel_checkbox':
            filter_data["options"] = await self._build_multilevel_options(definition)
        elif definition.options:
            filter_data["options"] = definition.options
        
        return filter_data
    
    async def _build_multilevel_options(self, definition: FilterDefinition) -> List[Dict[str, Any]]:
        """Build multilevel options for complex filters like location"""
        if definition.name.lower() == 'location':
            return await self._build_location_hierarchy_options()
        else:
            # For other multilevel filters, transform existing options
            return await self._transform_to_multilevel_structure(definition.options)
    
    async def _build_location_hierarchy_options(self) -> List[Dict[str, Any]]:
        """Build location options from location_hierarchy table"""
        try:
            async with self.db.get_db() as session:
                # Get all active locations grouped by tier
                result = await session.execute(
                    select(LocationHierarchy)
                    .where(LocationHierarchy.is_active == True)
                    .order_by(LocationHierarchy.tier, LocationHierarchy.name)
                )
                locations = result.scalars().all()
            
            # Group by tier
            tier_groups = {}
            for location in locations:
                tier = location.tier or 'other'
                if tier not in tier_groups:
                    tier_groups[tier] = []
                tier_groups[tier].append({
                    "label": location.name,
                    "value": location.code or location.name.lower().replace(' ', '_'),
                    "description": ""
                })
            
            # Build multilevel structure
            multilevel_options = []
            tier_names = {
                '1': 'Tier 1',
                '2': 'Tier 2', 
                '3': 'Tier 3 or Rural'
            }
            
            for tier in ['1', '2', '3']:
                if tier in tier_groups:
                    multilevel_options.append({
                        "subOptionName": tier_names[tier],
                        "subOptionType": "checkbox",
                        "collapsed": True,
                        "checkboxEnabled": True,
                        "subOptions": tier_groups[tier]
                    })
            
            return multilevel_options
            
        except Exception as e:
            # Fallback to default structure if location hierarchy fails
            return [
                {
                    "subOptionName": "Tier 1",
                    "subOptionType": "checkbox",
                    "collapsed": True,
                    "checkboxEnabled": True,
                    "subOptions": [
                        {"label": "Mumbai", "value": "mumbai", "description": ""},
                        {"label": "Delhi", "value": "delhi", "description": ""},
                        {"label": "Bangalore", "value": "bangalore", "description": ""},
                        {"label": "Chennai", "value": "chennai", "description": ""}
                    ]
                }
            ]
    
    async def _transform_to_multilevel_structure(self, options: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Transform simple options to multilevel structure"""
        if not options:
            return []
        
        # For non-location multilevel filters, create a single group
        return [
            {
                "subOptionName": "Options",
                "subOptionType": "checkbox",
                "collapsed": False,
                "checkboxEnabled": False,
                "subOptions": options
            }
        ]
    
    @with_trace_id
    async def invalidate_cache(
        self,
        channel: Optional[PlatformEnum] = None,
        option_for: Optional[OptionForTypeEnum] = None
    ):
        """Invalidate filter cache for specific or all combinations"""
        try:
            if channel and option_for:
                # Invalidate specific cache
                cache_key = f"filter_catalog:{channel.value}:{option_for.value}"
                await self.redis.delete(cache_key)
            else:
                # Invalidate all filter catalog caches
                pattern = "filter_catalog:*"
                keys = await self.redis.keys(pattern)
                if keys:
                    await self.redis.delete(*keys)
                    
        except Exception as e:
            # Don't fail the operation for cache invalidation errors
            pass
    
    @with_trace_id
    async def get_filter_statistics(self) -> Dict[str, Any]:
        """Get statistics about the filter catalog"""
        try:
            async with self.db.get_db() as session:
                # Count filter groups by platform and option_for
                result = await session.execute(
                    select(FilterGroup.channel, FilterGroup.option_for, FilterGroup.id)
                    .where(FilterGroup.is_active == True)
                )
                groups = result.all()
                
                # Count filter definitions
                result = await session.execute(
                    select(FilterDefinition.id)
                    .where(FilterDefinition.is_active == True)
                )
                definitions = result.all()
                
                # Group statistics
                stats = {
                    "total_groups": len(groups),
                    "total_definitions": len(definitions),
                    "by_platform": {},
                    "by_option_for": {}
                }
                
                for channel, option_for, _ in groups:
                    platform = channel.value
                    target = option_for.value
                    
                    if platform not in stats["by_platform"]:
                        stats["by_platform"][platform] = 0
                    stats["by_platform"][platform] += 1
                    
                    if target not in stats["by_option_for"]:
                        stats["by_option_for"][target] = 0
                    stats["by_option_for"][target] += 1
                
                return stats
                
        except Exception as e:
            raise CreatorVerseError(f"Failed to get filter statistics: {str(e)}")


# Global service instance
filter_catalog_service = FilterCatalogService()
