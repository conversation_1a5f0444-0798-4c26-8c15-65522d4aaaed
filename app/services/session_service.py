"""
Session management service with optimized Redis operations using cache-aside pattern.
"""
import uuid
from datetime import UTC, datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from uuid import UUID

from fastapi import Request
from sqlalchemy import select, update, desc, and_

from app.core.config import APP_CONFIG
from app.core.logger import get_logger
from app.core.redis_keys import RedisKeys
from app.core.security import create_access_token, create_refresh_token, get_refresh_token_expire_days
from app.core_helper.async_logger import with_trace_id
from app.core_helper.redis_client import RedisClient
from app.models.user_models import UserSession, User

# Maximum number of concurrent sessions per user (default: 3)
MAX_SESSIONS_PER_USER = 3

logger = get_logger()


@with_trace_id
async def create_oauth_user_session(
        db_conn,
        user: User,
        redis_client,
        request = None,
        rbac_service = None
) -> Dict[str, str]:
    """
    Create a user session specifically for OAuth flows.
    This function handles the database session management internally.
    
    Args:
        db_conn: Database connection
        user: The user entity
        redis_client: Redis client instance
        request: FastAPI request object (optional)
        rbac_service: RBAC service for permission encoding (optional)
        
    Returns:
        Dict with access_token, refresh_token, token_type and expires_in
    """
    try:
        async with db_conn.get_db() as session:
            async with session.begin():
                # Inject RBAC service into request if available
                if rbac_service and request:
                    request.rbac_service = rbac_service
                
                # Call the main session creation function with proper parameters
                session_data = await create_user_session(
                    session_user=user,
                    db_session=session,  # ✅ FIXED: Pass actual session
                    redis_client=redis_client,
                    request=request
                )
                
                # Commit the session creation
                await session.commit()
                
                return session_data
                
    except Exception as e:
        logger.error("Error creating OAuth user session", extra={
            "user_id": str(user.id) if user else "unknown",
            "error": str(e)
        })
        raise


@with_trace_id
async def create_user_session(
        session_user: User,
        db_session,
        redis_client: RedisClient,
        request: Request,
) -> Dict[str, str]:
    """
    Create a new user session and manage maximum concurrent sessions.
    
    This function:
    1. Creates access and refresh tokens
    2. Manages maximum concurrent sessions by revoking oldest sessions if needed
    3. Saves the session in both the database and Redis
    4. Returns the generated tokens and session information
    
    Args:
        session_user: The user instance (must be session-bound)
        db_session: SQLAlchemy session
        redis_client: Redis client instance
        request: FastAPI request object
        
    Returns:
        Dict with access_token, refresh_token, token_type and expires_in
    """
    user_id = session_user.id
    str_user_id = str(user_id)

    # 1. Check for existing active sessions and revoke oldest if needed
    query = (
        select(UserSession)
        .where(UserSession.user_id == user_id)
        .where(UserSession.is_revoked == False)
        .order_by(desc(UserSession.issued_at))
    )
    result = await db_session.execute(query)
    active_sessions = result.scalars().all()

    # If we have MAX_SESSIONS_PER_USER or more active sessions, revoke oldest ones
    if len(active_sessions) >= MAX_SESSIONS_PER_USER:

        sessions_to_revoke = sorted(
            active_sessions,
            key=lambda s: s.issued_at if s.issued_at else datetime.min
        )[:(len(active_sessions) - MAX_SESSIONS_PER_USER + 1)]  # +1 because we're adding a new one
        for old_session in sessions_to_revoke:
            # Update session in DB
            old_session.is_revoked = True

            # Delete from Redis
            old_session_key = RedisKeys.user_session(str_user_id, str(old_session.id))
            await redis_client.delete(old_session_key)

            logger.info(
                f"Revoked old session due to maximum sessions limit",
                extra={"user_id": str_user_id, "session_id": str(old_session.id)}
            )
    # 2. Generate tokens
    access_token_expires = timedelta(minutes=APP_CONFIG.access_token_expire_minutes)
    refresh_token_expires = timedelta(days=APP_CONFIG.refresh_token_expire_days)

    session_id = uuid.uuid4()  # Generate a new session ID
    
    # Try to get RBAC service for permission encoding
    rbac_service = getattr(request, 'rbac_service', None) if hasattr(request, 'rbac_service') else None
    
    # Create access token with permissions if RBAC service is available
    if rbac_service:
        from app.core.security import create_access_token_with_permissions
        access_token = await create_access_token_with_permissions(
            user_id=str(user_id),
            session_id=str(session_id),
            rbac_service=rbac_service,
            expires_delta=access_token_expires
        )
        logger.info("Created access token with permissions", extra={"user_id": str(user_id)})
    else:
        # Fallback to basic token without permissions
        access_token = create_access_token(
            data={"sub": str(user_id), "sid": str(session_id)},
            expires_delta=access_token_expires,
            include_permissions=False
        )
        logger.info("Created basic access token (no RBAC service)", extra={"user_id": str(user_id)})

    # Create short refresh token instead of JWT
    refresh_token = create_refresh_token(
        data={},  # Not used for the short token format
        expires_delta=refresh_token_expires
    )

    # 3. Create a new session record
    expires_at = datetime.now(UTC) + refresh_token_expires
    if request:
        ip_address = request.client.host if request.client else None
        user_agent = request.headers.get("User-Agent", "")
    else:
        ip_address = None
        user_agent = ""

    new_session = UserSession(
        id=session_id,
        user_id=user_id,
        access_token=access_token,
        refresh_token=refresh_token,
        expires_at=expires_at,
        ip_address=ip_address,
        user_agent=user_agent,
        is_revoked=False
    )

    db_session.add(new_session)

    # 4. Store session in Redis
    session_key = RedisKeys.user_session(str_user_id, str(new_session.id))
    session_data = {
        "session_id": str(session_id),
        "access_token": access_token,
        "refresh_token": refresh_token,
        "user_id": str_user_id,
        "device_info": user_agent,
        "ip_address": ip_address if ip_address else "",
        "issued_at": str(new_session.issued_at),
        "expires_at": str(expires_at),
    }
    # Store in Redis with expiration matching refresh token
    try:
        # Store session data
        await redis_client.hset_mapping(session_key, mapping=session_data)
        await redis_client.expire(session_key, 86400 * get_refresh_token_expire_days())

        # Store refresh token mapping for validation
        refresh_token_key = RedisKeys.refresh_token_mapping(refresh_token)
        refresh_data = {
            "user_id": str_user_id,
            "session_id": str(session_id),
            "expires_at": str(expires_at)
        }
        await redis_client.hset_mapping(refresh_token_key, mapping=refresh_data)
        await redis_client.expire(refresh_token_key, 86400 * get_refresh_token_expire_days())

        logger.debug("User session stored in Redis", extra={"user_id": str_user_id})
    except Exception as e:
        logger.error("Failed to store session in Redis", extra={
            "user_id": str_user_id,
            "error": str(e)
        })
        # Continue anyway since we have the DB record

    # 5. Return token data
    return {
        "access_token": access_token,
        "refresh_token": refresh_token,
        "token_type": "bearer",
        "expires_in": APP_CONFIG.access_token_expire_minutes * 60,
        "session_id": str(new_session.id)
    }


@with_trace_id
async def revoke_user_session(
        db_conn,
        redis_client,
        user_id: UUID,
        session_id: UUID
) -> Tuple[bool, str]:
    """
    Revoke a specific user session.
    
    Args:
        db_conn: Database connection
        redis_client: Redis client
        user_id: User ID
        session_id: Session ID to revoke
        
    Returns:
        Tuple of (success, message)
    """
    str_user_id = str(user_id)
    str_session_id = str(session_id)

    try:
        async with db_conn.get_db() as session:
            async with session.begin():
                # Update session in database
                stmt = (
                    update(UserSession)
                    .where(UserSession.id == session_id)
                    .where(UserSession.user_id == user_id)
                    .values(is_revoked=True)
                    .returning(UserSession.id)
                )
                result = await session.execute(stmt)
                session_exists = result.scalar_one_or_none() is not None

                if not session_exists:
                    return False, "Session not found"
                # Get the session data to find the refresh token
                session_key = RedisKeys.user_session(str_user_id, str_session_id)
                session_data = await redis_client.hgetall(session_key)

                # Delete session from Redis
                await redis_client.delete(session_key)

                # If refresh token exists in session data, delete its mapping too
                if session_data and "refresh_token" in session_data:
                    refresh_token = session_data["refresh_token"]
                    refresh_token_key = RedisKeys.refresh_token_mapping(refresh_token)
                    await redis_client.delete(refresh_token_key)
                    logger.debug("Refresh token mapping deleted", extra={
                        "user_id": str_user_id,
                        "session_id": str_session_id
                    })

                await session.commit()

                logger.info("Session revoked", extra={
                    "user_id": str_user_id,
                    "session_id": str_session_id
                })

                return True, "Session revoked successfully"
    except Exception as e:
        logger.error("Error revoking session", extra={
            "user_id": str_user_id,
            "session_id": str_session_id,
            "error": str(e)
        })
        return False, f"Error revoking session: {str(e)}"


@with_trace_id
async def get_user_active_sessions(
        db_conn,
        redis_client,
        user_id: UUID
) -> List[Dict[str, str]]:
    """
    Get all active sessions for a user.
    
    Args:
        db_conn: Database connection
        redis_client: Redis client
        user_id: User ID
          Returns:
        List of session dictionaries
    """
    str_user_id = str(user_id)
    sessions_list = []

    try:
        # Try to get from Redis first using pattern matching
        # Use RedisKeys helper to build the pattern
        base_key = RedisKeys.user_session(str_user_id, "*")
        keys = await redis_client.keys(base_key)

        if keys:
            # Get session data from Redis
            pipe = redis_client.pipeline()
            for key in keys:
                pipe.hgetall(key)
            results = await pipe.execute()

            for session_data in results:
                if session_data:  # Only add if we have data
                    sessions_list.append(session_data)

            if sessions_list:
                logger.debug("Sessions retrieved from Redis", extra={"user_id": str_user_id})
                return sessions_list
    except Exception as e:
        logger.warning("Error retrieving sessions from Redis", extra={
            "user_id": str_user_id,
            "error": str(e)
        })

    # Fall back to database
    try:
        async with db_conn.get_db() as session:
            query = (
                select(UserSession)
                .where(UserSession.user_id == user_id)
                .where(UserSession.is_revoked == False)
                .order_by(desc(UserSession.issued_at))
            )
            result = await session.execute(query)
            db_sessions = result.scalars().all()

            for db_session in db_sessions:
                session_dict = {
                    "session_id": str(db_session.id),
                    "user_id": str(db_session.user_id),
                    "issued_at": str(db_session.issued_at),
                    "expires_at": str(db_session.expires_at) if db_session.expires_at else "",
                    "ip_address": str(db_session.ip_address) if db_session.ip_address else "",
                    "device_info": db_session.user_agent or ""
                }
                sessions_list.append(session_dict)
                # Update Redis cache
                try:
                    session_key = RedisKeys.user_session(str_user_id, str(db_session.id))
                    await redis_client.hset_mapping(session_key, mapping=session_dict)
                    await redis_client.expire(session_key, 86400 * get_refresh_token_expire_days())
                except Exception as cache_err:
                    logger.warning("Failed to cache session", extra={
                        "user_id": str_user_id,
                        "error": str(cache_err)
                    })

            logger.debug("Sessions retrieved from database", extra={"user_id": str_user_id})
            return sessions_list
    except Exception as e:
        logger.error("Error retrieving sessions from database", extra={
            "user_id": str_user_id,
            "error": str(e)
        })
        return []


@with_trace_id
async def validate_refresh_token(
        db_conn,
        redis_client,
        refresh_token: str
) -> Tuple[bool, Optional[str], Optional[str]]:
    """
    Validates a refresh token and returns user_id and session_id if valid.
    Uses cache-aside pattern to check Redis first then fallback to database.
    
    Args:
        db_conn: Database connection
        redis_client: Redis client
        refresh_token: The refresh token to validate
        
    Returns:
        Tuple of (is_valid, user_id, session_id)
    """
    try:
        # Check if token exists in Redis using the mapping
        token_key = RedisKeys.refresh_token_mapping(refresh_token)
        token_data = await redis_client.hgetall(token_key)

        if token_data:
            # Token found in Redis, check if it's expired
            try:
                expires_at = datetime.fromisoformat(token_data.get("expires_at", ""))
                if expires_at <= datetime.now(UTC):
                    logger.warning("Refresh token expired", extra={"token": refresh_token[:8] + "..."})
                    await redis_client.delete(token_key)
                    return False, None, None

                return True, token_data.get("user_id"), token_data.get("session_id")
            except ValueError as e:
                logger.error("Invalid expires_at format in refresh token data", extra={"error": str(e)})
                return False, None, None

        # If not in Redis, check database for fallback
        async with db_conn.get_db() as session:
            # Query the database for this token
            query = (
                select(UserSession)
                .where(UserSession.refresh_token == refresh_token)
                .where(UserSession.is_revoked == False)
                .where(UserSession.expires_at > datetime.now(UTC))
            )
            result = await session.execute(query)
            db_session = result.scalar_one_or_none()

            if not db_session:
                logger.warning("Refresh token not found or expired in database")
                return False, None, None

            # Valid token found in database, store in Redis for future lookups
            user_id = str(db_session.user_id)
            session_id = str(db_session.id)

            # Store in Redis
            refresh_data = {
                "user_id": user_id,
                "session_id": session_id,
                "expires_at": str(db_session.expires_at)
            }

            await redis_client.hset_mapping(token_key, mapping=refresh_data)
            # Calculate TTL in seconds
            ttl = int((db_session.expires_at - datetime.now(UTC)).total_seconds())
            if ttl > 0:
                await redis_client.expire(token_key, ttl)

            return True, user_id, session_id

    except Exception as e:
        logger.error(
            "Error validating refresh token",
            extra={"error": str(e)}
        )
        return False, None, None


@with_trace_id
async def generate_auth_tokens(
        db_conn,
        redis_client,
        user_id: str,
        *,
        session_id: Optional[str] = None,  # 🔸 existing session on refresh
        old_refresh_token: Optional[str] = None,  # 🔸 existing token on refresh
        refresh: bool = False,
        rbac_service = None  # New parameter for permission encoding
) -> Dict[str, Any]:
    """
    • login   -> refresh == False  -> create brand-new session
    • refresh -> refresh == True   -> revoke session_id, rotate tokens
    All DB ops are wrapped in transactions; Redis ops are wrapped in try/except
    so DB consistency is never sacrificed by a cache failure.
    """
    logger = get_logger()
    now = datetime.now(UTC)

    # ---------- 1. Revoke the old session (refresh flow only) ----------
    if refresh and session_id:
        try:
            async with db_conn.get_db() as s, s.begin():
                await s.execute(
                    update(UserSession)
                    .where(
                        and_(
                            UserSession.user_id == UUID(user_id),
                            UserSession.id == session_id,
                            UserSession.is_revoked.is_(False)
                        )
                    )
                    .values(is_revoked=True)
                )
        except Exception as db_err:
            logger.error("DB error during session revoke",
                         extra={"user_id": user_id, "session_id": session_id, "error": str(db_err)})
            raise

        # Redis is best-effort; cache failures must not abort the refresh
        try:
            await redis_client.delete(RedisKeys.user_session(user_id, session_id))
            if old_refresh_token:
                await redis_client.delete(
                    RedisKeys.refresh_token_mapping(old_refresh_token)
                )
        except Exception as r_err:
            logger.warning("Failed to purge old Redis keys",
                           extra={"user_id": user_id, "session_id": session_id, "error": str(r_err)})

    # ---------- 2. Create the replacement session ----------
    new_session_id = str(uuid.uuid4())
    access_expires_td = timedelta(minutes=APP_CONFIG.access_token_expire_minutes)
    refresh_expires_td = timedelta(days=APP_CONFIG.refresh_token_expire_days)

    # Create access token with permissions if RBAC service is available
    if rbac_service:
        from app.core.security import create_access_token_with_permissions
        access_token = await create_access_token_with_permissions(
            user_id=user_id,
            session_id=new_session_id,
            rbac_service=rbac_service,
            expires_delta=access_expires_td
        )
        logger.debug("Generated access token with permissions for refresh", extra={"user_id": user_id})
    else:
        # Fallback to basic token
        access_token = create_access_token(
            data={"sub": user_id, "sid": new_session_id},
            expires_delta=access_expires_td,
            include_permissions=False
        )
        logger.debug("Generated basic access token for refresh", extra={"user_id": user_id})
    refresh_token = create_refresh_token(
        data={}, expires_delta=refresh_expires_td
    )

    # 2-a. persist to DB
    try:
        async with db_conn.get_db() as s, s.begin():
            s.add(
                UserSession(
                    id=new_session_id,
                    user_id=UUID(user_id),
                    access_token=access_token,
                    refresh_token=refresh_token,
                    issued_at=now,
                    expires_at=now + refresh_expires_td,
                    is_revoked=False
                )
            )
    except Exception as db_err:
        logger.error("DB error during new-session insert",
                     extra={"user_id": user_id, "error": str(db_err)})
        raise

    # 2-b. cache in Redis  (best-effort)
    ttl = int(refresh_expires_td.total_seconds())
    try:
        user_session_key = RedisKeys.user_session(user_id, new_session_id)
        refresh_token_key = RedisKeys.refresh_token_mapping(refresh_token)

        session_data = {
            "user_id": user_id,
            "session_id": new_session_id,
            "access_token": access_token,
            "refresh_token": refresh_token,
            "issued_at": now.isoformat(),
            "expires_at": (now + refresh_expires_td).isoformat(),
        }

        pipe = redis_client.pipeline()
        pipe.hset_mapping(user_session_key, mapping=session_data)
        pipe.expire(user_session_key, ttl)
        pipe.hset_mapping(refresh_token_key, mapping={
            "user_id": user_id,
            "session_id": new_session_id,
            "expires_at": session_data["expires_at"]
        })
        pipe.expire(refresh_token_key, ttl)
        await pipe.execute()
    except Exception as r_err:
        logger.warning("Redis cache write failed (session still valid)",
                       extra={"user_id": user_id, "error": str(r_err)})

    # ---------- 3. return payload ----------
    return {
        "access_token": access_token,
        "refresh_token": refresh_token,
        "token_type": "bearer",
        "expires_in": APP_CONFIG.access_token_expire_minutes * 60,
    }


@with_trace_id
async def validate_session_in_db(
        db_conn,
        user_id: str,
        session_id: str
) -> bool:
    """
    Validate if a session exists and is not revoked in the database.
    
    Args:
        db_conn: Database connection
        user_id: User ID
        session_id: Session ID
        
    Returns:
        True if session is valid, False otherwise
    """
    try:
        async with db_conn.get_db() as session:
            query = (
                select(UserSession.id)
                .where(UserSession.id == session_id)
                .where(UserSession.user_id == user_id)
                .where(UserSession.is_revoked == False)
                .where(UserSession.expires_at > datetime.now(UTC))
            )
            result = await session.execute(query)
            session_exists = result.scalar_one_or_none() is not None
            
            return session_exists
            
    except Exception as e:
        logger.error("Error validating session in database", extra={
            "user_id": user_id,
            "session_id": session_id,
            "error": str(e)
        })
        return False
