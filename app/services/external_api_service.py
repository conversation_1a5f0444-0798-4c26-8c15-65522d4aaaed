"""
External API Service - Main service for managing external API providers
This service provides a unified interface for all external API providers.
"""
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime

from app.core.config import APP_CONFIG
from app.core.exceptions import C<PERSON>VerseError, FilterValidationError
from app.core_helper.async_logger import with_trace_id
from app.schemas.external_api.provider_interface import (
    ExternalAPIProvider, APIProviderFactory, APIProviderType, APIProviderCapability
)
from app.schemas.external_api.frontend_schemas import (
    FrontendFilterSelections, FrontendSearchRequest, CreatorProfile, 
    SearchMetadata, FrontendSearchResponse, PlatformEnum, OptionForEnum
)


class ExternalAPIService:
    """Service for managing external API providers"""
    
    def __init__(self):
        self.logger = APP_CONFIG.logger or APP_CONFIG.initialize_logger()
        self._providers: Dict[APIProviderType, ExternalAPIProvider] = {}
        self._default_provider = APIProviderType.PHYLLO
        
        # Initialize providers
        self._initialize_providers()
    
    def _initialize_providers(self):
        """Initialize available API providers"""
        try:
            # Initialize Phyllo provider
            from app.services.external_providers.phyllo_provider import PhylloConfig, PhylloProvider
            
            phyllo_config = PhylloConfig()
            phyllo_provider = PhylloProvider(phyllo_config)
            self._providers[APIProviderType.PHYLLO] = phyllo_provider
            
            self.logger.info(f"Initialized external API providers: {list(self._providers.keys())}")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize API providers: {str(e)}")
            raise CreatorVerseError(f"API provider initialization failed: {str(e)}")
    
    def get_provider(self, provider_type: Optional[APIProviderType] = None) -> ExternalAPIProvider:
        """Get an API provider instance"""
        provider_type = provider_type or self._default_provider
        
        if provider_type not in self._providers:
            raise CreatorVerseError(f"API provider not available: {provider_type}")
        
        return self._providers[provider_type]
    
    def get_available_providers(self) -> List[APIProviderType]:
        """Get list of available providers"""
        return list(self._providers.keys())
    
    def supports_capability(
        self, 
        capability: APIProviderCapability,
        provider_type: Optional[APIProviderType] = None
    ) -> bool:
        """Check if provider supports a capability"""
        try:
            provider = self.get_provider(provider_type)
            return provider.supports_capability(capability)
        except:
            return False
    
    @with_trace_id
    async def search_creators(
        self,
        search_request: FrontendSearchRequest,
        provider_type: Optional[APIProviderType] = None,
        **kwargs
    ) -> FrontendSearchResponse:
        """
        Search creators using external API providers
        
        Args:
            search_request: Frontend search request with filters
            provider_type: Specific provider to use (optional)
            
        Returns:
            Standardized search response
        """
        start_time = datetime.utcnow()
        
        try:
            # Validate request
            self._validate_search_request(search_request)
            
            # Get provider
            provider = self.get_provider(provider_type)
            
            # Check capability
            if not provider.supports_capability(APIProviderCapability.CREATOR_SEARCH):
                raise CreatorVerseError(f"Provider {provider.get_provider_type()} does not support creator search")
            
            # Execute search
            creators, api_metadata = await provider.search_creators(
                search_request.filterSelections,
                **kwargs
            )
            
            # Calculate execution time
            execution_time_ms = int((datetime.utcnow() - start_time).total_seconds() * 1000)
            
            # Create standardized metadata
            metadata = SearchMetadata(
                total_count=api_metadata.get("total_count", len(creators)),
                page=search_request.filterSelections.page or 1,
                page_size=search_request.filterSelections.pageSize or 20,
                total_pages=self._calculate_total_pages(
                    api_metadata.get("total_count", len(creators)),
                    search_request.filterSelections.pageSize or 20
                ),
                execution_time_ms=execution_time_ms,
                cache_hit=api_metadata.get("cache_hit", False),
                external_api_calls=1,
                data_sources=[api_metadata.get("api_provider", str(provider.get_provider_type()))]
            )
            
            # Create response
            response = FrontendSearchResponse(
                profiles=creators,
                metadata=metadata,
                applied_filters=search_request.filterSelections.filters
            )
            
            self.logger.info(
                f"External search completed: {len(creators)} creators found "
                f"using {provider.get_provider_type()} in {execution_time_ms}ms"
            )
            
            return response
            
        except Exception as e:
            if isinstance(e, (CreatorVerseError, FilterValidationError)):
                raise
            self.logger.error(f"External search failed: {str(e)}")
            raise CreatorVerseError(f"External API search failed: {str(e)}")
    
    @with_trace_id
    async def quick_search(
        self,
        query: str,
        platform: PlatformEnum,
        limit: int = 10,
        provider_type: Optional[APIProviderType] = None,
        **kwargs
    ) -> List[CreatorProfile]:
        """
        Quick search for creators by name/username
        
        Args:
            query: Search query
            platform: Platform to search on
            limit: Maximum results
            provider_type: Specific provider to use (optional)
            
        Returns:
            List of creator profiles
        """
        try:
            # Validate inputs
            if not query or not query.strip():
                raise FilterValidationError("Search query cannot be empty")
            
            # Get provider
            provider = self.get_provider(provider_type)
            
            # Check capability
            if not provider.supports_capability(APIProviderCapability.QUICK_SEARCH):
                raise CreatorVerseError(f"Provider {provider.get_provider_type()} does not support quick search")
            
            # Execute search
            creators = await provider.quick_search(query, platform, limit, **kwargs)
            
            self.logger.info(
                f"Quick search completed: {len(creators)} creators found "
                f"for query '{query}' using {provider.get_provider_type()}"
            )
            
            return creators
            
        except Exception as e:
            if isinstance(e, (CreatorVerseError, FilterValidationError)):
                raise
            self.logger.error(f"Quick search failed: {str(e)}")
            raise CreatorVerseError(f"Quick search failed: {str(e)}")
    
    @with_trace_id
    async def get_creator_analytics(
        self,
        creator_id: str,
        platform: PlatformEnum,
        provider_type: Optional[APIProviderType] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Get detailed creator analytics
        
        Args:
            creator_id: Creator's external ID
            platform: Platform
            provider_type: Specific provider to use (optional)
            
        Returns:
            Creator analytics data
        """
        try:
            # Get provider
            provider = self.get_provider(provider_type)
            
            # Check capability
            if not provider.supports_capability(APIProviderCapability.CREATOR_ANALYTICS):
                raise CreatorVerseError(f"Provider {provider.get_provider_type()} does not support creator analytics")
            
            # Get analytics
            analytics = await provider.get_creator_analytics(creator_id, platform, **kwargs)
            
            self.logger.info(f"Retrieved analytics for creator {creator_id} using {provider.get_provider_type()}")
            return analytics
            
        except Exception as e:
            if isinstance(e, CreatorVerseError):
                raise
            self.logger.error(f"Analytics retrieval failed: {str(e)}")
            raise CreatorVerseError(f"Failed to get creator analytics: {str(e)}")
    
    @with_trace_id
    async def get_audience_insights(
        self,
        creator_id: str,
        platform: PlatformEnum,
        provider_type: Optional[APIProviderType] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Get audience insights for a creator
        
        Args:
            creator_id: Creator's external ID
            platform: Platform
            provider_type: Specific provider to use (optional)
            
        Returns:
            Audience insights data
        """
        try:
            # Get provider
            provider = self.get_provider(provider_type)
            
            # Check capability
            if not provider.supports_capability(APIProviderCapability.AUDIENCE_INSIGHTS):
                raise CreatorVerseError(f"Provider {provider.get_provider_type()} does not support audience insights")
            
            # Get insights
            insights = await provider.get_audience_insights(creator_id, platform, **kwargs)
            
            self.logger.info(f"Retrieved audience insights for creator {creator_id} using {provider.get_provider_type()}")
            return insights
            
        except Exception as e:
            if isinstance(e, CreatorVerseError):
                raise
            self.logger.error(f"Audience insights retrieval failed: {str(e)}")
            raise CreatorVerseError(f"Failed to get audience insights: {str(e)}")
    
    @with_trace_id
    async def multi_provider_search(
        self,
        search_request: FrontendSearchRequest,
        providers: List[APIProviderType],
        **kwargs
    ) -> Dict[APIProviderType, FrontendSearchResponse]:
        """
        Search across multiple providers and return combined results
        
        Args:
            search_request: Frontend search request
            providers: List of providers to query
            
        Returns:
            Dictionary mapping provider to their results
        """
        results = {}
        
        for provider_type in providers:
            try:
                if provider_type in self._providers:
                    result = await self.search_creators(search_request, provider_type, **kwargs)
                    results[provider_type] = result
                else:
                    self.logger.warning(f"Provider not available: {provider_type}")
            except Exception as e:
                self.logger.error(f"Search failed for provider {provider_type}: {str(e)}")
                # Continue with other providers
                continue
        
        return results
    
    def _validate_search_request(self, request: FrontendSearchRequest):
        """Validate search request"""
        if not request.filterSelections:
            raise FilterValidationError("Filter selections are required")
        
        if not request.filterSelections.channel:
            raise FilterValidationError("Platform channel is required")
        
        if not request.filterSelections.optionFor:
            raise FilterValidationError("Option target (creator/audience) is required")
        
        # Validate pagination
        if request.filterSelections.page and request.filterSelections.page < 1:
            raise FilterValidationError("Page number must be greater than 0")
        
        if request.filterSelections.pageSize and (request.filterSelections.pageSize < 1 or request.filterSelections.pageSize > 100):
            raise FilterValidationError("Page size must be between 1 and 100")
    
    def _calculate_total_pages(self, total_count: int, page_size: int) -> int:
        """Calculate total pages"""
        if page_size <= 0:
            return 0
        return (total_count + page_size - 1) // page_size
    
    def _merge_creator_profiles(self, profiles_list: List[List[CreatorProfile]]) -> List[CreatorProfile]:
        """Merge creator profiles from multiple providers, removing duplicates"""
        seen_external_ids = set()
        seen_usernames = set()
        merged_profiles = []
        
        for profiles in profiles_list:
            for profile in profiles:
                # Use external_id first, then username as fallback for deduplication
                identifier = profile.external_id or f"{profile.platform}:{profile.platform_username}"
                
                if identifier not in seen_external_ids and profile.platform_username not in seen_usernames:
                    merged_profiles.append(profile)
                    seen_external_ids.add(identifier)
                    seen_usernames.add(profile.platform_username)
        
        return merged_profiles
    
    async def health_check(self) -> Dict[str, Any]:
        """Check health of all providers"""
        health_status = {
            "service": "external_api",
            "status": "healthy",
            "providers": {}
        }
        
        for provider_type, provider in self._providers.items():
            try:
                # Basic provider check
                capabilities = provider.config.capabilities
                provider_status = {
                    "status": "healthy",
                    "capabilities": [cap.value for cap in capabilities],
                    "provider_type": provider_type.value
                }
            except Exception as e:
                provider_status = {
                    "status": "unhealthy",
                    "error": str(e),
                    "provider_type": provider_type.value
                }
                health_status["status"] = "degraded"
            
            health_status["providers"][provider_type.value] = provider_status
        
        return health_status


# Global service instance
external_api_service = ExternalAPIService()
