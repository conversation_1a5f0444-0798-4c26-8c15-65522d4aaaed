from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from .routers import router as phyllo_router
from .phyllo_api import phyllo_router as phyllo_api_router
from .database import create_initial_data
from .config import settings
from .database_connection import check_database_health

app = FastAPI(
    title="Phyllo Dummy API",
    description="A basic FastAPI application for demonstrating Phyllo-like functionality",
    version="0.1.0",
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, replace with actual frontend domains
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize the database with sample data
create_initial_data()

# Include routers
app.include_router(phyllo_router, tags=["Phyllo API"])
app.include_router(phyllo_api_router, tags=["Phyllo Compatible API"])

# Add additional router for the exact URL pattern you were using
from fastapi import APIRouter
from .phyllo_api import PhylloAdvancedSearchRequest, PhylloQuickSearchRequest, advanced_search_profiles, quick_search_profiles

# Router for /v1/social/creator-profile/* endpoints (without the extra /creator prefix)
direct_phyllo_router = APIRouter(prefix="/v1/social/creator-profile")

@direct_phyllo_router.post("/search")
async def direct_creator_profile_search(request: PhylloAdvancedSearchRequest):
    """Direct endpoint for /v1/social/creator-profile/search"""
    return await advanced_search_profiles(request)

@direct_phyllo_router.post("/quick-search")
async def direct_creator_profile_quick_search(request: PhylloQuickSearchRequest):
    """Direct endpoint for /v1/social/creator-profile/quick-search"""
    return await quick_search_profiles(request)

app.include_router(direct_phyllo_router, tags=["Direct Phyllo API"])


@app.get("/", tags=["Root"])
async def root():
    return {"message": "Welcome to Phyllo Dummy API!"}


@app.get("/health", tags=["Health"])
async def health_check():
    return {"status": "healthy"}


@app.get("/health/database", tags=["Health"])
async def database_health_check():
    """Check database connectivity and status"""
    return check_database_health()
