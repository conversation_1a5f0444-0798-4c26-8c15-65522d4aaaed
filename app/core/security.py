"""
Security utilities for CreatorVerse Backend.
"""
import hashlib
import secrets
from datetime import datetime, timedelta, UTC
from typing import Dict, Any, Optional, List, Set
from uuid import UUID

import jwt
from fastapi import HTTPEx<PERSON>, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from fastapi import Depends, Request

from app.core.config import APP_CONFIG
from app.core.logger import get_logger
from app.core.redis_keys import RedisKeys

logger = get_logger()
security = HTTPBearer()


def hash_otp(otp: str) -> str:
    """
    Hash OTP using SHA-256 for secure storage.
    
    Args:
        otp: Plain text OTP to hash
        
    Returns:
        Hashed OTP string
    """
    if not otp:
        raise ValueError("OTP cannot be empty")

    # Use SHA-256 for OTP hashing (simple but secure for OTPs)
    return hashlib.sha256(otp.encode('utf-8')).hexdigest()


def verify_otp_hash(plain_otp: str, hashed_otp: str) -> bool:
    """
    Verify if plain OTP matches the hashed OTP.
    
    Args:
        plain_otp: Plain text OTP provided by user
        hashed_otp: Stored hashed OTP
        
    Returns:
        True if OTP matches, False otherwise
    """
    if not plain_otp or not hashed_otp:
        return False

    return hash_otp(plain_otp) == hashed_otp


def generate_secure_token(length: int = 32) -> str:
    """
    Generate a cryptographically secure random token.
    
    Args:
        length: Length of the token in bytes
        
    Returns:
        Secure random token as hex string
    """
    return secrets.token_hex(length)


def create_access_token(data: Dict[str, Any], expires_delta: Optional[timedelta] = None, include_permissions: bool = True) -> str:
    """
    Create a JWT access token with optional permission encoding.
    
    Args:
        data: Data to include in the token (must contain 'sub' for user_id)
        expires_delta: Optional token expiration time
        include_permissions: Whether to include user permissions in token
        
    Returns:
        Encoded JWT token
    """
    to_encode = data.copy()

    if expires_delta:
        expire = datetime.now(UTC) + expires_delta
    else:
        expire = datetime.now(UTC) + timedelta(minutes=15)  # Default 15 minutes

    to_encode.update({
        "exp": expire,
        "iat": datetime.now(UTC),
        "type": "bearer"
    })

    # Include permissions in token if requested and user_id is available
    if include_permissions and "sub" in data:
        user_id = data["sub"]
        # Note: Permissions will be added by the calling function that has access to RBAC service
        # This is done to keep the security module independent of RBAC service
        logger.debug("Access token created with permission encoding enabled", extra={"user_id": user_id})

    encoded_jwt = jwt.encode(to_encode, APP_CONFIG.secret_key, algorithm=APP_CONFIG.jwt_algorithm)

    return encoded_jwt


def create_refresh_token(data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
    """
    Create a short, secure refresh token using URL-safe base64 encoding.
    This is more secure and efficient than using JWT for refresh tokens.
    
    Args:
        data: Data dictionary (used only for compatibility, not included in token)
        expires_delta: Optional token expiration time (used only in the calling context)
        
    Returns:
        A secure random URL-safe token string
    """
    # Generate a random token that is URL-safe and shorter than JWT
    # Using token_urlsafe which produces base64-encoded bytes
    return secrets.token_urlsafe(32)


def get_token_expire_minutes() -> int:
    """Get token expiration time in minutes from config"""
    return APP_CONFIG.access_token_expire_minutes


def get_refresh_token_expire_days() -> int:
    """Get refresh token expiration time in days from config"""
    return APP_CONFIG.refresh_token_expire_days


def verify_jwt_token(token: str) -> Dict[str, Any]:
    """
    Verify and decode JWT token.
    
    Args:
        token: JWT token string
        
    Returns:
        Decoded token payload
        
    Raises:
        HTTPException: If token is invalid or expired
    """
    try:
        payload = jwt.decode(
            token, 
            APP_CONFIG.secret_key, 
            algorithms=[APP_CONFIG.jwt_algorithm]
        )
        
        # Check if token is expired
        exp = payload.get("exp")
        if exp and datetime.fromtimestamp(exp, UTC) < datetime.now(UTC):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token has expired"
            )
            
        return payload
        
    except jwt.ExpiredSignatureError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token has expired"
        )
    except jwt.InvalidTokenError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token"
        )


async def get_current_user_from_token(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    redis_client = None,
    db_conn = None
) -> Dict[str, Any]:
    """
    Extract current user information from JWT token.
    
    Args:
        credentials: HTTP authorization credentials
        redis_client: Redis client for session validation
        db_conn: Database connection for user data
        
    Returns:
        User information dictionary
        
    Raises:
        HTTPException: If token is invalid or user not found
    """
    token = credentials.credentials
    payload = verify_jwt_token(token)
    
    user_id = payload.get("sub")
    session_id = payload.get("sid")
    
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token payload"
        )
    
    # Validate session exists and is not revoked
    if redis_client and session_id:
        try:
            session_key = RedisKeys.user_session(user_id, session_id)
            session_data = await redis_client.hgetall(session_key)
            
            if not session_data:
                # Session not found in cache, check database if available
                if db_conn:
                    from app.services.session_service import validate_session_in_db
                    is_valid = await validate_session_in_db(db_conn, user_id, session_id)
                    if not is_valid:
                        raise HTTPException(
                            status_code=status.HTTP_401_UNAUTHORIZED,
                            detail="Session has been revoked"
                        )
                else:
                    raise HTTPException(
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        detail="Session not found"
                    )
        except Exception as e:
            logger.warning("Session validation failed", extra={"error": str(e)})
            # Continue without session validation if Redis fails
    
    return {
        "user_id": user_id,
        "session_id": session_id,
        "token_payload": payload
    }


async def get_current_user_with_permissions(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    redis_client = None,
    db_conn = None,
    rbac_service = None
) -> Dict[str, Any]:
    """
    Enhanced version of get_current_user_from_token that extracts permissions from JWT token.
    
    Args:
        credentials: HTTP authorization credentials
        redis_client: Redis client for session validation
        db_conn: Database connection for user data
        rbac_service: RBAC service for fresh permission lookup if token is stale
        
    Returns:
        User information dictionary with permissions included
        
    Raises:
        HTTPException: If token is invalid or user not found
    """
    token = credentials.credentials
    payload = verify_jwt_token(token)
    
    user_id = payload.get("sub")
    session_id = payload.get("sid")
    
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token payload"
        )
    
    # Validate session exists and is not revoked
    if redis_client and session_id:
        try:
            session_key = RedisKeys.user_session(user_id, session_id)
            session_data = await redis_client.hgetall(session_key)
            
            if not session_data:
                # Session not found in cache, check database if available
                if db_conn:
                    from app.services.session_service import validate_session_in_db
                    is_valid = await validate_session_in_db(db_conn, user_id, session_id)
                    if not is_valid:
                        raise HTTPException(
                            status_code=status.HTTP_401_UNAUTHORIZED,
                            detail="Session has been revoked"
                        )
                else:
                    raise HTTPException(
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        detail="Session not found"
                    )
        except Exception as e:
            logger.warning("Session validation failed", extra={"error": str(e)})
            # Continue without session validation if Redis fails
    
    # Extract permissions from token
    token_permissions = extract_permissions_from_token(payload)
    
    # Check if permissions in token are stale and need refresh
    permissions = token_permissions["permissions"]
    roles = token_permissions["roles"]
    
    if rbac_service and is_token_permissions_stale(payload, max_age_minutes=30):
        logger.debug("Token permissions are stale, fetching fresh permissions", extra={
            "user_id": user_id,
            "token_perms_count": len(permissions)
        })
        
        try:
            # Get fresh permissions from RBAC service
            fresh_permissions = await get_user_permissions(user_id, rbac_service)
            if fresh_permissions:
                permissions = fresh_permissions
                logger.debug("Updated with fresh permissions", extra={
                    "user_id": user_id,
                    "fresh_perms_count": len(fresh_permissions)
                })
        except Exception as e:
            logger.warning("Failed to fetch fresh permissions, using token permissions", extra={
                "user_id": user_id,
                "error": str(e)
            })
            # Continue with token permissions if RBAC service fails
    
    return {
        "user_id": user_id,
        "session_id": session_id,
        "token_payload": payload,
        "permissions": permissions,
        "roles": roles,
        "permissions_timestamp": token_permissions["permissions_timestamp"]
    }


async def get_user_permissions(
    user_id: str,
    rbac_service = None
) -> Set[str]:
    """
    Get user permissions from RBAC service.
    
    Args:
        user_id: User ID
        rbac_service: RBAC service instance
        
    Returns:
        Set of permission keys
    """
    if not rbac_service:
        return set()
    
    try:
        # Get user roles
        user_roles = await rbac_service.get_user_roles(UUID(user_id))
        
        # Aggregate permissions from all roles
        permissions: Set[str] = set()
        for role_id in user_roles:
            role_permissions = await rbac_service.get_role_permissions(UUID(role_id))
            permissions.update(role_permissions)
            
        return permissions
    except Exception as e:
        logger.error("Failed to get user permissions", extra={
            "user_id": user_id,
            "error": str(e)
        })
        return set()


def require_permissions(required_permissions: List[str]):
    """
    Decorator factory for requiring specific permissions.
    
    Args:
        required_permissions: List of required permission keys
        
    Returns:
        Dependency function for FastAPI
    """
    async def permission_dependency(
        current_user: Dict[str, Any] = Depends(get_current_user_from_token),
        request: Request = None
    ) -> Dict[str, Any]:
        """
        Check if current user has required permissions.
        
        Args:
            current_user: Current user from JWT token
            request: FastAPI request object
            
        Returns:
            Current user data if authorized
            
        Raises:
            HTTPException: If user lacks required permissions
        """
        if not required_permissions:
            return current_user
            
        # Get RBAC service and Redis client from request state if available
        rbac_service = getattr(request.state, 'rbac_service', None) if request else None
        
        user_id = current_user["user_id"]
        
        # Try to get permissions from token first (faster)
        token_permissions = current_user.get("permissions", set())
        
        # Check if all required permissions are in token
        if token_permissions and all(perm in token_permissions for perm in required_permissions):
            logger.debug("Permission check passed using token permissions", extra={
                "user_id": user_id,
                "required": required_permissions,
                "source": "jwt_token"
            })
            return current_user
        
        # Fallback to RBAC service for permission check
        if not rbac_service:
            # If no RBAC service and no token permissions, check if token has permissions at all
            if token_permissions:
                # Token has some permissions but not the required ones
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Insufficient permissions. Required: {', '.join(required_permissions)}"
                )
            
            logger.warning("RBAC service not available for permission check")
            # Allow access if RBAC service is not available and no token permissions (fallback)
            return current_user
        
        # Check each required permission using RBAC service
        for permission in required_permissions:
            has_permission = await rbac_service.check_user_permission(
                UUID(user_id), 
                permission
            )
            
            if not has_permission:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Insufficient permissions. Required: {permission}"
                )
        
        logger.debug("Permission check passed using RBAC service", extra={
            "user_id": user_id,
            "required": required_permissions,
            "source": "rbac_service"
        })
                
        return current_user
    
    return permission_dependency


def require_any_permission(required_permissions: List[str]):
    """
    Decorator factory for requiring any of the specified permissions.
    
    Args:
        required_permissions: List of permission keys (user needs ANY one)
        
    Returns:
        Dependency function for FastAPI
    """
    async def permission_dependency(
        current_user: Dict[str, Any] = Depends(get_current_user_from_token),
        request: Request = None
    ) -> Dict[str, Any]:
        """
        Check if current user has any of the required permissions.
        
        Args:
            current_user: Current user from JWT token
            request: FastAPI request object
            
        Returns:
            Current user data if authorized
            
        Raises:
            HTTPException: If user lacks all required permissions
        """
        if not required_permissions:
            return current_user
            
        user_id = current_user["user_id"]
        
        # Try to get permissions from token first (faster)
        token_permissions = current_user.get("permissions", set())
        
        # Check if any required permission is in token
        if token_permissions and any(perm in token_permissions for perm in required_permissions):
            logger.debug("Permission check passed using token permissions", extra={
                "user_id": user_id,
                "required_any": required_permissions,
                "source": "jwt_token"
            })
            return current_user
        
        # Get RBAC service from request state if available
        rbac_service = getattr(request.state, 'rbac_service', None) if request else None
        
        if not rbac_service:
            # If no RBAC service and no token permissions, check if token has permissions at all
            if token_permissions:
                # Token has some permissions but not any of the required ones
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Insufficient permissions. Required one of: {', '.join(required_permissions)}"
                )
            
            logger.warning("RBAC service not available for permission check")
            # Allow access if RBAC service is not available and no token permissions (fallback)
            return current_user
        
        # Check if user has any of the required permissions using RBAC service
        permission_results = await rbac_service.batch_check_permissions(
            UUID(user_id), 
            required_permissions
        )
        
        if not any(permission_results.values()):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Insufficient permissions. Required one of: {', '.join(required_permissions)}"
            )
        
        logger.debug("Permission check passed using RBAC service", extra={
            "user_id": user_id,
            "required_any": required_permissions,
            "source": "rbac_service"
        })
                
        return current_user
    
    return permission_dependency


async def check_token_blacklist(token: str, redis_client) -> bool:
    """
    Check if a token is blacklisted.
    
    Args:
        token: JWT token to check
        redis_client: Redis client
        
    Returns:
        True if token is blacklisted, False otherwise
    """
    if not redis_client:
        return False
        
    try:
        blacklist_key = f"CreatorVerse:blacklist:token:{hash(token)}"
        is_blacklisted = await redis_client.exists(blacklist_key)
        return bool(is_blacklisted)
    except Exception as e:
        logger.error("Failed to check token blacklist", extra={"error": str(e)})
        return False


async def create_access_token_with_permissions(
    user_id: str,
    session_id: str,
    rbac_service = None,
    expires_delta: Optional[timedelta] = None
) -> str:
    """
    Create a JWT access token with user permissions and roles encoded.
    
    Args:
        user_id: User ID
        session_id: Session ID
        rbac_service: RBAC service instance for fetching permissions
        expires_delta: Optional token expiration time
        
    Returns:
        Encoded JWT token with permissions
    """
    try:
        # Base token data
        token_data = {
            "sub": user_id,
            "sid": session_id
        }
        
        # Fetch user permissions and roles if RBAC service is available
        if rbac_service:
            try:
                # Get user roles
                user_roles = await rbac_service.get_user_roles(UUID(user_id))
                
                # Get user permissions (aggregate from all roles)
                user_permissions: Set[str] = set()
                role_names: List[str] = []
                
                for role_id in user_roles:
                    # Get role permissions
                    role_perms = await rbac_service.get_role_permissions(UUID(role_id))
                    user_permissions.update(role_perms)
                    
                    # Get role name for better readability
                    role_name = await rbac_service.get_role_name_by_id(UUID(role_id))
                    if role_name:
                        role_names.append(role_name)
                
                # Convert permission IDs to permission keys
                permission_keys = set()
                if user_permissions:
                    # Use forward lookup to get permission keys
                    try:
                        from app.core.redis_keys import RedisKeys
                        forward_key = RedisKeys.rbac_permissions()
                        all_perms = await rbac_service.redis_client.hgetall(forward_key)
                        
                        # Map permission IDs to keys
                        for perm_id in user_permissions:
                            perm_key = all_perms.get(perm_id)
                            if perm_key:
                                permission_keys.add(perm_key)
                    except Exception as perm_err:
                        logger.warning("Failed to resolve permission keys", extra={"error": str(perm_err)})
                
                # Add permissions and roles to token (keep it concise to avoid token bloat)
                if permission_keys:
                    # Limit permissions to prevent token bloat (JWT has size limits)
                    token_data["perms"] = list(permission_keys)[:50]  # Limit to 50 most relevant permissions
                    
                if role_names:
                    token_data["roles"] = role_names
                    
                # Add a timestamp for permission data to handle staleness
                token_data["perms_ts"] = int(datetime.now(UTC).timestamp())
                
                logger.debug("Permissions encoded in JWT", extra={
                    "user_id": user_id,
                    "permission_count": len(permission_keys),
                    "role_count": len(role_names)
                })
                
            except Exception as rbac_err:
                logger.warning("Failed to fetch user permissions for JWT", extra={
                    "user_id": user_id,
                    "error": str(rbac_err)
                })
                # Continue without permissions - fallback to basic token
        
        # Create the token
        return create_access_token(token_data, expires_delta, include_permissions=False)
        
    except Exception as e:
        logger.error("Error creating access token with permissions", extra={
            "user_id": user_id,
            "error": str(e)
        })
        # Fallback to basic token without permissions
        return create_access_token({"sub": user_id, "sid": session_id}, expires_delta, include_permissions=False)


def extract_permissions_from_token(token_payload: Dict[str, Any]) -> Dict[str, Any]:
    """
    Extract permissions and roles from JWT token payload.
    
    Args:
        token_payload: Decoded JWT token payload
        
    Returns:
        Dictionary containing user permissions and metadata
    """
    return {
        "permissions": set(token_payload.get("perms", [])),
        "roles": token_payload.get("roles", []),
        "permissions_timestamp": token_payload.get("perms_ts"),
        "user_id": token_payload.get("sub"),
        "session_id": token_payload.get("sid")
    }


def is_token_permissions_stale(token_payload: Dict[str, Any], max_age_minutes: int = 30) -> bool:
    """
    Check if the permissions in the token are stale and need refresh.
    
    Args:
        token_payload: Decoded JWT token payload
        max_age_minutes: Maximum age of permissions in minutes
        
    Returns:
        True if permissions are stale and should be refreshed
    """
    perms_ts = token_payload.get("perms_ts")
    if not perms_ts:
        return True  # No timestamp means very old token
        
    try:
        token_time = datetime.fromtimestamp(perms_ts, UTC)
        now = datetime.now(UTC)
        age_minutes = (now - token_time).total_seconds() / 60
        
        return age_minutes > max_age_minutes
    except (ValueError, TypeError):
        return True  # Invalid timestamp, consider stale


async def blacklist_token(token: str, redis_client, expiry_seconds: int = None) -> bool:
    """
    Add a token to the blacklist.
    
    Args:
        token: JWT token to blacklist
        redis_client: Redis client
        expiry_seconds: TTL for blacklist entry (default: token expiry)
        
    Returns:
        True if successfully blacklisted, False otherwise
    """
    if not redis_client:
        return False
        
    try:
        # Extract expiry from token if not provided
        if not expiry_seconds:
            try:
                payload = jwt.decode(
                    token, 
                    APP_CONFIG.secret_key, 
                    algorithms=[APP_CONFIG.jwt_algorithm]
                )
                exp = payload.get("exp")
                if exp:
                    expiry_seconds = max(0, int(exp - datetime.now(UTC).timestamp()))
                else:
                    expiry_seconds = 3600  # Default 1 hour
            except jwt.InvalidTokenError:
                expiry_seconds = 3600  # Default 1 hour if token can't be decoded
                
        blacklist_key = f"CreatorVerse:blacklist:token:{hash(token)}"
        await redis_client.set(blacklist_key, "1", ex=expiry_seconds)
        return True
    except Exception as e:
        logger.error("Failed to blacklist token", extra={"error": str(e)})
        return False
