"""
Enhanced JWT authentication dependencies for CreatorVerse Backend.

This module provides improved JWT authentication dependencies that integrate with
RBAC services and Redis caching for efficient permission checking.
"""
from typing import Dict, Any, List, Optional
from uuid import UUID

from fastapi import Depends, HTTPException, Request, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials

from app.core.logger import get_logger
from app.core.security import (
    verify_jwt_token, 
    check_token_blacklist,
    get_user_permissions
)

logger = get_logger()
security = HTTPBearer()


async def get_current_user(
    request: Request,
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> Dict[str, Any]:
    """
    Extract current user information from <PERSON><PERSON><PERSON> token with enhanced validation.
    
    Args:
        request: FastAPI request object (with injected services)
        credentials: HTTP authorization credentials
        
    Returns:
        User information dictionary
        
    Raises:
        HTTPException: If token is invalid or user not found
    """
    token = credentials.credentials
    
    # Get services from request state
    redis_client = getattr(request.state, 'redis_client', None)
    db_conn = getattr(request.state, 'db_conn', None)
    
    # Check if token is blacklisted
    if redis_client and await check_token_blacklist(token, redis_client):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=\"Token has been revoked\"
        )
    
    # Verify and decode token
    payload = verify_jwt_token(token)
    
    user_id = payload.get(\"sub\")
    session_id = payload.get(\"sid\")
    
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=\"Invalid token payload\"
        )
    
    # Validate session exists and is not revoked
    if redis_client and session_id:
        try:
            from app.core.redis_keys import RedisKeys
            session_key = RedisKeys.user_session(user_id, session_id)
            session_data = await redis_client.hgetall(session_key)
            
            if not session_data:
                # Session not found in cache, check database if available
                if db_conn:
                    from app.services.session_service import validate_session_in_db
                    is_valid = await validate_session_in_db(db_conn, user_id, session_id)
                    if not is_valid:
                        raise HTTPException(
                            status_code=status.HTTP_401_UNAUTHORIZED,
                            detail=\"Session has been revoked\"
                        )
                else:
                    raise HTTPException(
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        detail=\"Session not found\"
                    )
        except HTTPException:
            raise
        except Exception as e:
            logger.warning(\"Session validation failed\", extra={\"error\": str(e)})
            # Continue without session validation if Redis fails
    
    return {
        \"user_id\": user_id,
        \"session_id\": session_id,
        \"token_payload\": payload
    }


async def get_current_user_with_permissions(
    request: Request,
    current_user: Dict[str, Any] = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Get current user with their permissions loaded.
    
    Args:
        request: FastAPI request object
        current_user: Current user from JWT token
        
    Returns:
        User data with permissions
    """
    rbac_service = getattr(request.state, 'rbac_service', None)
    
    if rbac_service:
        user_permissions = await get_user_permissions(
            current_user[\"user_id\"], 
            rbac_service
        )
        current_user[\"permissions\"] = user_permissions
    else:
        current_user[\"permissions\"] = set()
        logger.warning(\"RBAC service not available for user permissions\")
    
    return current_user


def require_permissions(required_permissions: List[str]):
    \"\"\"
    Dependency factory for requiring specific permissions.
    
    Args:
        required_permissions: List of required permission keys
        
    Returns:
        Dependency function for FastAPI
    \"\"\"
    async def permission_dependency(
        request: Request,
        current_user: Dict[str, Any] = Depends(get_current_user)
    ) -> Dict[str, Any]:
        \"\"\"
        Check if current user has required permissions.
        
        Args:
            request: FastAPI request object
            current_user: Current user from JWT token
            
        Returns:
            Current user data if authorized
            
        Raises:
            HTTPException: If user lacks required permissions
        \"\"\"
        if not required_permissions:
            return current_user
            
        rbac_service = getattr(request.state, 'rbac_service', None)
        
        if not rbac_service:
            logger.warning(\"RBAC service not available for permission check\")
            # Allow access if RBAC service is not available (fallback)
            return current_user
            
        user_id = current_user[\"user_id\"]
        
        # Use batch permission check for efficiency
        permission_results = await rbac_service.batch_check_permissions(
            UUID(user_id), 
            required_permissions
        )
        
        # Check if user has all required permissions
        missing_permissions = [
            perm for perm, has_perm in permission_results.items() 
            if not has_perm
        ]
        
        if missing_permissions:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f\"Insufficient permissions. Missing: {', '.join(missing_permissions)}\"
            )
                
        return current_user
    
    return permission_dependency


def require_any_permission(required_permissions: List[str]):
    \"\"\"
    Dependency factory for requiring any of the specified permissions.
    
    Args:
        required_permissions: List of permission keys (user needs ANY one)
        
    Returns:
        Dependency function for FastAPI
    \"\"\"
    async def permission_dependency(
        request: Request,
        current_user: Dict[str, Any] = Depends(get_current_user)
    ) -> Dict[str, Any]:
        \"\"\"
        Check if current user has any of the required permissions.
        
        Args:
            request: FastAPI request object
            current_user: Current user from JWT token
            
        Returns:
            Current user data if authorized
            
        Raises:
            HTTPException: If user lacks all required permissions
        \"\"\"
        if not required_permissions:
            return current_user
            
        rbac_service = getattr(request.state, 'rbac_service', None)
        
        if not rbac_service:
            logger.warning(\"RBAC service not available for permission check\")
            # Allow access if RBAC service is not available (fallback)
            return current_user
            
        user_id = current_user[\"user_id\"]
        
        # Check if user has any of the required permissions
        permission_results = await rbac_service.batch_check_permissions(
            UUID(user_id), 
            required_permissions
        )
        
        if not any(permission_results.values()):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f\"Insufficient permissions. Required one of: {', '.join(required_permissions)}\"
            )
                
        return current_user
    
    return permission_dependency


def require_role(required_roles: List[str]):
    \"\"\"
    Dependency factory for requiring specific roles.
    
    Args:
        required_roles: List of required role names
        
    Returns:
        Dependency function for FastAPI
    \"\"\"
    async def role_dependency(
        request: Request,
        current_user: Dict[str, Any] = Depends(get_current_user)
    ) -> Dict[str, Any]:
        \"\"\"
        Check if current user has required roles.
        
        Args:
            request: FastAPI request object
            current_user: Current user from JWT token
            
        Returns:
            Current user data if authorized
            
        Raises:
            HTTPException: If user lacks required roles
        \"\"\"
        if not required_roles:
            return current_user
            
        rbac_service = getattr(request.state, 'rbac_service', None)
        
        if not rbac_service:
            logger.warning(\"RBAC service not available for role check\")
            # Allow access if RBAC service is not available (fallback)
            return current_user
            
        user_id = current_user[\"user_id\"]
        
        try:
            # Get user roles
            user_role_ids = await rbac_service.get_user_roles(UUID(user_id))
            
            # Convert role IDs to role names for comparison
            user_role_names = set()
            for role_id in user_role_ids:
                role_name = await rbac_service.get_role_name_by_id(UUID(role_id))
                if role_name:
                    user_role_names.add(role_name)
            
            # Check if user has any of the required roles
            has_required_role = any(role in user_role_names for role in required_roles)
            
            if not has_required_role:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f\"Insufficient role. Required one of: {', '.join(required_roles)}\"
                )
                    
            return current_user
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(\"Error checking user roles\", extra={
                \"user_id\": user_id,
                \"error\": str(e)
            })
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=\"Error validating user roles\"
            )
    
    return role_dependency


# Convenience dependencies for common permission patterns
async def require_admin(
    request: Request,
    current_user: Dict[str, Any] = Depends(get_current_user)
) -> Dict[str, Any]:
    \"\"\"Dependency to require admin permissions.\"\"\"
    return await require_permissions([\"admin.all\"])(request, current_user)


async def require_user_management(
    request: Request,
    current_user: Dict[str, Any] = Depends(get_current_user)  
) -> Dict[str, Any]:
    \"\"\"Dependency to require user management permissions.\"\"\"
    return await require_any_permission([
        \"user.create\", \"user.update\", \"user.delete\", \"user.manage\"
    ])(request, current_user)


async def require_brand_access(
    request: Request,
    current_user: Dict[str, Any] = Depends(get_current_user)
) -> Dict[str, Any]:
    \"\"\"Dependency to require brand access permissions.\"\"\"
    return await require_any_permission([
        \"brand.read\", \"brand.write\", \"brand.manage\"
    ])(request, current_user)


async def require_influencer_access(
    request: Request,
    current_user: Dict[str, Any] = Depends(get_current_user)
) -> Dict[str, Any]:
    \"\"\"Dependency to require influencer access permissions.\"\"\"
    return await require_any_permission([
        \"influencer.read\", \"influencer.write\", \"influencer.manage\"
    ])(request, current_user)
