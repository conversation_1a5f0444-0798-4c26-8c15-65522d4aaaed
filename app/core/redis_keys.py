from uuid import UUID


class RedisKeys:
    """Centralized Redis keys for the CreatorVerse application."""
    
    @staticmethod
    def role_id_by_uuid(role_uuid: str) -> str:
        """
        Returns the Redis key for storing the role ID by UUID mapping.
        
        Args:
            role_uuid: The UUID of the role
            
        Returns:
            Redis key for role ID by UUID
        """
        return f"CreatorVerse:role:uuid:{role_uuid}"

    @staticmethod
    def auth_method_to_id(method_name: str) -> str:
        """
        Redis key for mapping authentication method names to their IDs.

        Usage: Cache mapping of auth methods for quick lookups
        Data Type: Hash
        TTL: 24 hours (MASTER_AUTH_METHODS_TTL)

        Sample Data:
        {
            "google_oauth": "auth-method-id-1",
            "github_oauth": "auth-method-id-2",
            "email_password": "auth-method-id-3"
        }
        """
        return f"CreatorVerse:auth_method_to_id:{method_name}"

    @staticmethod
    def otp_cooldown_key(identifier: str) -> str:
        return f"CreatorVerse:otp_cooldown:{identifier}"
        
    @staticmethod
    def user_role_key(user_id: str) -> str:
        """
        Redis key for storing user role information.

        Usage: Store user role for authentication checks
        Data Type: String
        TTL: 7 days
        """
        return f"CreatorVerse:user:role:{user_id}"

    @staticmethod
    def user_by_email(email: str) -> str:
        """
        Redis key for caching user data by email address.

        Usage: Cache user profile information for quick email-based lookups
        Data Type: Hash
        TTL: 7 days (USER_TTL)

        Actual Data Structure:
        {
            "id": "dd1cbca4-0891-474c-b74d-b329e49756a8",
            "email": "<EMAIL>",
            "phone_number": "",
            "status": "active",
            "is_active": "True",
            "is_email_verified": "True",
            "name": "Shoaib Sheikh"
        }        """
        return f"CreatorVerse:user:email:{email}"

    @staticmethod
    def welcome_email_sent(email: str) -> str:
        """
        Redis key for tracking if a welcome email has been sent to a user.

        Usage: Prevent duplicate welcome emails during registration
        Data Type: String (timestamp)
        TTL: 30 days

        Sample Data:
        "2025-01-27T10:30:00Z"
        """
        return f"CreatorVerse:welcome_email:sent:{email}"

    @staticmethod
    def get_email_bloom_filter_key() -> str:
        """
        Redis key for email bloom filter to check if email exists.

        Usage: Probabilistic data structure to quickly check if an email might exist in the system
        Data Type: String (bloom filter bits)
        TTL: No expiration (persistent)

        Note: This key returns the base key. The actual implementation uses:
        - CreatorVerse:bloom:email:bitmap (string) - Binary bloom filter data
        - CreatorVerse:bloom:email:metadata (hash) - Bloom filter configuration

        Actual Metadata Structure:
        {
            "capacity": "100000",
            "error_rate": "0.001",
            "bit_array_size": "1437758",
            "hash_count": "9",
            "element_count": "1"
        }
        """
        return "CreatorVerse:bloom:email"

    @staticmethod
    def master_auth_methods() -> str:
        """
        Redis key for caching master authentication methods configuration.

        Usage: Store available authentication methods for the application
        Data Type: Hash
        TTL: 24 hours (MASTER_AUTH_METHODS_TTL)

        Sample Data:
        {
            "google_oauth": "true",
            "github_oauth": "true",            "email_password": "true",
            "magic_link": "false"
        }
        """
        return "CreatorVerse:master:auth_methods"
        
    @staticmethod
    def organization_key(org_id: UUID | str) -> str:
        """
        Redis key for caching organization data by ID.

        Usage: Cache organization information by ID
        Data Type: Hash
        TTL: 3600 seconds (1 hour)

        Sample Data:
        {
            "id": "org-uuid",
            "name": "Example Corp",
            "domain": "example.com",
            "is_active": "true"
        }
        """
        return f"CreatorVerse:org:{org_id}"
        
    @staticmethod
    def organization_by_domain(domain: str) -> str:
        """
        Redis key for caching organization data by domain.

        Usage: Cache organization information for domain-based lookups
        Data Type: Hash or String (org_id)
        TTL: 1 hour (DOMAIN_ORG_TTL)

        Sample Data: "org-uuid" or full JSON
        """
        return f"CreatorVerse:organization:domain:{domain.lower()}"

    @staticmethod
    def organization_members(organization_id: str) -> str:
        """
        Redis key for caching organization members list.

        Usage: Cache list of users belonging to an organization for quick access
        Data Type: Set or List
        TTL: 30 minutes (ORGANIZATION_MEMBERS_TTL)

        Sample Data (Set):
        ["user-uuid-1", "user-uuid-2", "user-uuid-3"]

        Or as Hash with roles:
        {
            "user-uuid-1": "brand-admin",
            "user-uuid-2": "member",
            "user-uuid-3": "viewer"
        }
        """
        return f"CreatorVerse:organization:members:{organization_id}"  # OTP related keys

    @staticmethod
    def otp_key(email: str) -> str:
        """
        Redis key for storing OTP (One-Time Password) data.

        Usage: Store OTP codes and attempt counts for email verification
        Data Type: Hash
        TTL: 15 minutes (OTP_TTL)

        Sample Data:
        {
            "code": "123456",
            "attempts": "2",
            "created_at": "2025-01-01T00:00:00Z",
            "email": "<EMAIL>",
            "max_attempts": "5"
        }
        """
        return f"CreatorVerse:otp:{email}"

    @staticmethod
    def otp_mobile_key(mobile: str) -> str:
        """
        Redis key for storing OTP (One-Time Password) data for mobile numbers.

        Usage: Store OTP codes and attempt counts for mobile verification
        Data Type: Hash
        TTL: 15 minutes (OTP_TTL)

        Sample Data:
        {
            "code": "123456",
            "attempts": "2",
            "created_at": "2025-01-01T00:00:00Z",
            "mobile": "+1234567890",
            "max_attempts": "5"
        }
        """
        return f"CreatorVerse:otp:mobile:{mobile}"  # RBAC related keys

    @staticmethod
    def rbac_roles() -> str:
        """
        Redis key for caching all available roles in the system.

        """
        return "CreatorVerse:rbac:roles"

    @staticmethod
    def rbac_permissions() -> str:
        """
        Redis key for caching all available permissions in the system.

        Usage: Store master list of permissions for RBAC system
        Data Type: Hash
        TTL: 1 hour (RBAC_TTL)

        Sample Data:
        {
            "perm-uuid-1": "{'name': 'user.create', 'description': 'Create Users'}",
            "perm-uuid-2": "{'name': 'user.read', 'description': 'Read User Data'}",
            "perm-uuid-3": "{'name': 'org.manage', 'description': 'Manage Organization'}"
        }
        """
        return "CreatorVerse:rbac:permissions"

    @staticmethod
    def rbac_permissions_reverse() -> str:
        """
        Redis key for reverse lookup of permissions (permission_key -> permission_id).

        Usage: Store permission_key to permission_id mapping for efficient reverse lookup
        Data Type: Hash
        TTL: 1 hour (RBAC_TTL)

        Sample Data:
        {
            "user.create": "perm-uuid-1",
            "user.read": "perm-uuid-2",
            "org.manage": "perm-uuid-3"
        }
        """
        return "CreatorVerse:rbac:permissions_reverse"

    @staticmethod
    def rbac_user_roles(user_id: UUID | str) -> str:
        """
        Redis key for caching user's assigned roles.

        Usage: Store roles assigned to a specific user for quick authorization checks
        Data Type: Set
        TTL: 1 hour (RBAC_TTL)

        Sample Data (Set):
        {"role-uuid-1", "role-uuid-2"}
        """
        return f"CreatorVerse:rbac:user_roles:{user_id}"

    @staticmethod
    def rbac_role_permissions(role_id: UUID | str) -> str:
        """
        Redis key for caching permissions assigned to a specific role.

        Usage: Store permissions that belong to a role for quick role-based checks
        Data Type: Set
        TTL: 1 hour (RBAC_TTL)

        Sample Data:
        {"perm-uuid-1", "perm-uuid-2", "perm-uuid-3"}
        """
        return f"CreatorVerse:rbac:role_perms:{role_id}"

    @staticmethod
    def email_domain_mx(domain: str) -> str:
        """
        Redis key for caching email domain MX record validation results.

        Usage: Cache domain MX record validation for performance optimization
        Data Type: String ("true" or "false")
        TTL: 24 hours (EMAIL_DOMAIN_CACHE_TTL)

        Sample Data:
        "true" - Domain has valid MX records
        "false" - Domain has no valid MX records
        """
        return f"CreatorVerse:email:domain_mx:{domain}"

    @staticmethod
    def user_by_mobile(mobile: str) -> str:
        """
        Redis key for caching user data by mobile number.

        Usage: Cache user profile information for quick mobile-based lookups
        Data Type: Hash
        TTL: 7 days (USER_TTL)

        Actual Data Structure:
        {
            "id": "dd1cbca4-0891-474c-b74d-b329e49756a8",
            "phone_number": "+1234567890",
            "email": "",
            "status": "active",
            "is_active": "True",
            "is_mobile_verified": "True",
            "name": "John Doe"
        }
        """
        return f"CreatorVerse:user:mobile:{mobile}"

    @staticmethod
    def user_by_id(user_id: UUID | str) -> str:
        """
        Redis key for caching user data by user ID.

        Usage: Cache user profile information for quick ID-based lookups
        Data Type: Hash
        TTL: 7 days (USER_TTL)

        Sample Data Structure:
        {
            "id": "dd1cbca4-0891-474c-b74d-b329e49756a8",
            "email": "<EMAIL>",
            "name": "John Doe",
            "status": "active",
            "is_active": "True",
            "is_email_verified": "True"
        }
        """
        return f"CreatorVerse:user:id:{user_id}"

    @staticmethod
    def user_organizations(user_id: UUID | str) -> str:
        """
        Redis key for caching user's organization memberships.

        Usage: Cache list of organizations where user is a member
        Data Type: JSON String (list of organization dictionaries)
        TTL: 30 minutes (ORGANIZATION_BRAND_MEMBERS_TTL)

        Sample Data:
        [
            {
                "id": "org-uuid",
                "name": "Example Corp",
                "domain": "example.com",
                "role": "owner",
                "joined_at": "2025-01-01T00:00:00Z"
            }
        ]
        """
        return f"CreatorVerse:user:organizations:{user_id}"

    @staticmethod
    def user_brands(user_id: UUID | str) -> str:
        """
        Redis key for caching user's brand memberships.

        Usage: Cache list of brands where user is a member
        Data Type: JSON String (list of brand dictionaries)
        TTL: 30 minutes (ORGANIZATION_BRAND_MEMBERS_TTL)

        Sample Data:
        [
            {
                "id": "brand-uuid",
                "name": "Example Brand",
                "organization_id": "org-uuid",
                "role": "brand_owner",
                "joined_at": "2025-01-01T00:00:00Z"
            }
        ]
        """
        return f"CreatorVerse:user:brands:{user_id}"

    @staticmethod
    def user_auth_methods(user_id: UUID | str) -> str:
        """
        Redis key for caching user's enabled authentication methods.

        Usage: Cache list of authentication methods enabled for user
        Data Type: JSON String (list of auth method dictionaries)
        TTL: 7 days (USER_TTL)

        Sample Data:
        [
            {
                "id": "method-uuid",
                "method_key": "email_otp",
                "description": "Email OTP Authentication",
                "enabled_at": "2025-01-01T00:00:00Z"
            }
        ]
        """
        return f"CreatorVerse:user:auth_methods:{user_id}"
    
    @staticmethod
    def user_session(user_id: str ,session_id: str) -> str:
        """
        Redis key for storing user session data.

        Usage: Store active user sessions for authentication
        Data Type: Hash
        TTL: Based on refresh token expiry (typically 30 days)

        Sample Data:
        {
            "session_id": "5f9b3b1c-7890-4321-abcd-1234567890ab",
            "access_token": "eyJ0eXAiOiJKV...",
            "refresh_token": "eyJ0eXAiOiJKV...",
            "user_id": "dd1cbca4-0891-474c-b74d-b329e49756a8",
            "device_info": "Mozilla/5.0...",
            "ip_address": "***********",
            "issued_at": "2025-01-01T00:00:00Z",
            "expires_at": "2025-02-01T00:00:00Z"
        }
        
        Usage Pattern: 
        - For individual session: "user_id:session_id"
        - For user's sessions: "user_id:*" (retrievable via pattern matching)
        """
        return f"CreatorVerse:session:{user_id}:{session_id}"
        
    @staticmethod
    def oauth_state(state: str) -> str:
        """Redis key for storing OAuth state data."""
        return f"CreatorVerse:oauth:state:{state}"
    
    @staticmethod
    def oauth_token(provider: str, user_id: str) -> str:
        """Redis key for storing OAuth tokens."""
        return f"CreatorVerse:oauth:token:{provider}:{user_id}"

    @staticmethod
    def refresh_token_mapping(token: str) -> str:
        """
        Redis key for storing refresh token mapping data.

        Usage: Store metadata about refresh tokens for validation and user sessions
        Data Type: Hash
        TTL: Based on refresh token expiration (e.g., 30 days)

        Sample Data:
        {
            "user_id": "user-uuid",
            "session_id": "session-uuid", 
            "expires_at": "2025-01-31T00:00:00Z"
        }
        """
        return f"CreatorVerse:auth:refresh_token:{token}"

    @staticmethod
    def brand_key(brand_id: UUID | str) -> str:
        """
        Redis key for caching brand data by ID.

        Usage: Cache brand information by ID
        Data Type: Hash
        TTL: 3600 seconds (1 hour)

        Sample Data:
        {
            "id": "brand-uuid",
            "name": "Example Brand",
            "organization_id": "org-uuid",
            "is_active": "true"
        }
        """
        return f"CreatorVerse:brand:{brand_id}"

    @staticmethod
    def brand_members_list_key(brand_id: UUID | str, mode: str = "all") -> str:
        """
        Redis key for caching brand members list.

        Usage: Cache list of members belonging to a brand
        Data Type: Set or List
        TTL: 30 minutes (ORGANIZATION_BRAND_MEMBERS_TTL)

        Sample Data (List):
        ["user-uuid-1", "user-uuid-2", "user-uuid-3"]
        """
        return f"CreatorVerse:brand:members:{brand_id}:{mode}"

    @staticmethod
    def brand_member_status_key(brand_id: UUID | str, user_id: UUID | str) -> str:
        """
        Redis key for caching a member's status in a brand.

        Usage: Cache a user's membership status for a brand
        Data Type: Hash
        TTL: 30 minutes (ORGANIZATION_BRAND_MEMBERS_TTL)

        Sample Data:
        {
            "status": "active",
            "role": "brand_owner"
        }
        """
        return f"CreatorVerse:brand:mem:{brand_id}:{user_id}"

    @staticmethod
    def brands_by_org_key(org_id: UUID | str) -> str:
        """
        Redis key for caching brands list for an organization.

        Usage: Cache list of brands belonging to an organization
        Data Type: List
        TTL: 30 minutes (ORGANIZATION_BRAND_MEMBERS_TTL)

        Sample Data:
        ["brand-uuid-1", "brand-uuid-2"]
        """
        return f"CreatorVerse:org:{org_id}:brands"

    @staticmethod
    def org_member_status_key(org_id: UUID | str, user_id: UUID | str) -> str:
        """
        Redis key for caching a member's status in an organization.

        Usage: Cache a user's membership status for an organization
        Data Type: Hash
        TTL: 30 minutes (ORGANIZATION_BRAND_MEMBERS_TTL)

        Sample Data:
        {
            "is_active": "true",
            "role": "owner"
        }
        """
        return f"CreatorVerse:org:mem:{org_id}:{user_id}"

    @staticmethod
    def org_members_list_key(org_id: UUID | str) -> str:
        """
        Redis key for caching organization members list.

        Usage: Cache list of users belonging to an organization
        Data Type: Set or List
        TTL: 30 minutes (ORGANIZATION_BRAND_MEMBERS_TTL)

        Sample Data:
        ["user-uuid-1", "user-uuid-2", "user-uuid-3"]
        """
        return f"CreatorVerse:org:members:{org_id}"

    @staticmethod
    def org_by_user_key(user_id: UUID | str) -> str:
        """
        Redis key for caching organization ID for a user.

        Usage: Cache organization ID that a user belongs to
        Data Type: String
        TTL: 30 minutes (ORGANIZATION_BRAND_MEMBERS_TTL)

        Sample Data:
        "org-uuid"
        """
        return f"CreatorVerse:user:{user_id}:org"

    @staticmethod
    def brand_pending_requests_key(brand_id: UUID | str) -> str:
        """
        Redis key for caching pending join requests for a brand.

        Usage: Cache list of pending join requests for a brand
        Data Type: List
        TTL: 15 minutes (900 seconds)
        """
        return f"CreatorVerse:brand:pending_requests:{brand_id}"
        
    @staticmethod
    def user_pending_requests_key(user_id: UUID | str, org_id: UUID | str) -> str:
        """
        Redis key for caching aggregated pending join requests for a user across brands.

        Usage: Cache aggregated list of pending join requests for a user's organization or brands
        Data Type: JSON String
        TTL: 5 minutes (300 seconds)
        """
        return f"CreatorVerse:user:{user_id}:pending_requests:{org_id}"
    
    @staticmethod
    def sms_cooldown_key(phone_number: str) -> str:
        """
        Redis key for tracking SMS cooldown periods.
        
        Usage: Enforce cooldown between SMS sends to the same number
        Data Type: String (value is "1")
        TTL: Configured cooldown period (default 60s)
        """
        return f"CreatorVerse:sms:cooldown:{phone_number}"
    
    @staticmethod
    def sms_rate_limit_key(phone_number: str) -> str:
        """
        Redis key for tracking SMS rate limits.
        
        Usage: Count SMS sent to a number in hourly window
        Data Type: String (increment counter)
        TTL: 1 hour
        """
        return f"CreatorVerse:sms:rate_limit:{phone_number}"
    
    @staticmethod
    def sms_tracking_key(phone_number: str) -> str:
        """
        Redis key for tracking SMS send status.
        
        Usage: Track status of recent SMS sends
        Data Type: JSON string (serialized dict)
        TTL: 5 min for failed/pending, 1 hour for delivered
        
        Sample Data:
        {
            "status": "delivered",
            "timestamp": 1625097600,
            "message_type": "brand_otp"
        }
        """
        return f"CreatorVerse:sms:tracking:{phone_number}"


class RedisConfig:
    """Redis configuration constants for TTL values."""

    # RBAC cache TTL (1 hour)
    RBAC_TTL = 3600

    # User data TTL (7 days)
    USER_TTL = 604800
    ROLE_TTL = 3600  # Role cache TTL (1 hour)

    # OTP TTL (5 minutes)
    OTP_TTL = 300

    # Session TTL (24 hours)
    SESSION_TTL = 86400

    # Refresh token TTL (7 days)
    REFRESH_TOKEN_TTL = 604800

    # Email domain MX cache TTL (24 hours)
    EMAIL_DOMAIN_CACHE_TTL = 86400

    # Organization and brand member cache TTL (30 minutes)
    ORGANIZATION_BRAND_MEMBERS_TTL = 1800
    
    # Organization cache TTL (1 hour)
    ORGANIZATION_TTL = 3600
