from uuid import UUID
from typing import Union


class RedisKeys:
    """Centralized Redis keys for CreatorVerse Discovery Profile Analytics."""
    
    # ========== FILTER CATALOG & METADATA ==========
    
    @staticmethod
    def filter_groups_cache() -> str:
        """
        Redis key for caching filter groups configuration.
        
        Usage: Cache all filter groups for quick UI rendering
        Data Type: JSON String
        TTL: 24 hours (FILTER_METADATA_TTL)
        
        Sample Data:
        [
            {
                "id": "group-uuid-1",
                "name": "Demography & Identity",
                "option_for": "creator",
                "channel": "instagram",
                "sort_order": 1,
                "is_active": true
            }
        ]
        """
        return "CreatorVerse:Discovery:filter_groups"
    
    @staticmethod
    def filter_definitions_cache(group_id: str = None, channel: str = None) -> str:
        """
        Redis key for caching filter definitions.
        
        Usage: Cache filter definitions by group and/or channel
        Data Type: JSON String
        TTL: 24 hours (FILTER_METADATA_TTL)
        """
        key_parts = ["CreatorVerse", "Discovery", "filter_definitions"]
        if group_id:
            key_parts.append(f"group:{group_id}")
        if channel:
            key_parts.append(f"channel:{channel}")
        return ":".join(key_parts)
    
    @staticmethod
    def saved_filter_set(filter_set_id: str) -> str:
        """
        Redis key for caching saved filter sets.
        
        Usage: Cache saved filter combinations for quick retrieval
        Data Type: JSON String
        TTL: 1 hour (SAVED_FILTER_SET_TTL)
        """
        return f"CreatorVerse:Discovery:saved_filter_set:{filter_set_id}"
    
    @staticmethod
    def shared_filter_set(share_code: str) -> str:
        """
        Redis key for shared filter sets by share code.
        
        Usage: Cache shared filter sets for public access
        Data Type: JSON String
        TTL: 24 hours
        """
        return f"CreatorVerse:Discovery:shared_filter_set:{share_code}"
    
    @staticmethod
    def location_hierarchy_cache(tier: str = None) -> str:
        """
        Redis key for caching location hierarchy data.
        
        Usage: Cache location data by tier for quick filtering
        Data Type: JSON String
        TTL: 24 hours
        """
        if tier:
            return f"CreatorVerse:Discovery:location_hierarchy:tier:{tier}"
        return "CreatorVerse:Discovery:location_hierarchy"
    
    # ========== CREATOR DATA & PROFILES ==========
    
    @staticmethod
    def creator_profile(creator_id: str, platform: str) -> str:
        """
        Redis key for caching creator profile data.
        
        Usage: Cache complete creator profile from external APIs
        Data Type: JSON String
        TTL: 1 hour (CREATOR_DATA_TTL)
        """
        return f"CreatorVerse:Discovery:creator:{platform}:{creator_id}"
    
    @staticmethod
    def creator_audience_analytics(creator_id: str, platform: str) -> str:
        """
        Redis key for caching creator audience analytics.
        
        Usage: Cache audience demographics and analytics data
        Data Type: JSON String
        TTL: 30 minutes (AUDIENCE_DATA_TTL)
        """
        return f"CreatorVerse:Discovery:audience:{platform}:{creator_id}"
    
    @staticmethod
    def creator_search_results(filter_hash: str) -> str:
        """
        Redis key for caching creator search results by filter combination.
        
        Usage: Cache search results for specific filter combinations
        Data Type: JSON String
        TTL: 15 minutes (SEARCH_RESULTS_TTL)
        
        Sample Data:
        {
            "total_count": 1500,
            "creators": [{"id": "...", "platform": "instagram", ...}],
            "filters_applied": {...},
            "cached_at": "2025-01-01T00:00:00Z"
        }
        """
        return f"CreatorVerse:Discovery:search_results:{filter_hash}"
    
    @staticmethod
    def creator_analytics_summary(creator_id: str, platform: str) -> str:
        """
        Redis key for creator analytics summary (pricing, scores, etc).
        
        Usage: Cache calculated analytics and pricing info
        Data Type: JSON String
        TTL: 2 hours
        """
        return f"CreatorVerse:Discovery:analytics_summary:{platform}:{creator_id}"
    
    # ========== EXTERNAL API CACHE ==========
    
    @staticmethod
    def phyllo_creator_data(phyllo_user_id: str) -> str:
        """
        Redis key for caching raw Phyllo creator data.
        
        Usage: Cache raw data from Phyllo API
        Data Type: JSON String
        TTL: 2 hours (PHYLLO_DATA_TTL)
        """
        return f"CreatorVerse:Discovery:phyllo:creator:{phyllo_user_id}"
    
    @staticmethod
    def phyllo_audience_data(phyllo_user_id: str) -> str:
        """
        Redis key for caching raw Phyllo audience data.
        
        Usage: Cache audience insights from Phyllo API
        Data Type: JSON String
        TTL: 1 hour (PHYLLO_AUDIENCE_TTL)
        """
        return f"CreatorVerse:Discovery:phyllo:audience:{phyllo_user_id}"
    
    @staticmethod
    def modash_creator_data(modash_user_id: str) -> str:
        """
        Redis key for caching raw Modash creator data.
        
        Usage: Cache raw data from Modash API
        Data Type: JSON String
        TTL: 2 hours
        """
        return f"CreatorVerse:Discovery:modash:creator:{modash_user_id}"
    
    @staticmethod
    def api_rate_limit(provider: str, endpoint: str) -> str:
        """
        Redis key for tracking API rate limits.
        
        Usage: Track rate limits for external API calls
        Data Type: String (counter)
        TTL: Based on rate limit window (usually 60 seconds)
        """
        return f"CreatorVerse:Discovery:rate_limit:{provider}:{endpoint}"
    
    # ========== FILTER ENGINE & PROCESSING ==========
    
    @staticmethod
    def filter_processing_lock(filter_hash: str) -> str:
        """
        Redis key for locking filter processing to prevent duplicates.
        
        Usage: Prevent concurrent processing of same filter combination
        Data Type: String
        TTL: 5 minutes (MAX_PROCESSING_TIME)
        """
        return f"CreatorVerse:Discovery:filter_lock:{filter_hash}"
    
    @staticmethod
    def filter_validation_cache(filter_combination: str) -> str:
        """
        Redis key for caching filter validation results.
        
        Usage: Cache validation results for filter combinations
        Data Type: JSON String
        TTL: 1 hour
        """
        return f"CreatorVerse:Discovery:filter_validation:{filter_combination}"
    
    @staticmethod
    def aggregated_filter_stats() -> str:
        """
        Redis key for caching aggregated filter statistics.
        
        Usage: Cache stats like total creators per filter option
        Data Type: JSON String
        TTL: 6 hours
        """
        return "CreatorVerse:Discovery:filter_stats"
    
    # ========== USER SESSIONS & PREFERENCES ==========
    
    @staticmethod
    def user_filter_history(user_id: str) -> str:
        """
        Redis key for storing user's filter search history.
        
        Usage: Track user's recent filter combinations
        Data Type: List (FIFO, max 20 items)
        TTL: 7 days
        """
        return f"CreatorVerse:Discovery:user_history:{user_id}"
    
    @staticmethod
    def user_saved_searches(user_id: str) -> str:
        """
        Redis key for user's saved search configurations.
        
        Usage: Store user's saved filter combinations
        Data Type: JSON String
        TTL: 30 days
        """
        return f"CreatorVerse:Discovery:user_saved:{user_id}"
    
    @staticmethod
    def discovery_session(session_id: str) -> str:
        """
        Redis key for discovery session data.
        
        Usage: Store temporary session data for discovery flow
        Data Type: JSON String
        TTL: 2 hours
        """
        return f"CreatorVerse:Discovery:session:{session_id}"
    
    # ========== ANALYTICS & REPORTING ==========
    
    @staticmethod
    def platform_analytics(platform: str, date: str) -> str:
        """
        Redis key for platform-wide analytics by date.
        
        Usage: Cache daily analytics for each platform
        Data Type: JSON String
        TTL: 24 hours
        """
        return f"CreatorVerse:Discovery:platform_analytics:{platform}:{date}"
    
    @staticmethod
    def popular_filters(timeframe: str) -> str:
        """
        Redis key for caching popular filter combinations.
        
        Usage: Track most used filter combinations
        Data Type: JSON String
        TTL: 1 hour
        """
        return f"CreatorVerse:Discovery:popular_filters:{timeframe}"
    
    @staticmethod
    def creator_trending_scores(platform: str) -> str:
        """
        Redis key for caching trending creator scores.
        
        Usage: Cache trending/viral creator rankings
        Data Type: JSON String
        TTL: 30 minutes
        """
        return f"CreatorVerse:Discovery:trending:{platform}"
    
    # ========== BLOOM FILTERS & OPTIMIZATION ==========
    
    @staticmethod
    def creator_bloom_filter(platform: str) -> str:
        """
        Redis key for creator existence bloom filter by platform.
        
        Usage: Quick check if creator exists before expensive API calls
        Data Type: Bloom filter (string bitmap)
        TTL: No expiration (persistent)
        """
        return f"CreatorVerse:Discovery:bloom:creators:{platform}"
    
    @staticmethod
    def filter_combination_bloom() -> str:
        """
        Redis key for filter combination bloom filter.
        
        Usage: Quick check if filter combination was processed before
        Data Type: Bloom filter (string bitmap)
        TTL: No expiration (persistent)
        """
        return "CreatorVerse:Discovery:bloom:filter_combinations"


class RedisConfig:
    """Redis configuration constants for Discovery Profile Analytics TTL values."""

    # Filter & Metadata cache TTL
    FILTER_METADATA_TTL = 86400  # 24 hours
    SAVED_FILTER_SET_TTL = 3600  # 1 hour
    LOCATION_HIERARCHY_TTL = 86400  # 24 hours
    
    # Creator & Audience data TTL
    CREATOR_DATA_TTL = 3600  # 1 hour
    AUDIENCE_DATA_TTL = 1800  # 30 minutes
    ANALYTICS_SUMMARY_TTL = 7200  # 2 hours
    
    # Search & Processing TTL
    SEARCH_RESULTS_TTL = 900  # 15 minutes
    FILTER_PROCESSING_LOCK_TTL = 300  # 5 minutes
    FILTER_VALIDATION_TTL = 3600  # 1 hour
    
    # External API cache TTL
    PHYLLO_DATA_TTL = 7200  # 2 hours
    PHYLLO_AUDIENCE_TTL = 3600  # 1 hour
    MODASH_DATA_TTL = 7200  # 2 hours
    
    # Rate limiting TTL
    RATE_LIMIT_TTL = 60  # 1 minute
    
    # User & Session TTL
    USER_HISTORY_TTL = 604800  # 7 days
    USER_SAVED_SEARCHES_TTL = 2592000  # 30 days
    DISCOVERY_SESSION_TTL = 7200  # 2 hours
    
    # Analytics & Reporting TTL
    PLATFORM_ANALYTICS_TTL = 86400  # 24 hours
    POPULAR_FILTERS_TTL = 3600  # 1 hour
    TRENDING_SCORES_TTL = 1800  # 30 minutes
    FILTER_STATS_TTL = 21600  # 6 hours
