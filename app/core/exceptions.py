"""
Exception handling for CreatorVerse Discovery & Analytics Service
"""
from typing import Union
from fastapi import Request, HTTPException
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse

from app.utilities.response_handler import StandardResponse


class DiscoveryError(Exception):
    """Base exception for Discovery service"""
    
    def __init__(self, message: str, error_code: str = "DISCOVERY_ERROR", status_code: int = 400):
        self.message = message
        self.error_code = error_code
        self.status_code = status_code
        super().__init__(self.message)


class FilterValidationError(DiscoveryError):
    """Exception for filter validation errors"""
    
    def __init__(self, message: str):
        super().__init__(message, "FILTER_VALIDATION_ERROR", 400)


class ProfileNotFoundError(DiscoveryError):
    """Exception for when profile is not found"""
    
    def __init__(self, profile_id: str):
        super().__init__(f"Profile not found: {profile_id}", "PROFILE_NOT_FOUND", 404)


class ExternalAPIError(DiscoveryError):
    """Exception for external API errors"""
    
    def __init__(self, message: str, api_name: str = "external"):
        super().__init__(f"{api_name}: {message}", "EXTERNAL_API_ERROR", 502)


class CacheError(DiscoveryError):
    """Exception for cache-related errors"""
    
    def __init__(self, message: str):
        super().__init__(message, "CACHE_ERROR", 500)


class DatabaseError(DiscoveryError):
    """Exception for database-related errors"""
    
    def __init__(self, message: str):
        super().__init__(message, "DATABASE_ERROR", 500)


# Exception handlers
async def discovery_exception_handler(request: Request, exc: DiscoveryError) -> JSONResponse:
    """Handle DiscoveryError exceptions"""
    return StandardResponse.error(
        message=exc.message,
        status_code=exc.status_code,
        data={"error_code": exc.error_code}
    )


async def http_exception_handler(request: Request, exc: HTTPException) -> JSONResponse:
    """Handle HTTPException exceptions"""
    return StandardResponse.error(
        message=exc.detail,
        status_code=exc.status_code,
        error_code="HTTP_ERROR"
    )


async def validation_exception_handler(request: Request, exc: RequestValidationError) -> JSONResponse:
    """Handle RequestValidationError exceptions"""
    return StandardResponse.error(
        message="Validation error",
        status_code=422,
        error_code="VALIDATION_ERROR",
        details=exc.errors()
    )


async def general_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """Handle general exceptions"""
    return StandardResponse.error(
        message="Internal server error",
        status_code=500,
        error_code="INTERNAL_SERVER_ERROR"
    )


# Add CreatorVerseError alias for DiscoveryError
class CreatorVerseError(DiscoveryError):
    """General CreatorVerse error alias"""
    pass
