"""
Custom exceptions for CreatorVerse Backend
"""
from typing import Any, Dict, Optional

from fastapi import H<PERSON><PERSON><PERSON>xception, Request
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse


class CreatorVerseError(Exception):
    """Base exception for CreatorVerse application"""

    def __init__(self, message: str, status_code: int = 500, details: Optional[Dict[str, Any]] = None):
        self.message = message
        self.status_code = status_code
        self.details = details or {}
        super().__init__(self.message)


class ValidationError(CreatorVerseError):
    """Validation error exception"""

    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(message, status_code=400, details=details)


class NotFoundError(CreatorVerseError):
    """Not found error exception"""

    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(message, status_code=404, details=details)


class UnauthorizedError(CreatorVerseError):
    """Unauthorized error exception"""

    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(message, status_code=401, details=details)


class ForbiddenError(CreatorVerseError):
    """Forbidden error exception"""

    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(message, status_code=403, details=details)


# Exception handlers
async def creatorverse_exception_handler(request: Request, exc: CreatorVerseError) -> JSONResponse:
    """Handle CreatorVerse custom exceptions"""
    from app.core.logger import get_logger

    logger = get_logger()
    logger.error(f"CreatorVerse Exception: {exc.status_code} - {exc.message}")

    return JSONResponse(
        status_code=exc.status_code,
        content={
            "data": exc.details,
            "message": exc.message
        }
    )


async def http_exception_handler(request: Request, exc: HTTPException) -> JSONResponse:
    """Handle HTTP exceptions"""
    from app.core.logger import get_logger

    logger = get_logger()
    logger.error(f"HTTP Exception: {exc.status_code} - {exc.detail}")

    return JSONResponse(
        status_code=exc.status_code,
        content={
            "data": None,
            "message": str(exc.detail)
        }
    )


async def validation_exception_handler(request: Request, exc: RequestValidationError) -> JSONResponse:
    """Handle validation exceptions"""
    from app.core.logger import get_logger

    logger = get_logger()
    logger.error(f"Validation Error: {exc.errors()}")

    return JSONResponse(
        status_code=422,
        content={
            "success": False,
            "data": exc.errors(),
            "message": "Validation error"
        }
    )


async def general_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """Handle general exceptions"""
    from app.core.logger import get_logger

    logger = get_logger()
    logger.error(f"Unhandled Exception: {str(exc)}")

    return JSONResponse(
        status_code=500,
        content={
            "data": None,
            "message": "Internal server error"
        }
    )


class EmailValidationError(Exception):
    """Exception raised when email validation fails."""

    def __init__(self, message: str, email: str | None = None):
        self.message = message
        self.email = email
        super().__init__(self.message)


class EmailAlreadyExistsError(Exception):
    """Exception raised when email already exists in the system."""

    def __init__(self, email: str):
        self.email = email
        self.message = f"Email '{email}' already exists in the system"
        super().__init__(self.message)


class EmailNotFoundError(Exception):
    """Exception raised when email is not found in the system."""

    def __init__(self, email: str):
        self.email = email
        self.message = f"Email '{email}' not found in the system"
        super().__init__(self.message)


class ServiceUnavailableError(Exception):
    """Exception raised when a service is unavailable."""

    def __init__(self, service_name: str, message: str):
        self.service_name = service_name
        self.message = f"Service '{service_name}' unavailable: {message}"
        super().__init__(self.message)


def create_email_validation_error(message: str, email: str | None = None) -> EmailValidationError:
    """Factory function to create EmailValidationError."""
    return EmailValidationError(message, email)
