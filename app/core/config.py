import json
import sys
from typing import Optional, Dict, Any

from app.core_helper.async_logger import FastAPILog<PERSON>, LogLevel
from app.core_helper.database import AsyncDatabaseDB
from app.core_helper.redis_client import RedisClient


class AppConfig:
    """Application configuration class"""

    def __init__(self, config_file: str = "appsettings.json"):
        # Load configuration from appsettings.json
        with open(config_file, 'r') as f:
            config_data: Dict[str, Any] = json.load(f)

        # Basic settings
        self.service_name = config_data.get("service_name", "CreatorVerseUser Backend")
        self.environ = config_data.get("environ", "development")

        # Database settings
        self.connection_string = config_data.get("connection_string", "")

        # Redis settings
        redis_host = config_data.get("redis_host", "localhost")
        self.redis_url = f"redis://{redis_host}:6379"

        # Logging settings
        self.log_enabled = config_data.get("log_enabled", "true").lower() == "true"
        self.log_level = config_data.get("log_level", "INFO")
        self.log_type = config_data.get("log_type", "json")
        self.is_async_logger = config_data.get("is_async_logger", False)
        self.enhanced_features = config_data.get("enhanced_features", True)
        self.include_trace_id = config_data.get("include_trace_id", True)
        self.use_queue_handler = config_data.get("use_queue_handler", True)
        self.max_queue_size = config_data.get("max_queue_size", 1000)
        self.batch_size = config_data.get("batch_size", 10)
        self.flush_interval = config_data.get("flush_interval", 0.1)
        self.bind_uvicorn = config_data.get("bind_uvicorn", True)

        # Security settings
        security = config_data.get("security", {})
        self.secret_key = security.get("SECRET_KEY", "")
        self.jwt_algorithm = security.get("JWT_ALGORITHM", "HS256")
        self.access_token_expire_minutes = security.get("ACCESS_TOKEN_EXPIRE_MINUTES", 30)
        self.refresh_token_expire_days = security.get("REFRESH_TOKEN_EXPIRE_DAYS", 30)
        self.access_token_expire_minutes = security.get("ACCESS_TOKEN_EXPIRE_MINUTES", 30)
        self.refresh_token_expire_days = security.get("REFRESH_TOKEN_EXPIRE_DAYS", 30)
        self.algorithm = security.get("ALGORITHM", "HS256")

        # OAuth settings
        oauth = config_data.get("oauth", {})
        self.google_client_id = oauth.get("GOOGLE_CLIENT_ID", "")
        self.google_client_secret = oauth.get("GOOGLE_CLIENT_SECRET", "")
        self.google_redirect_uri = oauth.get("GOOGLE_REDIRECT_URI", "")
        self.instagram_client_id = oauth.get("INSTAGRAM_CLIENT_ID", "")
        self.instagram_client_secret = oauth.get("INSTAGRAM_CLIENT_SECRET", "")
        self.instagram_redirect_uri = oauth.get("INSTAGRAM_REDIRECT_URI", "")

        # CORS settings
        cors = config_data.get("cors", {})
        self.allowed_origins = cors.get("ALLOWED_ORIGINS", ["*"])

        # Email settings
        email = config_data.get("email", {})
        self.mailgun_domain = email.get("MAILGUN_DOMAIN", "")
        self.mailgun_secret = email.get("MAILGUN_SECRET", "")
        self.email_sender_name = email.get("SENDER_NAME", "CreatorVerse")

        # Webhooks
        self.g_chat_webhook = config_data.get("g_chat_webhook", "")
        self.g_chat_error_webhook = config_data.get("g_chat_error_webhook", "")

        # Initialize logger
        self.logger: Optional[FastAPILogger] = None

    def initialize_logger(self) -> FastAPILogger:
        """Initialize the application logger"""
        if self.logger is None:
            log_level_map = {
                "DEBUG": LogLevel.DEBUG,
                "INFO": LogLevel.INFO,
                "WARNING": LogLevel.WARNING,
                "ERROR": LogLevel.ERROR,
                "CRITICAL": LogLevel.CRITICAL}

            self.logger = FastAPILogger(
                service_name=self.service_name,
                sys_module=sys,
                log_level=log_level_map.get(self.log_level, LogLevel.INFO),
                trace_id_prefix="CVU",
                environment=self.environ
            )
        return self.logger


# Create global app config instance
APP_CONFIG = AppConfig()

# Global instances (to be initialized in main.py)
_db_instance: Optional[AsyncDatabaseDB] = None
_redis_instance: Optional[RedisClient] = None


def get_database() -> AsyncDatabaseDB:
    """Get database connection singleton"""
    global _db_instance
    if _db_instance is None:
        logger = APP_CONFIG.logger or APP_CONFIG.initialize_logger()
        _db_instance = AsyncDatabaseDB(
            connection_string=APP_CONFIG.connection_string,
            pool_size=5,
            max_overflow=10,
            logger=logger
        )
    return _db_instance


def get_locobuzz_redis() -> RedisClient:
    """Get Redis connection singleton"""
    global _redis_instance
    if _redis_instance is None:
        logger = APP_CONFIG.logger or APP_CONFIG.initialize_logger()
        _redis_instance = RedisClient(
            redis_url=APP_CONFIG.redis_url,
            logger=logger
        )
    return _redis_instance


# Application constants
DEFAULT_OTP_LENGTH = 4
MAX_OTP_ATTEMPTS = 5
OTP_LOCKOUT_DURATION = 900  # 15 minutes in seconds
OTP_RATE_LIMIT_PER_HOUR = 5
