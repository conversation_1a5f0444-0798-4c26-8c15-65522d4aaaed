import json
import sys
from typing import Optional, Dict, Any

from app.core_helper.async_logger import <PERSON>APILog<PERSON>, LogLevel
from app.core_helper.database import AsyncDatabaseDB
from app.core_helper.redis_client import RedisClient


class AppConfig:
    """Application configuration class for Discovery & Analytics service"""

    def __init__(self, config_file: str = "appsettings.json"):
        # Load configuration from appsettings.json
        with open(config_file, 'r') as f:
            config_data: Dict[str, Any] = json.load(f)

        # Basic settings
        self.service_name = config_data.get("service_name", "CreatorVerse Discovery & Analytics Backend")
        self.environ = config_data.get("environ", "development")

        # Database settings
        self.connection_string = config_data.get("connection_string", "")

        # Redis settings
        redis_host = config_data.get("redis_host", "localhost")
        self.redis_url = f"redis://{redis_host}:6379"

        # External API settings
        external_apis = config_data.get("external_apis", {})
        self.phyllo_base_url = external_apis.get("PHYLLO_BASE_URL", "")
        self.phyllo_client_id = external_apis.get("PHYLLO_CLIENT_ID", "")
        self.phyllo_client_secret = external_apis.get("PHYLLO_CLIENT_SECRET", "")
        self.phyllo_environment = external_apis.get("PHYLLO_ENVIRONMENT", "sandbox")

        # Cache settings
        cache = config_data.get("cache", {})
        self.profile_cache_ttl = cache.get("PROFILE_CACHE_TTL", 3600)
        self.filter_cache_ttl = cache.get("FILTER_CACHE_TTL", 86400)
        self.external_api_cache_ttl = cache.get("EXTERNAL_API_CACHE_TTL", 1800)

        # Kafka settings
        kafka = config_data.get("kafka", {})
        self.kafka_bootstrap_servers = kafka.get("BOOTSTRAP_SERVERS", "localhost:9092")
        self.kafka_topic_profile_updates = kafka.get("TOPIC_PROFILE_UPDATES", "profile_updates")
        self.kafka_topic_external_fetch = kafka.get("TOPIC_EXTERNAL_FETCH", "external_fetch_requests")
        self.kafka_consumer_group = kafka.get("CONSUMER_GROUP", "discovery_analytics_group")

        # Discovery settings
        discovery = config_data.get("discovery", {})
        self.default_page_size = discovery.get("DEFAULT_PAGE_SIZE", 20)
        self.max_page_size = discovery.get("MAX_PAGE_SIZE", 100)
        self.quick_view_fields = discovery.get("QUICK_VIEW_FIELDS", [
            "id", "platform_username", "full_name", "follower_count", "engagement_rate", "image_url"
        ])
        self.external_api_timeout = discovery.get("EXTERNAL_API_TIMEOUT", 30)
        self.max_concurrent_external_calls = discovery.get("MAX_CONCURRENT_EXTERNAL_CALLS", 5)

        # Logging settings
        self.log_enabled = config_data.get("log_enabled", "true").lower() == "true"
        self.log_level = config_data.get("log_level", "INFO")
        self.log_type = config_data.get("log_type", "json")
        self.is_async_logger = config_data.get("is_async_logger", False)
        self.enhanced_features = config_data.get("enhanced_features", True)
        self.include_trace_id = config_data.get("include_trace_id", True)
        self.use_queue_handler = config_data.get("use_queue_handler", True)
        self.max_queue_size = config_data.get("max_queue_size", 1000)
        self.batch_size = config_data.get("batch_size", 10)
        self.flush_interval = config_data.get("flush_interval", 0.1)
        self.bind_uvicorn = config_data.get("bind_uvicorn", True)

        # Security settings
        security = config_data.get("security", {})
        self.secret_key = security.get("SECRET_KEY", "")
        self.algorithm = security.get("ALGORITHM", "HS256")
        self.access_token_expire_minutes = security.get("ACCESS_TOKEN_EXPIRE_MINUTES", 30)
        self.refresh_token_expire_days = security.get("REFRESH_TOKEN_EXPIRE_DAYS", 30)

        # CORS settings
        cors = config_data.get("cors", {})
        self.allowed_origins = cors.get("ALLOWED_ORIGINS", ["*"])

        # Webhooks
        self.g_chat_webhook = config_data.get("g_chat_webhook", "")
        self.g_chat_error_webhook = config_data.get("g_chat_error_webhook", "")
        # Mock and SSL settings for external APIs
        self.use_mock_apis = config_data.get("use_mock_apis", False)
        self.disable_ssl_verification = config_data.get("disable_ssl_verification", False)

        # Initialize logger
        self.logger: Optional[FastAPILogger] = None

    def initialize_logger(self) -> FastAPILogger:
        """Initialize the application logger"""
        if self.logger is None:
            log_level_map = {
                "DEBUG": LogLevel.DEBUG,
                "INFO": LogLevel.INFO,
                "WARNING": LogLevel.WARNING,
                "ERROR": LogLevel.ERROR,
                "CRITICAL": LogLevel.CRITICAL
            }

            self.logger = FastAPILogger(
                service_name=self.service_name,
                sys_module=sys,
                log_level=log_level_map.get(self.log_level, LogLevel.INFO),
                trace_id_prefix="CVD",  # CreatorVerse Discovery
                environment=self.environ
            )
        return self.logger


# Create global app config instance
APP_CONFIG = AppConfig()

# Global instances (to be initialized in main.py)
_db_instance: Optional[AsyncDatabaseDB] = None
_redis_instance: Optional[RedisClient] = None


def get_database() -> AsyncDatabaseDB:
    """Get database connection singleton"""
    global _db_instance
    if _db_instance is None:
        logger = APP_CONFIG.logger or APP_CONFIG.initialize_logger()
        _db_instance = AsyncDatabaseDB(
            connection_string=APP_CONFIG.connection_string,
            pool_size=5,
            max_overflow=10,
            logger=logger
        )
    return _db_instance


def get_discovery_redis() -> RedisClient:
    """Get Redis connection singleton for discovery service"""
    global _redis_instance
    if _redis_instance is None:
        logger = APP_CONFIG.logger or APP_CONFIG.initialize_logger()
        _redis_instance = RedisClient(
            redis_url=APP_CONFIG.redis_url,
            logger=logger
        )
    return _redis_instance


# Alias for Locobuzz Redis client
def get_locobuzz_redis() -> RedisClient:
    """Alias to get_discovery_redis for Locobuzz Redis client usage"""
    return get_discovery_redis()


# Discovery service constants
DEFAULT_CACHE_PREFIX = "discovery:"
PROFILE_CACHE_PREFIX = "profile:"
FILTER_CACHE_PREFIX = "filter:"
EXTERNAL_API_CACHE_PREFIX = "external:"

# Filter type constants
FILTER_TYPE_DEMOGRAPHIC = "demographic"
FILTER_TYPE_PERFORMANCE = "performance"
FILTER_TYPE_AUDIENCE = "audience"
FILTER_TYPE_CONTENT = "content"

# Platform constants
PLATFORM_INSTAGRAM = "instagram"
PLATFORM_YOUTUBE = "youtube"
PLATFORM_TIKTOK = "tiktok"
