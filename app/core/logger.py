"""
Logger utility for CreatorVerse Backend
"""
from app.core.config import APP_CONFIG


def get_logger():
    """
    Get application logger, ensuring it's initialized.
    This centralizes the 'APP_CONFIG.logger or APP_CONFIG.initialize_logger()' pattern.
    
    Returns:
        FastAPILogger: The initialized application logger
    """
    return APP_CONFIG.logger or APP_CONFIG.initialize_logger()
