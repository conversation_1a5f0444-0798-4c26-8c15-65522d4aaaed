from typing import Dict, List, Optional
from pydantic import BaseModel, HttpUrl


class WebhookRegistration(BaseModel):
    """Model representing a webhook registration."""
    url: HttpUrl
    description: str
    events: List[str]
    active: bool = True
    
    
class WebhookEvent(BaseModel):
    """Model representing a webhook event."""
    event_type: str
    resource_type: str
    resource_id: str
    action: str
    timestamp: str
    data: Dict
    
    
class WebhookTestRequest(BaseModel):
    """Model for requesting a test webhook."""
    url: HttpUrl
    event_type: str = "test_event"
    resource_type: str = "test"
    resource_id: str = "test_123"
    action: str = "test"
