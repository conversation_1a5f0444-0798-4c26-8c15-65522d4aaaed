"""
Brand-related schemas for CreatorVerse Backend.
"""
from enum import Enum
from typing import Optional, List, Dict, Any
from uuid import UUID

from pydantic import BaseModel, Field, ConfigDict, validator


class BrandMembershipStatus(str, Enum):
    """Brand membership status options"""
    PENDING = "pending"
    ACTIVE = "active" 
    REJECTED = "rejected"
    NONE = "none"


class BrandRole(str, Enum):
    """Brand role options"""
    OWNER = "brand_owner"
    ADMIN = "brand_admin"
    MEMBER = "member"


class CreateBrandRequest(BaseModel):
    """Schema for creating a new brand"""
    name: str = Field(..., min_length=2, max_length=100, description="Brand name")
    description: Optional[str] = Field(None, max_length=500, description="Brand description")
    logo_url: Optional[str] = Field(None, max_length=2048, description="URL to brand logo")
    website_url: Optional[str] = Field(None, max_length=2048, description="<PERSON>'s website URL")
    contact_email: Optional[str] = Field(None, max_length=255, description="<PERSON>'s contact email")


class BrandMembershipRequest(BaseModel):
    """Schema for requesting to join a brand"""
    model_config = ConfigDict(from_attributes=True)

    reason: Optional[str] = Field({}, max_length=500, description="Reason for joining")


class ChangeBrandRoleRequest(BaseModel):
    """Schema for changing a user's role in a brand"""
    role: BrandRole = Field(..., description="New role for the user")


class BrandMemberResponse(BaseModel):
    """Schema for a brand member"""
    model_config = ConfigDict(from_attributes=True)

    user_id: str
    name: Optional[str] = None
    email: str
    role: str
    status: str
    joined_at: Optional[str] = None


class BrandMemberListResponse(BaseModel):
    """Schema for a list of brand members"""
    model_config = ConfigDict(from_attributes=True)

    members: List[BrandMemberResponse]
    total: int


class BrandResponse(BaseModel):
    """Schema for brand response"""
    model_config = ConfigDict(from_attributes=True)

    id: str
    name: str
    description: Optional[str] = None
    logo_url: Optional[str] = None
    website_url: Optional[str] = None
    contact_email: Optional[str] = None
    total_members: int = 0
    is_active: bool = True
    user_relationship: Optional[Dict[str, Any]] = None


class BrandListResponse(BaseModel):
    """Schema for a list of brands"""
    model_config = ConfigDict(from_attributes=True)

    brands: List[BrandResponse]
    total: int


class GenericSuccessResponse(BaseModel):
    """Generic success response"""
    success: bool = True
    message: str


class BrandMembershipApprovalRequest(BaseModel):
    """Schema for brand membership approval request"""
    membership_id: Optional[UUID] = None
    user_id: Optional[UUID] = None
    brand_id: UUID = Field(..., description="ID of the brand")
    
    model_config = ConfigDict(from_attributes=True)


class BrandMembershipResponseItem(BaseModel):
    """Schema for user in brand membership response"""
    user_id: str
    name: str
    email: str
    requested_at: Optional[str] = None
    updated_at: Optional[str] = None
    
    model_config = ConfigDict(from_attributes=True)


class BrandPendingRequestsItem(BaseModel):
    """Schema for brand item in pending requests response"""
    brand_id: str
    brand_name: str
    total_requests: int
    pending_requests: List[BrandMembershipResponseItem]
    
    model_config = ConfigDict(from_attributes=True)


class BrandPendingRequestsResponse(BaseModel):
    """Schema for aggregated pending requests response"""
    total_pending_requests: int
    brands_with_requests: List[BrandPendingRequestsItem]
    
    model_config = ConfigDict(from_attributes=True)
