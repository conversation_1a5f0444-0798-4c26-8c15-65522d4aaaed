"""
Influencer-related schemas for CreatorVerse Backend.
Handles influencer list management and related operations.
"""
from enum import Enum
from typing import Optional, List, Dict, Any
from uuid import UUID

from pydantic import BaseModel, Field, ConfigDict


class BrandInfluencerListStatus(str, Enum):
    """Brand influencer list status options"""
    ACTIVE = "active"
    ARCHIVED = "archived"
    DRAFT = "draft"


class InfluencerStatus(str, Enum):
    """Influencer status options in lists"""
    SHORTLISTED = "shortlisted"
    CONTACTED = "contacted"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    REJECTED = "rejected"


# ──────────────────────────────────────────────────────────────────────────────
# REQUEST SCHEMAS
# ──────────────────────────────────────────────────────────────────────────────

class CreateInfluencerListRequest(BaseModel):
    """Schema for creating a new influencer list"""
    name: str = Field(..., min_length=2, max_length=100, description="List name")
    description: Optional[str] = Field(None, max_length=500, description="List description")
    brand_id: UUID = Field(..., description="Brand ID this list belongs to")


class UpdateInfluencerListRequest(BaseModel):
    """Schema for updating an influencer list"""
    name: Optional[str] = Field(None, min_length=2, max_length=100, description="List name")
    description: Optional[str] = Field(None, max_length=500, description="List description")
    status: Optional[BrandInfluencerListStatus] = Field(None, description="List status")


class AddInfluencerToListRequest(BaseModel):
    """Schema for adding an influencer to a list"""
    influencer_id: str = Field(..., description="Influencer ID from profile analytics")
    influencer_name: Optional[str] = Field(None, description="Influencer display name")
    influencer_username: Optional[str] = Field(None, description="Influencer username/handle")
    influencer_avatar_url: Optional[str] = Field(None, description="Influencer avatar URL")
    user_id: Optional[UUID] = Field(None, description="Creatorverse user ID if exists")
    
    # Campaign fields
    status: Optional[InfluencerStatus] = Field(InfluencerStatus.SHORTLISTED, description="Initial status")
    channels: Optional[List[str]] = Field(default_factory=list, description="Social media channels")
    audience_size: Optional[int] = Field(None, description="Total audience/followers")
    engagement_rate: Optional[int] = Field(None, description="Engagement rate (percentage * 100)")
    campaign: Optional[str] = Field(None, description="Associated campaign")
    labels: Optional[List[str]] = Field(default_factory=list, description="Labels/tags")
    notes: Optional[str] = Field(None, description="Private notes")


class UpdateInfluencerInListRequest(BaseModel):
    """Schema for updating an influencer in a list"""
    status: Optional[InfluencerStatus] = Field(None, description="Influencer status")
    channels: Optional[List[str]] = Field(None, description="Social media channels")
    audience_size: Optional[int] = Field(None, description="Total audience/followers")
    engagement_rate: Optional[int] = Field(None, description="Engagement rate (percentage * 100)")
    campaign: Optional[str] = Field(None, description="Associated campaign")
    labels: Optional[List[str]] = Field(None, description="Labels/tags")
    notes: Optional[str] = Field(None, description="Private notes")


class BulkAddInfluencersRequest(BaseModel):
    """Schema for adding multiple influencers to a list"""
    influencers: List[AddInfluencerToListRequest] = Field(..., description="List of influencers to add")


class MoveInfluencersRequest(BaseModel):
    """Schema for moving influencers between lists"""
    source_list_id: UUID = Field(..., description="Source list ID")
    target_list_id: UUID = Field(..., description="Target list ID")
    influencer_ids: List[str] = Field(..., description="List of influencer IDs to move")


# ──────────────────────────────────────────────────────────────────────────────
# RESPONSE SCHEMAS
# ──────────────────────────────────────────────────────────────────────────────

class InfluencerEntryResponse(BaseModel):
    """Schema for an influencer entry in a list"""
    model_config = ConfigDict(from_attributes=True)

    id: str
    influencer_id: str
    influencer_name: Optional[str] = None
    influencer_username: Optional[str] = None
    influencer_avatar_url: Optional[str] = None
    user_id: Optional[str] = None
    
    # Campaign fields
    status: str
    channels: List[str] = []
    audience_size: Optional[int] = None
    engagement_rate: Optional[int] = None  # percentage * 100
    campaign: Optional[str] = None
    labels: List[str] = []
    notes: Optional[str] = None
    
    # Audit fields
    added_by: str
    added_at: str
    updated_at: str

    @property
    def engagement_rate_percent(self) -> Optional[float]:
        """Convert engagement rate to percentage"""
        if self.engagement_rate is not None:
            return self.engagement_rate / 100.0
        return None


class InfluencerListResponse(BaseModel):
    """Schema for an influencer list"""
    model_config = ConfigDict(from_attributes=True)

    id: str
    name: str
    description: Optional[str] = None
    status: str
    brand_id: str
    created_by: str
    created_at: str
    updated_at: str
    total_influencers: int = 0


class InfluencerListDetailResponse(BaseModel):
    """Schema for detailed influencer list with entries"""
    model_config = ConfigDict(from_attributes=True)

    id: str
    name: str
    description: Optional[str] = None
    status: str
    brand_id: str
    created_by: str
    created_at: str
    updated_at: str
    influencers: List[InfluencerEntryResponse] = []
    total_influencers: int = 0


class InfluencerListsResponse(BaseModel):
    """Schema for a list of influencer lists"""
    model_config = ConfigDict(from_attributes=True)

    lists: List[InfluencerListResponse]
    total: int


class BrandInfluencerListsResponse(BaseModel):
    """Schema for brand's influencer lists with summary"""
    model_config = ConfigDict(from_attributes=True)

    brand_id: str
    brand_name: str
    lists: List[InfluencerListResponse]
    total_lists: int
    total_influencers: int


# ──────────────────────────────────────────────────────────────────────────────
# UTILITY SCHEMAS
# ──────────────────────────────────────────────────────────────────────────────

class InfluencerListOption(BaseModel):
    """Schema for list selection options"""
    model_config = ConfigDict(from_attributes=True)

    id: str
    name: str
    total_influencers: int = 0


class InfluencerListOptionsResponse(BaseModel):
    """Schema for available list options"""
    model_config = ConfigDict(from_attributes=True)

    lists: List[InfluencerListOption]
    total: int


class BulkOperationResponse(BaseModel):
    """Schema for bulk operation results"""
    model_config = ConfigDict(from_attributes=True)

    success: bool
    total_processed: int
    successful: int
    failed: int
    errors: List[str] = []
    message: str


class GenericSuccessResponse(BaseModel):
    """Generic success response"""
    success: bool = True
    message: str


# ──────────────────────────────────────────────────────────────────────────────
# IMPORT/EXPORT SCHEMAS
# ──────────────────────────────────────────────────────────────────────────────

class ImportInfluencersRequest(BaseModel):
    """Schema for importing influencers from CSV"""
    list_id: Optional[UUID] = Field(None, description="Existing list ID or create new")
    list_name: Optional[str] = Field(None, description="New list name if creating")
    brand_id: UUID = Field(..., description="Brand ID")
    # CSV data will be handled separately


class ImportResultResponse(BaseModel):
    """Schema for import operation results"""
    model_config = ConfigDict(from_attributes=True)

    success: bool
    total_rows: int
    imported: int
    skipped: int
    errors: List[str] = []
    list_id: Optional[str] = None
    list_name: Optional[str] = None
    message: str


# ──────────────────────────────────────────────────────────────────────────────
# FILTER AND SEARCH SCHEMAS  
# ──────────────────────────────────────────────────────────────────────────────

class InfluencerListFilters(BaseModel):
    """Schema for filtering influencer lists"""
    status: Optional[BrandInfluencerListStatus] = None
    created_by: Optional[UUID] = None
    search: Optional[str] = None  # Search in name and description


class InfluencerEntryFilters(BaseModel):
    """Schema for filtering influencers in a list"""
    status: Optional[InfluencerStatus] = None
    campaign: Optional[str] = None
    labels: Optional[List[str]] = None
    min_audience: Optional[int] = None
    max_audience: Optional[int] = None
    min_engagement: Optional[int] = None  # percentage * 100
    max_engagement: Optional[int] = None  # percentage * 100
    channels: Optional[List[str]] = None
    search: Optional[str] = None  # Search in name and username
