"""
Authentication schemas for CreatorVerse Backend.
"""
import re
from typing import Optional

from pydantic import BaseModel, EmailStr, Field, model_validator, validator


class InfluencerRegisterRequest(BaseModel):
    """Schema for influencer registration request supporting both email and mobile"""
    email: Optional[EmailStr] = Field(None, description="Email address for registration")
    mobile: Optional[str] = Field(None, description="Mobile number in E.164 format (+1234567890)")
    role_uuid: str = Field(..., description="UUID of the influencer role")
    register_source: int = Field(
        default=1,
        description="Source of user registration",
        examples=[1, 2, 3, 4, 5]
    )

    @model_validator(mode='after')
    def validate_fields(cls, values):
        email = values.email
        mobile = values.mobile

        # Check that exactly one identifier is provided
        if (email is None and mobile is None) or (email is not None and mobile is not None):
            raise ValueError("Exactly one of email or mobile must be provided")

        return values

    @property
    def identifier(self) -> str:
        """Returns the provided identifier (email or mobile)"""
        return str(self.email) if self.email is not None else str(self.mobile)

    @property
    def identifier_type(self) -> str:
        """Returns the type of identifier provided"""
        return "email" if self.email is not None else "mobile"


class VerifyOTPRequest(BaseModel):
    """Schema for OTP verification request with email or mobile support"""
    email: Optional[EmailStr] = Field(None, description="Email address for verification")
    mobile: Optional[str] = Field(None, description="Mobile number for verification")
    otp: str = Field(..., description="OTP code to verify")
    role_uuid: str = Field(..., description="UUID of the role for verification")

    @validator("mobile")
    def validate_mobile(cls, v):
        if v is not None:
            # Validate E.164 format
            if not re.match(r"^\+[1-9]\d{1,14}$", v):
                raise ValueError("Mobile number must be in E.164 format (e.g., +1234567890)")
        return v

    # Replace field validator with model validator
    @model_validator(mode='after')
    def validate_fields(cls, values):
        email = values.email
        mobile = values.mobile

        # Check that exactly one identifier is provided
        if (email is None and mobile is None) or (email is not None and mobile is not None):
            raise ValueError("Exactly one of email or mobile must be provided")

        return values

    @property
    def identifier(self) -> str:
        """Returns the provided identifier (email or mobile)"""
        return str(self.email) if self.email is not None else str(self.mobile)

    @property
    def identifier_type(self) -> str:
        """Returns the type of identifier provided"""
        return "email" if self.email is not None else "mobile"


class ResendOTPRequest(BaseModel):
    """Schema for resending OTP"""
    email: Optional[EmailStr] = Field(None, description="Email address for OTP")
    mobile: Optional[str] = Field(None, description="Mobile number for OTP")

    @validator("mobile")
    def validate_mobile(cls, v):
        if v is not None:
            # Validate E.164 format
            if not re.match(r"^\+[1-9]\d{1,14}$", v):
                raise ValueError("Mobile number must be in E.164 format (e.g., +1234567890)")
        return v

    @validator("email", "mobile")
    def validate_identifier(cls, v, values):
        email = values.get("email")
        mobile = values.get("mobile")

        # Check that exactly one identifier is provided
        if (email is None and mobile is None) or (email is not None and mobile is not None):
            raise ValueError("Exactly one of email or mobile must be provided")

        return v

    @property
    def identifier(self) -> str:
        """Returns the provided identifier (email or mobile)"""
        return str(self.email) if self.email is not None else str(self.mobile)

    @property
    def identifier_type(self) -> str:
        """Returns the type of identifier provided"""
        return "email" if self.email is not None else "mobile"


class OtpResponse(BaseModel):
    """Response schema for OTP operations"""
    success: bool
    message: str
    email: Optional[str] = None
    mobile: Optional[str] = None


class AuthTokenResponse(BaseModel):
    """Response schema for authentication tokens"""
    access_token: str
    refresh_token: str
    token_type: str
    expires_in: int


class BrandMembershipInfo(BaseModel):
    """Schema for brand membership information"""
    status: str = ""
    role: Optional[str] = None


class BrandInfoSchema(BaseModel):
    """Schema for brand information in auth responses"""
    id: str
    name: str
    description: Optional[str] = None
    logo_url: Optional[str] = None
    website_url: Optional[str] = None
    user_relationship: Optional[BrandMembershipInfo] = None
    is_active: bool
    created_at: Optional[str] = None
    updated_at: Optional[str] = None


class AuthTokenBrandResponse(AuthTokenResponse):
    """Extended response schema for brand authentication tokens with additional brand information"""
    is_first: bool = False
    organization_id: Optional[str] = None
    organization_name: Optional[str] = None
    organization_brands: list[BrandInfoSchema] = []


class LoginRequest(BaseModel):
    """Schema for login request supporting both email and mobile"""
    email: Optional[EmailStr] = Field(None, description="Email address for login")
    mobile: Optional[str] = Field(None, description="Mobile number in E.164 format (+1234567890)")

    @validator("mobile")
    def validate_mobile_format(cls, v):
        if v is not None and not v.startswith("+"):
            raise ValueError("Mobile number must be in E.164 format starting with '+' (e.g. +1234567890)")
        return v

    @model_validator(mode='after')
    def check_email_or_mobile(self):
        email_provided = self.email is not None
        mobile_provided = self.mobile is not None

        if not email_provided and not mobile_provided:
            raise ValueError("Either email or mobile must be provided")

        if email_provided and mobile_provided:
            raise ValueError("Provide either email or mobile, not both")

        return self

    @property
    def identifier(self) -> str:
        """Get the identifier (email or mobile)."""
        return self.email or self.mobile

    @property
    def identifier_type(self) -> str:
        """Get the type of identifier (email or mobile)."""
        return "email" if self.email else "mobile"


class LoginVerifyOTPRequest(BaseModel):
    """Schema for login OTP verification request"""
    email: Optional[EmailStr] = Field(None, description="Email address for verification")
    mobile: Optional[str] = Field(None, description="Mobile number for verification")
    otp: str = Field(..., description="OTP code to verify")

    @validator("mobile")
    def validate_mobile(cls, v):
        if v is not None:
            # Validate E.164 format
            if not re.match(r"^\+[1-9]\d{1,14}$", v):
                raise ValueError("Mobile number must be in E.164 format (e.g., +1234567890)")
        return v

    @model_validator(mode='after')
    def validate_fields(cls, values):
        email = values.email
        mobile = values.mobile

        # Check that exactly one identifier is provided
        if (email is None and mobile is None) or (email is not None and mobile is not None):
            raise ValueError("Exactly one of email or mobile must be provided")

        return values

    @property
    def identifier(self) -> str:
        """Returns the provided identifier (email or mobile)"""
        return str(self.email) if self.email is not None else str(self.mobile)

    @property
    def identifier_type(self) -> str:
        """Returns the type of identifier provided"""
        return "email" if self.email is not None else "mobile"
