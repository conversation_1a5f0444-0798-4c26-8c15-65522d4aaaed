"""
Generic External API Provider Interface
This module defines the abstract interface that any external API provider must implement.
"""
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Tuple
from enum import Enum

from app.schemas.external_api.frontend_schemas import (
    FrontendFilterSelections, CreatorProfile, PlatformEnum, OptionForEnum
)


class APIProviderType(str, Enum):
    """Supported external API providers"""
    PHYLLO = "phyllo"
    MODASH = "modash"
    CUSTOM = "custom"
    # Add more providers as needed


class APIProviderCapability(str, Enum):
    """API provider capabilities"""
    CREATOR_SEARCH = "creator_search"
    QUICK_SEARCH = "quick_search"
    CREATOR_ANALYTICS = "creator_analytics"
    AUDIENCE_INSIGHTS = "audience_insights"
    BULK_SEARCH = "bulk_search"


class FilterMapping(Dict[str, str]):
    """Filter field mapping: frontend_field -> api_field"""
    pass


class APIProviderConfig(ABC):
    """Base configuration for API providers"""
    
    @property
    @abstractmethod
    def provider_type(self) -> APIProviderType:
        """Provider type identifier"""
        pass
    
    @property
    @abstractmethod
    def base_url(self) -> str:
        """API base URL"""
        pass
    
    @property
    @abstractmethod
    def capabilities(self) -> List[APIProviderCapability]:
        """Supported capabilities"""
        pass
    
    @abstractmethod
    def get_filter_mapping(self, platform: PlatformEnum, option_for: OptionForEnum) -> FilterMapping:
        """Get filter field mappings for platform and target"""
        pass


class ExternalAPIProvider(ABC):
    """Abstract base class for external API providers"""
    
    def __init__(self, config: APIProviderConfig):
        self.config = config
    
    @abstractmethod
    async def search_creators(
        self,
        filter_selections: FrontendFilterSelections,
        **kwargs
    ) -> Tuple[List[CreatorProfile], Dict[str, Any]]:
        """
        Search creators based on filter selections
        
        Returns:
            Tuple of (creator_profiles, metadata)
        """
        pass
    
    @abstractmethod
    async def quick_search(
        self,
        query: str,
        platform: PlatformEnum,
        limit: int = 10,
        **kwargs
    ) -> List[CreatorProfile]:
        """
        Quick search for creators by name/username
        
        Returns:
            List of creator profiles
        """
        pass
    
    @abstractmethod
    def transform_filters(
        self,
        filter_selections: FrontendFilterSelections
    ) -> Dict[str, Any]:
        """
        Transform frontend filter selections to API provider format
        
        Returns:
            API provider specific filter parameters
        """
        pass
    
    @abstractmethod
    async def get_creator_analytics(
        self,
        creator_id: str,
        platform: PlatformEnum,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Get detailed analytics for a creator
        
        Returns:
            Creator analytics data
        """
        pass
    
    @abstractmethod
    async def get_audience_insights(
        self,
        creator_id: str,
        platform: PlatformEnum,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Get audience insights for a creator
        
        Returns:
            Audience insights data
        """
        pass
    
    def supports_capability(self, capability: APIProviderCapability) -> bool:
        """Check if provider supports a capability"""
        return capability in self.config.capabilities
    
    def get_provider_type(self) -> APIProviderType:
        """Get provider type"""
        return self.config.provider_type


class APIProviderFactory:
    """Factory for creating API provider instances"""
    
    _providers: Dict[APIProviderType, type] = {}
    
    @classmethod
    def register_provider(cls, provider_type: APIProviderType, provider_class: type):
        """Register an API provider implementation"""
        cls._providers[provider_type] = provider_class
    
    @classmethod
    def create_provider(cls, provider_type: APIProviderType, config: APIProviderConfig) -> ExternalAPIProvider:
        """Create an API provider instance"""
        if provider_type not in cls._providers:
            raise ValueError(f"Unknown API provider type: {provider_type}")
        
        provider_class = cls._providers[provider_type]
        return provider_class(config)
    
    @classmethod
    def get_available_providers(cls) -> List[APIProviderType]:
        """Get list of registered providers"""
        return list(cls._providers.keys())


# Utility functions for filter transformation
def extract_range_values(value: Any) -> Optional[Dict[str, Any]]:
    """Extract min/max values from various range formats"""
    if isinstance(value, dict):
        if "min" in value or "max" in value:
            return {"min": value.get("min"), "max": value.get("max")}
    elif isinstance(value, str) and "-" in value:
        # Handle range strings like "1000-10000"
        try:
            parts = value.split("-")
            if len(parts) == 2:
                return {"min": int(parts[0]), "max": int(parts[1])}
        except ValueError:
            pass
    return None


def extract_list_values(value: Any) -> Optional[List[str]]:
    """Extract list values from various formats"""
    if isinstance(value, list):
        return [str(v) for v in value]
    elif isinstance(value, str):
        return [value]
    return None


def normalize_boolean_value(value: Any) -> Optional[bool]:
    """Normalize boolean values from various formats"""
    if isinstance(value, bool):
        return value
    elif isinstance(value, str):
        if value.lower() in ["true", "yes", "1"]:
            return True
        elif value.lower() in ["false", "no", "0"]:
            return False
    return None
