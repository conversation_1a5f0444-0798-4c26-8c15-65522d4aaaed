"""
Frontend Filter Schemas - Fixed Format
This module defines the EXACT format that frontend sends and will NEVER change.
"""
from typing import List, Dict, Any, Optional, Union
from pydantic import BaseModel, Field
from enum import Enum


class FilterTypeEnum(str, Enum):
    """Filter UI types from frontend"""
    RADIO_BUTTON = "radio-button"
    CHECKBOX = "checkbox" 
    MULTILEVEL_CHECKBOX = "multilevel-checkbox"
    ENTER_VALUE = "enter-value"


class PlatformEnum(str, Enum):
    """Platform types"""
    INSTAGRAM = "instagram"
    YOUTUBE = "youtube"
    TIKTOK = "tiktok"


class OptionForEnum(str, Enum):
    """Filter target types"""
    CREATOR = "creator"
    AUDIENCE = "audience"


class FilterOption(BaseModel):
    """Individual filter option"""
    label: str = Field(..., description="Display label")
    value: str = Field(..., description="Filter value")
    description: Optional[str] = Field("", description="Option description")


class SubOption(BaseModel):
    """Sub-option for multilevel filters"""
    subOptionName: str = Field(..., description="Sub-category name")
    subOptionType: str = Field(..., description="Sub-category type")
    collapsed: Optional[bool] = Field(True, description="Initially collapsed")
    checkboxEnabled: Optional[bool] = Field(True, description="Checkbox enabled")
    subOptions: List[FilterOption] = Field(..., description="Sub-options list")


class FilterDefinition(BaseModel):
    """Individual filter definition - FIXED FORMAT"""
    name: str = Field(..., description="Filter name")
    type: FilterTypeEnum = Field(..., description="Filter UI type")
    minmax: Optional[bool] = Field(False, description="Has min/max range")
    icon: Optional[str] = Field(None, description="Filter icon")
    enterValue: Optional[bool] = Field(False, description="User enters value")
    searchBox: Optional[bool] = Field(False, description="Has search box")
    placeholder: Optional[str] = Field("", description="Placeholder text")
    options: Optional[List[Union[FilterOption, SubOption]]] = Field(None, description="Filter options")


class FilterGroup(BaseModel):
    """Filter group - FIXED FORMAT from frontend"""
    optionName: str = Field(..., description="Filter group name")
    optionFor: OptionForEnum = Field(..., description="Creator or audience filter")
    channel: PlatformEnum = Field(..., description="Platform channel")
    filters: List[FilterDefinition] = Field(..., description="Filters in group")


class FrontendFilterDefinitions(BaseModel):
    """Complete filter definitions from frontend - FIXED FORMAT"""
    filterGroups: List[FilterGroup] = Field(..., description="All filter groups")


# ============ FILTER SELECTION SCHEMAS ============

class FilterValue(BaseModel):
    """Flexible filter value that can handle different formats"""
    pass


class RangeValue(FilterValue):
    """Range filter value (min/max)"""
    min: Optional[Union[int, float]] = Field(None, description="Minimum value")
    max: Optional[Union[int, float]] = Field(None, description="Maximum value")


class ListValue(FilterValue):
    """List of selected values"""
    values: List[str] = Field(..., description="Selected values")


class SingleValue(FilterValue):
    """Single selected value"""
    value: Union[str, int, float, bool] = Field(..., description="Selected value")


class TextValue(FilterValue):
    """Text input value"""
    text: str = Field(..., description="Entered text")


# Union type for all possible filter values
FilterValueType = Union[RangeValue, ListValue, SingleValue, TextValue, str, int, float, bool, List[str], Dict[str, Any]]


class FrontendFilterSelections(BaseModel):
    """User's filter selections from frontend - FIXED FORMAT"""
    channel: PlatformEnum = Field(..., description="Selected platform")
    optionFor: OptionForEnum = Field(..., description="Creator or audience")
    filters: Dict[str, FilterValueType] = Field(..., description="User selected filter values")
    page: Optional[int] = Field(1, description="Page number")
    pageSize: Optional[int] = Field(20, description="Results per page")
    sortBy: Optional[str] = Field(None, description="Sort field")
    sortOrder: Optional[str] = Field("desc", description="Sort order")


class FrontendSearchRequest(BaseModel):
    """Complete search request from frontend - FIXED FORMAT"""
    searchQuery: Optional[str] = Field(None, description="Text search query")
    filterSelections: FrontendFilterSelections = Field(..., description="Filter selections")
    includeExternal: Optional[bool] = Field(True, description="Include external API results")
    cachePreference: Optional[str] = Field("prefer", description="Cache preference")


# ============ RESPONSE SCHEMAS ============

class CreatorProfile(BaseModel):
    """Standardized creator profile response"""
    id: str = Field(..., description="Profile ID")
    external_id: Optional[str] = Field(None, description="External API ID")
    platform_username: str = Field(..., description="Platform username")
    full_name: Optional[str] = Field(None, description="Full name")
    platform: str = Field(..., description="Platform name")
    url: Optional[str] = Field(None, description="Profile URL")
    image_url: Optional[str] = Field(None, description="Profile image URL")
    follower_count: Optional[int] = Field(None, description="Follower count")
    engagement_rate: Optional[float] = Field(None, description="Engagement rate")
    is_verified: Optional[bool] = Field(None, description="Verified status")
    location: Optional[str] = Field(None, description="Location")
    category: Optional[str] = Field(None, description="Primary category")
    data_source: str = Field(..., description="Data source (internal/external)")


class SearchMetadata(BaseModel):
    """Search result metadata"""
    total_count: int = Field(..., description="Total results found")
    page: int = Field(..., description="Current page")
    page_size: int = Field(..., description="Results per page")
    total_pages: int = Field(..., description="Total pages")
    execution_time_ms: int = Field(..., description="Query execution time")
    cache_hit: bool = Field(..., description="Cache hit status")
    external_api_calls: int = Field(..., description="External API calls made")
    data_sources: List[str] = Field(..., description="Data sources used")


class FrontendSearchResponse(BaseModel):
    """Search response for frontend - FIXED FORMAT"""
    profiles: List[CreatorProfile] = Field(..., description="Creator profiles")
    metadata: SearchMetadata = Field(..., description="Search metadata")
    applied_filters: Dict[str, Any] = Field(..., description="Applied filters summary")
