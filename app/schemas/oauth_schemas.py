"""
OAuth schemas for CreatorVerse Backend.
"""
from typing import Literal, Optional

from pydantic import BaseModel, Field


class OAuthInitiateRequest(BaseModel):
    """Schema for initiating OAuth flow"""
    provider: Literal["google", "facebook", "instagram"] = Field(
        ..., description="OAuth provider name"
    )
    role_uuid: str = Field(
        ..., description="UUID of the role for the user (influencer or brand_user)"
    )


class OAuthInitiateResponse(BaseModel):
    """Response schema for OAuth initiation"""
    auth_url: str = Field(..., description="URL to redirect the user to for authentication")
    state: str = Field(..., description="CSRF state token to verify callback")


class OAuthCallbackPayload(BaseModel):
    """Schema for OAuth callback state data storage"""
    provider: str
    role_uuid: str


class OAuthProviderTokens(BaseModel):
    """Provider tokens from OAuth flow"""
    access_token: str
    refresh_token: Optional[str] = None
    expires_in: Optional[int] = None
    token_type: Optional[str] = None
    scope: Optional[str] = None


class OAuthProviderProfile(BaseModel):
    """User profile data from OAuth provider"""
    provider_id: str
    email: Optional[str] = None
    name: Optional[str] = None
    given_name: Optional[str] = None
    family_name: Optional[str] = None
    picture: Optional[str] = None
    is_email_verified: bool = False
