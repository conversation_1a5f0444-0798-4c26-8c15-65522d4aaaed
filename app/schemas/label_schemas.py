"""
Label-related schemas for CreatorVerse Backend.
Handles global labels and brand-specific label management.
"""
from typing import Optional, List, Dict, Any
from uuid import UUID

from pydantic import BaseModel, Field, ConfigDict


# ──────────────────────────────────────────────────────────────────────────────
# REQUEST SCHEMAS
# ──────────────────────────────────────────────────────────────────────────────

class CreateLabelRequest(BaseModel):
    """Schema for creating a new global label"""
    name: str = Field(..., min_length=1, max_length=100, description="Label name")
    description: Optional[str] = Field(None, max_length=500, description="Label description")
    color: Optional[str] = Field(None, pattern=r'^#[0-9A-Fa-f]{6}$', description="Hex color code")


class UpdateLabelRequest(BaseModel):
    """Schema for updating a global label"""
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="Label name")
    description: Optional[str] = Field(None, max_length=500, description="Label description")
    color: Optional[str] = Field(None, pattern=r'^#[0-9A-Fa-f]{6}$', description="Hex color code")


class AssignLabelsToEntryRequest(BaseModel):
    """Schema for assigning labels to an influencer entry"""
    label_ids: List[UUID] = Field(..., description="List of global label IDs to assign")


class BulkAssignLabelsRequest(BaseModel):
    """Schema for bulk assigning labels to multiple entries"""
    entry_ids: List[UUID] = Field(..., description="List of entry IDs")
    label_ids: List[UUID] = Field(..., description="List of global label IDs to assign")


# ──────────────────────────────────────────────────────────────────────────────
# RESPONSE SCHEMAS
# ──────────────────────────────────────────────────────────────────────────────

class GlobalLabelResponse(BaseModel):
    """Schema for a global label"""
    model_config = ConfigDict(from_attributes=True)

    id: str
    name: str
    description: Optional[str] = None
    color: str = "#6B7280"
    usage_count: int = 0
    created_at: str
    updated_at: str


class BrandLabelResponse(BaseModel):
    """Schema for a brand-specific label with usage stats"""
    model_config = ConfigDict(from_attributes=True)

    id: str
    brand_id: str
    global_label: GlobalLabelResponse
    usage_count: int = 0
    created_by: str
    created_at: str
    updated_at: str


class LabelAssignmentResponse(BaseModel):
    """Schema for a label assignment to an entry"""
    model_config = ConfigDict(from_attributes=True)

    id: str
    entry_id: str
    global_label: GlobalLabelResponse
    assigned_by: str
    assigned_at: str


class LabelsListResponse(BaseModel):
    """Schema for a list of labels"""
    model_config = ConfigDict(from_attributes=True)

    labels: List[GlobalLabelResponse]
    total: int


class BrandLabelsListResponse(BaseModel):
    """Schema for a list of brand-specific labels"""
    model_config = ConfigDict(from_attributes=True)

    labels: List[BrandLabelResponse]
    total: int


class LabelAutocompleteResponse(BaseModel):
    """Schema for label autocomplete suggestions"""
    model_config = ConfigDict(from_attributes=True)

    suggestions: List[GlobalLabelResponse]
    total: int


class LabelStatsResponse(BaseModel):
    """Schema for label usage statistics"""
    model_config = ConfigDict(from_attributes=True)

    label_id: str
    label_name: str
    global_usage: int
    brand_usage: int
    recent_usage: int  # usage in last 30 days


# ──────────────────────────────────────────────────────────────────────────────
# UTILITY SCHEMAS
# ──────────────────────────────────────────────────────────────────────────────

class LabelFilters(BaseModel):
    """Schema for filtering labels"""
    search: Optional[str] = None  # Search in name and description
    color: Optional[str] = None  # Filter by color
    min_usage: Optional[int] = None  # Minimum usage count
    max_usage: Optional[int] = None  # Maximum usage count


class BrandLabelFilters(BaseModel):
    """Schema for filtering brand-specific labels"""
    search: Optional[str] = None  # Search in name and description
    color: Optional[str] = None  # Filter by color
    min_usage: Optional[int] = None  # Minimum usage count within brand
    max_usage: Optional[int] = None  # Maximum usage count within brand
    created_by: Optional[UUID] = None  # Filter by creator


class GenericSuccessResponse(BaseModel):
    """Generic success response"""
    success: bool = True
    message: str


class BulkOperationResponse(BaseModel):
    """Schema for bulk operation results"""
    model_config = ConfigDict(from_attributes=True)

    success: bool
    total_processed: int
    successful: int
    failed: int
    errors: List[str] = []
    message: str
