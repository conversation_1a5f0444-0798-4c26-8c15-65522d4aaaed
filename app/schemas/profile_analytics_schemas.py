"""
Profile Analytics API Schemas for CreatorVerse Discovery
"""
from datetime import datetime
from typing import List, Optional, Dict, Any, Union
from pydantic import BaseModel, Field, validator
from enum import Enum

from app.schemas.filter_schemas import PlatformEnum


class DateRangeEnum(str, Enum):
    """Date range options for analytics"""
    THIRTY_DAYS = "30d"
    NINETY_DAYS = "90d"
    ONE_YEAR = "1y"
    ALL_TIME = "all"


class PerformanceMetrics(BaseModel):
    """Basic performance metrics"""
    followers: int = Field(..., description="Current follower count")
    follower_growth: float = Field(0.0, description="Follower growth percentage")
    average_impressions: int = Field(0, description="Average impressions per post")
    average_likes: float = Field(0.0, description="Average likes per post")
    average_comments: float = Field(0.0, description="Average comments per post")
    average_views: float = Field(0.0, description="Average views per post")
    average_reach: int = Field(0, description="Average reach per post")
    engagement_rate: float = Field(0.0, description="Overall engagement rate")


class ReputationHistoryItem(BaseModel):
    """Historical reputation data point"""
    month: str = Field(..., description="Month in YYYY-MM format")
    follower_count: int = Field(..., description="Follower count for that month")
    subscriber_count: Optional[int] = Field(None, description="Subscriber count (YouTube)")
    following_count: int = Field(0, description="Following count")
    average_likes: float = Field(0.0, description="Average likes for that month")
    total_views: Optional[int] = Field(None, description="Total views for that month")
    average_views: Optional[float] = Field(None, description="Average views per post")
    average_comments: Optional[float] = Field(None, description="Average comments")
    total_likes: Optional[int] = Field(None, description="Total likes for that month")


class ContentItem(BaseModel):
    """Individual content item"""
    type: str = Field(..., description="Content type (VIDEO, IMAGE, REELS)")
    url: str = Field(..., description="Content URL")
    title: Optional[str] = Field(None, description="Content title")
    description: Optional[str] = Field(None, description="Content description")
    thumbnail_url: Optional[str] = Field(None, description="Thumbnail URL")
    published_at: datetime = Field(..., description="Publication date")
    engagement: Dict[str, Any] = Field(..., description="Engagement metrics")
    mentions: Optional[List[Dict[str, str]]] = Field(None, description="Mentioned accounts")


class AudienceCountryData(BaseModel):
    """Audience country distribution"""
    code: str = Field(..., description="Country code (ISO)")
    value: float = Field(..., description="Percentage of audience")


class AudienceAgeGroup(BaseModel):
    """Audience age group distribution"""
    range: str = Field(..., description="Age range (e.g., '25-34')")
    percentage: float = Field(..., description="Percentage of audience")


class AudienceGenderData(BaseModel):
    """Audience gender distribution"""
    gender: str = Field(..., description="Gender category")
    percentage: float = Field(..., description="Percentage of audience")


class AudienceLanguageData(BaseModel):
    """Audience language preferences"""
    language: str = Field(..., description="Language code")
    percentage: float = Field(..., description="Percentage of audience")


class AudienceInterest(BaseModel):
    """Audience interest data"""
    name: str = Field(..., description="Interest category")
    score: Optional[float] = Field(None, description="Interest score")


class BrandAffinityData(BaseModel):
    """Brand affinity information"""
    brand: str = Field(..., description="Brand name")
    affinity_score: float = Field(..., description="Affinity score")
    engagement_rate: Optional[float] = Field(None, description="Engagement with brand content")


class AudienceDemographics(BaseModel):
    """Comprehensive audience demographics"""
    countries: List[AudienceCountryData] = Field(default_factory=list)
    states: Optional[List[Dict[str, Any]]] = Field(None, description="State distribution")
    cities: Optional[List[Dict[str, Any]]] = Field(None, description="City distribution")
    age_groups: List[AudienceAgeGroup] = Field(default_factory=list)
    gender_distribution: List[AudienceGenderData] = Field(default_factory=list)
    languages: List[AudienceLanguageData] = Field(default_factory=list)
    credibility_score: float = Field(0.0, description="Audience credibility score")


class AudienceInsights(BaseModel):
    """Detailed audience behavioral insights"""
    interests: List[AudienceInterest] = Field(default_factory=list)
    brand_affinity: List[BrandAffinityData] = Field(default_factory=list)
    follower_types: Optional[Dict[str, float]] = Field(None, description="Follower type distribution")
    engagement_patterns: Optional[Dict[str, Any]] = Field(None, description="Engagement behavior patterns")


class HashtagData(BaseModel):
    """Hashtag usage data"""
    name: str = Field(..., description="Hashtag name")
    value: float = Field(..., description="Usage frequency percentage")


class MentionData(BaseModel):
    """Mention data"""
    name: str = Field(..., description="Mentioned account")
    value: float = Field(..., description="Mention frequency percentage")


class PricingExplanation(BaseModel):
    """Pricing factor explanation"""
    level: str = Field(..., description="Factor level (High, Medium, Low)")
    description: str = Field(..., description="Explanation of the factor")


class SponsoredContentAnalysis(BaseModel):
    """Sponsored content performance analysis"""
    sponsored_contents: List[ContentItem] = Field(default_factory=list)
    sponsored_posts_performance: float = Field(0.0, description="Overall sponsored content performance")
    estimated_pricing: Optional[Dict[str, Any]] = Field(None, description="Pricing recommendations")
    price_explanations: Optional[Dict[str, PricingExplanation]] = Field(None, description="Pricing factors")


class SimilarCreator(BaseModel):
    """Similar creator recommendation"""
    id: str = Field(..., description="Creator ID")
    platform_username: str = Field(..., description="Username")
    full_name: Optional[str] = Field(None, description="Full name")
    follower_count: int = Field(..., description="Follower count")
    engagement_rate: float = Field(..., description="Engagement rate")
    similarity_score: float = Field(..., description="Similarity score (0-1)")
    image_url: Optional[str] = Field(None, description="Profile image URL")
    platform: str = Field(..., description="Platform name")


# API Request Schemas
class ProfileAnalyticsRequest(BaseModel):
    """Request schema for profile analytics"""
    include_audience: bool = Field(True, description="Include audience demographics")
    include_content_analysis: bool = Field(True, description="Include content analysis")
    refresh_external: bool = Field(False, description="Force refresh from external API")
    date_range: Optional[DateRangeEnum] = Field(None, description="Date range for analytics")


# API Response Schemas
class BasicProfileAnalyticsResponse(BaseModel):
    """Basic profile analytics response"""
    profile_id: str = Field(..., description="Profile ID")
    platform: str = Field(..., description="Platform name")
    platform_username: str = Field(..., description="Username")
    full_name: Optional[str] = Field(None, description="Full name")
    image_url: Optional[str] = Field(None, description="Profile image URL")
    is_verified: bool = Field(False, description="Verification status")
    performance_metrics: PerformanceMetrics = Field(..., description="Performance metrics")
    reputation_history: List[ReputationHistoryItem] = Field(default_factory=list)
    top_contents: List[ContentItem] = Field(default_factory=list)
    top_hashtags: List[HashtagData] = Field(default_factory=list)
    top_mentions: List[MentionData] = Field(default_factory=list)
    content_performance_score: float = Field(0.0, description="Content performance score")
    last_updated: datetime = Field(..., description="Last update timestamp")
    data_source: str = Field("internal", description="Data source")


class AudienceDemographicsResponse(BaseModel):
    """Audience demographics response"""
    profile_id: str = Field(..., description="Profile ID")
    demographics: AudienceDemographics = Field(..., description="Audience demographics")
    last_updated: datetime = Field(..., description="Last update timestamp")


class AudienceInsightsResponse(BaseModel):
    """Audience insights response"""
    profile_id: str = Field(..., description="Profile ID")
    insights: AudienceInsights = Field(..., description="Audience behavioral insights")
    last_updated: datetime = Field(..., description="Last update timestamp")


class SponsoredContentResponse(BaseModel):
    """Sponsored content analysis response"""
    profile_id: str = Field(..., description="Profile ID")
    analysis: SponsoredContentAnalysis = Field(..., description="Sponsored content analysis")
    last_updated: datetime = Field(..., description="Last update timestamp")


class SimilarCreatorsResponse(BaseModel):
    """Similar creators response"""
    profile_id: str = Field(..., description="Profile ID")
    similar_creators: List[SimilarCreator] = Field(default_factory=list)
    total_count: int = Field(0, description="Total number of similar creators")
    last_updated: datetime = Field(..., description="Last update timestamp")
