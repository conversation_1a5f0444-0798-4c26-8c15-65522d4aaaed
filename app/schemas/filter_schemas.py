"""
Pydantic schemas for filter definitions in CreatorVerse Discovery
Based on Excel analysis of filter requirements
"""
from typing import Optional, List, Union, Dict, Any
from enum import Enum
from pydantic import BaseModel, Field, validator


class FilterOperator(str, Enum):
    """Operators for filter comparisons"""
    EQUALS = "eq"
    NOT_EQUALS = "ne"
    GREATER_THAN = "gt"
    GREATER_THAN_OR_EQUAL = "gte"
    LESS_THAN = "lt"
    LESS_THAN_OR_EQUAL = "lte"
    IN = "in"
    NOT_IN = "not_in"
    CONTAINS = "contains"
    BETWEEN = "between"


class FilterType(str, Enum):
    """Types of filters"""
    RANGE = "range"
    CATEGORICAL = "categorical"
    MULTI_SELECT = "multi_select"
    BOOLEAN = "boolean"
    TEXT = "text"


class GenderEnum(str, Enum):
    """Gender filter options"""
    MALE = "male"
    FEMALE = "female"
    OTHER = "other"
    ANY = "any"


class AgeGroupEnum(str, Enum):
    """Age group filter options"""
    TEEN = "teen"          # 13-19
    YOUNG_ADULT = "young_adult"  # 20-35
    ADULT = "adult"        # 36-55
    SENIOR = "senior"      # 56+


class LocationTierEnum(str, Enum):
    """Location tier filter options"""
    TIER1 = "tier1"
    TIER2 = "tier2"
    TIER3 = "tier3"
    RURAL = "rural"


class FollowerTierEnum(str, Enum):
    """Follower count tier options"""
    NANO = "nano"          # 1K-10K
    MICRO = "micro"        # 10K-100K
    MID = "mid"           # 100K-500K
    MACRO = "macro"       # 500K-1M
    MEGA = "mega"         # 1M+


class EngagementTierEnum(str, Enum):
    """Engagement rate tier options"""
    LOW = "low"           # < 2%
    AVERAGE = "average"   # 2%-5%
    HIGH = "high"         # 5%-10%
    VERY_HIGH = "very_high"  # 10%+


class PlatformEnum(str, Enum):
    """Social media platforms"""
    INSTAGRAM = "instagram"
    YOUTUBE = "youtube"
    TIKTOK = "tiktok"


# Base filter classes
class RangeFilter(BaseModel):
    """Filter for numeric ranges (Min/Max)"""
    min_value: Optional[float] = Field(None, description="Minimum value")
    max_value: Optional[float] = Field(None, description="Maximum value")
    
    @validator('max_value')
    def validate_range(cls, v, values):
        if v is not None and values.get('min_value') is not None:
            if v < values['min_value']:
                raise ValueError('max_value must be greater than or equal to min_value')
        return v


class CategoricalFilter(BaseModel):
    """Filter for categorical values"""
    values: List[str] = Field(..., description="List of allowed values")
    operator: FilterOperator = Field(FilterOperator.IN, description="Filter operator")


class BooleanFilter(BaseModel):
    """Filter for boolean values"""
    value: bool = Field(..., description="Boolean value to filter by")


# Demographic & Identity Filters
class DemographicFilters(BaseModel):
    """Demographic and identity filters"""
    gender: Optional[List[GenderEnum]] = Field(None, description="Gender filter")
    age_group: Optional[List[AgeGroupEnum]] = Field(None, description="Age group filter")
    location_tier: Optional[List[LocationTierEnum]] = Field(None, description="Location tier filter")
    country: Optional[List[str]] = Field(None, description="Country codes (ISO)")
    state: Optional[List[str]] = Field(None, description="State/Province names")
    city: Optional[List[str]] = Field(None, description="City names")
    language: Optional[List[str]] = Field(None, description="Language codes")
    is_verified: Optional[bool] = Field(None, description="Verified account filter")


# Performance Metrics Filters
class PerformanceFilters(BaseModel):
    """Performance metrics filters"""
    follower_count: Optional[RangeFilter] = Field(None, description="Follower count range")
    follower_tier: Optional[List[FollowerTierEnum]] = Field(None, description="Follower tier categories")
    
    average_likes: Optional[RangeFilter] = Field(None, description="Average likes range")
    average_comments: Optional[RangeFilter] = Field(None, description="Average comments range")
    average_views: Optional[RangeFilter] = Field(None, description="Average views range")
    average_reels_views: Optional[RangeFilter] = Field(None, description="Average reels views range")
    
    engagement_rate: Optional[RangeFilter] = Field(None, description="Engagement rate percentage range")
    engagement_tier: Optional[List[EngagementTierEnum]] = Field(None, description="Engagement tier categories")
    
    follower_growth_rate: Optional[RangeFilter] = Field(None, description="Follower growth rate percentage")
    follower_growth_30d: Optional[RangeFilter] = Field(None, description="30-day follower growth")
    follower_growth_90d: Optional[RangeFilter] = Field(None, description="90-day follower growth")
    
    credibility_score: Optional[RangeFilter] = Field(None, description="Credibility score range (0-100)")
    content_count: Optional[RangeFilter] = Field(None, description="Total content count range")


# Content & Category Filters
class ContentFilters(BaseModel):
    """Content and category filters"""
    primary_category: Optional[List[str]] = Field(None, description="Primary content categories")
    secondary_categories: Optional[List[str]] = Field(None, description="Secondary content categories")
    interests: Optional[List[str]] = Field(None, description="Interest keywords")
    brand_affinity: Optional[List[str]] = Field(None, description="Brand affinity keywords")


# Audience Demographic Filters
class AudienceFilters(BaseModel):
    """Audience demographic filters"""
    audience_age_groups: Optional[Dict[str, RangeFilter]] = Field(None, description="Audience age group percentages")
    audience_gender: Optional[Dict[str, RangeFilter]] = Field(None, description="Audience gender percentages")
    audience_locations: Optional[List[str]] = Field(None, description="Audience location countries")
    audience_interests: Optional[List[str]] = Field(None, description="Audience interest categories")


# Platform & Technical Filters
class PlatformFilters(BaseModel):
    """Platform and technical filters"""
    platform: Optional[List[PlatformEnum]] = Field(None, description="Social media platforms")
    account_type: Optional[List[str]] = Field(None, description="Account types (personal, business, creator)")
    data_source: Optional[List[str]] = Field(None, description="Data sources (internal, phyllo, manual)")
    profile_status: Optional[List[str]] = Field(None, description="Profile status (active, inactive)")
    data_quality_score: Optional[RangeFilter] = Field(None, description="Data quality score range (0-1)")
    last_updated: Optional[int] = Field(None, description="Days since last update")


# Main Discovery Filter
class DiscoveryFilters(BaseModel):
    """Complete filter set for creator discovery"""
    demographic: Optional[DemographicFilters] = Field(None, description="Demographic and identity filters")
    performance: Optional[PerformanceFilters] = Field(None, description="Performance metrics filters")
    content: Optional[ContentFilters] = Field(None, description="Content and category filters")
    audience: Optional[AudienceFilters] = Field(None, description="Audience demographic filters")
    platform: Optional[PlatformFilters] = Field(None, description="Platform and technical filters")


# Saved Filter Set Schemas
class SavedFilterSetCreate(BaseModel):
    """Schema for creating a saved filter set"""
    name: str = Field(..., min_length=1, max_length=255, description="Filter set name")
    description: Optional[str] = Field(None, description="Filter set description")
    filters: DiscoveryFilters = Field(..., description="Filter criteria")
    is_public: bool = Field(False, description="Whether filter set is public")
    is_favorite: bool = Field(False, description="Whether filter set is marked as favorite")


class SavedFilterSetUpdate(BaseModel):
    """Schema for updating a saved filter set"""
    name: Optional[str] = Field(None, min_length=1, max_length=255, description="Filter set name")
    description: Optional[str] = Field(None, description="Filter set description")
    filters: Optional[DiscoveryFilters] = Field(None, description="Filter criteria")
    is_public: Optional[bool] = Field(None, description="Whether filter set is public")
    is_favorite: Optional[bool] = Field(None, description="Whether filter set is marked as favorite")


class SavedFilterSetResponse(BaseModel):
    """Schema for saved filter set response"""
    id: str = Field(..., description="Filter set ID")
    name: str = Field(..., description="Filter set name")
    description: Optional[str] = Field(None, description="Filter set description")
    filters: DiscoveryFilters = Field(..., description="Filter criteria")
    is_public: bool = Field(..., description="Whether filter set is public")
    is_favorite: bool = Field(..., description="Whether filter set is marked as favorite")
    use_count: int = Field(..., description="Number of times used")
    last_used_at: Optional[str] = Field(None, description="Last used timestamp")
    created_at: str = Field(..., description="Creation timestamp")
    updated_at: str = Field(..., description="Last update timestamp")

    class Config:
        from_attributes = True


# Filter Metadata for Frontend
class FilterOption(BaseModel):
    """Single filter option for frontend"""
    label: str = Field(..., description="Display label")
    value: str = Field(..., description="Filter value")
    description: Optional[str] = Field(None, description="Filter description")


class FilterDefinition(BaseModel):
    """Filter definition for frontend rendering"""
    key: str = Field(..., description="Filter key")
    label: str = Field(..., description="Display label")
    type: FilterType = Field(..., description="Filter type")
    options: Optional[List[FilterOption]] = Field(None, description="Available options for categorical filters")
    min_value: Optional[float] = Field(None, description="Minimum value for range filters")
    max_value: Optional[float] = Field(None, description="Maximum value for range filters")
    default_value: Optional[Any] = Field(None, description="Default value")
    required: bool = Field(False, description="Whether filter is required")
    multiple: bool = Field(False, description="Whether multiple values allowed")


class FilterCategory(BaseModel):
    """Filter category for organizing filters in frontend"""
    key: str = Field(..., description="Category key")
    label: str = Field(..., description="Category display label")
    description: Optional[str] = Field(None, description="Category description")
    filters: List[FilterDefinition] = Field(..., description="Filters in this category")


class FilterMetadata(BaseModel):
    """Complete filter metadata for frontend"""
    categories: List[FilterCategory] = Field(..., description="Filter categories")
    total_filters: int = Field(..., description="Total number of filters")
    version: str = Field("1.0", description="Filter schema version")


# Validation helpers
def validate_filter_combinations(filters: DiscoveryFilters) -> bool:
    """Validate filter combinations for logical consistency"""
    # Add custom validation logic here
    # For example, ensure follower_tier and follower_count don't conflict
    if filters.performance:
        if (filters.performance.follower_tier and 
            filters.performance.follower_count and
            filters.performance.follower_count.min_value):
            # Could add logic to validate tier vs count consistency
            pass
    
    return True


# Default filter options
DEFAULT_FILTER_CATEGORIES = [
    FilterCategory(
        key="demographic",
        label="Demographic & Identity",
        description="Creator demographic and identity filters",
        filters=[
            FilterDefinition(
                key="gender",
                label="Gender",
                type=FilterType.MULTI_SELECT,
                options=[
                    FilterOption(label="Male", value="male"),
                    FilterOption(label="Female", value="female"),
                    FilterOption(label="Other", value="other"),
                    FilterOption(label="Any", value="any")
                ],
                multiple=True
            ),
            FilterDefinition(
                key="age_group",
                label="Age Group",
                type=FilterType.MULTI_SELECT,
                options=[
                    FilterOption(label="Teen (13-19)", value="teen"),
                    FilterOption(label="Young Adult (20-35)", value="young_adult"),
                    FilterOption(label="Adult (36-55)", value="adult"),
                    FilterOption(label="Senior (56+)", value="senior")
                ],
                multiple=True
            ),
            FilterDefinition(
                key="location_tier",
                label="Location Tier",
                type=FilterType.MULTI_SELECT,
                options=[
                    FilterOption(label="Tier 1", value="tier1"),
                    FilterOption(label="Tier 2", value="tier2"),
                    FilterOption(label="Tier 3", value="tier3"),
                    FilterOption(label="Rural Areas", value="rural")
                ],
                multiple=True
            ),
            FilterDefinition(
                key="is_verified",
                label="Verified Account",
                type=FilterType.BOOLEAN,
                default_value=None
            )
        ]
    ),
    FilterCategory(
        key="performance",
        label="Performance Metrics",
        description="Creator performance and engagement filters",
        filters=[
            FilterDefinition(
                key="follower_count",
                label="Follower Count",
                type=FilterType.RANGE,
                min_value=0,
                max_value=********0
            ),
            FilterDefinition(
                key="engagement_rate",
                label="Engagement Rate (%)",
                type=FilterType.RANGE,
                min_value=0,
                max_value=100
            ),
            FilterDefinition(
                key="average_likes",
                label="Average Likes",
                type=FilterType.RANGE,
                min_value=0,
                max_value=********
            ),
            FilterDefinition(
                key="credibility_score",
                label="Credibility Score",
                type=FilterType.RANGE,
                min_value=0,
                max_value=100
            )
        ]
    )
]
