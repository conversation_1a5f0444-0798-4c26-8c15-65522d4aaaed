#!/usr/bin/env python3

import json
import random
import uuid
from datetime import datetime, timedelta
from faker import Faker
import string

# Initialize Faker
fake = Faker()

def generate_uuid():
    return str(uuid.uuid4())

def generate_image_url():
    return f"https://imgp.sptds.icu/v2?{fake.sha256()[:100]}"

def generate_platform():
    platforms = [
        {
            "id": "9bb8913b-ddd9-430b-a66a-d74d846e6c66",
            "name": "Instagram",
            "logo_url": "https://cdn.insightiq.ai/platforms_logo/logos/logo_instagram.png"
        },
        {
            "id": "8bb8913b-ddd9-430b-a66a-d74d846e6c67",
            "name": "YouTube",
            "logo_url": "https://cdn.insightiq.ai/platforms_logo/logos/logo_youtube.png"
        },
        {
            "id": "7bb8913b-ddd9-430b-a66a-d74d846e6c68",
            "name": "TikTok",
            "logo_url": "https://cdn.insightiq.ai/platforms_logo/logos/logo_tiktok.png"
        }
    ]
    return random.choice(platforms)

def generate_hashtag():
    tags = ["ad", "grwm", "fashion", "beauty", "lifestyle", "travel", "food", "fitness", "dance",
            "music", "photography", "art", "style", "makeup", "skincare", "trending"]
    return random.choice(tags)

def generate_hashtag_stats():
    tags = [
        "ad", "grwm", "fashion", "beauty", "lifestyle", "travel", 
        "dance", "music", "photography", "art", "style", "makeup",
        "food", "fitness", "family", "fun", "love", "happy", 
        "instagood", "photooftheday", "beautiful", "cute", "followme",
        "like4like", "follow4follow", "picoftheday", "selfie", "summer",
        "instadaily", "friends"
    ]
    sample_size = random.randint(10, min(len(tags), 20))
    return [
        {"name": tag, "value": round(random.uniform(2, 50), 4)}
        for tag in random.sample(tags, sample_size)
    ]

def generate_mentions():
    return [
        {
            "name": fake.user_name().lower(),
            "value": round(random.uniform(2, 15), 4)
        }
        for _ in range(random.randint(15, 25))
    ]

def generate_interests():
    interests = ["Beauty & Cosmetics", "Fashion", "Lifestyle", "Travel", "Food & Dining",
                "Fitness & Wellness", "Entertainment", "Technology", "Gaming", "Music"]
    return [{"name": interest} for interest in random.sample(interests, random.randint(1, 3))]

def generate_location_data():
    """Generate consistent location data for profile and audience"""
    # Define major countries with their cities and typical audience distribution
    location_data = {
        "United States": {
            "cities": ["New York", "Los Angeles", "Chicago", "Houston", "Phoenix", "Philadelphia"],
            "audience_weight": random.uniform(25, 45)
        },
        "United Kingdom": {
            "cities": ["London", "Manchester", "Birmingham", "Liverpool", "Leeds", "Sheffield"],
            "audience_weight": random.uniform(10, 20)
        },
        "Canada": {
            "cities": ["Toronto", "Vancouver", "Montreal", "Calgary", "Ottawa", "Edmonton"],
            "audience_weight": random.uniform(8, 15)
        },
        "Australia": {
            "cities": ["Sydney", "Melbourne", "Brisbane", "Perth", "Adelaide", "Gold Coast"],
            "audience_weight": random.uniform(5, 12)
        },
        "Germany": {
            "cities": ["Berlin", "Munich", "Hamburg", "Cologne", "Frankfurt", "Stuttgart"],
            "audience_weight": random.uniform(3, 8)
        },
        "France": {
            "cities": ["Paris", "Lyon", "Marseille", "Toulouse", "Nice", "Nantes"],
            "audience_weight": random.uniform(3, 8)
        },
        "Brazil": {
            "cities": ["São Paulo", "Rio de Janeiro", "Brasília", "Salvador", "Fortaleza", "Belo Horizonte"],
            "audience_weight": random.uniform(2, 6)
        },
        "India": {
            "cities": ["Mumbai", "Delhi", "Bangalore", "Hyderabad", "Chennai", "Kolkata"],
            "audience_weight": random.uniform(2, 6)
        }
    }

    # Select primary country (where creator is based)
    primary_country = random.choice(list(location_data.keys()))
    primary_city = random.choice(location_data[primary_country]["cities"])

    # Generate audience distribution ensuring it adds up to ~100%
    audience_locations = []
    remaining_percentage = 100.0

    # Primary country gets the highest percentage
    primary_percentage = location_data[primary_country]["audience_weight"]
    audience_locations.append({
        "country": primary_country,
        "value": round(primary_percentage, 2)
    })
    remaining_percentage -= primary_percentage

    # Add other countries
    other_countries = [c for c in location_data.keys() if c != primary_country]
    random.shuffle(other_countries)

    for i, country in enumerate(other_countries[:4]):  # Limit to top 5 countries total
        if i == 3:  # Last country gets remaining percentage
            percentage = remaining_percentage
        else:
            max_percentage = min(location_data[country]["audience_weight"], remaining_percentage * 0.7)
            percentage = round(random.uniform(2, max_percentage), 2)
            remaining_percentage -= percentage

        if percentage > 0:
            audience_locations.append({
                "country": country,
                "value": percentage
            })

    return {
        "profile_location": {
            "city": primary_city,
            "country": primary_country
        },
        "audience_locations": audience_locations
    }

def generate_audience_demographics():
    """Generate comprehensive audience demographics"""
    # Gender and age distribution
    gender_age = {}
    genders = ["MALE", "FEMALE"]
    age_ranges = ["13-17", "18-24", "25-34", "35-44", "45-54", "55+"]

    total_percentage = 100.0
    for gender in genders:
        for i, age_range in enumerate(age_ranges):
            if gender == "FEMALE" and age_range == "55+":  # Last entry gets remaining
                percentage = total_percentage
            else:
                # Realistic distribution - younger demographics typically higher
                if age_range in ["18-24", "25-34"]:
                    percentage = round(random.uniform(15, 25), 2)
                elif age_range in ["13-17", "35-44"]:
                    percentage = round(random.uniform(8, 15), 2)
                else:
                    percentage = round(random.uniform(2, 8), 2)
                total_percentage -= percentage

            gender_age[f"{gender.lower()}_{age_range}"] = percentage

    # Language distribution
    languages = [
        {"code": "en", "value": round(random.uniform(60, 85), 2)},
        {"code": "es", "value": round(random.uniform(5, 15), 2)},
        {"code": "fr", "value": round(random.uniform(2, 8), 2)},
        {"code": "de", "value": round(random.uniform(1, 5), 2)},
        {"code": "pt", "value": round(random.uniform(1, 5), 2)}
    ]

    # Normalize language percentages to 100%
    total_lang = sum(lang["value"] for lang in languages)
    for lang in languages:
        lang["value"] = round((lang["value"] / total_lang) * 100, 2)

    # Income distribution
    income_ranges = ["0-25000", "25001-50000", "50001-75000", "75001-100000", "100001+"]
    income_distribution = []
    remaining_income = 100.0

    for i, income_range in enumerate(income_ranges):
        if i == len(income_ranges) - 1:
            percentage = remaining_income
        else:
            percentage = round(random.uniform(15, 25), 2)
            remaining_income -= percentage

        income_distribution.append({
            "range": income_range,
            "value": percentage
        })

    # Device distribution
    device_distribution = [
        {"type": "MOBILE", "value": round(random.uniform(70, 85), 2)},
        {"type": "DESKTOP", "value": round(random.uniform(10, 20), 2)},
        {"type": "TABLET", "value": round(random.uniform(5, 15), 2)}
    ]

    # Normalize device percentages
    total_device = sum(device["value"] for device in device_distribution)
    for device in device_distribution:
        device["value"] = round((device["value"] / total_device) * 100, 2)

    return {
        "gender_age": gender_age,
        "languages": languages,
        "income": income_distribution,
        "devices": device_distribution
    }

def generate_content():
    types = ["VIDEO", "IMAGE", "REEL", "STORY"]
    content_type = random.choice(types)
    username = fake.user_name()
    description = fake.text(max_nb_chars=100)

    # Generate comprehensive engagement metrics
    base_views = random.randint(10000, 500000)
    engagement_rate = random.uniform(0.02, 0.08)

    engagement = {
        "like_count": int(base_views * engagement_rate * random.uniform(0.8, 1.2)),
        "comment_count": int(base_views * engagement_rate * random.uniform(0.05, 0.15)),
        "view_count": base_views if content_type in ["VIDEO", "REEL"] else None,
        "play_count": base_views if content_type in ["VIDEO", "REEL"] else None,
        "save_count": int(base_views * engagement_rate * random.uniform(0.1, 0.3)) if random.random() > 0.3 else None,
        "share_count": int(base_views * engagement_rate * random.uniform(0.05, 0.2)) if random.random() > 0.4 else None,
        "reach": int(base_views * random.uniform(1.2, 2.5)) if random.random() > 0.5 else None,
        "impressions": int(base_views * random.uniform(1.5, 3.0)) if random.random() > 0.5 else None
    }

    return {
        "type": content_type,
        "url": f"https://www.instagram.com/p/{fake.bothify('?#?#?#?#?#??')}",
        "title": None if random.random() > 0.3 else fake.sentence(),
        "description": description,
        "thumbnail_url": generate_image_url(),
        "engagement": engagement,
        "mentions": [] if random.random() > 0.7 else [username],
        "published_at": (datetime.now() - timedelta(days=random.randint(1, 365))).isoformat()
    }

def generate_reputation_history():
    current_followers = random.randint(1000000, 2000000)
    history = []
    for i in range(7):
        month = (datetime.now() + timedelta(days=30*i)).strftime("%Y-%m")
        history.append({
            "month": month,
            "follower_count": int(current_followers * (1 + random.uniform(-0.05, 0.05))),
            "subscriber_count": None,
            "following_count": random.randint(400, 800),
            "average_likes": random.randint(30000, 60000),
            "total_views": None,
            "average_views": None,
            "average_comments": None,
            "total_likes": None
        })
    return history

def generate_single_profile():
    platform = generate_platform()
    external_id = str(random.randint(1000000000, 9999999999))
    username = fake.user_name()
    full_name = fake.name()

    # Generate consistent location data
    location_data = generate_location_data()

    # Generate audience demographics
    audience_demographics = generate_audience_demographics()

    # Generate platform-specific URL
    platform_name = platform["name"].lower()
    if platform_name == "instagram":
        profile_url = f"https://www.instagram.com/{username}"
    elif platform_name == "youtube":
        profile_url = f"https://www.youtube.com/@{username}"
    elif platform_name == "tiktok":
        profile_url = f"https://www.tiktok.com/@{username}"
    else:
        profile_url = f"https://www.{platform_name}.com/{username}"

    profile = {
        "id": generate_uuid(),
        "work_platform": platform,
        "profile": {
            "external_id": external_id,
            "platform_username": username,
            "url": profile_url,
            "image_url": generate_image_url(),
            "full_name": full_name,
            "introduction": fake.text(max_nb_chars=50),
            "content_count": random.randint(500, 5000),
            "sponsored_posts_performance": round(random.uniform(0.1, 0.5), 6),
            "is_verified": random.random() > 0.7,
            "platform_account_type": "CREATOR",
            "gender": random.choice(["MALE", "FEMALE"]),
            "age_group": random.choice(["18-24", "25-34", "35-44"]),
            "language": "en",
            "follower_count": random.randint(100000, 2000000),
            "subscriber_count": None,
            "average_likes": random.randint(20000, 100000),
            "average_dislikes": None,
            "average_comments": random.randint(50, 500),
            "average_views": None,
            "average_reels_views": random.randint(200000, 1000000),
            "engagement_rate": round(random.uniform(0.01, 0.1), 6),
            "reputation_history": generate_reputation_history(),
            "location": location_data["profile_location"],
            "top_hashtags": generate_hashtag_stats(),
            "top_mentions": generate_mentions(),
            "top_interests": generate_interests(),
            "top_contents": [generate_content() for _ in range(15)],
            "recent_contents": [generate_content() for _ in range(10)]
        },
        "audience": {
            "gender_age": audience_demographics["gender_age"],
            "languages": audience_demographics["languages"],
            "locations": location_data["audience_locations"],
            "income": audience_demographics["income"],
            "devices": audience_demographics["devices"]
        },
        "pricing": {
            "post_type": {
                "reel": {
                    "price_range": {
                        "min": random.randint(500, 2000),
                        "max": random.randint(2000, 8000)
                    }
                },
                "story": {
                    "price_range": {
                        "min": random.randint(200, 1000),
                        "max": random.randint(1000, 4000)
                    }
                },
                "static_post": {
                    "price_range": {
                        "min": random.randint(300, 1500),
                        "max": random.randint(1500, 6000)
                    }
                },
                "carousel": {
                    "price_range": {
                        "min": random.randint(400, 1800),
                        "max": random.randint(1800, 7000)
                    }
                }
            }
        }
    }

    return profile

def generate_profiles(count=10000):
    profiles = []
    for i in range(count):
        if i > 0 and i % 100 == 0:
            print(f"Generated {i} profiles...")
        profiles.append(generate_single_profile())
    return profiles

if __name__ == "__main__":
    import sys
    count = 10000
    if len(sys.argv) > 1:
        try:
            count = int(sys.argv[1])
        except Exception:
            print("Invalid count argument, using default 10000")
    print(f"Generating {count} profile analytics records...")
    profiles = generate_profiles(count)
    
    output_file = "generated_profile_analytics.json"
    print(f"Writing profiles to {output_file}...")
    with open(output_file, "w") as f:
        json.dump(profiles, f, indent=2)
    print("Done!")
