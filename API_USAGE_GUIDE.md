# Phyllo API Proxy - Usage Guide

This guide demonstrates how to use the enhanced Phyllo API proxy with comprehensive validation and security features.

## Base URL
```
http://localhost:8000/v1/social/creator
```

## Authentication
Currently, no authentication is required for testing. In production, you would add API key authentication.

## Supported Platforms
- `instagram` - Instagram profiles
- `youtube` - YouTube channels  
- `tiktok` - TikTok profiles
- `twitter` - Twitter profiles
- `twitch` - Twitch channels

## API Endpoints

### 1. Profile Analytics
Get detailed analytics for a specific creator profile.

**Endpoint:** `POST /profile/analytics`

**Request Body:**
```json
{
  "profile_id": "cfa27c2b-6451-4433-99d7-98230dd1a1d6",
  "include_audience": true,
  "include_content": true,
  "include_pricing": true
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "cfa27c2b-6451-4433-99d7-98230dd1a1d6",
    "work_platform": {
      "id": "platform_001",
      "name": "Instagram"
    },
    "profile": {
      "external_id": "creator123",
      "platform_username": "johndoe",
      "full_name": "<PERSON> <PERSON>e",
      "follower_count": 50000,
      "engagement_rate": 0.045,
      "is_verified": true
    },
    "audience": {...},
    "content": {...},
    "pricing": {...}
  },
  "message": "Profile analytics retrieved successfully"
}
```

### 2. Quick Search
Search for creator profiles with basic filters.

**Endpoint:** `POST /profile/quick-search`

**Request Body:**
```json
{
  "platform": "instagram",
  "username": "john",
  "follower_count_min": 10000,
  "follower_count_max": 100000,
  "engagement_rate_min": 0.02,
  "engagement_rate_max": 0.1,
  "verified_only": true,
  "limit": 10
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "profiles": [
      {
        "id": "profile_001",
        "platform": "Instagram",
        "username": "johndoe",
        "full_name": "John Doe",
        "follower_count": 50000,
        "engagement_rate": 0.045,
        "is_verified": true,
        "image_url": "https://example.com/image.jpg",
        "url": "https://instagram.com/johndoe"
      }
    ],
    "total_found": 25,
    "returned": 10,
    "limit": 10,
    "filters_applied": {
      "platform": "instagram",
      "follower_count_min": 10000,
      "verified_only": true
    }
  },
  "message": "Found 25 profiles matching criteria"
}
```

### 3. Advanced Search
Search with comprehensive filters and pagination.

**Endpoint:** `POST /profile/search`

**Request Body:**
```json
{
  "platform": "youtube",
  "follower_count_min": 5000,
  "follower_count_max": 500000,
  "engagement_rate_min": 0.02,
  "engagement_rate_max": 0.15,
  "interests": ["gaming", "technology"],
  "location": "united states",
  "gender": "MALE",
  "age_group": "25-34",
  "verified_only": false,
  "content_type": "VIDEO",
  "limit": 10,
  "offset": 0
}
```

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "profile_002",
      "work_platform": {
        "id": "platform_002",
        "name": "YouTube"
      },
      "profile": {
        "external_id": "channel123",
        "platform_username": "techgamer",
        "full_name": "Tech Gamer",
        "follower_count": 75000,
        "engagement_rate": 0.065,
        "is_verified": false,
        "gender": "MALE",
        "age_group": "25-34",
        "location": {
          "city": "San Francisco",
          "country": "United States"
        }
      },
      "top_interests": [
        {"name": "gaming"},
        {"name": "technology"}
      ],
      "audience_summary": {
        "top_locations": [...],
        "primary_language": {...},
        "device_breakdown": [...]
      }
    }
  ],
  "pagination": {
    "offset": 0,
    "limit": 10,
    "total": 45,
    "returned": 10,
    "has_next": true,
    "has_prev": false
  }
}
```

## Sample API Calls

### Using cURL

#### 1. Profile Analytics
```bash
curl -X POST "http://localhost:8000/v1/social/creator/profile/analytics" \
  -H "Content-Type: application/json" \
  -d '{
    "profile_id": "cfa27c2b-6451-4433-99d7-98230dd1a1d6",
    "include_audience": true,
    "include_content": true
  }'
```

#### 2. Quick Search
```bash
curl -X POST "http://localhost:8000/v1/social/creator/profile/quick-search" \
  -H "Content-Type: application/json" \
  -d '{
    "platform": "instagram",
    "follower_count_min": 10000,
    "verified_only": true,
    "limit": 5
  }'
```

#### 3. Advanced Search
```bash
curl -X POST "http://localhost:8000/v1/social/creator/profile/search" \
  -H "Content-Type: application/json" \
  -d '{
    "platform": "youtube",
    "interests": ["gaming"],
    "gender": "MALE",
    "limit": 10,
    "offset": 0
  }'
```

### Using Python Requests

```python
import requests

BASE_URL = "http://localhost:8000/v1/social/creator"

# Profile Analytics
response = requests.post(f"{BASE_URL}/profile/analytics", json={
    "profile_id": "cfa27c2b-6451-4433-99d7-98230dd1a1d6",
    "include_audience": True
})
print(response.json())

# Quick Search
response = requests.post(f"{BASE_URL}/profile/quick-search", json={
    "platform": "instagram",
    "follower_count_min": 10000,
    "limit": 5
})
print(response.json())

# Advanced Search
response = requests.post(f"{BASE_URL}/profile/search", json={
    "platform": "youtube",
    "interests": ["gaming", "technology"],
    "gender": "MALE",
    "limit": 10,
    "offset": 0
})
print(response.json())
```

### Using JavaScript/Node.js

```javascript
const axios = require('axios');

const BASE_URL = 'http://localhost:8000/v1/social/creator';

// Profile Analytics
async function getProfileAnalytics() {
  try {
    const response = await axios.post(`${BASE_URL}/profile/analytics`, {
      profile_id: 'cfa27c2b-6451-4433-99d7-98230dd1a1d6',
      include_audience: true
    });
    console.log(response.data);
  } catch (error) {
    console.error('Error:', error.response.data);
  }
}

// Quick Search
async function quickSearch() {
  try {
    const response = await axios.post(`${BASE_URL}/profile/quick-search`, {
      platform: 'instagram',
      follower_count_min: 10000,
      limit: 5
    });
    console.log(response.data);
  } catch (error) {
    console.error('Error:', error.response.data);
  }
}

// Advanced Search
async function advancedSearch() {
  try {
    const response = await axios.post(`${BASE_URL}/profile/search`, {
      platform: 'youtube',
      interests: ['gaming'],
      gender: 'MALE',
      limit: 10,
      offset: 0
    });
    console.log(response.data);
  } catch (error) {
    console.error('Error:', error.response.data);
  }
}
```

## Validation Rules

### Platform-Specific Constraints
- **Instagram**: Max 500M followers, supports REEL, STORY, STATIC_POST, CAROUSEL
- **YouTube**: Max 200M subscribers, supports VIDEO only
- **TikTok**: Max 150M followers, supports VIDEO, REEL
- **Twitter**: Max 130M followers, supports STATIC_POST only
- **Twitch**: Max 20M followers, supports VIDEO only

### Input Validation
- Profile IDs must be at least 8 characters
- Usernames are sanitized for security
- Follower counts must be non-negative
- Engagement rates must be between 0.0 and 1.0
- Maximum 20 interests per search
- Gender must be MALE or FEMALE
- Age groups: 13-17, 18-24, 25-34, 35-44, 45-54, 55-64, 65+

### Error Responses
```json
{
  "success": false,
  "message": "Validation failed",
  "error_code": 422,
  "errors": [
    "Platform must be one of: instagram, youtube, tiktok, twitter, twitch",
    "follower_count_min cannot be greater than follower_count_max"
  ]
}
```

## Security Features
- Input sanitization prevents XSS attacks
- SQL injection prevention
- Rate limiting (configurable)
- Content safety validation
- Response data validation
