"""
JWT Permissions Integration & Cache Optimization Implementation Guide

This document demonstrates how to use the enhanced JWT authentication system with 
permission-based authorization and optimized caching.
"""

# =============================================================================
# 1. MIDDLEWARE SETUP
# =============================================================================

# In your main.py or application startup:
from app.middleware.auth_middleware import setup_auth_middleware

def create_app():
    app = FastAPI()
    
    # Setup authentication middleware (injects RBAC services into request state)
    setup_auth_middleware(app)
    
    return app


# =============================================================================
# 2. BASIC JWT AUTHENTICATION
# =============================================================================

from fastapi import APIRouter, Depends
from app.core.auth_dependencies import get_current_user

router = APIRouter()

@router.get("/profile")
async def get_user_profile(
    current_user: dict = Depends(get_current_user)
):
    """Protected endpoint that requires valid JWT token."""
    return {
        "user_id": current_user["user_id"],
        "session_id": current_user["session_id"],
        "message": "Access granted"
    }


# =============================================================================
# 3. PERMISSION-BASED AUTHORIZATION
# =============================================================================

from app.core.auth_dependencies import require_permissions, require_any_permission

@router.post("/users")
async def create_user(
    current_user: dict = Depends(require_permissions(["user.create"]))
):
    """Endpoint that requires specific permission."""
    return {"message": "User created successfully"}


@router.get("/admin/dashboard")
async def admin_dashboard(
    current_user: dict = Depends(require_permissions(["admin.dashboard", "admin.read"]))
):
    """Endpoint that requires multiple permissions (user must have ALL)."""
    return {"message": "Admin dashboard data"}


@router.get("/content/manage")
async def manage_content(
    current_user: dict = Depends(require_any_permission(["content.create", "content.update", "content.delete"]))
):
    """Endpoint that requires any of the specified permissions (user needs ANY ONE)."""
    return {"message": "Content management access granted"}


# =============================================================================
# 4. ROLE-BASED AUTHORIZATION
# =============================================================================

from app.core.auth_dependencies import require_role

@router.get("/brand/dashboard")
async def brand_dashboard(
    current_user: dict = Depends(require_role(["brand_admin", "brand_owner"]))
):
    """Endpoint that requires specific roles."""
    return {"message": "Brand dashboard access granted"}


# =============================================================================
# 5. CONVENIENCE DEPENDENCIES FOR COMMON PATTERNS
# =============================================================================

from app.core.auth_dependencies import (
    require_admin,
    require_user_management, 
    require_brand_access,
    require_influencer_access
)

@router.delete("/users/{user_id}")
async def delete_user(
    user_id: str,
    current_user: dict = Depends(require_admin)
):
    """Admin-only endpoint."""
    return {"message": f"User {user_id} deleted"}

@router.get("/brands/{brand_id}")
async def get_brand(
    brand_id: str,
    current_user: dict = Depends(require_brand_access)
):
    """Endpoint requiring brand access permissions."""
    return {"brand_id": brand_id, "data": "..."}


# =============================================================================
# 6. USER WITH PERMISSIONS LOADED
# =============================================================================

from app.core.auth_dependencies import get_current_user_with_permissions

@router.get("/my-permissions")
async def get_my_permissions(
    current_user: dict = Depends(get_current_user_with_permissions)
):
    """Get current user with their permissions pre-loaded."""
    return {
        "user_id": current_user["user_id"],
        "permissions": list(current_user["permissions"])
    }


# =============================================================================
# 7. CUSTOM PERMISSION LOGIC IN ENDPOINTS
# =============================================================================

from fastapi import Request, HTTPException, status

@router.post("/brands/{brand_id}/content")
async def create_brand_content(
    brand_id: str,
    request: Request,
    current_user: dict = Depends(get_current_user)
):
    """Endpoint with custom permission logic."""
    rbac_service = getattr(request.state, 'rbac_service', None)
    
    if rbac_service:
        # Check if user can create content for this specific brand
        user_id = UUID(current_user["user_id"])
        
        # Option 1: Check specific permission
        can_create = await rbac_service.check_user_permission(user_id, "content.create")
        
        # Option 2: Batch check multiple permissions
        permission_results = await rbac_service.batch_check_permissions(
            user_id, 
            ["content.create", "brand.manage", f"brand.{brand_id}.write"]
        )
        
        if not any(permission_results.values()):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions for this brand"
            )
    
    return {"message": "Content created"}


# =============================================================================
# 8. TOKEN BLACKLISTING
# =============================================================================

from app.core.security import blacklist_token

@router.post("/logout")
async def logout(
    request: Request,
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """Logout endpoint that blacklists the token."""
    redis_client = getattr(request.state, 'redis_client', None)
    
    if redis_client:
        # Blacklist the token
        success = await blacklist_token(credentials.credentials, redis_client)
        if success:
            return {"message": "Logged out successfully"}
    
    return {"message": "Logout processed"}


# =============================================================================
# 9. RBAC SERVICE USAGE (Direct)
# =============================================================================

@router.get("/roles/{role_name}/permissions")
async def get_role_permissions(
    role_name: str,
    request: Request,
    current_user: dict = Depends(require_admin)
):
    """Get permissions for a specific role."""
    rbac_service = getattr(request.state, 'rbac_service', None)
    
    if not rbac_service:
        raise HTTPException(status_code=500, detail="RBAC service unavailable")
    
    # Get role ID by name
    role_id = await rbac_service.get_role_id_by_name(role_name)
    if not role_id:
        raise HTTPException(status_code=404, detail="Role not found")
    
    # Get role permissions
    permissions = await rbac_service.get_role_permissions(role_id)
    
    return {
        "role_name": role_name,
        "role_id": str(role_id),
        "permissions": list(permissions)
    }


# =============================================================================
# 10. CACHE OPTIMIZATION FEATURES
# =============================================================================

@router.post("/rbac/refresh-cache")
async def refresh_rbac_cache(
    request: Request,
    current_user: dict = Depends(require_admin)
):
    """Force refresh RBAC cache (admin only)."""
    rbac_service = getattr(request.state, 'rbac_service', None)
    
    if not rbac_service:
        raise HTTPException(status_code=500, detail="RBAC service unavailable")
    
    success = await rbac_service.refresh_rbac_cache()
    
    return {"success": success, "message": "RBAC cache refreshed"}


@router.delete("/users/{user_id}/cache")
async def invalidate_user_cache(
    user_id: str,
    request: Request,
    current_user: dict = Depends(require_user_management)
):
    """Invalidate specific user's cache."""
    rbac_service = getattr(request.state, 'rbac_service', None)
    
    if rbac_service:
        success = await rbac_service.invalidate_user_roles_cache(UUID(user_id))
        return {"success": success, "message": f"User {user_id} cache invalidated"}
    
    return {"success": False, "message": "RBAC service unavailable"}


# =============================================================================
# 11. ERROR HANDLING EXAMPLES
# =============================================================================

from fastapi import HTTPException

@router.get("/protected-with-fallback")
async def protected_with_fallback(
    request: Request,
    current_user: dict = Depends(get_current_user)
):
    """Endpoint with graceful fallback when services are unavailable."""
    rbac_service = getattr(request.state, 'rbac_service', None)
    redis_client = getattr(request.state, 'redis_client', None)
    
    user_permissions = set()
    
    if rbac_service:
        try:
            user_permissions = await rbac_service.get_user_roles(UUID(current_user["user_id"]))
        except Exception as e:
            # Log error but continue with empty permissions
            logger.warning(f"Failed to get user permissions: {e}")
    
    return {
        "user_id": current_user["user_id"],
        "permissions_available": rbac_service is not None,
        "redis_available": redis_client is not None,
        "permissions_count": len(user_permissions)
    }


# =============================================================================
# 12. IMPLEMENTATION NOTES
# =============================================================================

"""
KEY IMPROVEMENTS IMPLEMENTED:

1. JWT Token Validation with Session Verification
   - Validates JWT tokens and checks session status in Redis/DB
   - Supports token blacklisting for logout functionality
   - Graceful fallback when Redis is unavailable

2. Permission-Based Authorization
   - Fine-grained permission checking using RBAC service
   - Batch permission checks to reduce Redis round trips
   - Support for both "all required" and "any required" patterns

3. Enhanced Caching
   - Fixed Redis pipeline usage throughout the codebase
   - Improved error handling with database fallbacks
   - Cache invalidation strategies for role/permission changes

4. Middleware Integration
   - Authentication middleware injects services into request state
   - Services available to all route handlers and dependencies
   - Clean separation of concerns

5. Performance Optimizations
   - Batch permission checks reduce database queries
   - Efficient Redis key patterns and TTL management
   - Proper pipeline usage for bulk operations

6. Error Handling & Resilience
   - Graceful degradation when services are unavailable
   - Comprehensive logging for debugging
   - Fallback mechanisms for critical operations

USAGE PATTERNS:

- Use get_current_user for basic JWT authentication
- Use require_permissions for specific permission requirements
- Use require_any_permission for flexible permission checks
- Use require_role for role-based access control
- Use convenience dependencies for common patterns
- Access rbac_service directly for complex logic
- Implement proper error handling and fallbacks

SECURITY CONSIDERATIONS:

- All JWT tokens are validated and can be blacklisted
- Session validation prevents token reuse after logout
- Permission checks are cached but fall back to database
- Services gracefully handle Redis failures
- Comprehensive audit logging for security events
"""
