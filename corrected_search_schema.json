{"work_platform_id": "instagram", "sort_by": {"field": "FOLLOWER_COUNT", "order": "DESCENDING"}, "follower_count": {"min": 1000, "max": 1000000}, "subscriber_count": {"min": 100, "max": 500000}, "content_count": {"min": 10, "max": 1000}, "audience_gender": {"type": "FEMALE", "operator": "GT", "percentage_value": 50}, "creator_gender": "FEMALE", "audience_age": {"min": 18, "max": 35, "percentage_value": 60}, "creator_age": {"min": 18, "max": 45}, "description_keywords": "fashion beauty lifestyle", "is_verified": true, "has_contact_details": true, "specific_contact_details": [{"type": "EMAIL", "preference": "SHOULD_HAVE"}], "last_post_timestamp": "2024-01-01T00:00:00Z", "audience_language": [{"code": "en", "percentage_value": "70"}], "creator_language": {"code": "en"}, "audience_interests": ["fashion", "beauty"], "audience_interest_affinities": [{"value": "fashion", "operation": "GT", "percentage_value": "25"}], "creator_interests": ["fashion"], "audience_brand_affinities": ["nike", "adidas"], "creator_brand_affinities": ["chanel"], "average_likes": {"min": 500, "max": 50000}, "average_views": {"min": 1000, "max": 100000}, "engagement_rate": {"percentage_value": "3.5"}, "has_sponsored_posts": true, "brand_sponsors": ["nike"], "instagram_options": {"reel_views": {"min": 10000, "max": 500000}}, "audience_locations": [{"location_id": "US", "percentage_value": 45.0, "operator": "GT"}], "creator_locations": ["US"], "follower_growth": {"interval": 3, "interval_unit": "MONTH", "operator": "GT", "percentage_value": 10}, "subscriber_growth": {"interval": 6, "interval_unit": "MONTH", "operator": "GT", "percentage_value": 5}, "bio_phrase": "content creator", "hashtags": [{"name": "fashion"}], "mentions": [{"name": "brand"}], "topic_relevance": {"name": ["fashion"], "weight": 0.5, "threshold": 0.55}, "audience_lookalikes": "influencer", "platform_account_type": "CREATOR", "creator_account_type": ["CREATOR"], "creator_lookalikes": "@influencer", "audience_location": [{"name": "United States", "percentage_value": 45, "operator": "GT"}], "limit": 10, "offset": 0, "audience_source": "LIKERS", "total_engagements": {"min": 1000, "max": 50000}, "audience_credibility_category": ["HIGH"], "audience_credibility_score": 0.7, "is_official_artist": false, "has_audience_info": false, "share_count": {"min": 50, "max": 5000}, "save_count": {"min": 100, "max": 10000}, "exclude_private_profiles": false, "creator_age_bracket": "OVER_18", "livestream_options": {"recent_activity": "gaming", "category": "GAMING", "categories_streamed": "2-5", "hours_watched": {"min": 100, "max": 10000}, "average_concurrent_viewers": {"min": 50, "max": 1000}, "absolute_follower_growth": {"min": 100, "max": 5000}, "peak_viewers": {"min": 200, "max": 10000}, "vtubers": {"enabled": true}}}