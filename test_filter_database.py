"""
Simple test to validate the filter system database connectivity and core functionality
"""

import asyncio
import asyncpg
import json
from datetime import datetime

# Database configuration
DATABASE_CONFIG = {
    "host": "************",
    "port": 5432,
    "database": "postgres",
    "user": "postgres",
    "password": "s81JKkaoe42Tm5W"
}

async def test_filter_database():
    """Test database connectivity and filter data"""
    
    print("🔍 Testing CreatorVerse Filter System Database...")
    print("=" * 60)
    
    try:
        # Connect to database
        conn = await asyncpg.connect(**DATABASE_CONFIG)
        print("✅ Database connection successful")
        
        # Test 1: Check if schema exists
        schema_check = await conn.fetchval("""
            SELECT schema_name FROM information_schema.schemata 
            WHERE schema_name = 'filter_catalog'
        """)
        
        if schema_check:
            print("✅ filter_catalog schema exists")
        else:
            print("❌ filter_catalog schema not found")
            return False
        
        # Test 2: Check enums
        enums = await conn.fetch("""
            SELECT typname FROM pg_type t 
            JOIN pg_namespace n ON t.typnamespace = n.oid 
            WHERE nspname = 'filter_catalog' AND typtype = 'e'
        """)
        print(f"✅ Found {len(enums)} enum types: {[e['typname'] for e in enums]}")
        
        # Test 3: Check tables
        tables = await conn.fetch("""
            SELECT table_name FROM information_schema.tables 
            WHERE table_schema = 'filter_catalog'
        """)
        print(f"✅ Found {len(tables)} tables: {[t['table_name'] for t in tables]}")
        
        # Test 4: Check filter groups
        groups = await conn.fetch("""
            SELECT channel, option_for, COUNT(*) as count
            FROM filter_catalog.filter_groups 
            WHERE is_active = true
            GROUP BY channel, option_for
            ORDER BY channel, option_for
        """)
        
        print(f"\n📊 Filter Groups by Platform:")
        for group in groups:
            print(f"   • {group['channel']} {group['option_for']}: {group['count']} groups")
        
        # Test 5: Check filter definitions for Instagram creators
        instagram_filters = await conn.fetch("""
            SELECT fg.name as group_name, fd.name, fd.filter_type, 
                   JSONB_ARRAY_LENGTH(COALESCE(fd.options, '[]'::jsonb)) as option_count
            FROM filter_catalog.filter_definitions fd
            JOIN filter_catalog.filter_groups fg ON fd.group_id = fg.id
            WHERE fg.channel = 'instagram' AND fg.option_for = 'creator' AND fd.is_active = true
            ORDER BY fg.sort_order, fd.sort_order
        """)
        
        print(f"\n📱 Instagram Creator Filters ({len(instagram_filters)} total):")
        for filter_def in instagram_filters:
            print(f"   • {filter_def['group_name']} > {filter_def['name']} ({filter_def['filter_type']}) - {filter_def['option_count']} options")
        
        # Test 6: Check a specific filter's options
        gender_filter = await conn.fetchrow("""
            SELECT fd.name, fd.options
            FROM filter_catalog.filter_definitions fd
            JOIN filter_catalog.filter_groups fg ON fd.group_id = fg.id
            WHERE fg.channel = 'instagram' AND fg.option_for = 'creator' 
            AND fd.name ILIKE 'gender' AND fd.is_active = true
        """)
        
        if gender_filter:
            print(f"\n🎯 Sample Filter - {gender_filter['name']}:")
            options = gender_filter['options']
            if options:
                for i, option in enumerate(options):
                    print(f"   {i+1}. {option.get('label', 'Unknown')} (value: {option.get('value', 'Unknown')})")
        
        # Test 7: Test disabling a channel (Instagram audience filters)
        print(f"\n🔄 Testing Channel Disable Functionality...")
        
        # Get current state
        instagram_audience_groups = await conn.fetch("""
            SELECT id, name, is_active 
            FROM filter_catalog.filter_groups 
            WHERE channel = 'instagram' AND option_for = 'audience'
        """)
        
        print(f"📋 Instagram Audience Groups (before disable):")
        for group in instagram_audience_groups:
            print(f"   • {group['name']}: {'✅ Active' if group['is_active'] else '❌ Inactive'}")
        
        if instagram_audience_groups:
            # Disable all Instagram audience filters
            await conn.execute("""
                UPDATE filter_catalog.filter_groups 
                SET is_active = false, updated_at = CURRENT_TIMESTAMP
                WHERE channel = 'instagram' AND option_for = 'audience'
            """)
            
            # Disable all filter definitions in those groups
            await conn.execute("""
                UPDATE filter_catalog.filter_definitions 
                SET is_active = false, updated_at = CURRENT_TIMESTAMP
                WHERE group_id IN (
                    SELECT id FROM filter_catalog.filter_groups 
                    WHERE channel = 'instagram' AND option_for = 'audience'
                )
            """)
            
            print("🚫 Disabled Instagram audience filters")
            
            # Verify the change
            disabled_check = await conn.fetchval("""
                SELECT COUNT(*) FROM filter_catalog.filter_groups 
                WHERE channel = 'instagram' AND option_for = 'audience' AND is_active = false
            """)
            print(f"✅ Confirmed: {disabled_check} Instagram audience groups disabled")
            
            # Re-enable them (for testing)
            await conn.execute("""
                UPDATE filter_catalog.filter_groups 
                SET is_active = true, updated_at = CURRENT_TIMESTAMP
                WHERE channel = 'instagram' AND option_for = 'audience'
            """)
            
            await conn.execute("""
                UPDATE filter_catalog.filter_definitions 
                SET is_active = true, updated_at = CURRENT_TIMESTAMP
                WHERE group_id IN (
                    SELECT id FROM filter_catalog.filter_groups 
                    WHERE channel = 'instagram' AND option_for = 'audience'
                )
            """)
            
            print("✅ Re-enabled Instagram audience filters")
        
        await conn.close()
        
        print(f"\n🎉 Filter System Database Test Completed Successfully!")
        print("=" * 60)
        print("📋 Summary:")
        print("   ✅ Database connectivity working")
        print("   ✅ Schema and tables properly set up")
        print("   ✅ Filter data populated")
        print("   ✅ Channel disable/enable functionality working")
        print("   ✅ All filter types and options properly configured")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during testing: {str(e)}")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_filter_database())
    exit(0 if success else 1)
