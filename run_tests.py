"""
Test Execution Script for Creatorverse User Backend
Orchestrates execution of all test suites with proper reporting and configuration.
"""
import os
import sys
import time
import asyncio
import subprocess
import argparse
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any

import pytest
import psutil


class TestExecutor:
    """Main test execution orchestrator"""
    
    def __init__(self):
        self.start_time = None
        self.results = {}
        self.test_dir = Path(__file__).parent
        
    def setup_environment(self):
        """Setup test environment variables and dependencies"""
        print("Setting up test environment...")
        
        # Set test environment
        os.environ["ENVIRONMENT"] = "test"
        os.environ["TESTING"] = "true"
        
        # Create test results directory
        results_dir = self.test_dir / "test_results"
        results_dir.mkdir(exist_ok=True)
        
        # Check system resources
        memory_gb = psutil.virtual_memory().total / (1024**3)
        cpu_count = psutil.cpu_count()
        
        print(f"System Resources: {memory_gb:.1f}GB RAM, {cpu_count} CPUs")
        
        if memory_gb < 4:
            print("⚠️  Warning: Less than 4GB RAM available. Some tests may be skipped.")
        
        return results_dir
    
    def run_test_suite(self, test_file: str, suite_name: str, 
                      pytest_args: List[str] = None) -> Dict[str, Any]:
        """Run a specific test suite"""
        print(f"\n{'='*60}")
        print(f"Running {suite_name}")
        print(f"{'='*60}")
        
        if pytest_args is None:
            pytest_args = ["-v", "--tb=short"]
        
        start_time = time.time()
        
        # Build pytest command
        cmd = [
            sys.executable, "-m", "pytest",
            test_file,
            *pytest_args,
            f"--junit-xml=test_results/{suite_name.lower().replace(' ', '_')}_results.xml"
        ]
        
        try:
            # Run the test
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                cwd=self.test_dir
            )
            
            duration = time.time() - start_time
            
            # Parse results
            return {
                "suite": suite_name,
                "file": test_file,
                "duration": duration,
                "return_code": result.returncode,
                "stdout": result.stdout,
                "stderr": result.stderr,
                "success": result.returncode == 0
            }
            
        except Exception as e:
            duration = time.time() - start_time
            return {
                "suite": suite_name,
                "file": test_file,
                "duration": duration,
                "return_code": 1,
                "stdout": "",
                "stderr": str(e),
                "success": False,
                "error": str(e)
            }
    
    def run_all_tests(self, include_performance: bool = True, 
                     include_security: bool = True,
                     parallel: bool = False) -> Dict[str, Any]:
        """Run all test suites"""
        self.start_time = time.time()
        results_dir = self.setup_environment()
        
        # Define test suites
        test_suites = [
            {
                "file": "test_user_service_comprehensive.py",
                "name": "User Service Tests",
                "category": "unit",
                "args": ["-v", "--tb=short"]
            },
            {
                "file": "test_influencer_list_service_comprehensive.py", 
                "name": "Influencer List Service Tests",
                "category": "unit",
                "args": ["-v", "--tb=short"]
            },
            {
                "file": "test_label_service_comprehensive.py",
                "name": "Label Service Tests", 
                "category": "unit",
                "args": ["-v", "--tb=short"]
            },
            {
                "file": "test_authentication_comprehensive.py",
                "name": "Authentication Tests",
                "category": "integration",
                "args": ["-v", "--tb=short"]
            },
            {
                "file": "test_api_integration_comprehensive.py",
                "name": "API Integration Tests",
                "category": "integration", 
                "args": ["-v", "--tb=short"]
            }
        ]
        
        # Add performance tests if requested
        if include_performance:
            test_suites.append({
                "file": "test_performance_comprehensive.py",
                "name": "Performance Tests",
                "category": "performance",
                "args": ["-v", "--tb=short", "-s"]
            })
        
        # Add security tests if requested
        if include_security:
            test_suites.append({
                "file": "test_security_comprehensive.py", 
                "name": "Security Tests",
                "category": "security",
                "args": ["-v", "--tb=short", "-s"]
            })
        
        # Run tests
        all_results = []
        
        if parallel and len(test_suites) > 1:
            # Run tests in parallel (for independent test suites)
            print("Running tests in parallel...")
            # Note: Actual parallel execution would require more sophisticated handling
            # For now, run sequentially but indicate parallel capability
        
        for suite in test_suites:
            print(f"\n🧪 Starting {suite['name']}...")
            
            result = self.run_test_suite(
                suite["file"],
                suite["name"], 
                suite["args"]
            )
            
            all_results.append(result)
            
            # Print immediate feedback
            if result["success"]:
                print(f"✅ {suite['name']} PASSED ({result['duration']:.1f}s)")
            else:
                print(f"❌ {suite['name']} FAILED ({result['duration']:.1f}s)")
                if result.get("stderr"):
                    print(f"Error: {result['stderr'][:200]}...")
        
        # Generate final report
        total_duration = time.time() - self.start_time
        self.generate_final_report(all_results, total_duration, results_dir)
        
        return {
            "total_duration": total_duration,
            "results": all_results,
            "summary": self.get_summary(all_results)
        }
    
    def get_summary(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate test summary statistics"""
        total_suites = len(results)
        passed_suites = len([r for r in results if r["success"]])
        failed_suites = total_suites - passed_suites
        
        total_duration = sum(r["duration"] for r in results)
        
        return {
            "total_suites": total_suites,
            "passed_suites": passed_suites,
            "failed_suites": failed_suites,
            "success_rate": passed_suites / total_suites if total_suites > 0 else 0,
            "total_duration": total_duration
        }
    
    def generate_final_report(self, results: List[Dict[str, Any]], 
                            total_duration: float, results_dir: Path):
        """Generate comprehensive test report"""
        summary = self.get_summary(results)
        
        print(f"\n{'='*80}")
        print("COMPREHENSIVE TEST EXECUTION REPORT")
        print(f"{'='*80}")
        print(f"Execution Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"Total Duration: {total_duration:.1f} seconds")
        print(f"")
        print(f"SUMMARY:")
        print(f"  Total Test Suites: {summary['total_suites']}")
        print(f"  Passed: {summary['passed_suites']}")
        print(f"  Failed: {summary['failed_suites']}")
        print(f"  Success Rate: {summary['success_rate']:.1%}")
        print(f"")
        
        print("DETAILED RESULTS:")
        for result in results:
            status = "✅ PASS" if result["success"] else "❌ FAIL"
            print(f"  {status} {result['suite']:<30} ({result['duration']:.1f}s)")
            
            if not result["success"] and result.get("stderr"):
                print(f"    Error: {result['stderr'][:100]}...")
        
        # Write detailed report to file
        report_file = results_dir / f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        
        with open(report_file, 'w') as f:
            f.write("CREATORVERSE USER BACKEND - COMPREHENSIVE TEST REPORT\n")
            f.write("="*60 + "\n\n")
            f.write(f"Execution Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Total Duration: {total_duration:.1f} seconds\n\n")
            
            f.write("SUMMARY:\n")
            f.write(f"  Total Test Suites: {summary['total_suites']}\n")
            f.write(f"  Passed: {summary['passed_suites']}\n")
            f.write(f"  Failed: {summary['failed_suites']}\n")
            f.write(f"  Success Rate: {summary['success_rate']:.1%}\n\n")
            
            f.write("DETAILED RESULTS:\n")
            for result in results:
                f.write(f"\n{result['suite']}:\n")
                f.write(f"  Status: {'PASS' if result['success'] else 'FAIL'}\n")
                f.write(f"  Duration: {result['duration']:.1f}s\n")
                f.write(f"  Return Code: {result['return_code']}\n")
                
                if result.get("stdout"):
                    f.write(f"  Output:\n{result['stdout']}\n")
                
                if result.get("stderr"):
                    f.write(f"  Errors:\n{result['stderr']}\n")
        
        print(f"\nDetailed report saved to: {report_file}")
        
        # Generate HTML report if possible
        try:
            self.generate_html_report(results, summary, total_duration, results_dir)
        except Exception as e:
            print(f"Could not generate HTML report: {e}")
        
        return report_file
    
    def generate_html_report(self, results: List[Dict[str, Any]], 
                           summary: Dict[str, Any], total_duration: float, 
                           results_dir: Path):
        """Generate HTML test report"""
        html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <title>CreatorVerse Test Report</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .header {{ background: #2563eb; color: white; padding: 20px; border-radius: 8px; }}
        .summary {{ background: #f8fafc; padding: 20px; margin: 20px 0; border-radius: 8px; }}
        .test-suite {{ margin: 10px 0; padding: 15px; border-radius: 8px; }}
        .pass {{ background: #dcfce7; border-left: 4px solid #16a34a; }}
        .fail {{ background: #fef2f2; border-left: 4px solid #dc2626; }}
        .metrics {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; }}
        .metric {{ background: white; padding: 15px; border-radius: 8px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); }}
        .metric-value {{ font-size: 24px; font-weight: bold; color: #2563eb; }}
        pre {{ background: #f8fafc; padding: 10px; border-radius: 4px; overflow-x: auto; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>CreatorVerse User Backend - Test Report</h1>
        <p>Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
    </div>
    
    <div class="summary">
        <h2>Test Summary</h2>
        <div class="metrics">
            <div class="metric">
                <div class="metric-value">{summary['total_suites']}</div>
                <div>Total Suites</div>
            </div>
            <div class="metric">
                <div class="metric-value">{summary['passed_suites']}</div>
                <div>Passed</div>
            </div>
            <div class="metric">
                <div class="metric-value">{summary['failed_suites']}</div>
                <div>Failed</div>
            </div>
            <div class="metric">
                <div class="metric-value">{summary['success_rate']:.1%}</div>
                <div>Success Rate</div>
            </div>
            <div class="metric">
                <div class="metric-value">{total_duration:.1f}s</div>
                <div>Total Duration</div>
            </div>
        </div>
    </div>
    
    <h2>Test Suite Results</h2>
"""
        
        for result in results:
            status_class = "pass" if result["success"] else "fail"
            status_text = "PASSED" if result["success"] else "FAILED"
            
            html_content += f"""
    <div class="test-suite {status_class}">
        <h3>{result['suite']} - {status_text}</h3>
        <p><strong>Duration:</strong> {result['duration']:.1f} seconds</p>
        <p><strong>File:</strong> {result['file']}</p>
"""
            
            if result.get("stderr"):
                html_content += f"""
        <details>
            <summary>Error Details</summary>
            <pre>{result['stderr']}</pre>
        </details>
"""
            
            html_content += "</div>\n"
        
        html_content += """
</body>
</html>
"""
        
        html_file = results_dir / f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
        with open(html_file, 'w') as f:
            f.write(html_content)
        
        print(f"HTML report saved to: {html_file}")
    
    def run_quick_tests(self):
        """Run a subset of tests for quick feedback"""
        print("Running quick test suite...")
        
        quick_suites = [
            {
                "file": "test_user_service_comprehensive.py",
                "name": "User Service Tests (Quick)",
                "args": ["-v", "--tb=short", "-k", "test_user_creation or test_user_retrieval"]
            }
        ]
        
        results = []
        for suite in quick_suites:
            result = self.run_test_suite(
                suite["file"],
                suite["name"],
                suite["args"]
            )
            results.append(result)
        
        return results
    
    def run_smoke_tests(self):
        """Run smoke tests to verify basic functionality"""
        print("Running smoke tests...")
        
        smoke_suites = [
            {
                "file": "test_user_service_comprehensive.py",
                "name": "User Service Smoke Tests",
                "args": ["-v", "--tb=short", "-k", "test_create_user_success"]
            },
            {
                "file": "test_authentication_comprehensive.py",
                "name": "Authentication Smoke Tests", 
                "args": ["-v", "--tb=short", "-k", "test_create_jwt_token"]
            }
        ]
        
        results = []
        for suite in smoke_suites:
            result = self.run_test_suite(
                suite["file"],
                suite["name"],
                suite["args"]
            )
            results.append(result)
        
        return results


def main():
    """Main entry point for test execution"""
    parser = argparse.ArgumentParser(description="CreatorVerse Test Execution")
    parser.add_argument("--suite", choices=["all", "unit", "integration", "performance", "security", "quick", "smoke"],
                       default="all", help="Test suite to run")
    parser.add_argument("--no-performance", action="store_true", 
                       help="Skip performance tests")
    parser.add_argument("--no-security", action="store_true",
                       help="Skip security tests")
    parser.add_argument("--parallel", action="store_true",
                       help="Run tests in parallel (experimental)")
    parser.add_argument("--verbose", "-v", action="store_true",
                       help="Verbose output")
    
    args = parser.parse_args()
    
    executor = TestExecutor()
    
    print("🚀 CreatorVerse User Backend - Comprehensive Test Suite")
    print("="*60)
    
    try:
        if args.suite == "quick":
            results = executor.run_quick_tests()
        elif args.suite == "smoke":
            results = executor.run_smoke_tests()
        elif args.suite == "all":
            results = executor.run_all_tests(
                include_performance=not args.no_performance,
                include_security=not args.no_security,
                parallel=args.parallel
            )
        else:
            print(f"Running {args.suite} tests...")
            # Implement specific suite running logic
            results = executor.run_all_tests()
        
        # Exit with appropriate code
        if isinstance(results, dict) and results.get("summary"):
            summary = results["summary"]
            if summary["failed_suites"] == 0:
                print("\n🎉 All tests passed!")
                sys.exit(0)
            else:
                print(f"\n💥 {summary['failed_suites']} test suite(s) failed!")
                sys.exit(1)
        else:
            # For quick/smoke tests
            failed_count = len([r for r in results if not r["success"]])
            if failed_count == 0:
                print("\n🎉 All tests passed!")
                sys.exit(0)
            else:
                print(f"\n💥 {failed_count} test suite(s) failed!")
                sys.exit(1)
                
    except KeyboardInterrupt:
        print("\n\n⏹️  Test execution interrupted by user")
        sys.exit(130)
    except Exception as e:
        print(f"\n💥 Test execution failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
