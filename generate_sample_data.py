#!/usr/bin/env python3

import random
import uuid
from datetime import datetime
import json
import os
import sys

# Add explicit print statements for debugging
print("Starting the sample data generator script")
print(f"Current working directory: {os.getcwd()}")

# Sample platforms
PLATFORMS = [
    {"id": "platform_1", "name": "YouTube"},
    {"id": "platform_2", "name": "Instagram"},
    {"id": "platform_3", "name": "TikTok"},
    {"id": "platform_4", "name": "Twitter"},
]

# Sample user data
def generate_users(count=5):
    print(f"Generating {count} sample users")
    users = []
    for i in range(count):
        user_id = f"user_{uuid.uuid4().hex[:8]}"
        user = {
            "id": user_id,
            "name": f"Test User {i+1}",
            "email": f"user{i+1}@example.com",
            "status": random.choice(["active", "inactive", "pending"]),
            "platform": random.choice([p["name"] for p in PLATFORMS]) if random.random() > 0.3 else None,
        }
        users.append(user)
    return users

# Sample account data
def generate_accounts(users, count_per_user=2):
    print(f"Generating accounts for {len(users)} users")
    accounts = []
    for user in users:
        platforms = random.sample(PLATFORMS, min(count_per_user, len(PLATFORMS)))
        for platform in platforms:
            account_id = f"account_{uuid.uuid4().hex[:8]}"
            account = {
                "id": account_id,
                "user_id": user["id"],
                "platform_id": platform["id"],
                "status": random.choice(["connected", "disconnected", "expired"]),
                "connected_at": datetime.now().isoformat(),
            }
            accounts.append(account)
    return accounts

# Sample data for accounts
def generate_data(accounts, count_per_account=3):
    print(f"Generating data for {len(accounts)} accounts")
    all_data = []
    data_types = ["profile", "posts", "engagement", "income"]
    
    for account in accounts:
        for _ in range(count_per_account):
            data_type = random.choice(data_types)
            data_id = f"data_{uuid.uuid4().hex[:8]}"
            
            # Generate content based on data type
            if data_type == "profile":
                content = {
                    "username": f"creator_{random.randint(1000, 9999)}",
                    "followers": random.randint(100, 1000000),
                    "following": random.randint(10, 1000),
                    "bio": f"Sample creator bio {random.randint(1, 100)}"
                }
            elif data_type == "posts":
                content = {
                    "total_posts": random.randint(10, 500),
                    "recent_posts": [
                        {
                            "id": f"post_{i}",
                            "title": f"Sample Post {i}",
                            "likes": random.randint(10, 10000),
                            "views": random.randint(100, 100000)
                        } for i in range(1, 4)
                    ]
                }
            elif data_type == "engagement":
                content = {
                    "avg_views": random.randint(1000, 100000),
                    "avg_likes": random.randint(100, 10000),
                    "avg_comments": random.randint(10, 1000),
                    "engagement_rate": round(random.uniform(0.5, 15), 2)
                }
            else:  # income
                content = {
                    "estimated_earnings": round(random.uniform(100, 10000), 2),
                    "revenue_sources": [
                        {
                            "source": "ads",
                            "percentage": random.randint(20, 80)
                        },
                        {
                            "source": "sponsorships",
                            "percentage": random.randint(10, 70)
                        }
                    ]
                }
                
            data_entry = {
                "id": data_id,
                "account_id": account["id"],
                "data_type": data_type,
                "content": content,
                "created_at": datetime.now().isoformat()
            }
            all_data.append(data_entry)
    
    return all_data

# Generate sample data
def generate_sample_data():
    print("Starting to generate sample data...")
    
    # Create the sample_data directory in the project root
    sample_data_dir = os.path.join(os.getcwd(), "sample_data")
    print(f"Creating sample data directory at: {sample_data_dir}")
    
    try:
        os.makedirs(sample_data_dir, exist_ok=True)
        print(f"Sample data directory created/verified at: {sample_data_dir}")
    except Exception as e:
        print(f"Error creating directory: {e}")
        return
    
    users = generate_users(5)
    accounts = generate_accounts(users)
    data = generate_data(accounts)
    
    # Save to JSON files for reference
    try:
        users_file = os.path.join(sample_data_dir, "users.json")
        print(f"Writing users to: {users_file}")
        with open(users_file, "w") as f:
            json.dump(users, f, indent=2)
        
        accounts_file = os.path.join(sample_data_dir, "accounts.json")  
        print(f"Writing accounts to: {accounts_file}")
        with open(accounts_file, "w") as f:
            json.dump(accounts, f, indent=2)
        
        data_file = os.path.join(sample_data_dir, "data.json")
        print(f"Writing data to: {data_file}")
        with open(data_file, "w") as f:
            json.dump(data, f, indent=2)
        
        print(f"Generated {len(users)} users, {len(accounts)} accounts, and {len(data)} data entries")
        print("Sample data has been saved to the 'sample_data' directory")
    except Exception as e:
        print(f"Error writing files: {e}")

def insert_one_profile_from_json():
    import psycopg2
    # Load one record from profile_analytics.json
    with open('profile_analytics.json', 'r') as f:
        data = json.load(f)
    # Connect to Postgres (update with your credentials or use MCP tool)
    conn = psycopg2.connect(dbname='your_db', user='your_user', password='your_password', host='localhost', port='5432')
    cur = conn.cursor()
    # 1. Insert work_platform if not exists
    work_platform = data['work_platform']
    cur.execute('''
        INSERT INTO profile_analytics.work_platform (id, name, logo_url)
        VALUES (%s, %s, %s)
        ON CONFLICT (id) DO NOTHING
    ''', (work_platform['id'], work_platform['name'], work_platform['logo_url']))
    # 2. Insert profile
    profile = data['profile']
    profile_id = data['id']
    cur.execute('''
        INSERT INTO profile_analytics.profile (
            id, work_platform_id, external_id, platform_username, url, image_url, full_name, introduction, content_count,
            is_verified, platform_account_type, gender, age_group, language, follower_count, subscriber_count,
            average_likes, average_comments, average_views, average_reels_views, engagement_rate, credibility_score,
            created_at, updated_at
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        ON CONFLICT (id) DO NOTHING
    ''', (
        profile_id,
        work_platform['id'],
        profile['external_id'],
        profile['platform_username'],
        profile['url'],
        profile['image_url'],
        profile['full_name'],
        profile['introduction'],
        profile['content_count'],
        profile['is_verified'],
        profile['platform_account_type'],
        profile['gender'],
        profile['age_group'],
        profile['language'],
        profile['follower_count'],
        profile['subscriber_count'],
        profile['average_likes'],
        profile['average_comments'],
        profile['average_views'],
        profile['average_reels_views'],
        profile['engagement_rate'],
        None,  # credibility_score
        datetime.now(),
        datetime.now()
    ))
    # 3. Insert reputation_history
    for rh in profile.get('reputation_history', []):
        cur.execute('''
            INSERT INTO profile_analytics.reputation_history (
                profile_id, month, follower_count, subscriber_count, following_count, average_likes, average_views, average_comments, total_views, total_likes
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            ON CONFLICT (profile_id, month) DO NOTHING
        ''', (
            profile_id,
            f"{rh['month']}-01",  # convert YYYY-MM to YYYY-MM-01
            rh.get('follower_count'),
            rh.get('subscriber_count'),
            rh.get('following_count'),
            rh.get('average_likes'),
            rh.get('average_views'),
            rh.get('average_comments'),
            rh.get('total_views'),
            rh.get('total_likes')
        ))
    conn.commit()
    cur.close()
    conn.close()
    print('Inserted one profile and related data.')

# Execute the functions
if __name__ == "__main__":
    print("Script is being run directly")
    generate_sample_data()
    insert_one_profile_from_json()
    print("Script execution complete")
