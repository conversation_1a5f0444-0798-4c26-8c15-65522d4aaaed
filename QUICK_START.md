# Quick Start Guide - Phyllo API Proxy

## 🚀 Start the Server
```bash
cd /home/<USER>/Desktop/workspace/creatorverse_services/phyllo_dummy
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

## 📋 Sample API Calls

### 1. Profile Analytics
```bash
curl -X POST "http://localhost:8000/v1/social/creator/profile/analytics" \
  -H "Content-Type: application/json" \
  -d '{
    "profile_id": "cfa27c2b-6451-4433-99d7-98230dd1a1d6",
    "include_audience": true,
    "include_content": true,
    "include_pricing": true
  }'
```

### 2. Quick Search - Instagram Creators
```bash
curl -X POST "http://localhost:8000/v1/social/creator/profile/quick-search" \
  -H "Content-Type: application/json" \
  -d '{
    "platform": "instagram",
    "follower_count_min": 1000,
    "follower_count_max": 1000000,
    "engagement_rate_min": 0.01,
    "verified_only": false,
    "limit": 5
  }'
```

### 3. Advanced Search - YouTube Gaming Creators
```bash
curl -X POST "http://localhost:8000/v1/social/creator/profile/search" \
  -H "Content-Type: application/json" \
  -d '{
    "platform": "youtube",
    "interests": ["gaming", "technology"],
    "gender": "MALE",
    "age_group": "25-34",
    "content_type": "VIDEO",
    "limit": 10,
    "offset": 0
  }'
```

### 4. Test Validation - Invalid Platform
```bash
curl -X POST "http://localhost:8000/v1/social/creator/profile/quick-search" \
  -H "Content-Type: application/json" \
  -d '{
    "platform": "invalid_platform"
  }'
```

### 5. Test Security - XSS Prevention
```bash
curl -X POST "http://localhost:8000/v1/social/creator/profile/quick-search" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "<script>alert(\"xss\")</script>",
    "limit": 5
  }'
```

## 🧪 Run Tests
```bash
# Run comprehensive test suite
python test_enhanced_api.py

# Run sample API demonstrations
python sample_api_calls.py
```

## 📖 Documentation
- **API Usage Guide**: `API_USAGE_GUIDE.md`
- **Implementation Summary**: `IMPLEMENTATION_SUMMARY.md`
- **Interactive Docs**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

## ✅ Validation Features
- Platform-specific constraints (follower limits, content types)
- Input sanitization (XSS, SQL injection prevention)
- Range validation (min/max values)
- Content safety checks
- Security event logging

## 🛡️ Security Features
- XSS prevention through input sanitization
- SQL injection prevention
- Content safety validation
- Rate limiting framework
- Comprehensive error handling
