```sql
-- DROP SCHEMA filter_catalog;

CREATE SCHEMA filter_catalog AUTHORIZATION postgres;

-- DROP TYPE filter_catalog."data_type_e";

CREATE TYPE filter_catalog."data_type_e" AS ENUM (
	'range',
	'enum',
	'multi_enum',
	'boolean',
	'keyword',
	'location',
	'date_range');

-- DROP TYPE filter_catalog."filter_type";

CREATE TYPE filter_catalog."filter_type" AS ENUM (
	'radio-button',
	'checkbox',
	'multilevel-checkbox',
	'enter-value');

-- DROP TYPE filter_catalog."option_for_type";

CREATE TYPE filter_catalog."option_for_type" AS ENUM (
	'creator',
	'audience');

-- DROP TYPE filter_catalog."platform_type";

CREATE TYPE filter_catalog."platform_type" AS ENUM (
	'instagram',
	'youtube',
	'tiktok');

-- DROP TYPE filter_catalog."provider_e";

CREATE TYPE filter_catalog."provider_e" AS ENUM (
	'phyllo_v1',
	'modash',
	'custom',
	'phyllo_v2');

-- DROP TYPE filter_catalog."validation_type_e";

CREATE TYPE filter_catalog."validation_type_e" AS ENUM (
	'none',
	'min_max',
	'regex',
	'length',
	'custom');
-- filter_catalog.filter_groups definition

-- Drop table

-- DROP TABLE filter_catalog.filter_groups;

CREATE TABLE filter_catalog.filter_groups (
	id uuid DEFAULT gen_random_uuid() NOT NULL,
	"name" varchar(100) NOT NULL,
	option_for filter_catalog."option_for_type" DEFAULT 'creator'::filter_catalog.option_for_type NOT NULL,
	channel filter_catalog."platform_type" DEFAULT 'instagram'::filter_catalog.platform_type NOT NULL,
	sort_order int2 DEFAULT 0 NULL,
	is_active bool DEFAULT true NULL,
	created_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
	updated_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
	CONSTRAINT filter_groups_pkey PRIMARY KEY (id),
	CONSTRAINT uq_filter_group_context UNIQUE (name, option_for, channel)
);

-- Permissions

ALTER TABLE filter_catalog.filter_groups OWNER TO postgres;
GRANT ALL ON TABLE filter_catalog.filter_groups TO postgres;


-- filter_catalog.saved_filter_sets definition

-- Drop table

-- DROP TABLE filter_catalog.saved_filter_sets;

CREATE TABLE filter_catalog.saved_filter_sets (
	id uuid DEFAULT gen_random_uuid() NOT NULL,
	"name" varchar(100) NOT NULL,
	channel filter_catalog."platform_type" DEFAULT 'instagram'::filter_catalog.platform_type NOT NULL,
	option_for filter_catalog."option_for_type" DEFAULT 'creator'::filter_catalog.option_for_type NOT NULL,
	filter_values jsonb NOT NULL,
	user_id uuid NULL,
	is_shared bool DEFAULT false NULL,
	share_code varchar(50) NULL,
	usage_count int4 DEFAULT 0 NULL,
	created_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
	updated_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
	CONSTRAINT saved_filter_sets_pkey PRIMARY KEY (id),
	CONSTRAINT saved_filter_sets_share_code_key UNIQUE (share_code)
);
CREATE INDEX idx_saved_filters_user ON filter_catalog.saved_filter_sets USING btree (user_id);

-- Permissions

ALTER TABLE filter_catalog.saved_filter_sets OWNER TO postgres;
GRANT ALL ON TABLE filter_catalog.saved_filter_sets TO postgres;


-- filter_catalog.filter_definitions definition

-- Drop table

-- DROP TABLE filter_catalog.filter_definitions;

CREATE TABLE filter_catalog.filter_definitions (
	id uuid DEFAULT gen_random_uuid() NOT NULL,
	group_id uuid NOT NULL,
	"name" varchar(100) NOT NULL,
	"filter_type" filter_catalog."filter_type" DEFAULT 'checkbox'::filter_catalog.filter_type NOT NULL,
	icon varchar(50) NULL,
	has_minmax bool DEFAULT false NULL,
	has_enter_value bool DEFAULT false NULL,
	has_search_box bool DEFAULT false NULL,
	placeholder varchar(200) NULL,
	"options" jsonb DEFAULT '[]'::jsonb NULL,
	db_field varchar(100) NULL,
	api_field varchar(100) NULL,
	sort_order int2 DEFAULT 0 NULL,
	is_active bool DEFAULT true NULL,
	created_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
	updated_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
	CONSTRAINT filter_definitions_pkey PRIMARY KEY (id),
	CONSTRAINT uq_filter_group_name UNIQUE (group_id, name),
	CONSTRAINT filter_definitions_group_id_fkey FOREIGN KEY (group_id) REFERENCES filter_catalog.filter_groups(id) ON DELETE CASCADE
);
CREATE INDEX idx_filter_context ON filter_catalog.filter_definitions USING btree (group_id, is_active);
CREATE INDEX idx_filter_name ON filter_catalog.filter_definitions USING btree (name);

-- Permissions

ALTER TABLE filter_catalog.filter_definitions OWNER TO postgres;
GRANT ALL ON TABLE filter_catalog.filter_definitions TO postgres;


-- filter_catalog.location_hierarchy definition

-- Drop table

-- DROP TABLE filter_catalog.location_hierarchy;

CREATE TABLE filter_catalog.location_hierarchy (
	id uuid DEFAULT gen_random_uuid() NOT NULL,
	"name" varchar(100) NOT NULL,
	code varchar(20) NULL,
	"level" int4 DEFAULT 0 NULL,
	parent_id uuid NULL,
	population int4 NULL,
	tier varchar(10) NULL,
	is_active bool DEFAULT true NULL,
	created_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
	updated_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
	CONSTRAINT location_hierarchy_pkey PRIMARY KEY (id),
	CONSTRAINT uq_location_context UNIQUE (name, parent_id, level),
	CONSTRAINT location_hierarchy_parent_id_fkey FOREIGN KEY (parent_id) REFERENCES filter_catalog.location_hierarchy(id) ON DELETE CASCADE
);
CREATE INDEX idx_location_hierarchy_level ON filter_catalog.location_hierarchy USING btree (level, is_active);
CREATE INDEX idx_location_hierarchy_parent ON filter_catalog.location_hierarchy USING btree (parent_id, is_active);

-- Permissions

ALTER TABLE filter_catalog.location_hierarchy OWNER TO postgres;
GRANT ALL ON TABLE filter_catalog.location_hierarchy TO postgres;



-- DROP FUNCTION filter_catalog.update_updated_at();

CREATE OR REPLACE FUNCTION filter_catalog.update_updated_at()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$function$
;

-- Permissions

ALTER FUNCTION filter_catalog.update_updated_at() OWNER TO postgres;
GRANT ALL ON FUNCTION filter_catalog.update_updated_at() TO postgres;


-- Permissions

GRANT ALL ON SCHEMA filter_catalog TO postgres;
```