"""
Comprehensive Test Suite for Creatorverse User Service
Tests all user management functionality including cache-aside patterns, authentication, and edge cases.
"""
import asyncio
import pytest
import uuid
from datetime import datetime, UTC, timedelta
from typing import Dict, Any, Optional
from unittest.mock import AsyncMock, MagicMock, patch

import asyncpg
from sqlalchemy import select, delete
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import get_database, get_locobuzz_redis
from app.models.user_models import User, Organization, OrganizationMembership, UserOTP, MagicLink
from app.services.user_service import (
    get_user_by_email_cache_aside,
    get_user_by_id_cache_aside,
    create_user_cache_aside,
    update_user_cache_aside,
    verify_user_email_cache_aside,
    verify_login_otp_cache_aside,
    update_user_last_login_cache_aside,
    get_organization_by_domain_cache_aside,
    register_brand_with_organization_cache_aside,
    get_user_organizations_cache_aside,
    get_user_brands_cache_aside,
    get_user_roles_cache_aside,
    get_user_auth_methods_cache_aside,
    get_user_by_mobile_cache_aside
)
from app.utilities.bloom_filter import CreatorBloomFilter


class TestUserServiceComprehensive:
    \"\"\"Comprehensive test suite for user service functionality\"\"\\"

    @pytest.fixture(autouse=True)
    async def setup_test_environment(self):
        \"\"\"Setup test environment with clean database and Redis\"\"\"
        self.db_conn = get_database()
        self.redis_client = get_locobuzz_redis()
        
        await self.db_conn.initialize()
        await self.redis_client.initialize()
        
        # Clean up test data
        await self._cleanup_test_data()
        
        yield
        
        # Cleanup after test
        await self._cleanup_test_data()
        await self.redis_client.close()
        await self.db_conn.shutdown()

    async def _cleanup_test_data(self):
        \"\"\"Clean up test data from database and Redis\"\"\"
        try:
            async with self.db_conn.get_db() as session:
                # Delete test users (cascade will handle related records)
                await session.execute(
                    delete(User).where(User.email.like('%test%'))
                )
                await session.commit()
            
            # Clear Redis test keys
            keys = await self.redis_client.keys("CreatorVerse:*test*")
            if keys:
                await self.redis_client.delete(*keys)
                
        except Exception as e:
            print(f"Cleanup error: {e}")

    def _create_test_user_data(self, email: str = None) -> Dict[str, Any]:
        \"\"\"Create test user data\"\"\"
        if not email:
            email = f"test.user.{uuid.uuid4().hex[:8]}@testdomain.com"
        
        return {
            "email": email,
            "name": "Test User",
            "status": "active",
            "is_email_verified": True,
            "is_phone_verified": False,
            "phone_number": "+1234567890",
            "profile_image": "https://example.com/avatar.jpg"
        }

    # ──────────────────────────────────────────────────────────────────────────────
    # USER RETRIEVAL TESTS
    # ──────────────────────────────────────────────────────────────────────────────

    @pytest.mark.asyncio
    async def test_get_user_by_email_cache_miss_then_hit(self):
        \"\"\"Test cache-aside pattern: cache miss, database fetch, cache population, cache hit\"\"\"
        user_data = self._create_test_user_data()
        
        # Create user directly in database
        async with self.db_conn.get_db() as session:
            user = User(**user_data)
            session.add(user)
            await session.commit()
            user_id = user.id

        # First call should be cache miss, database hit
        result1 = await get_user_by_email_cache_aside(
            self.db_conn, self.redis_client, user_data["email"]
        )
        
        assert result1 is not None
        assert result1["email"] == user_data["email"]
        assert result1["id"] == str(user_id)

        # Second call should be cache hit
        with patch.object(self.db_conn, 'get_db') as mock_db:
            result2 = await get_user_by_email_cache_aside(
                self.db_conn, self.redis_client, user_data["email"]
            )
            
            # Verify cache hit (database should not be called)
            mock_db.assert_not_called()
            assert result2 == result1

    @pytest.mark.asyncio
    async def test_get_user_by_email_not_found(self):
        \"\"\"Test user retrieval with non-existent email\"\"\"
        result = await get_user_by_email_cache_aside(
            self.db_conn, self.redis_client, "<EMAIL>"
        )
        assert result is None

    @pytest.mark.asyncio
    async def test_get_user_by_id_cache_aside(self):
        \"\"\"Test user retrieval by ID with cache-aside pattern\"\"\"
        user_data = self._create_test_user_data()
        
        # Create user
        success, created_user, message = await create_user_cache_aside(
            self.db_conn, self.redis_client, user_data
        )
        assert success
        user_id = uuid.UUID(created_user["id"])

        # Test cache miss -> database hit -> cache population
        result1 = await get_user_by_id_cache_aside(
            self.db_conn, self.redis_client, user_id
        )
        
        assert result1 is not None
        assert result1["id"] == str(user_id)

        # Test cache hit
        with patch.object(self.db_conn, 'get_db') as mock_db:
            result2 = await get_user_by_id_cache_aside(
                self.db_conn, self.redis_client, user_id
            )
            mock_db.assert_not_called()
            assert result2 == result1

    @pytest.mark.asyncio
    async def test_get_user_by_mobile_cache_aside(self):
        \"\"\"Test user retrieval by mobile number with cache-aside pattern\"\"\"
        user_data = self._create_test_user_data()
        mobile = "+1987654321"
        user_data["phone_number"] = mobile
        
        # Create user
        success, created_user, message = await create_user_cache_aside(
            self.db_conn, self.redis_client, user_data
        )
        assert success

        # Test cache miss -> database hit -> cache population
        result1 = await get_user_by_mobile_cache_aside(
            self.db_conn, self.redis_client, mobile
        )
        
        assert result1 is not None
        assert result1["phone_number"] == mobile

        # Test cache hit
        with patch.object(self.db_conn, 'get_db') as mock_db:
            result2 = await get_user_by_mobile_cache_aside(
                self.db_conn, self.redis_client, mobile
            )
            mock_db.assert_not_called()
            assert result2 == result1

    # ──────────────────────────────────────────────────────────────────────────────
    # USER CREATION TESTS
    # ──────────────────────────────────────────────────────────────────────────────

    @pytest.mark.asyncio
    async def test_create_user_success(self):
        \"\"\"Test successful user creation with cache population\"\"\"
        user_data = self._create_test_user_data()
        
        success, created_user, message = await create_user_cache_aside(
            self.db_conn, self.redis_client, user_data
        )
        
        assert success
        assert created_user is not None
        assert created_user["email"] == user_data["email"]
        assert created_user["name"] == user_data["name"]
        assert message == "User created successfully"

        # Verify user exists in database
        async with self.db_conn.get_db() as session:
            db_user = await session.execute(
                select(User).where(User.email == user_data["email"])
            )
            assert db_user.scalar_one_or_none() is not None

        # Verify user is cached
        cached_user = await get_user_by_email_cache_aside(
            self.db_conn, self.redis_client, user_data["email"]
        )
        assert cached_user is not None

    @pytest.mark.asyncio
    async def test_create_user_duplicate_email(self):
        \"\"\"Test user creation with duplicate email\"\"\"
        user_data = self._create_test_user_data()
        
        # Create first user
        success1, _, _ = await create_user_cache_aside(
            self.db_conn, self.redis_client, user_data
        )
        assert success1

        # Try to create second user with same email
        success2, user2, message2 = await create_user_cache_aside(
            self.db_conn, self.redis_client, user_data
        )
        
        assert not success2
        assert user2 is None
        assert "already exists" in message2.lower()

    @pytest.mark.asyncio
    async def test_create_user_missing_email(self):
        \"\"\"Test user creation without email\"\"\"
        user_data = self._create_test_user_data()
        del user_data["email"]
        
        success, user, message = await create_user_cache_aside(
            self.db_conn, self.redis_client, user_data
        )
        
        assert not success
        assert user is None
        assert "email is required" in message.lower()

    @pytest.mark.asyncio
    async def test_create_user_bloom_filter_integration(self):
        \"\"\"Test bloom filter integration during user creation\"\"\"
        user_data = self._create_test_user_data()
        
        # Mock bloom filter to simulate existing email
        with patch('app.services.user_service.CreatorBloomFilter') as mock_bloom:
            mock_filter = AsyncMock()
            mock_filter.exists.return_value = True
            mock_bloom.return_value = mock_filter
            
            # Create existing user first
            await create_user_cache_aside(
                self.db_conn, self.redis_client, user_data
            )
            
            # Try to create duplicate - should be caught by bloom filter
            success, user, message = await create_user_cache_aside(
                self.db_conn, self.redis_client, user_data
            )
            
            assert not success
            assert "already exists" in message.lower()

    # ──────────────────────────────────────────────────────────────────────────────
    # USER UPDATE TESTS
    # ──────────────────────────────────────────────────────────────────────────────

    @pytest.mark.asyncio
    async def test_update_user_success(self):
        \"\"\"Test successful user update with cache invalidation\"\"\"
        user_data = self._create_test_user_data()
        
        # Create user
        success, created_user, _ = await create_user_cache_aside(
            self.db_conn, self.redis_client, user_data
        )
        assert success
        user_id = uuid.UUID(created_user["id"])

        # Update user
        update_data = {
            "name": "Updated Name",
            "phone_number": "+1111111111"
        }
        
        success, updated_user, message = await update_user_cache_aside(
            self.db_conn, self.redis_client, user_id, update_data
        )
        
        assert success
        assert updated_user["name"] == "Updated Name"
        assert updated_user["phone_number"] == "+1111111111"
        assert message == "User updated successfully"

        # Verify cache is updated
        cached_user = await get_user_by_email_cache_aside(
            self.db_conn, self.redis_client, user_data["email"]
        )
        assert cached_user["name"] == "Updated Name"

    @pytest.mark.asyncio
    async def test_update_user_not_found(self):
        \"\"\"Test user update with non-existent user ID\"\"\"
        fake_id = uuid.uuid4()
        update_data = {"name": "New Name"}
        
        success, user, message = await update_user_cache_aside(
            self.db_conn, self.redis_client, fake_id, update_data
        )
        
        assert not success
        assert user is None
        assert "not found" in message.lower()

    @pytest.mark.asyncio
    async def test_update_user_empty_data(self):
        \"\"\"Test user update with empty update data\"\"\"
        user_data = self._create_test_user_data()
        
        # Create user
        success, created_user, _ = await create_user_cache_aside(
            self.db_conn, self.redis_client, user_data
        )
        assert success
        user_id = uuid.UUID(created_user["id"])

        # Try to update with empty data
        success, user, message = await update_user_cache_aside(
            self.db_conn, self.redis_client, user_id, {}
        )
        
        assert not success
        assert user is None
        assert "no data to update" in message.lower()

    # ──────────────────────────────────────────────────────────────────────────────
    # EMAIL VERIFICATION TESTS
    # ──────────────────────────────────────────────────────────────────────────────

    @pytest.mark.asyncio
    async def test_verify_user_email_success(self):
        \"\"\"Test successful email verification\"\"\"
        user_data = self._create_test_user_data()
        user_data["is_email_verified"] = False
        
        # Create unverified user
        success, created_user, _ = await create_user_cache_aside(
            self.db_conn, self.redis_client, user_data
        )
        assert success
        user_id = uuid.UUID(created_user["id"])

        # Verify email
        success, updated_user, message = await verify_user_email_cache_aside(
            self.db_conn, self.redis_client, user_id
        )
        
        assert success
        assert updated_user["is_email_verified"] == "True"
        assert message == "Email verified successfully"

        # Verify cache is updated
        cached_user = await get_user_by_email_cache_aside(
            self.db_conn, self.redis_client, user_data["email"]
        )
        assert cached_user["is_email_verified"] == "True"

    @pytest.mark.asyncio
    async def test_verify_email_already_verified(self):
        \"\"\"Test email verification for already verified user\"\"\"
        user_data = self._create_test_user_data()
        user_data["is_email_verified"] = True
        
        # Create verified user
        success, created_user, _ = await create_user_cache_aside(
            self.db_conn, self.redis_client, user_data
        )
        assert success
        user_id = uuid.UUID(created_user["id"])

        # Try to verify again
        success, user, message = await verify_user_email_cache_aside(
            self.db_conn, self.redis_client, user_id
        )
        
        assert success
        assert user is None
        assert "already verified" in message.lower()

    # ──────────────────────────────────────────────────────────────────────────────
    # OTP VERIFICATION TESTS
    # ──────────────────────────────────────────────────────────────────────────────

    @pytest.mark.asyncio
    async def test_verify_login_otp_success(self):
        \"\"\"Test successful OTP verification\"\"\"
        user_data = self._create_test_user_data()
        
        # Create user
        success, created_user, _ = await create_user_cache_aside(
            self.db_conn, self.redis_client, user_data
        )
        assert success

        # Mock OTP manager
        with patch('app.services.user_service.get_optimized_otp_manager') as mock_otp:
            mock_manager = AsyncMock()
            mock_manager.verify_otp.return_value = (True, 0)
            mock_otp.return_value = mock_manager
            
            success, verified_user, message = await verify_login_otp_cache_aside(
                self.db_conn, self.redis_client, user_data["email"], "123456"
            )
            
            assert success
            assert verified_user is not None
            assert verified_user["email"] == user_data["email"]
            assert "successfully" in message.lower()

    @pytest.mark.asyncio
    async def test_verify_login_otp_invalid(self):
        \"\"\"Test OTP verification with invalid OTP\"\"\"
        user_data = self._create_test_user_data()
        
        # Create user
        await create_user_cache_aside(
            self.db_conn, self.redis_client, user_data
        )

        # Mock OTP manager with invalid OTP
        with patch('app.services.user_service.get_optimized_otp_manager') as mock_otp:
            mock_manager = AsyncMock()
            mock_manager.verify_otp.return_value = (False, 0)
            mock_otp.return_value = mock_manager
            
            success, user, message = await verify_login_otp_cache_aside(
                self.db_conn, self.redis_client, user_data["email"], "000000"
            )
            
            assert not success
            assert user is None
            assert "invalid" in message.lower()

    @pytest.mark.asyncio
    async def test_verify_login_otp_lockout(self):
        \"\"\"Test OTP verification with account lockout\"\"\"
        user_data = self._create_test_user_data()
        
        # Create user
        await create_user_cache_aside(
            self.db_conn, self.redis_client, user_data
        )

        # Mock OTP manager with lockout
        with patch('app.services.user_service.get_optimized_otp_manager') as mock_otp:
            mock_manager = AsyncMock()
            mock_manager.verify_otp.return_value = (False, 300)  # 5 minutes lockout
            mock_otp.return_value = mock_manager
            
            success, user, message = await verify_login_otp_cache_aside(
                self.db_conn, self.redis_client, user_data["email"], "000000"
            )
            
            assert not success
            assert user is None
            assert "too many failed attempts" in message.lower()
            assert "300" in message

    # ──────────────────────────────────────────────────────────────────────────────
    # ORGANIZATION MANAGEMENT TESTS
    # ──────────────────────────────────────────────────────────────────────────────

    @pytest.mark.asyncio
    async def test_get_organization_by_domain_cache_aside(self):
        \"\"\"Test organization retrieval by domain with cache-aside pattern\"\"\"
        domain = "testcompany.com"
        
        # Create organization directly in database
        async with self.db_conn.get_db() as session:
            org = Organization(
                domain=domain,
                name="Test Company",
                contact_email="<EMAIL>"
            )
            session.add(org)
            await session.commit()

        # Test cache miss -> database hit -> cache population
        result1 = await get_organization_by_domain_cache_aside(
            self.db_conn, self.redis_client, domain
        )
        
        assert result1 is not None
        assert result1["domain"] == domain
        assert result1["name"] == "Test Company"

        # Test cache hit
        with patch.object(self.db_conn, 'get_db') as mock_db:
            result2 = await get_organization_by_domain_cache_aside(
                self.db_conn, self.redis_client, domain
            )
            mock_db.assert_not_called()
            assert result2 == result1

    @pytest.mark.asyncio
    async def test_register_brand_with_organization_new_domain(self):
        \"\"\"Test brand registration with new organization creation\"\"\"
        user_data = self._create_test_user_data("<EMAIL>")
        
        # Create user
        success, created_user, _ = await create_user_cache_aside(
            self.db_conn, self.redis_client, user_data
        )
        assert success

        brand_data = {
            "name": "New Brand",
            "description": "A test brand",
            "org_name": "New Company"
        }

        # Register brand
        success, brand_dict, message = await register_brand_with_organization_cache_aside(
            self.db_conn, self.redis_client, user_data["email"], brand_data
        )
        
        assert success
        assert brand_dict is not None
        assert brand_dict["name"] == "New Brand"
        assert "organization_name" in brand_dict
        assert message == "Brand registered successfully"

        # Verify organization was created
        org_result = await get_organization_by_domain_cache_aside(
            self.db_conn, self.redis_client, "newcompany.com"
        )
        assert org_result is not None

    # ──────────────────────────────────────────────────────────────────────────────
    # CACHE FAILURE TESTS
    # ──────────────────────────────────────────────────────────────────────────────

    @pytest.mark.asyncio
    async def test_cache_failure_graceful_degradation(self):
        \"\"\"Test that cache failures don't break functionality\"\"\"
        user_data = self._create_test_user_data()
        
        # Create user
        success, created_user, _ = await create_user_cache_aside(
            self.db_conn, self.redis_client, user_data
        )
        assert success

        # Mock Redis to fail
        with patch.object(self.redis_client, 'hgetall', side_effect=Exception("Redis down")):
            # Should still work by hitting database
            result = await get_user_by_email_cache_aside(
                self.db_conn, self.redis_client, user_data["email"]
            )
            assert result is not None
            assert result["email"] == user_data["email"]

    @pytest.mark.asyncio
    async def test_concurrent_user_creation(self):
        \"\"\"Test concurrent user creation scenarios\"\"\"
        base_email = "<EMAIL>"
        
        async def create_user_task(task_id):
            user_data = self._create_test_user_data(f"user{task_id}@example.com")
            return await create_user_cache_aside(
                self.db_conn, self.redis_client, user_data
            )

        # Create multiple users concurrently
        tasks = [create_user_task(i) for i in range(5)]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # All should succeed with unique emails
        successful_creations = [r for r in results if not isinstance(r, Exception) and r[0]]
        assert len(successful_creations) == 5

    @pytest.mark.asyncio
    async def test_database_transaction_rollback(self):
        \"\"\"Test database transaction rollback scenarios\"\"\"
        user_data = self._create_test_user_data()
        
        # Mock database to fail after user creation but before commit
        with patch.object(self.db_conn, 'get_db') as mock_db:
            mock_session = AsyncMock()
            mock_session.__aenter__.return_value = mock_session
            mock_session.commit.side_effect = Exception("Database error")
            mock_db.return_value = mock_session
            
            success, user, message = await create_user_cache_aside(
                self.db_conn, self.redis_client, user_data
            )
            
            assert not success
            assert user is None
            assert "error" in message.lower()

    # ──────────────────────────────────────────────────────────────────────────────
    # PERFORMANCE TESTS
    # ──────────────────────────────────────────────────────────────────────────────

    @pytest.mark.asyncio
    async def test_bulk_user_retrieval_performance(self):
        \"\"\"Test performance of bulk user retrieval operations\"\"\"
        # Create multiple users
        user_emails = []
        for i in range(100):
            user_data = self._create_test_user_data(f"bulk.user.{i}@test.com")
            success, created_user, _ = await create_user_cache_aside(
                self.db_conn, self.redis_client, user_data
            )
            assert success
            user_emails.append(user_data["email"])

        # Measure retrieval time
        import time
        start_time = time.time()
        
        # Retrieve all users (should hit cache for most)
        tasks = [
            get_user_by_email_cache_aside(self.db_conn, self.redis_client, email)
            for email in user_emails
        ]
        results = await asyncio.gather(*tasks)
        
        end_time = time.time()
        retrieval_time = end_time - start_time
        
        # All users should be retrieved
        assert len(results) == 100
        assert all(r is not None for r in results)
        
        # Should be fast due to caching (adjust threshold as needed)
        assert retrieval_time < 5.0  # Should complete in under 5 seconds

    # ──────────────────────────────────────────────────────────────────────────────
    # EDGE CASE TESTS
    # ──────────────────────────────────────────────────────────────────────────────

    @pytest.mark.asyncio
    async def test_special_characters_in_user_data(self):
        \"\"\"Test handling of special characters in user data\"\"\"
        user_data = self._create_test_user_data("<EMAIL>")
        user_data["name"] = "Test User with Special Characters: äöü€ @#$%"
        
        success, created_user, _ = await create_user_cache_aside(
            self.db_conn, self.redis_client, user_data
        )
        
        assert success
        assert created_user["name"] == user_data["name"]

        # Verify retrieval works correctly
        retrieved_user = await get_user_by_email_cache_aside(
            self.db_conn, self.redis_client, user_data["email"]
        )
        assert retrieved_user["name"] == user_data["name"]

    @pytest.mark.asyncio
    async def test_very_long_user_data(self):
        \"\"\"Test handling of very long user data\"\"\"
        user_data = self._create_test_user_data("<EMAIL>")
        user_data["name"] = "A" * 250  # Very long name
        
        success, created_user, _ = await create_user_cache_aside(
            self.db_conn, self.redis_client, user_data
        )
        
        assert success
        assert created_user["name"] == user_data["name"]

    @pytest.mark.asyncio
    async def test_null_and_empty_fields(self):
        \"\"\"Test handling of null and empty fields\"\"\"
        user_data = {
            "email": "<EMAIL>",
            "name": "",  # Empty string
            "profile_image": None,  # Null value
            "phone_number": "",  # Empty string
        }
        
        success, created_user, _ = await create_user_cache_aside(
            self.db_conn, self.redis_client, user_data
        )
        
        assert success
        assert created_user["name"] == ""
        assert created_user["profile_image"] == ""
        assert created_user["phone_number"] == ""


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v", "--tb=short"])
