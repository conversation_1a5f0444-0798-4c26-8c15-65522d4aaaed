from collections.abc import Async<PERSON>enerator
from contextlib import asynccontextmanager

from fastapi import FastAP<PERSON>, HTTPException
from fastapi.exceptions import RequestValidationError
from fastapi.middleware.cors import CORSMiddleware

from app.api.api_v1.api import api_router
from app.core.config import APP_CONFIG, get_database, get_locobuzz_redis
from app.core.exceptions import (
    C<PERSON>VerseError,
    creatorverse_exception_handler,
    http_exception_handler,
    validation_exception_handler,
    general_exception_handler
)
from app.utilities.response_handler import create_success_response, create_health_response, StandardResponse


@asynccontextmanager
async def lifespan(app_fast: FastAPI) -> AsyncGenerator[None, None]:
    """Lifespan context manager for FastAPI application."""
    # Startup
    print(f"Application starting up...{app_fast}")

    # Initialize logger first
    logger = APP_CONFIG.initialize_logger()
    logger.info("Logger initialized successfully")
    # Initialize database and redis with logger
    try:
        # Reset singletons to ensure fresh initialization
        from app.core_helper.database import AsyncDatabaseDB
        from app.core_helper.redis_client import RedisClient
        AsyncDatabaseDB._instance = None
        RedisClient._instance = None

        # Get database instance and initialize it
        db_conn = get_database()
        await db_conn.initialize()
        logger.info("Database initialized successfully")        # Get redis instance and initialize it  
        redis_client = get_locobuzz_redis()
        await redis_client.initialize()
        logger.info("Redis initialized successfully")
        
        # Initialize startup tasks
        try:
            # Initialize synchronous startup tasks first
            from app.utilities.startup_tasks import initialize_all_startup_tasks_sync
            initialize_all_startup_tasks_sync()
            
            # Initialize async startup tasks
            from app.utilities.startup_tasks import initialize_all_startup_tasks_async
            await initialize_all_startup_tasks_async()
            
            logger.info("Startup tasks completed successfully")
        except Exception as startup_error:
            logger.warning(f"Startup tasks failed: {startup_error}")

    except Exception as e:
        logger.error(f"Failed to initialize services: {e}")
        print(f"Failed to initialize services: {e}")

    yield

    # Shutdown
    logger.info("Application shutting down...")

    # Cleanup resources
    try:
        redis_client = get_locobuzz_redis()
        await redis_client.close()
        logger.info("Redis connection closed")
        db_conn = get_database()
        await db_conn.shutdown()
        logger.info("Database connection pool shutdown completed")
        # Database cleanup happens automatically via connection pool
        logger.info("Shutdown completed successfully")
    except Exception as e:
        logger.error(f"Error during shutdown: {e}")
        print(f"Error during shutdown: {e}")

    print("Application shutting down...")


app = FastAPI(
    title="CreatorVerse Users Backend API",
    version="1.0.0",
    description="CreatorVerse Users Backend API",
    lifespan=lifespan
)

# Register exception handlers for centralized error handling
app.add_exception_handler(CreatorVerseError, creatorverse_exception_handler)
app.add_exception_handler(HTTPException, http_exception_handler)
app.add_exception_handler(RequestValidationError, validation_exception_handler)
app.add_exception_handler(Exception, general_exception_handler)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*", "ngrok-skip-browser-warning"],
)

# Include API router
app.include_router(api_router, prefix="/v1")


@app.get("/")
async def root():
    """Root endpoint with new standardized response format"""
    return StandardResponse.success(
        data={"environment": APP_CONFIG.environ},
        message="Welcome to CreatorVerse API"
    )


@app.get("/health")
async def health_check():
    """Health check endpoint with new standardized response format"""
    return StandardResponse.success(
        data={
            "status": "healthy",
            "service": "CreatorVerse Backend",
            "environment": APP_CONFIG.environ
        },
        message="Service is healthy"
    )
