#!/usr/bin/env python3
"""
Test script to verify main.py implementation
Tests basic import and FastAPI app initialization
"""
import sys
import os
import asyncio

# Add project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test that all imports work correctly"""
    print("🔍 Testing imports...")
    try:
        # Test basic FastAPI imports
        from fastapi import FastAPI
        from fastapi.testclient import TestClient
        print("✅ FastAPI imports successful")
        
        # Test project imports
        from app.core.config import APP_CONFIG
        from app.core.exceptions import DiscoveryError
        from app.utilities.response_handler import StandardResponse
        from app.api.api_v1.api import api_router
        print("✅ Project imports successful")
        
        return True
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error during imports: {e}")
        return False


def test_app_creation():
    """Test that the FastAPI app can be created"""
    print("\n🏗️ Testing app creation...")
    try:
        # Import main module
        import main
        
        # Check if app exists
        if hasattr(main, 'app'):
            app = main.app
            print(f"✅ FastAPI app created successfully: {type(app)}")
            print(f"   - Title: {app.title}")
            print(f"   - Version: {app.version}")
            return app
        else:
            print("❌ App not found in main module")
            return None
            
    except Exception as e:
        print(f"❌ Error creating app: {e}")
        return None


def test_configuration():
    """Test that configuration is properly loaded"""
    print("\n⚙️ Testing configuration...")
    try:
        from app.core.config import APP_CONFIG
        
        print(f"✅ Service name: {APP_CONFIG.service_name}")
        print(f"✅ Environment: {APP_CONFIG.environ}")
        print(f"✅ Database configured: {'Yes' if APP_CONFIG.connection_string else 'No'}")
        print(f"✅ Redis configured: {'Yes' if APP_CONFIG.redis_url else 'No'}")
        
        return True
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        return False


def test_basic_endpoints():
    """Test basic endpoints using TestClient"""
    print("\n🔗 Testing basic endpoints...")
    try:
        from fastapi.testclient import TestClient
        import main
        
        client = TestClient(main.app)
        
        # Test root endpoint
        response = client.get("/")
        print(f"✅ Root endpoint (/) status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   - Success: {data.get('success', 'unknown')}")
            print(f"   - Message: {data.get('message', 'unknown')}")
        
        # Test simple health endpoint
        response = client.get("/health/simple")
        print(f"✅ Simple health endpoint (/health/simple) status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   - Status: {data.get('status', 'unknown')}")
        
        return True
    except Exception as e:
        print(f"❌ Endpoint testing error: {e}")
        return False


def test_exception_handlers():
    """Test that exception handlers are properly registered"""
    print("\n🛡️ Testing exception handlers...")
    try:
        import main
        
        # Check if exception handlers are registered
        app = main.app
        exception_handlers = getattr(app, 'exception_handlers', {})
        
        print(f"✅ Exception handlers registered: {len(exception_handlers)}")
        
        # Check for specific handlers
        from app.core.exceptions import DiscoveryError
        from fastapi import HTTPException
        from fastapi.exceptions import RequestValidationError
        
        handlers_found = []
        for exc_type in [DiscoveryError, HTTPException, RequestValidationError, Exception]:
            if exc_type in exception_handlers:
                handlers_found.append(exc_type.__name__)
        
        print(f"✅ Found handlers for: {', '.join(handlers_found)}")
        
        return True
    except Exception as e:
        print(f"❌ Exception handler testing error: {e}")
        return False


def test_middleware():
    """Test that middleware is properly configured"""
    print("\n🔧 Testing middleware...")
    try:
        import main
        
        app = main.app
        middleware_stack = getattr(app, 'user_middleware', [])
        
        print(f"✅ Middleware stack length: {len(middleware_stack)}")
        
        # Look for CORS middleware
        cors_found = False
        for middleware in middleware_stack:
            if hasattr(middleware, 'cls') and 'CORS' in str(middleware.cls):
                cors_found = True
                break
        
        print(f"✅ CORS middleware: {'Found' if cors_found else 'Not found'}")
        
        return True
    except Exception as e:
        print(f"❌ Middleware testing error: {e}")
        return False


def main():
    """Run all tests"""
    print("🚀 Starting CreatorVerse Discovery & Analytics - main.py Implementation Test")
    print("=" * 80)
    
    tests = [
        ("Imports", test_imports),
        ("App Creation", test_app_creation),
        ("Configuration", test_configuration),
        ("Basic Endpoints", test_basic_endpoints),
        ("Exception Handlers", test_exception_handlers),
        ("Middleware", test_middleware)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test '{test_name}' failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 80)
    print("📋 Test Summary:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall Result: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! main.py implementation is working correctly.")
        print("✅ The project is ready to run with: uvicorn main:app --reload")
        return True
    else:
        print("⚠️  Some tests failed. Please check the issues above.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
