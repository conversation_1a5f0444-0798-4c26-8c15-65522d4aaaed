#!/usr/bin/env python3
"""
Fixed Database Setup Script for CreatorVerse
This script uses the clean SQL files to properly set up the database
"""

import asyncio
import asyncpg
import sys
from pathlib import Path
from datetime import datetime

class Colors:
    HEADER = '\033[95m'
    OKBLUE = '\033[94m'
    OKCYAN = '\033[96m'
    OKGREEN = '\033[92m'
    WARNING = '\033[93m'
    FAIL = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'

def print_colored(message: str, color: str = Colors.ENDC) -> None:
    """Print colored message to console."""
    print(f"{color}{message}{Colors.ENDC}")

def print_step(step: str, status: str = "INFO") -> None:
    """Print a step with status indicator."""
    colors = {
        "INFO": Colors.OKBLUE,
        "SUCCESS": Colors.OKGREEN,
        "WARNING": Colors.WARNING,
        "ERROR": Colors.FAIL
    }
    symbols = {
        "INFO": "🔄",
        "SUCCESS": "✅",
        "WARNING": "⚠️",
        "ERROR": "❌"
    }
    color = colors.get(status, Colors.ENDC)
    symbol = symbols.get(status, "•")
    print_colored(f"{symbol} {step}", color)

async def main():
    """Main setup function using clean SQL files."""
    
    print_colored("\n" + "="*60, Colors.HEADER)
    print_colored("CreatorVerse Database Setup - Fixed Version", Colors.HEADER + Colors.BOLD)
    print_colored("="*60, Colors.HEADER)
    print_colored(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", Colors.OKBLUE)
    
    # Database configuration
    config = {
        "host": "************",
        "port": 5432,
        "database": "postgres",
        "user": "postgres",
        "password": "s81JKkaoe42Tm5W"
    }
    
    print_step(f"Target: {config['host']}:{config['port']}/{config['database']}", "INFO")
    
    # File paths
    current_dir = Path(__file__).parent
    schema_file = current_dir / "fixed_schema.sql"
    seed_file = current_dir / "comprehensive_seed_data.sql"
    
    # Check if SQL files exist
    if not schema_file.exists():
        print_step(f"Schema file not found: {schema_file}", "ERROR")
        sys.exit(1)
    
    if not seed_file.exists():
        print_step(f"Seed data file not found: {seed_file}", "ERROR")
        sys.exit(1)
    
    try:
        # Connect to database
        print_step("Connecting to database...", "INFO")
        connection = await asyncpg.connect(
            host=config["host"],
            port=config["port"],
            database=config["database"],
            user=config["user"],
            password=config["password"],
            command_timeout=300
        )
        print_step("Connected successfully", "SUCCESS")
        
        # Read and execute schema file
        print_step("Reading schema file...", "INFO")
        schema_sql = schema_file.read_text(encoding='utf-8')
        
        print_step("Executing schema creation...", "INFO")
        await connection.execute(schema_sql)
        print_step("Schema created successfully", "SUCCESS")
        
        # Read and execute seed data file
        print_step("Reading seed data file...", "INFO")
        seed_sql = seed_file.read_text(encoding='utf-8')
        
        print_step("Inserting seed data...", "INFO")
        await connection.execute(seed_sql)
        print_step("Seed data inserted successfully", "SUCCESS")
        
        # Verify setup
        print_step("Verifying database setup...", "INFO")
        
        # Check enums
        enum_count = await connection.fetchval("""
            SELECT COUNT(*) FROM pg_type t 
            JOIN pg_namespace n ON t.typnamespace = n.oid 
            WHERE nspname = 'filter_catalog' AND typtype = 'e'
        """)
        
        # Check tables
        table_count = await connection.fetchval("""
            SELECT COUNT(*) FROM information_schema.tables 
            WHERE table_schema = 'filter_catalog'
        """)
        
        # Check filter groups
        group_count = await connection.fetchval("""
            SELECT COUNT(*) FROM filter_catalog.filter_groups
        """)
        
        # Check filter definitions
        def_count = await connection.fetchval("""
            SELECT COUNT(*) FROM filter_catalog.filter_definitions
        """)
        
        # Check indexes
        index_count = await connection.fetchval("""
            SELECT COUNT(*) FROM pg_indexes 
            WHERE schemaname = 'filter_catalog'
        """)
        
        print_colored(f"\n{'='*40}", Colors.HEADER)
        print_colored("Verification Results", Colors.HEADER + Colors.BOLD)
        print_colored(f"{'='*40}", Colors.HEADER)
        
        print_step(f"Enums: {enum_count}", "SUCCESS" if enum_count >= 6 else "WARNING")
        print_step(f"Tables: {table_count}", "SUCCESS" if table_count >= 5 else "WARNING")
        print_step(f"Filter Groups: {group_count}", "SUCCESS" if group_count > 0 else "WARNING")
        print_step(f"Filter Definitions: {def_count}", "SUCCESS" if def_count > 0 else "WARNING")
        print_step(f"Indexes: {index_count}", "SUCCESS" if index_count > 0 else "WARNING")
        
        # Check if setup was successful
        success = (enum_count >= 6 and table_count >= 5 and group_count > 0 and def_count > 0)
        
        if success:
            print_colored(f"\n🎉 Database Setup Completed Successfully!", Colors.OKGREEN + Colors.BOLD)
            print_colored("\nNext steps:", Colors.OKBLUE)
            print_colored("1. Test filter APIs", Colors.ENDC)
            print_colored("2. Verify enable/disable functionality", Colors.ENDC)
            print_colored("3. Check Phyllo API integration", Colors.ENDC)
        else:
            print_step("Setup verification failed", "ERROR")
            sys.exit(1)
        
        # Close connection
        await connection.close()
        print_step("Database connection closed", "INFO")
        
    except Exception as e:
        print_step(f"Setup failed: {str(e)}", "ERROR")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
