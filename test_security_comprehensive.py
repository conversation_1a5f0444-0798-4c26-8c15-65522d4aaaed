"""
Security Test Suite for Creatorverse User Backend
Tests various security aspects including injection attacks, authentication bypasses, 
authorization checks, and data validation.
"""
import asyncio
import pytest
import uuid
import jwt
import hashlib
import secrets
from datetime import datetime, UTC, timedelta
from typing import Dict, Any, List, Optional
from unittest.mock import AsyncMock, MagicMock, patch

from sqlalchemy import select, text
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import get_database, get_locobuzz_redis, APP_CONFIG
from app.models.user_models import (
    User, Organization, Brand, BrandMembership, UserSession, 
    BrandInfluencerList, BrandInfluencerListEntry, GlobalLabel,
    OAuthAccount, UserOTP, MagicLink
)
from app.services.user_service import (
    create_user_cache_aside,
    get_user_by_email_cache_aside,
    verify_login_otp_cache_aside
)
from app.utilities.jwt_utils import create_jwt_token, verify_jwt_token
from app.utilities.otp_manager import get_optimized_otp_manager


class SecurityTestResults:
    """Class to collect and analyze security test results"""
    
    def __init__(self):
        self.vulnerabilities = []
        self.security_checks = []
    
    def add_vulnerability(self, test_name: str, severity: str, description: str, 
                         impact: str, recommendation: str):
        """Add a discovered vulnerability"""
        self.vulnerabilities.append({
            "test": test_name,
            "severity": severity,  # "critical", "high", "medium", "low"
            "description": description,
            "impact": impact,
            "recommendation": recommendation,
            "timestamp": datetime.now(UTC)
        })
    
    def add_security_check(self, test_name: str, passed: bool, description: str):
        """Add a security check result"""
        self.security_checks.append({
            "test": test_name,
            "passed": passed,
            "description": description,
            "timestamp": datetime.now(UTC)
        })
    
    def print_security_summary(self):
        """Print security test summary"""
        print("\n" + "="*80)
        print("SECURITY TEST SUMMARY")
        print("="*80)
        
        if self.vulnerabilities:
            print("\n🚨 VULNERABILITIES FOUND:")
            for vuln in self.vulnerabilities:
                print(f"\n  [{vuln['severity'].upper()}] {vuln['test']}")
                print(f"  Description: {vuln['description']}")
                print(f"  Impact: {vuln['impact']}")
                print(f"  Recommendation: {vuln['recommendation']}")
        else:
            print("\n✅ No vulnerabilities found in tested areas")
        
        print(f"\nSECURITY CHECKS:")
        passed_checks = sum(1 for check in self.security_checks if check["passed"])
        total_checks = len(self.security_checks)
        print(f"  Passed: {passed_checks}/{total_checks} ({passed_checks/total_checks*100:.1f}%)")
        
        for check in self.security_checks:
            status = "✅" if check["passed"] else "❌"
            print(f"  {status} {check['test']}: {check['description']}")


class TestSecurityComprehensive:
    """Comprehensive security test suite"""

    @pytest.fixture(autouse=True)
    async def setup_test_environment(self):
        """Setup test environment for security testing"""
        self.db_conn = get_database()
        self.redis_client = get_locobuzz_redis()
        self.security_results = SecurityTestResults()
        
        await self.db_conn.initialize()
        await self.redis_client.initialize()
        
        # Setup test data
        await self._setup_security_test_data()
        
        yield
        
        # Print security results and cleanup
        self.security_results.print_security_summary()
        await self._cleanup_test_data()
        await self.redis_client.close()
        await self.db_conn.shutdown()

    async def _setup_security_test_data(self):
        """Setup test data for security tests"""
        async with self.db_conn.get_db() as session:
            # Create test organization
            self.test_org = Organization(
                domain="sectest.com",
                name="Security Test Company",
                contact_email="<EMAIL>"
            )
            session.add(self.test_org)
            await session.flush()

            # Create test user
            self.test_user = User(
                email="<EMAIL>",
                name="Security Test User",
                status="active",
                is_email_verified=True
            )
            session.add(self.test_user)
            await session.flush()

            # Create test brand
            self.test_brand = Brand(
                organization_id=self.test_org.id,
                name="Security Test Brand",
                description="Brand for security testing",
                created_by=self.test_user.id
            )
            session.add(self.test_brand)
            await session.flush()

            # Create brand membership
            self.brand_membership = BrandMembership(
                brand_id=self.test_brand.id,
                user_id=self.test_user.id,
                role="brand_admin",
                is_active=True
            )
            session.add(self.brand_membership)
            
            await session.commit()

    async def _cleanup_test_data(self):
        """Clean up security test data"""
        try:
            async with self.db_conn.get_db() as session:
                from sqlalchemy import delete
                
                # Delete test data
                await session.execute(
                    delete(User).where(User.email.like('%sectest%'))
                )
                await session.execute(
                    delete(User).where(User.email.like('%security%'))
                )
                await session.execute(
                    delete(Organization).where(Organization.domain.like('%sectest%'))
                )
                await session.commit()
            
            # Clear Redis
            keys = await self.redis_client.keys("*sectest*")
            if keys:
                await self.redis_client.delete(*keys)
                
        except Exception as e:
            print(f"Cleanup error: {e}")

    # ──────────────────────────────────────────────────────────────────────────────
    # SQL INJECTION TESTS
    # ──────────────────────────────────────────────────────────────────────────────

    @pytest.mark.asyncio
    async def test_sql_injection_user_creation(self):
        """Test SQL injection attempts in user creation"""
        print("\n\nTesting SQL injection in user creation...")
        
        sql_injection_payloads = [
            "'; DROP TABLE users; --",
            "' OR '1'='1",
            "'; INSERT INTO users (email) VALUES ('<EMAIL>'); --",
            "' UNION SELECT * FROM users WHERE '1'='1",
            "'; UPDATE users SET email='<EMAIL>' WHERE '1'='1; --",
            "<script>alert('xss')</script>",
            "'; EXEC xp_cmdshell('dir'); --",
            "' OR 1=1 LIMIT 1 OFFSET 1 --",
            "'; SELECT password FROM users; --"
        ]
        
        injection_successful = False
        
        for payload in sql_injection_payloads:
            try:
                # Test injection in email field
                user_data = {
                    "email": payload,
                    "name": "Test User",
                    "status": "active"
                }
                
                success, created_user, message = await create_user_cache_aside(
                    self.db_conn, self.redis_client, user_data
                )
                
                # If user creation succeeds, check if injection was executed
                if success:
                    # Check if the payload was stored as-is (safe) or executed (vulnerable)
                    retrieved_user = await get_user_by_email_cache_aside(
                        self.db_conn, self.redis_client, payload
                    )
                    
                    if retrieved_user and retrieved_user["email"] == payload:
                        # Payload stored as-is, which is good (parameterized queries working)
                        continue
                    else:
                        # Potential injection - investigate further
                        injection_successful = True
                        break
                        
            except Exception as e:
                # Exceptions during injection attempts are expected and good
                error_message = str(e).lower()
                if "syntax error" in error_message or "invalid input" in error_message:
                    # Good - the database rejected malicious input
                    continue
                else:
                    # Unexpected error - might indicate vulnerability
                    print(f"Unexpected error with payload '{payload}': {e}")
        
        if injection_successful:
            self.security_results.add_vulnerability(
                "sql_injection_user_creation",
                "critical",
                "SQL injection vulnerability detected in user creation",
                "Attackers could execute arbitrary SQL commands, access sensitive data, or modify database",
                "Ensure all database queries use parameterized statements and input validation"
            )
        else:
            self.security_results.add_security_check(
                "sql_injection_user_creation",
                True,
                "User creation properly protected against SQL injection"
            )

    @pytest.mark.asyncio
    async def test_sql_injection_user_search(self):
        """Test SQL injection in user search/retrieval"""
        print("\n\nTesting SQL injection in user search...")
        
        search_injection_payloads = [
            "<EMAIL>' OR '1'='1",
            "'; SELECT * FROM users; --",
            "<EMAIL>' UNION SELECT password FROM users WHERE '1'='1",
            "' OR email LIKE '%@%' --"
        ]
        
        injection_detected = False
        
        for payload in search_injection_payloads:
            try:
                # Attempt user retrieval with injection payload
                result = await get_user_by_email_cache_aside(
                    self.db_conn, self.redis_client, payload
                )
                
                # If we get unexpected results (like multiple users or wrong user),
                # it might indicate successful injection
                if result and result["email"] != payload:
                    injection_detected = True
                    break
                    
            except Exception as e:
                # Expected - malicious input should be rejected
                continue
        
        if injection_detected:
            self.security_results.add_vulnerability(
                "sql_injection_user_search",
                "high",
                "SQL injection vulnerability in user search functionality",
                "Attackers could bypass authentication or access unauthorized user data",
                "Use parameterized queries and validate all input parameters"
            )
        else:
            self.security_results.add_security_check(
                "sql_injection_user_search",
                True,
                "User search functionality protected against SQL injection"
            )

    # ──────────────────────────────────────────────────────────────────────────────
    # AUTHENTICATION BYPASS TESTS
    # ──────────────────────────────────────────────────────────────────────────────

    @pytest.mark.asyncio
    async def test_jwt_token_manipulation(self):
        """Test JWT token manipulation attempts"""
        print("\n\nTesting JWT token manipulation...")
        
        # Create legitimate token
        legitimate_payload = {
            "user_id": str(self.test_user.id),
            "email": "<EMAIL>",
            "roles": ["user"],
            "exp": datetime.now(UTC) + timedelta(hours=1)
        }
        legitimate_token = create_jwt_token(legitimate_payload)
        
        # Test various manipulation attempts
        manipulation_attempts = [
            # Try to modify payload without proper signature
            {
                "name": "payload_manipulation",
                "token": legitimate_token.split('.')[0] + '.eyJ1c2VyX2lkIjoiYWRtaW4iLCJyb2xlcyI6WyJhZG1pbiJdfQ.' + legitimate_token.split('.')[2]
            },
            # Try with different algorithm
            {
                "name": "algorithm_confusion",
                "token": jwt.encode({"user_id": "admin", "roles": ["admin"]}, "public_key", algorithm="HS256")
            },
            # Try with no signature
            {
                "name": "no_signature",
                "token": legitimate_token.rsplit('.', 1)[0] + '.'
            },
            # Try completely malformed token
            {
                "name": "malformed_token",
                "token": "invalid.token.format"
            }
        ]
        
        vulnerabilities_found = 0
        
        for attempt in manipulation_attempts:
            try:
                # Try to verify the manipulated token
                decoded = verify_jwt_token(attempt["token"])
                
                # If verification succeeds, we have a serious vulnerability
                if decoded:
                    vulnerabilities_found += 1
                    self.security_results.add_vulnerability(
                        f"jwt_manipulation_{attempt['name']}",
                        "critical",
                        f"JWT token manipulation successful: {attempt['name']}",
                        "Attackers could forge authentication tokens and impersonate any user",
                        "Ensure JWT signature verification is properly implemented and cannot be bypassed"
                    )
                    
            except (jwt.InvalidTokenError, jwt.DecodeError, jwt.InvalidSignatureError) as e:
                # Good - token manipulation was detected and rejected
                continue
            except Exception as e:
                # Unexpected error - investigate
                print(f"Unexpected error with {attempt['name']}: {e}")
        
        if vulnerabilities_found == 0:
            self.security_results.add_security_check(
                "jwt_token_manipulation",
                True,
                "JWT token manipulation attempts properly rejected"
            )

    @pytest.mark.asyncio
    async def test_session_fixation(self):
        """Test session fixation vulnerabilities"""
        print("\n\nTesting session fixation...")
        
        # Create a session
        async with self.db_conn.get_db() as session:
            from app.services.session_service import create_user_session
            
            user_session = await create_user_session(
                session,
                self.redis_client,
                user_id=self.test_user.id,
                ip_address="*************",
                user_agent="Test Browser"
            )
            await session.commit()
            
            original_token = user_session.access_token
            
            # Try to reuse session from different IP/User Agent
            from app.services.session_service import validate_jwt_token
            
            # In a secure implementation, this should fail or require re-authentication
            is_valid, user_data = await validate_jwt_token(
                session,
                self.redis_client,
                original_token
            )
            
            # For now, we'll just verify that session validation works
            # In a more secure implementation, you'd check IP/User Agent consistency
            if is_valid:
                self.security_results.add_security_check(
                    "session_basic_validation",
                    True,
                    "Session validation functional (consider adding IP/User Agent validation)"
                )
            else:
                self.security_results.add_security_check(
                    "session_validation",
                    False,
                    "Session validation not working properly"
                )

    @pytest.mark.asyncio
    async def test_brute_force_protection(self):
        """Test brute force attack protection"""
        print("\n\nTesting brute force protection...")
        
        otp_manager = get_optimized_otp_manager(self.redis_client)
        test_email = "<EMAIL>"
        
        # Generate legitimate OTP
        legitimate_otp = await otp_manager.generate_otp(test_email)
        
        # Attempt brute force attack
        failed_attempts = 0
        lockout_triggered = False
        
        for attempt in range(20):  # Try 20 wrong codes
            try:
                is_valid, lockout_time = await otp_manager.verify_otp(
                    test_email, 
                    f"{attempt:06d}"  # Wrong codes: 000000, 000001, etc.
                )
                
                if not is_valid:
                    failed_attempts += 1
                    
                    if lockout_time > 0:
                        lockout_triggered = True
                        print(f"Lockout triggered after {failed_attempts} failed attempts")
                        break
                        
            except Exception as e:
                # Errors during brute force attempts are acceptable
                continue
        
        if lockout_triggered and failed_attempts <= 10:
            self.security_results.add_security_check(
                "brute_force_protection",
                True,
                f"Brute force protection activated after {failed_attempts} attempts"
            )
        elif lockout_triggered:
            self.security_results.add_security_check(
                "brute_force_protection_weak",
                False,
                f"Brute force protection too lenient - {failed_attempts} attempts allowed"
            )
        else:
            self.security_results.add_vulnerability(
                "brute_force_no_protection",
                "medium",
                "No brute force protection detected",
                "Attackers could attempt unlimited login attempts",
                "Implement account lockout after multiple failed authentication attempts"
            )

    # ──────────────────────────────────────────────────────────────────────────────
    # AUTHORIZATION BYPASS TESTS
    # ──────────────────────────────────────────────────────────────────────────────

    @pytest.mark.asyncio
    async def test_horizontal_privilege_escalation(self):
        """Test horizontal privilege escalation vulnerabilities"""
        print("\n\nTesting horizontal privilege escalation...")
        
        # Create another user
        async with self.db_conn.get_db() as session:
            other_user = User(
                email="<EMAIL>",
                name="Other User",
                status="active"
            )
            session.add(other_user)
            await session.commit()
        
        # Create tokens for both users
        user1_token = create_jwt_token({
            "user_id": str(self.test_user.id),
            "email": self.test_user.email,
            "roles": ["user"],
            "exp": datetime.now(UTC) + timedelta(hours=1)
        })
        
        user2_token = create_jwt_token({
            "user_id": str(other_user.id),
            "email": other_user.email,
            "roles": ["user"],
            "exp": datetime.now(UTC) + timedelta(hours=1)
        })
        
        # Test if user1 can access user2's data
        # This would require testing actual API endpoints
        # For now, we'll test the token validation logic
        
        # Verify each user can only access their own data through token validation
        user1_decoded = verify_jwt_token(user1_token)
        user2_decoded = verify_jwt_token(user2_token)
        
        if (user1_decoded["user_id"] == str(self.test_user.id) and 
            user2_decoded["user_id"] == str(other_user.id)):
            self.security_results.add_security_check(
                "horizontal_privilege_isolation",
                True,
                "User tokens properly isolated - each user has their own token"
            )
        else:
            self.security_results.add_vulnerability(
                "horizontal_privilege_escalation",
                "high",
                "User token isolation not working properly",
                "Users might access other users' data",
                "Ensure proper user isolation in token generation and validation"
            )

    @pytest.mark.asyncio
    async def test_vertical_privilege_escalation(self):
        """Test vertical privilege escalation vulnerabilities"""
        print("\n\nTesting vertical privilege escalation...")
        
        # Create user with limited privileges
        limited_user_token = create_jwt_token({
            "user_id": str(uuid.uuid4()),
            "email": "<EMAIL>",
            "roles": ["user"],  # Limited role
            "permissions": ["user.read"],
            "exp": datetime.now(UTC) + timedelta(hours=1)
        })
        
        # Create admin token for comparison
        admin_token = create_jwt_token({
            "user_id": str(uuid.uuid4()),
            "email": "<EMAIL>",
            "roles": ["admin"],
            "permissions": ["user.read", "user.write", "brand.admin"],
            "exp": datetime.now(UTC) + timedelta(hours=1)
        })
        
        # Verify role separation
        limited_decoded = verify_jwt_token(limited_user_token)
        admin_decoded = verify_jwt_token(admin_token)
        
        if (limited_decoded["roles"] == ["user"] and 
            admin_decoded["roles"] == ["admin"] and
            len(admin_decoded["permissions"]) > len(limited_decoded["permissions"])):
            self.security_results.add_security_check(
                "vertical_privilege_separation",
                True,
                "Role-based privilege separation working correctly"
            )
        else:
            self.security_results.add_vulnerability(
                "vertical_privilege_escalation",
                "high",
                "Role-based privilege separation not working",
                "Users might gain elevated privileges",
                "Ensure proper role and permission checking in all endpoints"
            )

    # ──────────────────────────────────────────────────────────────────────────────
    # INPUT VALIDATION TESTS
    # ──────────────────────────────────────────────────────────────────────────────

    @pytest.mark.asyncio
    async def test_input_validation_and_sanitization(self):
        """Test input validation and sanitization"""
        print("\n\nTesting input validation and sanitization...")
        
        malicious_inputs = [
            # XSS payloads
            "<script>alert('xss')</script>",
            "javascript:alert('xss')",
            "<img src=x onerror=alert('xss')>",
            "';alert('xss');//",
            
            # Path traversal
            "../../../etc/passwd",
            "..\\..\\..\\windows\\system32\\config\\sam",
            
            # Command injection
            "; cat /etc/passwd",
            "| whoami",
            "&& rm -rf /",
            
            # NoSQL injection
            "{'$ne': null}",
            "'; return true; //",
            
            # LDAP injection
            "*()(uid=*)",
            "admin)(&(password=*))",
            
            # XXE payloads
            "<?xml version='1.0'?><!DOCTYPE foo [<!ENTITY xxe SYSTEM 'file:///etc/passwd'>]><foo>&xxe;</foo>",
            
            # Buffer overflow attempts
            "A" * 10000,
            
            # Unicode attacks
            "\\u003cscript\\u003ealert('xss')\\u003c/script\\u003e",
            
            # Null byte injection
            "test\\x00.jpg",
        ]
        
        validation_failures = 0
        
        for payload in malicious_inputs:
            try:
                # Test in user creation
                user_data = {
                    "email": f"<EMAIL>",
                    "name": payload,  # Malicious payload in name field
                    "status": "active"
                }
                
                success, created_user, message = await create_user_cache_aside(
                    self.db_conn, self.redis_client, user_data
                )
                
                if success and created_user:
                    # Check if payload was stored as-is or sanitized
                    if created_user["name"] == payload:
                        # Payload stored without sanitization - potential issue
                        if any(dangerous in payload.lower() for dangerous in 
                               ['script', 'javascript', 'onerror', 'alert', '../', '..\\', ';', '|', '&']):
                            validation_failures += 1
                            print(f"Dangerous input stored unsanitized: {payload[:50]}...")
                            
            except Exception as e:
                # Input validation errors are good
                if "validation" in str(e).lower() or "invalid" in str(e).lower():
                    continue
                else:
                    print(f"Unexpected error with payload: {e}")
        
        if validation_failures > 0:
            self.security_results.add_vulnerability(
                "input_validation_insufficient",
                "medium",
                f"Insufficient input validation - {validation_failures} dangerous inputs accepted",
                "Stored XSS, command injection, or other attacks possible",
                "Implement comprehensive input validation and sanitization for all user inputs"
            )
        else:
            self.security_results.add_security_check(
                "input_validation",
                True,
                "Input validation and sanitization working properly"
            )

    # ──────────────────────────────────────────────────────────────────────────────
    # DATA EXPOSURE TESTS
    # ──────────────────────────────────────────────────────────────────────────────

    @pytest.mark.asyncio
    async def test_sensitive_data_exposure(self):
        """Test for sensitive data exposure"""
        print("\n\nTesting sensitive data exposure...")
        
        # Create user with potentially sensitive data
        user_data = {
            "email": "<EMAIL>",
            "name": "Sensitive User",
            "phone_number": "+1234567890",
            "status": "active"
        }
        
        success, created_user, message = await create_user_cache_aside(
            self.db_conn, self.redis_client, user_data
        )
        
        if success:
            # Check what data is returned in user object
            sensitive_fields_exposed = []
            
            # Fields that should not be exposed in API responses
            sensitive_fields = ["password", "password_hash", "secret_key", "private_key"]
            
            for field in sensitive_fields:
                if field in created_user:
                    sensitive_fields_exposed.append(field)
            
            # Check if internal IDs or system information is exposed
            if "internal_id" in created_user or "system_role" in created_user:
                sensitive_fields_exposed.append("internal_data")
            
            if sensitive_fields_exposed:
                self.security_results.add_vulnerability(
                    "sensitive_data_exposure",
                    "medium",
                    f"Sensitive fields exposed in API response: {sensitive_fields_exposed}",
                    "Sensitive information could be accessed by unauthorized parties",
                    "Remove sensitive fields from API responses and implement proper data filtering"
                )
            else:
                self.security_results.add_security_check(
                    "sensitive_data_exposure",
                    True,
                    "No obvious sensitive data exposure in user API responses"
                )

    @pytest.mark.asyncio
    async def test_error_message_information_disclosure(self):
        """Test for information disclosure through error messages"""
        print("\n\nTesting error message information disclosure...")
        
        information_disclosure = False
        
        # Test various error scenarios that might leak information
        error_test_cases = [
            {
                "name": "invalid_email_format",
                "data": {"email": "invalid-email", "name": "Test"},
                "expected_info": ["invalid", "format", "email"]
            },
            {
                "name": "duplicate_user",
                "data": {"email": "<EMAIL>", "name": "Duplicate"},  # Existing user
                "expected_info": ["exists", "duplicate", "already"]
            },
            {
                "name": "missing_fields",
                "data": {"name": "Test"},  # Missing email
                "expected_info": ["required", "missing", "field"]
            }
        ]
        
        for test_case in error_test_cases:
            try:
                success, user, message = await create_user_cache_aside(
                    self.db_conn, self.redis_client, test_case["data"]
                )
                
                # Check if error message contains sensitive information
                if not success and message:
                    message_lower = message.lower()
                    
                    # Good: Contains expected error information
                    contains_expected = any(info in message_lower for info in test_case["expected_info"])
                    
                    # Bad: Contains sensitive system information
                    sensitive_info = ["database", "sql", "table", "column", "postgres", 
                                    "redis", "exception", "traceback", "file path"]
                    contains_sensitive = any(info in message_lower for info in sensitive_info)
                    
                    if contains_sensitive:
                        information_disclosure = True
                        print(f"Sensitive info in error message ({test_case['name']}): {message}")
                        
            except Exception as e:
                # Check if exception messages leak sensitive information
                error_message = str(e).lower()
                sensitive_info = ["database", "sql", "postgres", "redis", "file", "path"]
                if any(info in error_message for info in sensitive_info):
                    information_disclosure = True
                    print(f"Sensitive info in exception: {e}")
        
        if information_disclosure:
            self.security_results.add_vulnerability(
                "information_disclosure_errors",
                "low",
                "Error messages contain sensitive system information",
                "Attackers could gain insights into system architecture and components",
                "Implement generic error messages for external users, log detailed errors internally"
            )
        else:
            self.security_results.add_security_check(
                "error_message_security",
                True,
                "Error messages do not disclose sensitive system information"
            )

    # ──────────────────────────────────────────────────────────────────────────────
    # CRYPTOGRAPHIC SECURITY TESTS
    # ──────────────────────────────────────────────────────────────────────────────

    @pytest.mark.asyncio
    async def test_password_storage_security(self):
        """Test password storage security (if implemented)"""
        print("\n\nTesting password storage security...")
        
        # Note: This test assumes password hashing is implemented
        # Since the current system uses OTP/magic links, we'll test OTP storage
        
        otp_manager = get_optimized_otp_manager(self.redis_client)
        test_email = "<EMAIL>"
        
        # Generate OTP
        otp_code = await otp_manager.generate_otp(test_email)
        
        # Check if OTP is stored in plaintext in Redis
        redis_keys = await self.redis_client.keys(f"*{test_email}*")
        plaintext_storage = False
        
        for key in redis_keys:
            try:
                value = await self.redis_client.get(key)
                if value and otp_code in str(value):
                    plaintext_storage = True
                    break
            except:
                continue
        
        if plaintext_storage:
            self.security_results.add_vulnerability(
                "plaintext_otp_storage",
                "medium",
                "OTP codes stored in plaintext",
                "OTP codes could be compromised if Redis is accessed",
                "Hash OTP codes before storing them"
            )
        else:
            self.security_results.add_security_check(
                "otp_storage_security",
                True,
                "OTP codes not stored in plaintext"
            )

    @pytest.mark.asyncio
    async def test_jwt_security_configuration(self):
        """Test JWT security configuration"""
        print("\n\nTesting JWT security configuration...")
        
        security_issues = []
        
        # Test JWT secret strength
        jwt_secret = APP_CONFIG.jwt_secret_key
        
        if len(jwt_secret) < 32:
            security_issues.append("JWT secret too short")
        
        # Test for default/weak secrets
        weak_secrets = ["secret", "password", "123456", "jwt_secret", "your-secret-key"]
        if jwt_secret.lower() in weak_secrets:
            security_issues.append("JWT secret is a common/default value")
        
        # Test token with weak algorithm
        try:
            # Try to create token with 'none' algorithm (should be rejected)
            weak_token = jwt.encode({"user_id": "test"}, "", algorithm="none")
            decoded = jwt.decode(weak_token, options={"verify_signature": False})
            if decoded:
                security_issues.append("JWT accepts 'none' algorithm")
        except:
            # Good - 'none' algorithm rejected
            pass
        
        if security_issues:
            self.security_results.add_vulnerability(
                "jwt_configuration_weak",
                "medium",
                f"JWT configuration issues: {', '.join(security_issues)}",
                "Weak JWT configuration could allow token forgery",
                "Use strong JWT secrets (32+ chars), reject 'none' algorithm, use secure algorithms"
            )
        else:
            self.security_results.add_security_check(
                "jwt_configuration",
                True,
                "JWT configuration appears secure"
            )

    # ──────────────────────────────────────────────────────────────────────────────
    # RATE LIMITING TESTS
    # ──────────────────────────────────────────────────────────────────────────────

    @pytest.mark.asyncio
    async def test_api_rate_limiting(self):
        """Test API rate limiting"""
        print("\n\nTesting API rate limiting...")
        
        # Test rapid user creation attempts
        rapid_requests = []
        rate_limited = False
        
        for i in range(50):  # Try 50 rapid requests
            try:
                start_time = datetime.now()
                
                user_data = {
                    "email": f"rate.test.{i}@sectest.com",
                    "name": f"Rate Test User {i}",
                    "status": "active"
                }
                
                success, user, message = await create_user_cache_aside(
                    self.db_conn, self.redis_client, user_data
                )
                
                end_time = datetime.now()
                duration = (end_time - start_time).total_seconds()
                
                rapid_requests.append({
                    "attempt": i,
                    "success": success,
                    "duration": duration,
                    "message": message
                })
                
                # Check if request was rate limited
                if not success and "rate" in message.lower():
                    rate_limited = True
                    print(f"Rate limiting activated at attempt {i}")
                    break
                    
            except Exception as e:
                if "rate" in str(e).lower() or "limit" in str(e).lower():
                    rate_limited = True
                    break
        
        successful_requests = len([r for r in rapid_requests if r["success"]])
        
        if rate_limited:
            self.security_results.add_security_check(
                "api_rate_limiting",
                True,
                f"Rate limiting activated after {len(rapid_requests)} attempts"
            )
        elif successful_requests > 30:
            self.security_results.add_vulnerability(
                "no_rate_limiting",
                "medium",
                f"No rate limiting detected - {successful_requests} rapid requests succeeded",
                "API could be abused for DoS attacks or spam",
                "Implement rate limiting on API endpoints"
            )
        else:
            self.security_results.add_security_check(
                "api_rate_limiting_unclear",
                True,
                f"Rate limiting status unclear - {successful_requests} requests succeeded"
            )

    # ──────────────────────────────────────────────────────────────────────────────
    # SECURITY HEADERS AND CONFIGURATION TESTS
    # ──────────────────────────────────────────────────────────────────────────────

    @pytest.mark.asyncio
    async def test_security_configuration(self):
        """Test overall security configuration"""
        print("\n\nTesting security configuration...")
        
        # Test environment configuration
        config_issues = []
        
        # Check if running in debug mode (if applicable)
        if hasattr(APP_CONFIG, 'debug') and APP_CONFIG.debug:
            config_issues.append("Debug mode enabled in production")
        
        # Check for secure defaults
        if hasattr(APP_CONFIG, 'cors_origins'):
            if "*" in str(APP_CONFIG.cors_origins):
                config_issues.append("CORS allows all origins")
        
        # Database connection security
        # Note: This would require access to database connection string
        # For now, we'll just verify we can connect securely
        try:
            async with self.db_conn.get_db() as session:
                # Test if we can execute raw SQL (should be restricted)
                try:
                    await session.execute(text("SELECT version()"))
                    # If this succeeds, raw SQL execution is enabled
                    config_issues.append("Raw SQL execution allowed")
                except:
                    # Good - raw SQL execution restricted
                    pass
        except:
            config_issues.append("Database connection issues")
        
        if config_issues:
            self.security_results.add_vulnerability(
                "security_configuration_issues",
                "low",
                f"Security configuration issues: {', '.join(config_issues)}",
                "Insecure configuration could expose application to attacks",
                "Review and harden security configuration"
            )
        else:
            self.security_results.add_security_check(
                "security_configuration",
                True,
                "Basic security configuration appears adequate"
            )


if __name__ == "__main__":
    # Run security tests
    pytest.main([__file__, "-v", "--tb=short", "-s"])
