# Phyllo Dummy API

A basic FastAPI application created with Python 3.11 and UV dependency manager.

## Requirements

- Python 3.11
- UV package manager

## Setup

1. Install dependencies with UV:

```bash
uv pip install -r requirements.txt
```

2. Run the application:

```bash
./run.py
```

Alternatively, you can run with:

```bash
uvicorn app.main:app --reload
```

## API Endpoints

- `GET /`: Welcome message
- `GET /health`: Health check endpoint

## Documentation

Once the API is running, you can access the auto-generated documentation:

- OpenAPI UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc
