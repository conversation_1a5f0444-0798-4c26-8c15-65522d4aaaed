# CreatorVerse Filter Discovery System

A focused, single-purpose platform for filter-based creator discovery across social media platforms.

## 🎯 Overview

This system provides advanced filtering capabilities to discover creators on Instagram, YouTube, and TikTok through a clean, cache-optimized API.

## 🏗️ Architecture

**Single Filter Discovery Flow:**
```
UI Request → Filter Service → Cache Check → Database → External API → Processing → Cache → Response
```

## 🔧 Tech Stack

- **FastAPI** - Modern async web framework
- **PostgreSQL** - Filter catalog database
- **Redis** - Filter result caching  
- **Phyllo API** - Creator discovery provider

## 🚀 Quick Start

1. **Install Dependencies**
   ```bash
   uv pip install -r requirements.txt
   ```

2. **Environment Setup**
   ```bash
   cp .env.example .env
   # Configure database and Redis connections
   ```

3. **Start Redis**
   ```bash
   redis-server
   ```

4. **Run Application**
   ```bash
   python main.py
   ```

5. **API Documentation**
   - Swagger UI: http://localhost:8000/docs
   - Filter Health: http://localhost:8000/api/v1/filters/health

## 🎛️ Filter System

### Supported Platforms
- Instagram
- YouTube  
- TikTok

### Filter Types
- Demographics & Identity
- Performance Metrics
- Content & Niche
- Credibility & Platform

### Key Endpoints
- `GET /api/v1/filters/` - Get available filters
- `POST /api/v1/filters/apply` - Apply filters to discover creators
- `GET /api/v1/filters/groups` - Get filter groups
- `POST /api/v1/filters/saved-sets` - Save filter combinations

## 📁 Project Structure

```
├── main.py                    # Application entry point
├── app/
│   ├── api/filter_endpoints.py    # Filter API
│   ├── services/filter_service.py # Filter logic
│   ├── models/filter_models.py    # Database models
│   └── schemas/filter_schemas.py  # Validation schemas
├── utils/redis_client.py          # Caching layer
└── config/                        # Configuration
```

## 🔍 What This System Does

✅ Creator discovery through advanced filtering  
✅ Multi-platform creator search  
✅ Filter configuration and management  
✅ Cached filter results for performance  
✅ Public creator metadata discovery  

## 🚫 What This System Does NOT Do

❌ Analytics data collection or processing  
❌ User authentication or OAuth management  
❌ Real-time data streaming  
❌ Database migrations  
❌ Background job processing  
❌ Complex deployment orchestration  

## 🐳 Docker Deployment

```bash
docker build -t creatorverse-filter-discovery .
docker run -p 8000:8000 creatorverse-filter-discovery
```

## 📊 Database Schema

The system uses PostgreSQL with a `filter_catalog` schema containing:
- `filter_groups` - Logical filter groupings
- `filter_definitions` - Individual filter configurations  
- `saved_filter_sets` - User-saved filter combinations
- `location_hierarchy` - Geographic filtering data

## 🔄 Filter Discovery Flow

1. Frontend sends filter criteria
2. Filter service processes configuration
3. Redis cache checked for existing results
4. Database queried for filter definitions
5. Phyllo API called for creator discovery
6. Results processed and filtered
7. Results cached for future requests
8. Filtered creator list returned

## 🤝 Contributing

This is a focused filter discovery system. Contributions should maintain the clean, single-purpose architecture.

## 📝 License

[Your License Here]
