<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CreatorVerse Data Collection Pipeline: A Visual Journey</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: hidden;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 50px;
            color: white;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .pipeline-container {
            position: relative;
            margin: 50px 0;
        }

        .pipeline-step {
            display: flex;
            align-items: center;
            margin: 40px 0;
            padding: 30px;
            background: white;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            transform: translateX(-100px);
            opacity: 0;
            animation: slideIn 0.8s ease forwards;
            position: relative;
            overflow: hidden;
            cursor: pointer;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .pipeline-step:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .pipeline-step:nth-child(even) {
            flex-direction: row-reverse;
            transform: translateX(100px);
        }

        .pipeline-step:nth-child(1) {
            animation-delay: 0.2s;
        }

        .pipeline-step:nth-child(2) {
            animation-delay: 0.4s;
        }

        .pipeline-step:nth-child(3) {
            animation-delay: 0.6s;
        }

        .pipeline-step:nth-child(4) {
            animation-delay: 0.8s;
        }

        .pipeline-step:nth-child(5) {
            animation-delay: 1.0s;
        }

        .pipeline-step:nth-child(6) {
            animation-delay: 1.2s;
        }

        .pipeline-step:nth-child(7) {
            animation-delay: 1.4s;
        }

        .pipeline-step:nth-child(8) {
            animation-delay: 1.6s;
        }

        @keyframes slideIn {
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        .step-icon {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            margin: 0 30px;
            position: relative;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
            flex-shrink: 0;
            transition: transform 0.3s ease;
        }

        .step-content {
            flex: 1;
            padding: 0 20px;
        }

        .step-number {
            display: inline-block;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-bottom: 15px;
        }

        .step-title {
            font-size: 1.8rem;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c3e50;
        }

        .step-description {
            font-size: 1rem;
            line-height: 1.6;
            color: #555;
            margin-bottom: 20px;
        }

        .step-details {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 12px;
            border-left: 4px solid #667eea;
            margin-top: 15px;
            transition: all 0.3s ease;
        }

        .step-details h4 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 1.1rem;
        }

        .step-details ul {
            list-style: none;
            padding-left: 0;
        }

        .step-details li {
            padding: 5px 0;
            padding-left: 20px;
            position: relative;
        }

        .step-details li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #27ae60;
            font-weight: bold;
        }

        .timeline-connector {
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            width: 4px;
            height: 100px;
            background: linear-gradient(to bottom, #667eea, #764ba2);
            z-index: -1;
        }

        .data-flow {
            display: flex;
            justify-content: space-around;
            margin: 50px 0;
            padding: 30px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            backdrop-filter: blur(10px);
            flex-wrap: wrap;
        }

        .data-box {
            text-align: center;
            color: white;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            backdrop-filter: blur(5px);
            margin: 10px;
            flex: 1;
            min-width: 200px;
            transition: transform 0.3s ease;
        }

        .data-box:hover {
            transform: scale(1.05);
        }

        .data-box h3 {
            margin-bottom: 10px;
            font-size: 1.2rem;
        }

        .data-box p {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
            }

            50% {
                transform: scale(1.05);
            }

            100% {
                transform: scale(1);
            }
        }

        .floating {
            animation: floating 3s ease-in-out infinite;
        }

        @keyframes floating {

            0%,
            100% {
                transform: translateY(0px);
            }

            50% {
                transform: translateY(-10px);
            }
        }

        .tech-stack {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 15px;
            margin: 30px 0;
        }

        .tech-badge {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 8px 16px;
            border-radius: 25px;
            font-size: 0.9rem;
            backdrop-filter: blur(5px);
            transition: transform 0.3s ease;
        }

        .tech-badge:hover {
            transform: scale(1.1);
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 40px 0;
        }

        .metric-card {
            background: rgba(255, 255, 255, 0.9);
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .metric-card:hover {
            transform: translateY(-5px);
        }

        .metric-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }

        .metric-label {
            color: #555;
            font-size: 1rem;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e0e0e0;
            border-radius: 4px;
            margin: 10px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 4px;
            transition: width 2s ease;
        }

        @media (max-width: 768px) {
            .pipeline-step {
                flex-direction: column !important;
                text-align: center;
                padding: 20px;
            }

            .pipeline-step:nth-child(even) {
                flex-direction: column !important;
            }

            .step-icon {
                margin: 0 0 20px 0;
                width: 80px;
                height: 80px;
                font-size: 2rem;
            }

            .header h1 {
                font-size: 2rem;
            }

            .data-flow {
                flex-direction: column;
            }
        }

        .code-snippet {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            overflow-x: auto;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>🚀 CreatorVerse Data Collection Pipeline</h1>
            <p>A Visual Journey Through Authorized Creator Analytics Collection</p>
            <div class="tech-stack">
                <span class="tech-badge">Python FastAPI</span>
                <span class="tech-badge">PostgreSQL</span>
                <span class="tech-badge">ClickHouse</span>
                <span class="tech-badge">Redis</span>
                <span class="tech-badge">Kafka</span>
                <span class="tech-badge">OAuth 2.0</span>
            </div>
        </div>

        <!-- Live Metrics Dashboard -->
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-number" id="userCount">1,247</div>
                <div class="metric-label">Authorized Users</div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 95%"></div>
                </div>
            </div>
            <div class="metric-card">
                <div class="metric-number" id="successRate">98.5%</div>
                <div class="metric-label">Success Rate</div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 98.5%"></div>
                </div>
            </div>
            <div class="metric-card">
                <div class="metric-number" id="responseTime">12ms</div>
                <div class="metric-label">API Response Time</div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 88%"></div>
                </div>
            </div>
            <div class="metric-card">
                <div class="metric-number" id="dataPoints">2,891</div>
                <div class="metric-label">Daily Collections</div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 92%"></div>
                </div>
            </div>
        </div>

        <div class="pipeline-container">
            <!-- Step 1: Scheduler -->
            <div class="pipeline-step">
                <div class="step-icon pulse">
                    ⏰
                </div>
                <div class="step-content">
                    <div class="step-number">1</div>
                    <h2 class="step-title">Scheduler Awakens</h2>
                    <p class="step-description">
                        Every night at 2:00 AM UTC, our intelligent scheduler springs to life. Like a digital guardian,
                        it ensures no overlapping processes and validates system health before beginning the day's
                        collection journey.
                    </p>
                    <div class="step-details">
                        <h4>🔧 What Happens Here:</h4>
                        <ul>
                            <li>APScheduler triggers daily collection job</li>
                            <li>System health validation (PostgreSQL, Redis, Kafka)</li>
                            <li>Prevents overlapping runs with max_instances=1</li>
                            <li>Sends monitoring notifications to track start</li>
                            <li>Creates unique execution ID for traceability</li>
                        </ul>
                        <div class="code-snippet">
                            # The moment of awakening<br>
                            execution_id = f"daily_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}"<br>
                            self.logger.info(f"🌅 Daily collection awakens: {execution_id}")
                        </div>
                    </div>
                </div>
            </div>

            <!-- Step 2: Token Manager -->
            <div class="pipeline-step">
                <div class="step-icon floating">
                    🔐
                </div>
                <div class="step-content">
                    <div class="step-number">2</div>
                    <h2 class="step-title">Token Guardian Searches</h2>
                    <p class="step-description">
                        The Token Manager dives deep into our OAuth vault, seeking users who have trusted us with their
                        social media keys.
                        It validates each token like a security expert, ensuring we only collect from willing
                        participants.
                    </p>
                    <div class="step-details">
                        <h4>🔍 Discovery Process:</h4>
                        <ul>
                            <li>Query users.oauth_accounts for active tokens</li>
                            <li>Filter: access_token NOT NULL and not expired</li>
                            <li>Group by platform: Instagram, YouTube, TikTok</li>
                            <li>Apply 1-hour expiration buffer for safety</li>
                            <li>Log statistics: "Found 1,247 users with 2,891 connections"</li>
                        </ul>
                        <div class="code-snippet">
                            SELECT DISTINCT user_id, provider<br>
                            FROM users.oauth_accounts<br>
                            WHERE provider IN ('instagram', 'youtube', 'tiktok')<br>
                            AND access_token IS NOT NULL<br>
                            AND expires_at > NOW() + INTERVAL '1 hour'
                        </div>
                    </div>
                </div>
            </div>

            <!-- Step 3: Collection Service -->
            <div class="pipeline-step">
                <div class="step-icon pulse">
                    🎯
                </div>
                <div class="step-content">
                    <div class="step-number">3</div>
                    <h2 class="step-title">Master Planner Strategizes</h2>
                    <p class="step-description">
                        The Collection Service transforms raw user lists into an optimized battle plan. Like a chess
                        master,
                        it considers priorities, estimates timing, and creates the perfect strategy for efficient data
                        collection.
                    </p>
                    <div class="step-details">
                        <h4>🧠 Strategic Planning:</h4>
                        <ul>
                            <li>Create user-platform mapping for optimization</li>
                            <li>Prioritize high-value users (verified, enterprise clients)</li>
                            <li>Estimate processing time: 30-60 seconds per platform</li>
                            <li>Create batches of 50 users for parallel processing</li>
                            <li>Balance load across platforms and time zones</li>
                        </ul>
                        <div class="code-snippet">
                            user_platforms = {<br>
                            &nbsp;&nbsp;'user123': ['instagram', 'youtube'],<br>
                            &nbsp;&nbsp;'user456': ['instagram', 'tiktok']<br>
                            }<br>
                            batches = create_optimal_batches(collection_items, batch_size=50)
                        </div>
                    </div>
                </div>
            </div>

            <!-- Step 4: Job Factory -->
            <div class="pipeline-step">
                <div class="step-icon floating">
                    🏭
                </div>
                <div class="step-content">
                    <div class="step-number">4</div>
                    <h2 class="step-title">Job Factory Manufactures</h2>
                    <p class="step-description">
                        The Job Factory works like a precision assembly line, crafting thousands of structured
                        collection jobs.
                        Each job is a perfect package containing user details, platform configs, and retry instructions.
                    </p>
                    <div class="step-details">
                        <h4>⚙️ Manufacturing Process:</h4>
                        <ul>
                            <li>Generate unique job IDs: "daily_20240115_000001"</li>
                            <li>Set platform configs: Instagram (45s), YouTube (60s)</li>
                            <li>Add retry logic: max 3 attempts with backoff</li>
                            <li>Include metadata: priority, tags, timing estimates</li>
                            <li>Create 2,891 jobs from 1,247 users across platforms</li>
                        </ul>
                        <div class="code-snippet">
                            DataCollectionJob(<br>
                            &nbsp;&nbsp;job_id="daily_20240115_000001",<br>
                            &nbsp;&nbsp;user_id="user-uuid",<br>
                            &nbsp;&nbsp;platform=PlatformType.INSTAGRAM,<br>
                            &nbsp;&nbsp;priority=JobPriority.HIGH,<br>
                            &nbsp;&nbsp;estimated_duration_seconds=45<br>
                            )
                        </div>
                    </div>
                </div>
            </div>

            <!-- Step 5: Job Publisher -->
            <div class="pipeline-step">
                <div class="step-icon pulse">
                    📡
                </div>
                <div class="step-content">
                    <div class="step-number">5</div>
                    <h2 class="step-title">Publisher Broadcasts Jobs</h2>
                    <p class="step-description">
                        The Job Publisher acts like a sophisticated radio station, broadcasting jobs across Kafka
                        channels.
                        It ensures high-priority users get VIP treatment while maintaining perfect message ordering and
                        delivery.
                    </p>
                    <div class="step-details">
                        <h4>📻 Broadcasting Strategy:</h4>
                        <ul>
                            <li>Publish to "creator-analytics-jobs" Kafka topic</li>
                            <li>Priority ordering: urgent → high → normal → low</li>
                            <li>Message key: "user123:instagram" for partitioning</li>
                            <li>Headers: job_id, platform, priority, execution_id</li>
                            <li>Track success rate: 2,847 published, 44 failed</li>
                        </ul>
                        <div class="code-snippet">
                            kafka_message = {<br>
                            &nbsp;&nbsp;"key": "user123:instagram",<br>
                            &nbsp;&nbsp;"value": job.model_dump_json(),<br>
                            &nbsp;&nbsp;"headers": {"job_id": job.job_id, "priority": "high"}<br>
                            }
                        </div>
                    </div>
                </div>
            </div>

            <!-- Step 6: Consumers -->
            <div class="pipeline-step">
                <div class="step-icon floating">
                    🤖
                </div>
                <div class="step-content">
                    <div class="step-number">6</div>
                    <h2 class="step-title">Worker Bots Collect Data</h2>
                    <p class="step-description">
                        A fleet of specialized consumer bots springs into action! Each bot is an expert in its platform,
                        speaking fluent Instagram Graph API, YouTube Analytics, and TikTok Business API to gather
                        precious insights.
                    </p>
                    <div class="step-details">
                        <h4>🤖 Collection Army in Action:</h4>
                        <ul>
                            <li>Instagram bots: 200 req/hour limit, 18-second delays</li>
                            <li>YouTube bots: 10K quota units/day, smart allocation</li>
                            <li>TikTok bots: 100 req/minute, 0.6-second intervals</li>
                            <li>Validate tokens before each API call</li>
                            <li>Auto-refresh expired tokens seamlessly</li>
                        </ul>
                        <div class="code-snippet">
                            # Platform-specific collection<br>
                            if platform == 'instagram':<br>
                            &nbsp;&nbsp;analytics = await instagram_adapter.collect_user_analytics(user_id)<br>
                            elif platform == 'youtube':<br>
                            &nbsp;&nbsp;analytics = await youtube_adapter.collect_user_analytics(user_id)
                        </div>
                    </div>
                </div>
            </div>

            <!-- Step 7: Dual Storage -->
            <div class="pipeline-step">
                <div class="step-icon pulse">
                    🗄️
                </div>
                <div class="step-content">
                    <div class="step-number">7</div>
                    <h2 class="step-title">Data Librarians Store Treasures</h2>
                    <p class="step-description">
                        Our data librarians work in perfect harmony, filing each piece of analytics in two specialized
                        libraries.
                        PostgreSQL keeps the complete stories, while ClickHouse organizes time-series data for
                        lightning-fast analysis.
                    </p>
                    <div class="step-details">
                        <h4>📚 Dual Library System:</h4>
                        <ul>
                            <li>PostgreSQL: Complete JSON analytics for operations</li>
                            <li>ClickHouse: Time-series metrics for analysis</li>
                            <li>Automatic data transformation and optimization</li>
                            <li>ACID compliance for data integrity</li>
                            <li>Monthly partitioning for query performance</li>
                        </ul>
                        <div class="code-snippet">
                            # Dual storage strategy<br>
                            await store_postgres(analytics) # Complete JSON<br>
                            await store_clickhouse(analytics) # Time-series<br>
                            # Best of both worlds!
                        </div>
                    </div>
                </div>
            </div>

            <!-- Step 8: Caching -->
            <div class="pipeline-step">
                <div class="step-icon floating">
                    ⚡
                </div>
                <div class="step-content">
                    <div class="step-number">8</div>
                    <h2 class="step-title">Speed Demon Caches Results</h2>
                    <p class="step-description">
                        Finally, our Redis speed demon caches the fresh analytics for instant API responses.
                        Users get sub-second data access while the system completes its daily collection summary with
                        pride.
                    </p>
                    <div class="step-details">
                        <h4>⚡ Lightning Fast Access:</h4>
                        <ul>
                            <li>Redis caching with 1-hour TTL for fresh data</li>
                            <li>API-ready format: "latest_analytics:user123:instagram"</li>
                            <li>Sub-second response times for user queries</li>
                            <li>Collection summaries for monitoring dashboards</li>
                            <li>Cache invalidation strategies for real-time updates</li>
                        </ul>
                        <div class="code-snippet">
                            # Lightning-fast caching<br>
                            await redis.set(<br>
                            &nbsp;&nbsp;f"user_analytics:{user_id}:{platform}",<br>
                            &nbsp;&nbsp;json.dumps(analytics),<br>
                            &nbsp;&nbsp;ex=3600 # 1 hour TTL<br>
                            )
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="data-flow">
            <div class="data-box">
                <h3>📊 Data Collected</h3>
                <p>Profile metrics, engagement rates, audience insights, content performance</p>
            </div>
            <div class="data-box">
                <h3>⏱️ Processing Time</h3>
                <p>2-4 hours for complete daily collection of all authorized users</p>
            </div>
            <div class="data-box">
                <h3>🎯 Success Rate</h3>
                <p>98.5% average success rate with automatic retry and token refresh</p>
            </div>
            <div class="data-box">
                <h3>🔒 Privacy First</h3>
                <p>Only authorized users' data collected through explicit OAuth consent</p>
            </div>
        </div>

        <div class="header">
            <h2 style="color: white; margin-top: 50px;">🎯 The Result</h2>
            <p style="color: white; opacity: 0.9; font-size: 1.1rem; max-width: 800px; margin: 20px auto;">
                Every morning, brands and marketers wake up to fresh, comprehensive analytics from thousands of creators
                who have trusted our platform with their data. This automated pipeline ensures privacy compliance,
                platform API respect, and lightning-fast insights delivery.
            </p>
        </div>
    </div>

    <script>
        // Add interactive elements
        document.querySelectorAll('.step-icon').forEach(icon => {
            icon.addEventListener('mouseenter', function () {
                this.style.transform = 'scale(1.1) rotate(5deg)';
                this.style.transition = 'transform 0.3s ease';
            });

            icon.addEventListener('mouseleave', function () {
                this.style.transform = 'scale(1) rotate(0deg)';
            });
        });

        // Toggle details on click
        document.querySelectorAll('.pipeline-step').forEach(step => {
            const details = step.querySelector('.step-details');

            step.addEventListener('click', function () {
                if (details.style.maxHeight) {
                    details.style.maxHeight = null;
                    details.style.opacity = '0';
                } else {
                    details.style.maxHeight = details.scrollHeight + "px";
                    details.style.opacity = '1';
                }
            });
        });

        // Animate metrics
        function animateMetrics() {
            const userCount = document.getElementById('userCount');
            const successRate = document.getElementById('successRate');
            const responseTime = document.getElementById('responseTime');
            const dataPoints = document.getElementById('dataPoints');

            // Simulate live updates
            let users = 1200;
            setInterval(() => {
                users += Math.floor(Math.random() * 5);
                userCount.textContent = users.toLocaleString();
            }, 5000);

            // Simulate response time fluctuation
            setInterval(() => {
                const time = Math.floor(Math.random() * 10) + 8;
                responseTime.textContent = time + 'ms';
            }, 3000);

            // Simulate data points growth
            let points = 2800;
            setInterval(() => {
                points += Math.floor(Math.random() * 20);
                dataPoints.textContent = points.toLocaleString();
            }, 7000);
        }

        // Simulate data flow animation
        function simulateDataFlow() {
            const dataBoxes = document.querySelectorAll('.data-box');
            dataBoxes.forEach((box, index) => {
                setTimeout(() => {
                    box.style.transform = 'scale(1.05)';
                    box.style.transition = 'transform 0.3s ease';
                    setTimeout(() => {
                        box.style.transform = 'scale(1)';
                    }, 300);
                }, index * 500);
            });
        }

        // Add ripple effect to cards
        document.querySelectorAll('.metric-card, .pipeline-step').forEach(card => {
            card.addEventListener('click', function (e) {
                const ripple = document.createElement('div');
                const rect = this.getBoundingClientRect();
                const size = Math.max(rect.width, rect.height);
                const x = e.clientX - rect.left - size / 2;
                const y = e.clientY - rect.top - size / 2;

                ripple.style.width = ripple.style.height = size + 'px';
                ripple.style.left = x + 'px';
                ripple.style.top = y + 'px';
                ripple.style.position = 'absolute';
                ripple.style.borderRadius = '50%';
                ripple.style.background = 'rgba(255,255,255,0.3)';
                ripple.style.transform = 'scale(0)';
                ripple.style.animation = 'ripple 0.6s linear';
                ripple.style.pointerEvents = 'none';

                this.style.position = 'relative';
                this.style.overflow = 'hidden';
                this.appendChild(ripple);

                setTimeout(() => {
                    ripple.remove();
                }, 600);
            });
        });

        // CSS for ripple animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes ripple {
                to {
                    transform: scale(2);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);

        // Initialize animations
        animateMetrics();
        setInterval(simulateDataFlow, 8000);

        // Add scroll animations
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        });

        document.querySelectorAll('.metric-card').forEach(card => {
            observer.observe(card);
        });
    </script>
</body>

</html>