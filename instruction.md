# Project Summary

This document lays out the core architecture, guidelines, and implementation instructions for the standalone Publishing Module. The module empowers Creators to draft, submit, schedule, and publish content to social channels, while providing Brand<PERSON> with a transparent approval workflow.

**Scope:** Focus exclusively on publishing workflows. Analytics dashboards and the upstream data‑collection pipeline are out of scope for Phase 1.

## 1. Architecture Principles

| Principle | Rationale |
| --------- | --------- |
| **Event‑Driven Microservices – Kafka is the "central nervous system"** | Decouples services; enables back‑pressure handling & replayability. |
| **Async‑First Design (Python 3.11 + uv + async SQLAlchemy)** | Non‑blocking IO for high concurrency. |
| **Platform‑Agnostic Adapters** | Plug‑and‑play integrations for Instagram, YouTube today; Twitter, Facebook, LinkedIn tomorrow. |

## 2. Key Publishing Flows

| Flow                     | Path                                                                                      | SLA / Latency Targets                                   |
| ------------------------ | ----------------------------------------------------------------------------------------- | ------------------------------------------------------- |
| **⚡ Immediate Publishing**  | Client → Publishing Service → Kafka → Social Adapter → Platform                           | ≤ 50 ms sync API response; 2‑5 s end‑to‑end publish     |
| **⏰ Scheduled Publishing**   | Store schedule → Scheduler Service (Cron every 60 s) → Kafka → Social Adapter → Platform   | 99 % of posts published within ± 60 s of scheduled time   |
| **🔄 5‑Stage Pipeline**        | Content Validation → Media Processing → Platform Transformation → Publishing Execution → Result Aggregation | Each stage emits its own Kafka topic for observability & retries |

## 3. Service Architecture

| Service                    | Tech                               | Core Responsibilities                                                       |
| -------------------------- | ---------------------------------- | --------------------------------------------------------------------------- |
| **Publishing Service**     | FastAPI, async SQLAlchemy          | REST API; business orchestration; writes to Kafka.                          |
| **Scheduler Service**      | Async Python CronJob               | Time‑zone aware trigger; publishes publish_job messages.                    |
| **Social Media Adapters**  | Async Python SDK wrappers          | Channel‑specific publish & status‑poll; one deployment per platform.         |
| **Media Processing Service** | Python + ffmpeg libs               | Validate, transcode, compress media; store into pluggable object storage (e.g., MinIO). |
| **Retry Pipeline**         | Lightweight worker                 | Re‑queues failed publishes with exponential back‑off.                       |
| **Notification Service**   | FastAPI                            | Sends Slack / email notifications on publish success/failure.               |

*Supporting infrastructure:* Self‑hosted Kafka, PostgreSQL, Redis. Existing async logger utilities must be reused for uniform structured logging.

## 4. Core Data Models & Tables (PostgreSQL – publishing schema)

| Table       | Key Fields                                              | Purpose                                  |
| ----------- | ------------------------------------------------------- | ---------------------------------------- |
| **drafts**      | id, creator_id, media_url, caption, state, timestamps   | Mutable work‑in‑progress content.         |
| **submissions** | id, draft_id, brand_id, feedback_json, state, timestamps | Drafts sent to brands.                    |
| **approvals**   | id, submission_id, approver_id, decision, comment, decided_at | Brand audit log.                          |
| **schedules**   | id, approval_id, channel, scheduled_at, is_post_now     | Post timing.                              |
| **posts**       | id, schedule_id, external_post_id, status, published_at, raw_json | Final platform publish reference.         |

*Note:* Redis is used for rate limiter keys & distributed locks.

## 5. Technology Stack

- Python 3.11 + uv for ultra‑fast dependency resolution.
- FastAPI + Pydantic v2 for type‑safe REST.
- Async SQLAlchemy for Postgres access (asyncpg driver).
- Kafka for event streaming – topics per stage.
- PostgreSQL for transactional state.
- **Rate limiit slow api python package**

## 6. Guidelines & Constraints

### 6.1 Do’s
- Single‑responsibility micro‑services – avoid God classes.
- Idempotent processing – dedup by external_post_id.
- Reuse existing Redis and async‑logger utilities across all services.
- Health/readiness probes on `/healthz` & `/readyz`.

### 6.2 Don’ts
- No Celery, RabbitMQ – Kafka only.
- No .env files – use appsettings.json or ENV‑mapped ConfigMap.
- No AWS‑specific services – API Gateway, Cognito, S3, Secrets Manager, etc. are strictly disallowed.
- No server‑side SDK secrets hard‑coded – read via environment or Vault.

## 7. Project Locations & MCP Instructions

| Context                        | Path                                                                                          | Instruction                                                                                                                                                                    |
| ------------------------------ | --------------------------------------------------------------------------------------------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **Publishing Module codebase** | /home/<USER>/Desktop/workspace/creatorverse_services/publishing_pipeline                        | Create all new services (Publishing, Scheduler, Adapters, etc.) here. Follow package layout: src/domain, src/application, src/infrastructure, src/api, mirroring Clean‑Architecture used elsewhere. |
| **Reference backend**          | /home/<USER>/Desktop/workspace/creatorverse_services/creatorverse_user_backend                   | Reuse database layer, Redis helpers & key patterns, and async logger modules from this project. Adhere to its coding conventions (linting, tests, config parsing).          |
| **MCP memory Server**          | Provided via internal tooling                                                                 | Update this guideline file after every significant change using MCP memory tracking (additions, deletions, modifications).                                                     |

## 8. Next Steps

- Finalise Postgres ERD & indices.
- Define Kafka topic configs (partitions, retention, DLQ).
- Produce sequence diagrams for both Immediate & Scheduled flows.
- Draft per‑service README with local dev commands.
