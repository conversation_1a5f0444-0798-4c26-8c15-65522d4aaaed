{
  "servers": {
    "redis-mcp": {
      "type": "sse",
      "url": "http://127.0.0.1:8000/sse"
    },
    "sequential-thinking": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]
    },
    "everything-search": {
      "command": "uvx",
      "args": ["mcp-server-everything-search"]
    },
    "postgres": {
      "command": "uv",
      "args": [
        "run",
        "/home/<USER>/Desktop/workspace/cmps/postgres/.venv/bin/postgres-mcp",
        "--access-mode=unrestricted"
      ],
      "env": {
        "DATABASE_URI": "*******************************************************/postgres"
      }
    },

    "memory": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-memory"]
    },
    "postman-runner": {
      "command": "node",
      "args": ["/home/<USER>/Desktop/workspace/cmps/mcp-postman/build/index.js"]
    },
    "markdownify": {
      "command": "node",
      "args": [
        "/home/<USER>/Desktop/workspace/cmps/markdownify-mcp/dist/index.js"
      ]
    },
    "mcp-mermaid": {
      "command": "npx",
      "args": ["-y", "mcp-mermaid"]
    }
    // "mcp-server-docker": {
    //   "command": "uvx",
    //   "args": ["mcp-server-docker"]
    // }
  }
}
