#!/usr/bin/env python3
"""
Test script to verify JSON serialization fix
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import json
from app.services.external_providers.phyllo_provider import make_json_serializable

# Test various data types that might cause issues
test_data = {
    "platform": "instagram",
    "filters": {
        "some_complex_object": None,  # This would be replaced with actual complex objects
        "range_value": {"min": 1000, "max": 10000},
        "list_value": ["test1", "test2"],
        "nested": {
            "inner": "value"
        }
    },
    "limit": 20,
    "offset": 0
}

def test_json_serialization():
    """Test if our JSON serialization works"""
    print("Testing JSON serialization...")
    
    try:
        # First, test if it fails without our fix
        original_json = json.dumps(test_data)
        print("✓ Original data is JSON serializable")
        print(f"  Original: {original_json[:100]}...")
        
        # Test with our serializer
        serializable_data = make_json_serializable(test_data)
        converted_json = json.dumps(serializable_data)
        print("✓ Converted data is JSON serializable")
        print(f"  Converted: {converted_json[:100]}...")
        
        # Test with complex objects (simulate RangeValue-like objects)
        class MockRangeValue:
            def __init__(self, min_val, max_val):
                self.min = min_val
                self.max = max_val
        
        complex_data = {
            "platform": "instagram",
            "filters": {
                "follower_count": MockRangeValue(1000, 10000),
                "engagement_rate": MockRangeValue(2.0, 15.0)
            }
        }
        
        print("\nTesting with complex objects...")
        try:
            # This should fail
            json.dumps(complex_data)
            print("✗ Unexpected: complex data was serializable")
        except TypeError as e:
            print(f"✓ Expected error with complex data: {e}")
        
        # This should work
        serializable_complex = make_json_serializable(complex_data)
        complex_json = json.dumps(serializable_complex)
        print("✓ Complex data made serializable")
        print(f"  Result: {complex_json}")
        
    except Exception as e:
        print(f"✗ Error during testing: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = test_json_serialization()
    if success:
        print("\n✅ All JSON serialization tests passed!")
    else:
        print("\n❌ JSON serialization tests failed!")
        sys.exit(1)
