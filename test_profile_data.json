{"id": "test-profile-123", "work_platform": {"id": "platform-instagram", "name": "Instagram", "logo_url": "https://cdn.insightiq.ai/platforms_logo/logos/logo_instagram.png"}, "profile": {"external_id": "test_user_456", "platform_username": "test_creator", "url": "https://www.instagram.com/test_creator", "image_url": "https://example.com/profile.jpg", "full_name": "Test Creator", "introduction": "This is a test profile for API testing", "content_count": 150, "sponsored_posts_performance": 0.15, "is_verified": true, "platform_account_type": "CREATOR", "gender": "MALE", "age_group": "25-34", "language": "en", "follower_count": 50000, "subscriber_count": null, "average_likes": 2500, "average_dislikes": null, "average_comments": 150, "average_views": null, "average_reels_views": 25000, "engagement_rate": 0.052, "location": {"city": "Los Angeles", "country": "United States"}, "top_hashtags": [{"name": "fitness", "value": 25.5}, {"name": "lifestyle", "value": 20.3}, {"name": "motivation", "value": 18.7}], "top_mentions": [{"name": "partner_brand", "value": 15.2}, {"name": "friend_creator", "value": 12.8}], "top_interests": [{"name": "Fitness"}, {"name": "Lifestyle"}], "top_contents": [{"type": "REEL", "url": "https://www.instagram.com/p/test123", "title": "Test Reel", "description": "This is a test reel for API testing", "thumbnail_url": "https://example.com/thumbnail.jpg", "engagement": {"like_count": 5000, "comment_count": 250, "view_count": 50000, "play_count": 50000, "save_count": 500, "share_count": 150, "reach": 75000, "impressions": 100000}, "mentions": ["partner_brand"], "published_at": "2025-06-20T12:00:00.000000"}], "recent_contents": [{"type": "IMAGE", "url": "https://www.instagram.com/p/test456", "title": "Test Image", "description": "This is a test image for API testing", "thumbnail_url": "https://example.com/thumbnail2.jpg", "engagement": {"like_count": 3000, "comment_count": 180, "view_count": null, "play_count": null, "save_count": 300, "share_count": 100, "reach": 45000, "impressions": 60000}, "mentions": [], "published_at": "2025-06-19T12:00:00.000000"}]}, "audience": {"gender_age": {"male_13-17": 5.2, "male_18-24": 18.5, "male_25-34": 25.3, "male_35-44": 15.8, "male_45-54": 8.2, "male_55+": 3.5, "female_13-17": 4.8, "female_18-24": 12.7, "female_25-34": 18.9, "female_35-44": 12.1, "female_45-54": 7.3, "female_55+": 2.8}, "languages": [{"code": "en", "value": 85.2}, {"code": "es", "value": 8.5}, {"code": "fr", "value": 3.8}, {"code": "de", "value": 2.5}], "locations": [{"country": "United States", "value": 65.5}, {"country": "Canada", "value": 12.3}, {"country": "United Kingdom", "value": 8.7}, {"country": "Australia", "value": 6.2}, {"country": "Germany", "value": 4.8}], "income": [{"range": "0-25000", "value": 12.5}, {"range": "25001-50000", "value": 18.7}, {"range": "50001-75000", "value": 25.3}, {"range": "************", "value": 22.8}, {"range": "100001+", "value": 20.7}], "devices": [{"type": "MOBILE", "value": 78.5}, {"type": "DESKTOP", "value": 15.2}, {"type": "TABLET", "value": 6.3}]}, "pricing": {"post_type": {"reel": {"price_range": {"min": 800, "max": 2500}}, "story": {"price_range": {"min": 400, "max": 1200}}, "static_post": {"price_range": {"min": 600, "max": 1800}}, "carousel": {"price_range": {"min": 500, "max": 1500}}}}}