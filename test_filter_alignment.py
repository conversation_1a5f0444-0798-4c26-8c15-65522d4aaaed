"""
Test script for filter enum alignment between SQLAlchemy and PostgreSQL.
"""

import asyncio
from sqlalchemy import select
from app.models.filter_models import FilterDefinition, FilterTypeEnum
from app.core.config import get_database


async def test_filter_type_enum_alignment():
    """Test that the filter_type enum is properly aligned between SQLAlchemy and PostgreSQL."""
    print("Testing filter_type enum alignment...")
    
    db = get_database()
    async with db.get_session() as session:
        # Query the filter definitions
        query = select(FilterDefinition)
        result = await session.execute(query)
        filter_defs = result.scalars().all()
        
        # Verify the enum values
        for filter_def in filter_defs:
            print(f"Filter: {filter_def.name}")
            print(f"  - Type (Enum): {filter_def.filter_type}")
            print(f"  - Type (Value): {filter_def.filter_type.value}")
            
            # Check if the filter_type matches one of our enum values
            try:
                assert filter_def.filter_type in list(FilterTypeEnum)
                print("  - ✅ Enum validation passed")
            except AssertionError:
                print(f"  - ❌ Enum validation failed! {filter_def.filter_type} is not in {list(FilterTypeEnum)}")
            
            # Verify that the string representation matches the enum value
            try:
                assert filter_def.filter_type.value == filter_def.filter_type.value
                print("  - ✅ Value validation passed")
            except AssertionError:
                print(f"  - ❌ Value validation failed! {filter_def.filter_type.value} != {filter_def.filter_type.value}")
            
            print("-" * 50)
    
    print("Alignment test complete!")


if __name__ == "__main__":
    asyncio.run(test_filter_type_enum_alignment())
