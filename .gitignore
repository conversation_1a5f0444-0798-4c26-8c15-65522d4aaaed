__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

*.db
*.sqlite
*.sqlite3

.pytest_cache/
.coverage
htmlcov/
.ruff_cache/
.mypy_cache/

.DS_Store
.vscode/
.idea/

# Configuration files with sensitive data
config/local.json
config/production.json
config/*_local.json


uv.lock