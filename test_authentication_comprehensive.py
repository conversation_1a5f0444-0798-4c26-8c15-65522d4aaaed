"""
Comprehensive Test Suite for Creatorverse Authentication & Authorization
Tests all auth methods including OAuth, OTP, magic links, JWT tokens, and RBAC.
"""
import asyncio
import pytest
import uuid
import jwt
from datetime import datetime, UTC, timedelta
from typing import Dict, Any, List, Optional
from unittest.mock import AsyncMock, MagicMock, patch

from sqlalchemy import select, delete
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import get_database, get_locobuzz_redis, APP_CONFIG
from app.models.user_models import (
    User, UserSession, UserOTP, MagicLink, OAuthAccount, Organization, 
    OrganizationMembership, MasterRole, UserRoleModel, MasterPermission,
    RolePermission, UserAuthMethod, MasterAuthMethod, Brand, BrandMembership
)
from app.services.session_service import (
    create_user_session,
    validate_jwt_token,
    revoke_user_session,
    get_user_sessions,
    cleanup_expired_sessions
)
from app.utilities.otp_manager import get_optimized_otp_manager
from app.utilities.jwt_utils import create_jwt_token, verify_jwt_token
from app.utilities.magic_link_manager import MagicLinkManager


class TestAuthenticationComprehensive:
    """Comprehensive test suite for authentication functionality"""

    @pytest.fixture(autouse=True)
    async def setup_test_environment(self):
        """Setup test environment with clean database and Redis"""
        self.db_conn = get_database()
        self.redis_client = get_locobuzz_redis()
        
        await self.db_conn.initialize()
        await self.redis_client.initialize()
        
        # Clean up test data
        await self._cleanup_test_data()
        
        # Setup test data
        await self._setup_test_data()
        
        yield
        
        # Cleanup after test
        await self._cleanup_test_data()
        await self.redis_client.close()
        await self.db_conn.shutdown()

    async def _cleanup_test_data(self):
        """Clean up test data from database and Redis"""
        try:
            async with self.db_conn.get_db() as session:
                # Delete test data (cascade will handle related records)
                await session.execute(
                    delete(User).where(User.email.like('%test%'))
                )
                await session.execute(
                    delete(Organization).where(Organization.domain.like('%test%'))
                )
                await session.commit()
            
            # Clear Redis test keys
            keys = await self.redis_client.keys("*test*")
            if keys:
                await self.redis_client.delete(*keys)
                
        except Exception as e:
            print(f"Cleanup error: {e}")

    async def _setup_test_data(self):
        """Setup test data for auth tests"""
        async with self.db_conn.get_db() as session:
            # Create test organization
            self.test_org = Organization(
                domain="testauth.com",
                name="Test Auth Company",
                contact_email="<EMAIL>"
            )
            session.add(self.test_org)
            await session.flush()

            # Create test user
            self.test_user = User(
                email="<EMAIL>",
                name="Test Auth User",
                status="active",
                is_email_verified=True
            )
            session.add(self.test_user)
            await session.flush()

            # Create master auth methods
            self.auth_methods = []
            for method in ["email", "oauth_google", "oauth_facebook", "magic_link", "otp"]:
                auth_method = MasterAuthMethod(
                    method_key=method,
                    description=f"{method.replace('_', ' ').title()} authentication"
                )
                session.add(auth_method)
                self.auth_methods.append(auth_method)
            
            await session.flush()

            # Create master roles
            self.roles = []
            role_configs = [
                {"name": "user", "description": "Regular user"},
                {"name": "brand_admin", "description": "Brand administrator"},
                {"name": "org_admin", "description": "Organization administrator"},
                {"name": "super_admin", "description": "Super administrator"}
            ]
            
            for config in role_configs:
                role = MasterRole(
                    role_name=config["name"],
                    description=config["description"]
                )
                session.add(role)
                self.roles.append(role)
            
            await session.flush()

            # Create master permissions
            self.permissions = []
            permission_configs = [
                {"key": "user.read", "description": "Read user data"},
                {"key": "user.write", "description": "Write user data"},
                {"key": "brand.read", "description": "Read brand data"},
                {"key": "brand.write", "description": "Write brand data"},
                {"key": "org.read", "description": "Read organization data"},
                {"key": "org.write", "description": "Write organization data"}
            ]
            
            for config in permission_configs:
                permission = MasterPermission(
                    permission_key=config["key"],
                    description=config["description"]
                )
                session.add(permission)
                self.permissions.append(permission)
            
            await session.flush()

            # Assign permissions to roles
            role_permission_assignments = [
                (0, 0), (0, 1),  # user role gets user.read, user.write
                (1, 0), (1, 1), (1, 2), (1, 3),  # brand_admin gets user + brand perms
                (2, 0), (2, 1), (2, 2), (2, 3), (2, 4), (2, 5),  # org_admin gets all
                (3, 0), (3, 1), (3, 2), (3, 3), (3, 4), (3, 5)   # super_admin gets all
            ]
            
            for role_idx, perm_idx in role_permission_assignments:
                role_permission = RolePermission(
                    role_id=self.roles[role_idx].id,
                    permission_id=self.permissions[perm_idx].id
                )
                session.add(role_permission)
            
            # Assign user role to test user
            user_role = UserRoleModel(
                user_id=self.test_user.id,
                role_id=self.roles[0].id  # user role
            )
            session.add(user_role)
            
            await session.commit()

    def _create_test_jwt_payload(self, user_id: str = None, roles: List[str] = None) -> Dict[str, Any]:
        """Create test JWT payload"""
        if not user_id:
            user_id = str(self.test_user.id)
        
        if not roles:
            roles = ["user"]
        
        return {
            "user_id": user_id,
            "email": "<EMAIL>",
            "roles": roles,
            "permissions": ["user.read", "user.write"],
            "iat": datetime.now(UTC),
            "exp": datetime.now(UTC) + timedelta(hours=1)
        }

    # ──────────────────────────────────────────────────────────────────────────────
    # JWT TOKEN TESTS
    # ──────────────────────────────────────────────────────────────────────────────

    @pytest.mark.asyncio
    async def test_create_jwt_token_success(self):
        """Test successful JWT token creation"""
        payload = self._create_test_jwt_payload()
        
        token = create_jwt_token(payload)
        
        assert token is not None
        assert isinstance(token, str)
        assert len(token.split('.')) == 3  # JWT has 3 parts

    @pytest.mark.asyncio
    async def test_verify_jwt_token_success(self):
        """Test successful JWT token verification"""
        payload = self._create_test_jwt_payload()
        
        token = create_jwt_token(payload)
        decoded_payload = verify_jwt_token(token)
        
        assert decoded_payload is not None
        assert decoded_payload["user_id"] == payload["user_id"]
        assert decoded_payload["email"] == payload["email"]
        assert decoded_payload["roles"] == payload["roles"]

    @pytest.mark.asyncio
    async def test_verify_jwt_token_expired(self):
        """Test JWT token verification with expired token"""
        payload = self._create_test_jwt_payload()
        payload["exp"] = datetime.now(UTC) - timedelta(hours=1)  # Expired
        
        token = jwt.encode(payload, APP_CONFIG.jwt_secret_key, algorithm="HS256")
        
        with pytest.raises(jwt.ExpiredSignatureError):
            verify_jwt_token(token)

    @pytest.mark.asyncio
    async def test_verify_jwt_token_invalid_signature(self):
        """Test JWT token verification with invalid signature"""
        payload = self._create_test_jwt_payload()
        
        # Create token with wrong secret
        token = jwt.encode(payload, "wrong_secret", algorithm="HS256")
        
        with pytest.raises(jwt.InvalidSignatureError):
            verify_jwt_token(token)

    @pytest.mark.asyncio
    async def test_verify_jwt_token_malformed(self):
        """Test JWT token verification with malformed token"""
        malformed_tokens = [
            "invalid.token",
            "not.jwt.format.token",
            "",
            "Bearer token",
            "completely_invalid_token"
        ]
        
        for token in malformed_tokens:
            with pytest.raises((jwt.InvalidTokenError, jwt.DecodeError)):
                verify_jwt_token(token)

    # ──────────────────────────────────────────────────────────────────────────────
    # USER SESSION TESTS
    # ──────────────────────────────────────────────────────────────────────────────

    @pytest.mark.asyncio
    async def test_create_user_session_success(self):
        """Test successful user session creation"""
        async with self.db_conn.get_db() as session:
            session_data = {
                "user_id": self.test_user.id,
                "ip_address": "***********",
                "user_agent": "Mozilla/5.0 Test Browser"
            }
            
            session_result = await create_user_session(
                session,
                self.redis_client,
                **session_data
            )
            
            assert session_result is not None
            assert session_result.user_id == self.test_user.id
            assert session_result.ip_address.compressed == "***********"
            assert session_result.user_agent == "Mozilla/5.0 Test Browser"
            assert session_result.access_token is not None
            assert session_result.refresh_token is not None
            assert not session_result.is_revoked
            
            await session.commit()

    @pytest.mark.asyncio
    async def test_validate_jwt_token_success(self):
        """Test successful JWT token validation against session"""
        async with self.db_conn.get_db() as session:
            # Create session
            session_result = await create_user_session(
                session,
                self.redis_client,
                user_id=self.test_user.id,
                ip_address="***********",
                user_agent="Test Browser"
            )
            await session.commit()
            
            # Validate token
            is_valid, user_data = await validate_jwt_token(
                session,
                self.redis_client,
                session_result.access_token
            )
            
            assert is_valid
            assert user_data is not None
            assert user_data["user_id"] == str(self.test_user.id)

    @pytest.mark.asyncio
    async def test_validate_jwt_token_revoked_session(self):
        """Test JWT token validation with revoked session"""
        async with self.db_conn.get_db() as session:
            # Create and revoke session
            session_result = await create_user_session(
                session,
                self.redis_client,
                user_id=self.test_user.id,
                ip_address="***********",
                user_agent="Test Browser"
            )
            await session.commit()
            
            # Revoke session
            success = await revoke_user_session(
                session,
                self.redis_client,
                session_result.id
            )
            assert success
            await session.commit()
            
            # Try to validate token from revoked session
            is_valid, user_data = await validate_jwt_token(
                session,
                self.redis_client,
                session_result.access_token
            )
            
            assert not is_valid
            assert user_data is None

    @pytest.mark.asyncio
    async def test_get_user_sessions(self):
        """Test retrieving user sessions"""
        async with self.db_conn.get_db() as session:
            # Create multiple sessions
            session_configs = [
                {"ip_address": "***********", "user_agent": "Browser 1"},
                {"ip_address": "***********", "user_agent": "Browser 2"},
                {"ip_address": "********", "user_agent": "Mobile App"}
            ]
            
            created_sessions = []
            for config in session_configs:
                session_result = await create_user_session(
                    session,
                    self.redis_client,
                    user_id=self.test_user.id,
                    **config
                )
                created_sessions.append(session_result)
            
            await session.commit()
            
            # Get user sessions
            sessions = await get_user_sessions(
                session,
                self.test_user.id
            )
            
            assert len(sessions) == 3
            ip_addresses = [s.ip_address.compressed for s in sessions]
            assert "***********" in ip_addresses
            assert "***********" in ip_addresses
            assert "********" in ip_addresses

    @pytest.mark.asyncio
    async def test_cleanup_expired_sessions(self):
        """Test cleanup of expired sessions"""
        async with self.db_conn.get_db() as session:
            # Create session with past expiration
            session_result = await create_user_session(
                session,
                self.redis_client,
                user_id=self.test_user.id,
                ip_address="***********",
                user_agent="Test Browser"
            )
            
            # Manually set expiration to past
            session_result.expires_at = datetime.now(UTC) - timedelta(hours=1)
            await session.commit()
            
            # Cleanup expired sessions
            cleaned_count = await cleanup_expired_sessions(session)
            assert cleaned_count == 1
            
            await session.commit()
            
            # Verify session is marked as revoked
            await session.refresh(session_result)
            assert session_result.is_revoked

    # ──────────────────────────────────────────────────────────────────────────────
    # OTP AUTHENTICATION TESTS
    # ──────────────────────────────────────────────────────────────────────────────

    @pytest.mark.asyncio
    async def test_otp_generation_and_verification(self):
        """Test OTP generation and verification flow"""
        otp_manager = get_optimized_otp_manager(self.redis_client)
        test_email = "<EMAIL>"
        
        # Generate OTP
        otp_code = await otp_manager.generate_otp(test_email)
        assert otp_code is not None
        assert len(otp_code) == 6
        assert otp_code.isdigit()
        
        # Verify correct OTP
        is_valid, lockout_time = await otp_manager.verify_otp(test_email, otp_code)
        assert is_valid
        assert lockout_time == 0

    @pytest.mark.asyncio
    async def test_otp_verification_invalid_code(self):
        """Test OTP verification with invalid code"""
        otp_manager = get_optimized_otp_manager(self.redis_client)
        test_email = "<EMAIL>"
        
        # Generate OTP but verify with wrong code
        await otp_manager.generate_otp(test_email)
        
        is_valid, lockout_time = await otp_manager.verify_otp(test_email, "000000")
        assert not is_valid
        assert lockout_time == 0  # No lockout on first failure

    @pytest.mark.asyncio
    async def test_otp_rate_limiting_and_lockout(self):
        """Test OTP rate limiting and account lockout"""
        otp_manager = get_optimized_otp_manager(self.redis_client)
        test_email = "<EMAIL>"
        
        # Generate OTP
        await otp_manager.generate_otp(test_email)
        
        # Make multiple failed attempts to trigger lockout
        for i in range(5):
            is_valid, lockout_time = await otp_manager.verify_otp(test_email, "000000")
            assert not is_valid
            
            if i >= 4:  # After 5 failed attempts
                assert lockout_time > 0  # Should be locked out
            else:
                assert lockout_time == 0

    @pytest.mark.asyncio
    async def test_otp_expiration(self):
        """Test OTP expiration handling"""
        otp_manager = get_optimized_otp_manager(self.redis_client)
        test_email = "<EMAIL>"
        
        # Mock time to simulate expiration
        with patch('app.utilities.otp_manager.datetime') as mock_datetime:
            # Generate OTP
            mock_datetime.now.return_value = datetime.now(UTC)
            otp_code = await otp_manager.generate_otp(test_email)
            
            # Simulate time passing (OTP expires after 5 minutes)
            mock_datetime.now.return_value = datetime.now(UTC) + timedelta(minutes=6)
            
            # Try to verify expired OTP
            is_valid, lockout_time = await otp_manager.verify_otp(test_email, otp_code)
            assert not is_valid

    # ──────────────────────────────────────────────────────────────────────────────
    # MAGIC LINK AUTHENTICATION TESTS
    # ──────────────────────────────────────────────────────────────────────────────

    @pytest.mark.asyncio
    async def test_magic_link_generation_and_verification(self):
        """Test magic link generation and verification"""
        async with self.db_conn.get_db() as session:
            magic_link_manager = MagicLinkManager(session, self.redis_client)
            
            # Generate magic link
            magic_link = await magic_link_manager.generate_magic_link(
                self.test_user.id,
                expires_in_minutes=30
            )
            
            assert magic_link is not None
            assert magic_link.user_id == self.test_user.id
            assert magic_link.token is not None
            assert len(magic_link.token) == 64  # SHA-256 hash length
            assert not magic_link.consumed_at
            
            await session.commit()
            
            # Verify magic link
            user = await magic_link_manager.verify_magic_link(magic_link.token)
            assert user is not None
            assert user.id == self.test_user.id
            
            await session.commit()
            
            # Verify link is marked as consumed
            await session.refresh(magic_link)
            assert magic_link.consumed_at is not None

    @pytest.mark.asyncio
    async def test_magic_link_verification_invalid_token(self):
        """Test magic link verification with invalid token"""
        async with self.db_conn.get_db() as session:
            magic_link_manager = MagicLinkManager(session, self.redis_client)
            
            # Try to verify non-existent token
            user = await magic_link_manager.verify_magic_link("invalid_token_123")
            assert user is None

    @pytest.mark.asyncio
    async def test_magic_link_verification_expired(self):
        """Test magic link verification with expired link"""
        async with self.db_conn.get_db() as session:
            magic_link_manager = MagicLinkManager(session, self.redis_client)
            
            # Generate magic link with past expiration
            magic_link = await magic_link_manager.generate_magic_link(
                self.test_user.id,
                expires_in_minutes=-30  # Expired 30 minutes ago
            )
            await session.commit()
            
            # Try to verify expired link
            user = await magic_link_manager.verify_magic_link(magic_link.token)
            assert user is None

    @pytest.mark.asyncio
    async def test_magic_link_verification_already_consumed(self):
        """Test magic link verification with already consumed link"""
        async with self.db_conn.get_db() as session:
            magic_link_manager = MagicLinkManager(session, self.redis_client)
            
            # Generate and consume magic link
            magic_link = await magic_link_manager.generate_magic_link(
                self.test_user.id,
                expires_in_minutes=30
            )
            await session.commit()
            
            # First verification should succeed
            user1 = await magic_link_manager.verify_magic_link(magic_link.token)
            assert user1 is not None
            await session.commit()
            
            # Second verification should fail
            user2 = await magic_link_manager.verify_magic_link(magic_link.token)
            assert user2 is None

    # ──────────────────────────────────────────────────────────────────────────────
    # OAUTH AUTHENTICATION TESTS
    # ──────────────────────────────────────────────────────────────────────────────

    @pytest.mark.asyncio
    async def test_oauth_account_creation_and_linking(self):
        """Test OAuth account creation and user linking"""
        async with self.db_conn.get_db() as session:
            # Create OAuth account linked to user
            oauth_account = OAuthAccount(
                user_id=self.test_user.id,
                provider="google",
                provider_user_id="google_123456",
                access_token="google_access_token_123",
                refresh_token="google_refresh_token_123",
                expires_at=datetime.now(UTC) + timedelta(hours=1),
                scope="openid email profile"
            )
            
            session.add(oauth_account)
            await session.commit()
            
            # Verify OAuth account was created
            oauth_query = select(OAuthAccount).where(
                OAuthAccount.user_id == self.test_user.id,
                OAuthAccount.provider == "google"
            )
            result = await session.execute(oauth_query)
            stored_account = result.scalar_one_or_none()
            
            assert stored_account is not None
            assert stored_account.provider_user_id == "google_123456"
            assert stored_account.access_token == "google_access_token_123"

    @pytest.mark.asyncio
    async def test_oauth_account_unique_constraint(self):
        """Test OAuth account unique constraint per provider"""
        async with self.db_conn.get_db() as session:
            # Create first OAuth account
            oauth_account1 = OAuthAccount(
                user_id=self.test_user.id,
                provider="facebook",
                provider_user_id="facebook_123456",
                access_token="facebook_access_token_123"
            )
            session.add(oauth_account1)
            await session.commit()
            
            # Try to create duplicate OAuth account (same provider + provider_user_id)
            oauth_account2 = OAuthAccount(
                user_id=self.test_user.id,  # Different user but same provider ID
                provider="facebook",
                provider_user_id="facebook_123456",  # Same provider user ID
                access_token="facebook_access_token_456"
            )
            session.add(oauth_account2)
            
            with pytest.raises(Exception):  # Should raise IntegrityError
                await session.commit()

    @pytest.mark.asyncio
    async def test_oauth_token_refresh_simulation(self):
        """Test OAuth token refresh simulation"""
        async with self.db_conn.get_db() as session:
            # Create OAuth account with expired token
            oauth_account = OAuthAccount(
                user_id=self.test_user.id,
                provider="google",
                provider_user_id="google_refresh_test",
                access_token="expired_access_token",
                refresh_token="valid_refresh_token",
                expires_at=datetime.now(UTC) - timedelta(hours=1)  # Expired
            )
            session.add(oauth_account)
            await session.commit()
            
            # Simulate token refresh
            oauth_account.access_token = "new_access_token"
            oauth_account.expires_at = datetime.now(UTC) + timedelta(hours=1)
            oauth_account.updated_at = datetime.now(UTC)
            
            await session.commit()
            
            # Verify token was updated
            await session.refresh(oauth_account)
            assert oauth_account.access_token == "new_access_token"
            assert oauth_account.expires_at > datetime.now(UTC)

    # ──────────────────────────────────────────────────────────────────────────────
    # ROLE-BASED ACCESS CONTROL (RBAC) TESTS
    # ──────────────────────────────────────────────────────────────────────────────

    @pytest.mark.asyncio
    async def test_user_role_assignment(self):
        """Test user role assignment and retrieval"""
        async with self.db_conn.get_db() as session:
            # Create test user for role testing
            role_test_user = User(
                email="<EMAIL>",
                name="Role Test User",
                status="active"
            )
            session.add(role_test_user)
            await session.flush()
            
            # Assign brand_admin role
            user_role = UserRoleModel(
                user_id=role_test_user.id,
                role_id=self.roles[1].id  # brand_admin
            )
            session.add(user_role)
            await session.commit()
            
            # Verify role assignment
            role_query = select(UserRoleModel).where(
                UserRoleModel.user_id == role_test_user.id
            )
            result = await session.execute(role_query)
            assigned_roles = result.scalars().all()
            
            assert len(assigned_roles) == 1
            assert assigned_roles[0].role_id == self.roles[1].id

    @pytest.mark.asyncio
    async def test_permission_inheritance_through_roles(self):
        """Test permission inheritance through role assignments"""
        async with self.db_conn.get_db() as session:
            # Create user with org_admin role
            admin_user = User(
                email="<EMAIL>",
                name="Admin Test User",
                status="active"
            )
            session.add(admin_user)
            await session.flush()
            
            # Assign org_admin role (should have all permissions)
            user_role = UserRoleModel(
                user_id=admin_user.id,
                role_id=self.roles[2].id  # org_admin
            )
            session.add(user_role)
            await session.commit()
            
            # Get user permissions through role
            permission_query = select(MasterPermission).join(
                RolePermission, MasterPermission.id == RolePermission.permission_id
            ).join(
                UserRoleModel, RolePermission.role_id == UserRoleModel.role_id
            ).where(
                UserRoleModel.user_id == admin_user.id
            )
            
            result = await session.execute(permission_query)
            permissions = result.scalars().all()
            
            # org_admin should have all 6 permissions
            assert len(permissions) == 6
            permission_keys = [p.permission_key for p in permissions]
            assert "user.read" in permission_keys
            assert "user.write" in permission_keys
            assert "brand.read" in permission_keys
            assert "brand.write" in permission_keys
            assert "org.read" in permission_keys
            assert "org.write" in permission_keys

    @pytest.mark.asyncio
    async def test_multiple_role_assignment(self):
        """Test user with multiple roles"""
        async with self.db_conn.get_db() as session:
            # Create user
            multi_role_user = User(
                email="<EMAIL>",
                name="Multi Role User",
                status="active"
            )
            session.add(multi_role_user)
            await session.flush()
            
            # Assign multiple roles
            roles_to_assign = [0, 1]  # user and brand_admin
            for role_idx in roles_to_assign:
                user_role = UserRoleModel(
                    user_id=multi_role_user.id,
                    role_id=self.roles[role_idx].id
                )
                session.add(user_role)
            
            await session.commit()
            
            # Verify multiple role assignment
            role_query = select(UserRoleModel).where(
                UserRoleModel.user_id == multi_role_user.id
            )
            result = await session.execute(role_query)
            assigned_roles = result.scalars().all()
            
            assert len(assigned_roles) == 2

    # ──────────────────────────────────────────────────────────────────────────────
    # AUTHENTICATION METHOD MANAGEMENT TESTS
    # ──────────────────────────────────────────────────────────────────────────────

    @pytest.mark.asyncio
    async def test_user_auth_method_enablement(self):
        """Test enabling authentication methods for users"""
        async with self.db_conn.get_db() as session:
            # Enable email and OAuth Google for test user
            auth_methods_to_enable = [0, 1]  # email, oauth_google
            
            for method_idx in auth_methods_to_enable:
                user_auth_method = UserAuthMethod(
                    user_id=self.test_user.id,
                    auth_method_id=self.auth_methods[method_idx].id,
                    is_enabled=True,
                    enabled_at=datetime.now(UTC)
                )
                session.add(user_auth_method)
            
            await session.commit()
            
            # Verify auth methods are enabled
            auth_query = select(UserAuthMethod).where(
                UserAuthMethod.user_id == self.test_user.id,
                UserAuthMethod.is_enabled == True
            )
            result = await session.execute(auth_query)
            enabled_methods = result.scalars().all()
            
            assert len(enabled_methods) == 2

    @pytest.mark.asyncio
    async def test_auth_method_disable_and_enable(self):
        """Test disabling and re-enabling authentication methods"""
        async with self.db_conn.get_db() as session:
            # Enable email auth method
            user_auth_method = UserAuthMethod(
                user_id=self.test_user.id,
                auth_method_id=self.auth_methods[0].id,  # email
                is_enabled=True,
                enabled_at=datetime.now(UTC)
            )
            session.add(user_auth_method)
            await session.commit()
            
            # Disable the method
            user_auth_method.is_enabled = False
            user_auth_method.disabled_at = datetime.now(UTC)
            await session.commit()
            
            # Verify method is disabled
            await session.refresh(user_auth_method)
            assert not user_auth_method.is_enabled
            assert user_auth_method.disabled_at is not None
            
            # Re-enable the method
            user_auth_method.is_enabled = True
            user_auth_method.enabled_at = datetime.now(UTC)
            user_auth_method.disabled_at = None
            await session.commit()
            
            # Verify method is re-enabled
            await session.refresh(user_auth_method)
            assert user_auth_method.is_enabled
            assert user_auth_method.disabled_at is None

    # ──────────────────────────────────────────────────────────────────────────────
    # SECURITY TESTS
    # ──────────────────────────────────────────────────────────────────────────────

    @pytest.mark.asyncio
    async def test_jwt_token_injection_prevention(self):
        """Test prevention of JWT token injection attacks"""
        # Test with malicious payloads
        malicious_payloads = [
            {"user_id": "'; DROP TABLE users; --"},
            {"user_id": "<script>alert('xss')</script>"},
            {"roles": ["admin'; DROP TABLE roles; --"]},
            {"permissions": ["<img src=x onerror=alert('xss')>"]}
        ]
        
        for payload in malicious_payloads:
            # Token creation should handle malicious data
            token = create_jwt_token(payload)
            decoded = verify_jwt_token(token)
            
            # Verify that malicious data is preserved as strings (not executed)
            assert decoded["user_id"] == payload.get("user_id", "")
            if "roles" in payload:
                assert decoded["roles"] == payload["roles"]

    @pytest.mark.asyncio
    async def test_session_hijacking_prevention(self):
        """Test session hijacking prevention measures"""
        async with self.db_conn.get_db() as session:
            # Create session with specific IP and user agent
            original_session = await create_user_session(
                session,
                self.redis_client,
                user_id=self.test_user.id,
                ip_address="***********00",
                user_agent="Original Browser"
            )
            await session.commit()
            
            # Simulate session validation from different IP/user agent
            # In a real implementation, this would be checked
            session_from_db = await session.get(UserSession, original_session.id)
            
            # Verify session contains security metadata
            assert session_from_db.ip_address.compressed == "***********00"
            assert session_from_db.user_agent == "Original Browser"
            assert session_from_db.issued_at is not None

    @pytest.mark.asyncio
    async def test_brute_force_protection_simulation(self):
        """Test brute force protection through OTP lockout"""
        otp_manager = get_optimized_otp_manager(self.redis_client)
        test_email = "<EMAIL>"
        
        # Generate legitimate OTP
        legitimate_otp = await otp_manager.generate_otp(test_email)
        
        # Simulate brute force attack with wrong codes
        for attempt in range(10):
            is_valid, lockout_time = await otp_manager.verify_otp(
                test_email, 
                f"{attempt:06d}"  # Wrong codes: 000000, 000001, etc.
            )
            
            assert not is_valid
            
            # After enough failed attempts, should be locked out
            if attempt >= 4:
                assert lockout_time > 0
                break

    # ──────────────────────────────────────────────────────────────────────────────
    # CONCURRENT AUTHENTICATION TESTS
    # ──────────────────────────────────────────────────────────────────────────────

    @pytest.mark.asyncio
    async def test_concurrent_session_creation(self):
        """Test concurrent session creation for same user"""
        async def create_session_task(task_id):
            async with self.db_conn.get_db() as session:
                try:
                    session_result = await create_user_session(
                        session,
                        self.redis_client,
                        user_id=self.test_user.id,
                        ip_address=f"192.168.1.{task_id}",
                        user_agent=f"Browser {task_id}"
                    )
                    await session.commit()
                    return True
                except Exception as e:
                    print(f"Session creation task {task_id} failed: {e}")
                    return False

        # Create multiple sessions concurrently
        tasks = [create_session_task(i) for i in range(5)]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # All should succeed
        successful_creations = sum(1 for r in results if r is True)
        assert successful_creations == 5
        
        # Verify all sessions exist
        async with self.db_conn.get_db() as session:
            sessions = await get_user_sessions(session, self.test_user.id)
            assert len(sessions) >= 5  # At least 5 new sessions


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v", "--tb=short"])
