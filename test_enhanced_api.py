#!/usr/bin/env python3
"""
Comprehensive test suite for the enhanced Phyllo API proxy.
Tests all validation logic, edge cases, and API functionality.
"""

import requests
import json
from typing import Dict, Any

# Configuration
BASE_URL = "http://localhost:8000"
API_PREFIX = "/v1/social/creator"

class TestPhylloAPI:
    """Test class for Phyllo API endpoints"""
    
    def setup_method(self):
        """Setup method run before each test"""
        self.base_url = BASE_URL
        self.headers = {
            "Content-Type": "application/json",
            "User-Agent": "PhylloAPITest/1.0"
        }
    
    def test_profile_analytics_valid_request(self):
        """Test profile analytics with valid profile ID"""
        url = f"{self.base_url}{API_PREFIX}/profile/analytics"
        payload = {
            "profile_id": "cfa27c2b-6451-4433-99d7-98230dd1a1d6",
            "include_audience": True,
            "include_content": True,
            "include_pricing": True
        }
        
        response = requests.post(url, json=payload, headers=self.headers)
        assert response.status_code == 200
        
        data = response.json()
        assert data["success"] is True
        assert "data" in data
        assert "profile" in data["data"]
        print(f"✅ Profile Analytics Test Passed: {data['message']}")
    
    def test_profile_analytics_invalid_id(self):
        """Test profile analytics with invalid profile ID"""
        url = f"{self.base_url}{API_PREFIX}/profile/analytics"
        payload = {
            "profile_id": "invalid-id"
        }
        
        response = requests.post(url, json=payload, headers=self.headers)
        assert response.status_code == 404
        print("✅ Invalid Profile ID Test Passed")
    
    def test_profile_analytics_malicious_input(self):
        """Test profile analytics with malicious input"""
        url = f"{self.base_url}{API_PREFIX}/profile/analytics"
        payload = {
            "profile_id": "<script>alert('xss')</script>"
        }
        
        response = requests.post(url, json=payload, headers=self.headers)
        # Should either sanitize or reject
        assert response.status_code in [400, 422, 404]
        print("✅ Malicious Input Test Passed")
    
    def test_quick_search_valid_filters(self):
        """Test quick search with valid filters"""
        url = f"{self.base_url}{API_PREFIX}/profile/quick-search"
        payload = {
            "platform": "instagram",
            "follower_count_min": 1000,
            "follower_count_max": 100000,
            "engagement_rate_min": 0.01,
            "engagement_rate_max": 0.1,
            "verified_only": False,
            "limit": 5
        }
        
        response = requests.post(url, json=payload, headers=self.headers)
        assert response.status_code == 200
        
        data = response.json()
        assert data["success"] is True
        assert "data" in data
        assert "profiles" in data["data"]
        print(f"✅ Quick Search Test Passed: Found {data['data']['total_found']} profiles")
    
    def test_quick_search_invalid_platform(self):
        """Test quick search with invalid platform"""
        url = f"{self.base_url}{API_PREFIX}/profile/quick-search"
        payload = {
            "platform": "invalid_platform",
            "limit": 5
        }
        
        response = requests.post(url, json=payload, headers=self.headers)
        assert response.status_code == 422
        
        data = response.json()
        assert data["success"] is False
        print("✅ Invalid Platform Test Passed")
    
    def test_quick_search_invalid_follower_range(self):
        """Test quick search with invalid follower count range"""
        url = f"{self.base_url}{API_PREFIX}/profile/quick-search"
        payload = {
            "follower_count_min": 100000,
            "follower_count_max": 1000,  # Max less than min
            "limit": 5
        }
        
        response = requests.post(url, json=payload, headers=self.headers)
        assert response.status_code == 422
        print("✅ Invalid Follower Range Test Passed")
    
    def test_advanced_search_comprehensive(self):
        """Test advanced search with comprehensive filters"""
        url = f"{self.base_url}{API_PREFIX}/profile/search"
        payload = {
            "platform": "youtube",
            "follower_count_min": 5000,
            "follower_count_max": 500000,
            "engagement_rate_min": 0.02,
            "engagement_rate_max": 0.15,
            "interests": ["gaming", "technology"],
            "location": "united states",
            "gender": "MALE",
            "age_group": "25-34",
            "verified_only": False,
            "content_type": "VIDEO",
            "limit": 10,
            "offset": 0
        }
        
        response = requests.post(url, json=payload, headers=self.headers)
        assert response.status_code == 200
        
        data = response.json()
        assert data["success"] is True
        assert "data" in data
        assert "pagination" in data
        print(f"✅ Advanced Search Test Passed: Found {data['pagination']['total']} profiles")
    
    def test_advanced_search_invalid_content_type(self):
        """Test advanced search with invalid content type for platform"""
        url = f"{self.base_url}{API_PREFIX}/profile/search"
        payload = {
            "platform": "twitter",
            "content_type": "VIDEO",  # Twitter doesn't support VIDEO content type
            "limit": 5
        }
        
        response = requests.post(url, json=payload, headers=self.headers)
        assert response.status_code == 422
        print("✅ Invalid Content Type Test Passed")
    
    def test_advanced_search_pagination(self):
        """Test advanced search pagination"""
        url = f"{self.base_url}{API_PREFIX}/profile/search"
        
        # First page
        payload = {
            "platform": "instagram",
            "limit": 3,
            "offset": 0
        }
        
        response = requests.post(url, json=payload, headers=self.headers)
        assert response.status_code == 200
        
        data = response.json()
        first_page_results = data["data"]
        
        # Second page
        payload["offset"] = 3
        response = requests.post(url, json=payload, headers=self.headers)
        assert response.status_code == 200
        
        data = response.json()
        second_page_results = data["data"]
        
        # Ensure different results
        if first_page_results and second_page_results:
            assert first_page_results[0]["id"] != second_page_results[0]["id"]
        
        print("✅ Pagination Test Passed")
    
    def test_security_sql_injection(self):
        """Test SQL injection prevention"""
        url = f"{self.base_url}{API_PREFIX}/profile/quick-search"
        payload = {
            "username": "'; DROP TABLE profiles; --",
            "limit": 5
        }
        
        response = requests.post(url, json=payload, headers=self.headers)
        # Should handle gracefully without errors
        assert response.status_code in [200, 422]
        print("✅ SQL Injection Prevention Test Passed")
    
    def test_security_xss_prevention(self):
        """Test XSS prevention"""
        url = f"{self.base_url}{API_PREFIX}/profile/quick-search"
        payload = {
            "username": "<script>alert('xss')</script>",
            "interests": ["<img src=x onerror=alert('xss')>"],
            "location": "javascript:alert('xss')",
            "limit": 5
        }
        
        response = requests.post(url, json=payload, headers=self.headers)
        # Should sanitize input
        assert response.status_code in [200, 422]
        print("✅ XSS Prevention Test Passed")
    
    def test_rate_limiting_simulation(self):
        """Test rate limiting (simulated)"""
        url = f"{self.base_url}{API_PREFIX}/profile/quick-search"
        payload = {"limit": 5}
        
        # Make multiple rapid requests
        responses = []
        for i in range(5):
            response = requests.post(url, json=payload, headers=self.headers)
            responses.append(response.status_code)
        
        # All should succeed in test environment
        assert all(status in [200, 422] for status in responses)
        print("✅ Rate Limiting Test Passed")


def run_manual_tests():
    """Run manual tests and display results"""
    print("🚀 Starting Enhanced Phyllo API Tests\n")
    
    # Test 1: Profile Analytics
    print("1. Testing Profile Analytics Endpoint:")
    try:
        response = requests.post(f"{BASE_URL}{API_PREFIX}/profile/analytics", 
                               json={"profile_id": "cfa27c2b-6451-4433-99d7-98230dd1a1d6"})
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Success: {data['message']}")
            profile = data['data']['profile']
            print(f"      Creator: {profile.get('full_name')} (@{profile.get('platform_username')})")
            print(f"      Followers: {profile.get('follower_count', 0):,}")
        else:
            print(f"   ❌ Failed: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 2: Quick Search
    print("\n2. Testing Quick Search Endpoint:")
    try:
        response = requests.post(f"{BASE_URL}{API_PREFIX}/profile/quick-search",
                               json={
                                   "platform": "instagram",
                                   "follower_count_min": 10000,
                                   "verified_only": True,
                                   "limit": 3
                               })
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Success: Found {data['data']['total_found']} profiles")
            for profile in data['data']['profiles'][:2]:
                print(f"      - {profile.get('full_name')} ({profile.get('follower_count', 0):,} followers)")
        else:
            print(f"   ❌ Failed: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 3: Advanced Search
    print("\n3. Testing Advanced Search Endpoint:")
    try:
        response = requests.post(f"{BASE_URL}{API_PREFIX}/profile/search",
                               json={
                                   "platform": "youtube",
                                   "interests": ["gaming"],
                                   "gender": "MALE",
                                   "age_group": "25-34",
                                   "limit": 5,
                                   "offset": 0
                               })
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Success: Found {data['pagination']['total']} profiles")
            print(f"      Returned: {data['pagination']['returned']} profiles")
        else:
            print(f"   ❌ Failed: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 4: Validation Tests
    print("\n4. Testing Input Validation:")
    
    # Invalid platform
    try:
        response = requests.post(f"{BASE_URL}{API_PREFIX}/profile/quick-search",
                               json={"platform": "invalid_platform"})
        if response.status_code == 422:
            print("   ✅ Invalid platform validation works")
        else:
            print(f"   ❌ Invalid platform test failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Invalid follower range
    try:
        response = requests.post(f"{BASE_URL}{API_PREFIX}/profile/quick-search",
                               json={
                                   "follower_count_min": 100000,
                                   "follower_count_max": 1000
                               })
        if response.status_code == 422:
            print("   ✅ Invalid follower range validation works")
        else:
            print(f"   ❌ Invalid follower range test failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Error: {e}")


if __name__ == "__main__":
    run_manual_tests()
