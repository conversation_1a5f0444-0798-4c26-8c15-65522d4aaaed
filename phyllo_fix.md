# Fixing Phyllo API SSL Verification Issues

## Issue
The system is encountering SSL certificate verification failures when connecting to the Phyllo API:
```
ERROR: [SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: Hostname mismatch, certificate is not valid for 'api.phyllo.com'
```

## Root Cause
Even though we have `disable_ssl_verification: true` in the testing configuration and we're using `verify=not APP_CONFIG.disable_ssl_verification` in the HTTP client, the requests are still failing with SSL verification errors.

## Solution

1. Make sure `disable_ssl_verification` is correctly set to `true` in `appsettings.json` (already done)
2. Ensure the `PhylloAPIService` class uses the instance variable for the setting
3. Fix indentation issues in the code

### Code Updates Required:

1. In `PhylloAPIService.__init__` method:
   - Add a class instance variable for SSL verification setting

2. In all httpx client instances:
   - Use the class instance variable instead of directly accessing APP_CONFIG

### Implementation

```python
def __init__(self):
    self.logger = APP_CONFIG.logger or APP_CONFIG.initialize_logger()
    self.redis = get_locobuzz_redis()
    self.base_url = APP_CONFIG.phyllo_api_url
    self.client_id = APP_CONFIG.phyllo_client_id
    self.client_secret = APP_CONFIG.phyllo_client_secret
    self._access_token = None
    self._token_expires_at = None
    
    # Testing mode settings
    self.use_mock_apis = APP_CONFIG.use_mock_apis
    self.mock_data_enabled = APP_CONFIG.mock_data_enabled
    self.disable_ssl_verification = APP_CONFIG.disable_ssl_verification
    
    # Log API configuration
    self.logger.info(f"Phyllo API URL: {self.base_url}")
    self.logger.info(f"SSL verification disabled: {self.disable_ssl_verification}")
```

For each HTTP client instantiation:

```python
async with httpx.AsyncClient(verify=not self.disable_ssl_verification) as client:
    # Request implementation
```

## Verification

After making these changes, restart the server and verify that:
1. The log shows "SSL verification disabled: True"
2. API requests to Phyllo no longer result in SSL verification errors
