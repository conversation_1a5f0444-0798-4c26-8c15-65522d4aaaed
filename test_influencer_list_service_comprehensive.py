"""
Comprehensive Test Suite for Creatorverse Influencer List Service
Tests all influencer list management functionality including CSV import, filtering, and cache patterns.
"""
import asyncio
import pytest
import uuid
import json
import csv
import io
from datetime import datetime, UTC
from typing import Dict, Any, List, Optional
from unittest.mock import AsyncMock, MagicMock, patch

from sqlalchemy import select, delete
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import get_database, get_locobuzz_redis
from app.models.user_models import (
    User, Organization, Brand, BrandMembership, BrandInfluencerList,
    BrandInfluencerListEntry, BrandInfluencerListStatus, InfluencerStatus,
    BrandMembershipStatus, GlobalLabel, BrandLabel, BrandInfluencerListEntryLabel
)
from app.services.influencer_list_service import (
    create_influencer_list,
    get_brand_influencer_lists,
    get_influencer_list_detail,
    update_influencer_list,
    delete_influencer_list,
    add_influencer_to_list,
    update_influencer_in_list,
    remove_influencer_from_list,
    get_list_options_for_brand
)
from app.services.label_service import (
    create_global_label,
    assign_labels_to_entry
)
from app.schemas.influencer_schemas import (
    CreateInfluencerListRequest,
    UpdateInfluencerListRequest,
    AddInfluencerToListRequest,
    UpdateInfluencerInListRequest,
    InfluencerListFilters,
    InfluencerEntryFilters
)


class TestInfluencerListServiceComprehensive:
    \"\"\"Comprehensive test suite for influencer list service functionality\"\"\"

    @pytest.fixture(autouse=True)
    async def setup_test_environment(self):
        \"\"\"Setup test environment with clean database and Redis\"\"\"
        self.db_conn = get_database()
        self.redis_client = get_locobuzz_redis()
        
        await self.db_conn.initialize()
        await self.redis_client.initialize()
        
        # Clean up test data
        await self._cleanup_test_data()
        
        # Setup test data
        await self._setup_test_data()
        
        yield
        
        # Cleanup after test
        await self._cleanup_test_data()
        await self.redis_client.close()
        await self.db_conn.shutdown()

    async def _cleanup_test_data(self):
        \"\"\"Clean up test data from database and Redis\"\"\"
        try:
            async with self.db_conn.get_db() as session:
                # Delete test data (cascade will handle related records)
                await session.execute(
                    delete(User).where(User.email.like('%test%'))
                )
                await session.execute(
                    delete(Organization).where(Organization.domain.like('%test%'))
                )
                await session.execute(
                    delete(GlobalLabel).where(GlobalLabel.name.like('%test%'))
                )
                await session.commit()
            
            # Clear Redis test keys
            keys = await self.redis_client.keys("*test*")
            if keys:
                await self.redis_client.delete(*keys)
                
        except Exception as e:
            print(f"Cleanup error: {e}")

    async def _setup_test_data(self):
        \"\"\"Setup test data for influencer list tests\"\"\"
        async with self.db_conn.get_db() as session:
            # Create test organization
            self.test_org = Organization(
                domain="testcompany.com",
                name="Test Company",
                contact_email="<EMAIL>"
            )
            session.add(self.test_org)
            await session.flush()

            # Create test user
            self.test_user = User(
                email="<EMAIL>",
                name="Test User",
                status="active",
                is_email_verified=True
            )
            session.add(self.test_user)
            await session.flush()

            # Create test brand
            self.test_brand = Brand(
                organization_id=self.test_org.id,
                name="Test Brand",
                description="A test brand",
                created_by=self.test_user.id
            )
            session.add(self.test_brand)
            await session.flush()

            # Create brand membership
            self.brand_membership = BrandMembership(
                brand_id=self.test_brand.id,
                user_id=self.test_user.id,
                role="brand_admin",
                status=BrandMembershipStatus.active
            )
            session.add(self.brand_membership)
            
            # Create test labels
            self.test_labels = []
            for label_name in ["Entertainment", "Music", "Tech", "Fashion"]:
                label = GlobalLabel(
                    name=f"test_{label_name}",
                    description=f"Test {label_name} label",
                    color="#6B7280"
                )
                session.add(label)
                self.test_labels.append(label)
            
            await session.commit()

    def _create_test_influencer_data(self, influencer_id: str = None) -> Dict[str, Any]:
        \"\"\"Create test influencer data\"\"\"
        if not influencer_id:
            influencer_id = f"test_influencer_{uuid.uuid4().hex[:8]}"
        
        return {
            "influencer_id": influencer_id,
            "influencer_name": "Test Influencer",
            "influencer_username": "@testinfluencer",
            "influencer_avatar_url": "https://example.com/avatar.jpg",
            "status": InfluencerStatus.shortlisted,
            "channels": ["youtube", "instagram"],
            "audience_size": 50000,
            "engagement_rate": 350,  # 3.5%
            "campaign": "Summer Campaign",
            "labels": [],
            "notes": "Test influencer for campaigns"
        }

    # ──────────────────────────────────────────────────────────────────────────────
    # INFLUENCER LIST MANAGEMENT TESTS
    # ──────────────────────────────────────────────────────────────────────────────

    @pytest.mark.asyncio
    async def test_create_influencer_list_success(self):
        \"\"\"Test successful influencer list creation\"\"\"
        async with self.db_conn.get_db() as session:
            result = await create_influencer_list(
                session,
                self.redis_client,
                self.test_brand.id,
                self.test_user.id,
                "Lifestyle Creators",
                "Influencers focused on lifestyle content"
            )
            
            assert result is not None
            assert result.name == "Lifestyle Creators"
            assert result.brand_id == self.test_brand.id
            assert result.created_by == self.test_user.id
            assert result.status == BrandInfluencerListStatus.active
            
            await session.commit()

    @pytest.mark.asyncio
    async def test_create_influencer_list_duplicate_name(self):
        \"\"\"Test influencer list creation with duplicate name\"\"\"
        async with self.db_conn.get_db() as session:
            # Create first list
            await create_influencer_list(
                session,
                self.redis_client,
                self.test_brand.id,
                self.test_user.id,
                "Fashion Events",
                "Fashion-focused influencers"
            )
            await session.commit()

            # Try to create second list with same name
            with pytest.raises(Exception):  # Should raise IntegrityError
                await create_influencer_list(
                    session,
                    self.redis_client,
                    self.test_brand.id,
                    self.test_user.id,
                    "Fashion Events",
                    "Another fashion list"
                )

    @pytest.mark.asyncio
    async def test_create_influencer_list_invalid_brand(self):
        \"\"\"Test influencer list creation with invalid brand\"\"\"
        fake_brand_id = uuid.uuid4()
        
        async with self.db_conn.get_db() as session:
            with pytest.raises(ValueError, match="Brand not found"):
                await create_influencer_list(
                    session,
                    self.redis_client,
                    fake_brand_id,
                    self.test_user.id,
                    "Invalid Brand List",
                    "This should fail"
                )

    @pytest.mark.asyncio
    async def test_create_influencer_list_unauthorized_user(self):
        \"\"\"Test influencer list creation by unauthorized user\"\"\"
        # Create unauthorized user
        async with self.db_conn.get_db() as session:
            unauthorized_user = User(
                email="<EMAIL>",
                name="Unauthorized User",
                status="active"
            )
            session.add(unauthorized_user)
            await session.flush()
            
            with pytest.raises(ValueError, match="not a member"):
                await create_influencer_list(
                    session,
                    self.redis_client,
                    self.test_brand.id,
                    unauthorized_user.id,
                    "Unauthorized List",
                    "This should fail"
                )

    @pytest.mark.asyncio
    async def test_get_brand_influencer_lists_cache_aside(self):
        \"\"\"Test brand influencer lists retrieval with cache-aside pattern\"\"\"
        # Create test lists
        async with self.db_conn.get_db() as session:
            for i, list_name in enumerate(["Lifestyle Creators", "Fashion Events", "Tech Reviewers"]):
                await create_influencer_list(
                    session,
                    self.redis_client,
                    self.test_brand.id,
                    self.test_user.id,
                    list_name,
                    f"Description for {list_name}"
                )
            await session.commit()

        # First call - cache miss, database hit
        result1 = await get_brand_influencer_lists(
            self.db_conn,
            self.redis_client,
            self.test_brand.id
        )
        
        assert len(result1) == 3
        assert any(lst["name"] == "Lifestyle Creators" for lst in result1)
        assert any(lst["name"] == "Fashion Events" for lst in result1)
        assert any(lst["name"] == "Tech Reviewers" for lst in result1)

        # Second call - should hit cache
        with patch.object(self.db_conn, 'get_db') as mock_db:
            result2 = await get_brand_influencer_lists(
                self.db_conn,
                self.redis_client,
                self.test_brand.id
            )
            
            # Database should not be called (cache hit)
            mock_db.assert_not_called()
            assert result2 == result1

    @pytest.mark.asyncio
    async def test_get_brand_influencer_lists_with_filters(self):
        \"\"\"Test brand influencer lists retrieval with filters\"\"\"
        # Create test lists with different statuses
        async with self.db_conn.get_db() as session:
            # Active list
            active_list = await create_influencer_list(
                session,
                self.redis_client,
                self.test_brand.id,
                self.test_user.id,
                "Active List",
                "Active list description"
            )
            
            # Archived list
            archived_list = await create_influencer_list(
                session,
                self.redis_client,
                self.test_brand.id,
                self.test_user.id,
                "Archived List",
                "Archived list description"
            )
            archived_list.status = BrandInfluencerListStatus.archived
            
            await session.commit()

        # Test filter by status
        from app.schemas.influencer_schemas import InfluencerListFilters
        
        # Filter for active lists only
        active_filter = InfluencerListFilters(status=BrandInfluencerListStatus.active)
        active_results = await get_brand_influencer_lists(
            self.db_conn,
            self.redis_client,
            self.test_brand.id,
            active_filter
        )
        
        assert len(active_results) == 1
        assert active_results[0]["name"] == "Active List"
        assert active_results[0]["status"] == "active"

        # Filter by search term
        search_filter = InfluencerListFilters(search="Archived")
        search_results = await get_brand_influencer_lists(
            self.db_conn,
            self.redis_client,
            self.test_brand.id,
            search_filter
        )
        
        assert len(search_results) == 1
        assert search_results[0]["name"] == "Archived List"

    # ──────────────────────────────────────────────────────────────────────────────
    # INFLUENCER ENTRY MANAGEMENT TESTS
    # ──────────────────────────────────────────────────────────────────────────────

    @pytest.mark.asyncio
    async def test_add_influencer_to_list_success(self):
        \"\"\"Test successful influencer addition to list\"\"\"
        # Create test list
        async with self.db_conn.get_db() as session:
            test_list = await create_influencer_list(
                session,
                self.redis_client,
                self.test_brand.id,
                self.test_user.id,
                "Test List",
                "Test list for influencers"
            )
            await session.commit()

            # Add influencer to list
            influencer_data = self._create_test_influencer_data()
            
            from app.schemas.influencer_schemas import AddInfluencerToListRequest
            request = AddInfluencerToListRequest(**influencer_data)
            
            result = await add_influencer_to_list(
                session,
                self.redis_client,
                test_list.id,
                self.test_user.id,
                request
            )
            
            assert result is not None
            assert result.influencer_id == influencer_data["influencer_id"]
            assert result.list_id == test_list.id
            assert result.status == InfluencerStatus.shortlisted
            assert result.audience_size == 50000
            assert result.engagement_rate == 350
            
            await session.commit()

    @pytest.mark.asyncio
    async def test_add_duplicate_influencer_to_list(self):
        \"\"\"Test adding duplicate influencer to same list\"\"\"
        async with self.db_conn.get_db() as session:
            # Create test list
            test_list = await create_influencer_list(
                session,
                self.redis_client,
                self.test_brand.id,
                self.test_user.id,
                "Test List",
                "Test list for influencers"
            )
            await session.commit()

            # Add influencer first time
            influencer_data = self._create_test_influencer_data("duplicate_influencer")
            
            from app.schemas.influencer_schemas import AddInfluencerToListRequest
            request = AddInfluencerToListRequest(**influencer_data)
            
            await add_influencer_to_list(
                session,
                self.redis_client,
                test_list.id,
                self.test_user.id,
                request
            )
            await session.commit()

            # Try to add same influencer again
            with pytest.raises(Exception):  # Should raise IntegrityError
                await add_influencer_to_list(
                    session,
                    self.redis_client,
                    test_list.id,
                    self.test_user.id,
                    request
                )

    @pytest.mark.asyncio
    async def test_get_influencer_list_detail_with_entries(self):
        \"\"\"Test detailed list retrieval with influencer entries\"\"\"
        # Create test list with influencers
        async with self.db_conn.get_db() as session:
            test_list = await create_influencer_list(
                session,
                self.redis_client,
                self.test_brand.id,
                self.test_user.id,
                "Detailed List",
                "List with multiple influencers"
            )
            await session.commit()

            # Add multiple influencers
            from app.schemas.influencer_schemas import AddInfluencerToListRequest
            
            influencer_names = ["Alice", "Bob", "Charlie"]
            for i, name in enumerate(influencer_names):
                influencer_data = self._create_test_influencer_data(f"influencer_{i}")
                influencer_data["influencer_name"] = name
                influencer_data["audience_size"] = 10000 * (i + 1)
                
                request = AddInfluencerToListRequest(**influencer_data)
                await add_influencer_to_list(
                    session,
                    self.redis_client,
                    test_list.id,
                    self.test_user.id,
                    request
                )
            await session.commit()

        # Get detailed list
        detail = await get_influencer_list_detail(
            self.db_conn,
            self.redis_client,
            test_list.id
        )
        
        assert detail is not None
        assert detail["name"] == "Detailed List"
        assert detail["total_influencers"] == 3
        assert len(detail["influencers"]) == 3
        
        # Check influencer details
        influencer_names_found = [inf["influencer_name"] for inf in detail["influencers"]]
        assert "Alice" in influencer_names_found
        assert "Bob" in influencer_names_found
        assert "Charlie" in influencer_names_found

    @pytest.mark.asyncio
    async def test_get_influencer_list_detail_with_filters(self):
        \"\"\"Test detailed list retrieval with entry filters\"\"\"
        # Create test list with influencers of different statuses
        async with self.db_conn.get_db() as session:
            test_list = await create_influencer_list(
                session,
                self.redis_client,
                self.test_brand.id,
                self.test_user.id,
                "Filtered List",
                "List for filter testing"
            )
            await session.commit()

            # Add influencers with different statuses
            from app.schemas.influencer_schemas import AddInfluencerToListRequest
            
            statuses = [InfluencerStatus.shortlisted, InfluencerStatus.contacted, InfluencerStatus.in_progress]
            for i, status in enumerate(statuses):
                influencer_data = self._create_test_influencer_data(f"filtered_influencer_{i}")
                influencer_data["status"] = status
                influencer_data["audience_size"] = 20000 + (i * 10000)
                
                request = AddInfluencerToListRequest(**influencer_data)
                await add_influencer_to_list(
                    session,
                    self.redis_client,
                    test_list.id,
                    self.test_user.id,
                    request
                )
            await session.commit()

        # Test filter by status
        from app.schemas.influencer_schemas import InfluencerEntryFilters
        
        contacted_filter = InfluencerEntryFilters(status=InfluencerStatus.contacted)
        detail = await get_influencer_list_detail(
            self.db_conn,
            self.redis_client,
            test_list.id,
            contacted_filter
        )
        
        assert detail is not None
        assert len(detail["influencers"]) == 1
        assert detail["influencers"][0]["status"] == "contacted"

        # Test filter by audience size
        audience_filter = InfluencerEntryFilters(min_audience=25000, max_audience=35000)
        detail = await get_influencer_list_detail(
            self.db_conn,
            self.redis_client,
            test_list.id,
            audience_filter
        )
        
        assert detail is not None
        assert len(detail["influencers"]) == 1
        assert detail["influencers"][0]["audience_size"] == 30000

    @pytest.mark.asyncio
    async def test_update_influencer_in_list(self):
        \"\"\"Test updating influencer details in list\"\"\"
        # Create test list with influencer
        async with self.db_conn.get_db() as session:
            test_list = await create_influencer_list(
                session,
                self.redis_client,
                self.test_brand.id,
                self.test_user.id,
                "Update Test List",
                "List for update testing"
            )
            await session.commit()

            # Add influencer
            influencer_data = self._create_test_influencer_data()
            
            from app.schemas.influencer_schemas import AddInfluencerToListRequest
            request = AddInfluencerToListRequest(**influencer_data)
            
            entry = await add_influencer_to_list(
                session,
                self.redis_client,
                test_list.id,
                self.test_user.id,
                request
            )
            await session.commit()

            # Update influencer status and notes
            from app.schemas.influencer_schemas import UpdateInfluencerInListRequest
            
            update_request = UpdateInfluencerInListRequest(
                status=InfluencerStatus.contacted,
                notes="Reached out via email",
                campaign="Updated Campaign"
            )
            
            updated_entry = await update_influencer_in_list(
                session,
                self.redis_client,
                entry.id,
                self.test_user.id,
                update_request
            )
            
            assert updated_entry is not None
            assert updated_entry.status == InfluencerStatus.contacted
            assert updated_entry.notes == "Reached out via email"
            assert updated_entry.campaign == "Updated Campaign"
            
            await session.commit()

    @pytest.mark.asyncio
    async def test_remove_influencer_from_list(self):
        \"\"\"Test removing influencer from list\"\"\"
        # Create test list with influencer
        async with self.db_conn.get_db() as session:
            test_list = await create_influencer_list(
                session,
                self.redis_client,
                self.test_brand.id,
                self.test_user.id,
                "Remove Test List",
                "List for removal testing"
            )
            await session.commit()

            # Add influencer
            influencer_data = self._create_test_influencer_data()
            
            from app.schemas.influencer_schemas import AddInfluencerToListRequest
            request = AddInfluencerToListRequest(**influencer_data)
            
            entry = await add_influencer_to_list(
                session,
                self.redis_client,
                test_list.id,
                self.test_user.id,
                request
            )
            await session.commit()

            # Remove influencer
            success = await remove_influencer_from_list(
                session,
                self.redis_client,
                entry.id,
                self.test_user.id
            )
            
            assert success
            await session.commit()

            # Verify influencer is removed
            detail = await get_influencer_list_detail(
                self.db_conn,
                self.redis_client,
                test_list.id
            )
            
            assert detail["total_influencers"] == 0
            assert len(detail["influencers"]) == 0

    # ──────────────────────────────────────────────────────────────────────────────
    # CSV IMPORT SIMULATION TESTS
    # ──────────────────────────────────────────────────────────────────────────────

    @pytest.mark.asyncio
    async def test_bulk_influencer_import_simulation(self):
        \"\"\"Test bulk influencer import (simulating CSV import functionality)\"\"\"
        # Create test list
        async with self.db_conn.get_db() as session:
            test_list = await create_influencer_list(
                session,
                self.redis_client,
                self.test_brand.id,
                self.test_user.id,
                "Bulk Import List",
                "List for bulk import testing"
            )
            await session.commit()

            # Simulate CSV data
            csv_data = [
                {
                    "influencer_id": f"bulk_influencer_{i}",
                    "influencer_name": f"Influencer {i}",
                    "influencer_username": f"@influencer{i}",
                    "audience_size": 10000 + (i * 1000),
                    "engagement_rate": 300 + (i * 10),
                    "channels": ["instagram", "youtube"],
                    "campaign": "Summer Campaign 2025"
                }
                for i in range(20)  # 20 influencers
            ]

            # Import influencers in batch
            from app.schemas.influencer_schemas import AddInfluencerToListRequest
            
            successful_imports = 0
            failed_imports = []
            
            for data in csv_data:
                try:
                    request = AddInfluencerToListRequest(**data)
                    await add_influencer_to_list(
                        session,
                        self.redis_client,
                        test_list.id,
                        self.test_user.id,
                        request
                    )
                    successful_imports += 1
                except Exception as e:
                    failed_imports.append({"data": data, "error": str(e)})
            
            await session.commit()

            # Verify import results
            assert successful_imports == 20
            assert len(failed_imports) == 0

            # Verify list contains all imported influencers
            detail = await get_influencer_list_detail(
                self.db_conn,
                self.redis_client,
                test_list.id
            )
            
            assert detail["total_influencers"] == 20
            assert len(detail["influencers"]) == 20

    @pytest.mark.asyncio
    async def test_csv_import_with_duplicates_and_errors(self):
        \"\"\"Test CSV import handling duplicates and invalid data\"\"\"
        # Create test list
        async with self.db_conn.get_db() as session:
            test_list = await create_influencer_list(
                session,
                self.redis_client,
                self.test_brand.id,
                self.test_user.id,
                "Error Handling List",
                "List for error handling testing"
            )
            await session.commit()

            # CSV data with duplicates and invalid entries
            csv_data = [
                # Valid entry
                {
                    "influencer_id": "valid_influencer_1",
                    "influencer_name": "Valid Influencer",
                    "influencer_username": "@validinfluencer",
                    "audience_size": 50000,
                    "engagement_rate": 350
                },
                # Duplicate entry (same influencer_id)
                {
                    "influencer_id": "valid_influencer_1",
                    "influencer_name": "Duplicate Influencer",
                    "influencer_username": "@duplicate",
                    "audience_size": 60000,
                    "engagement_rate": 400
                },
                # Invalid entry (missing required field)
                {
                    "influencer_name": "Invalid Influencer",
                    "audience_size": 30000
                    # Missing influencer_id
                },
                # Valid entry
                {
                    "influencer_id": "valid_influencer_2",
                    "influencer_name": "Another Valid Influencer",
                    "influencer_username": "@anothervalid",
                    "audience_size": 70000,
                    "engagement_rate": 450
                }
            ]

            # Process CSV data
            from app.schemas.influencer_schemas import AddInfluencerToListRequest
            
            successful_imports = 0
            failed_imports = []
            
            for i, data in enumerate(csv_data):
                try:
                    request = AddInfluencerToListRequest(**data)
                    await add_influencer_to_list(
                        session,
                        self.redis_client,
                        test_list.id,
                        self.test_user.id,
                        request
                    )
                    successful_imports += 1
                except Exception as e:
                    failed_imports.append({
                        "row": i + 1,
                        "data": data,
                        "error": str(e)
                    })
            
            await session.commit()

            # Verify results
            assert successful_imports == 2  # Only 2 valid, non-duplicate entries
            assert len(failed_imports) == 2  # 1 duplicate, 1 invalid

            # Check specific error types
            duplicate_error = next((f for f in failed_imports if "duplicate" in f["error"].lower() or "unique" in f["error"].lower()), None)
            invalid_error = next((f for f in failed_imports if "required" in f["error"].lower() or "missing" in f["error"].lower()), None)
            
            assert duplicate_error is not None
            assert invalid_error is not None

    # ──────────────────────────────────────────────────────────────────────────────
    # LABEL INTEGRATION TESTS
    # ──────────────────────────────────────────────────────────────────────────────

    @pytest.mark.asyncio
    async def test_influencer_label_assignment(self):
        \"\"\"Test assigning labels to influencer entries\"\"\"
        # Create test list with influencer
        async with self.db_conn.get_db() as session:
            test_list = await create_influencer_list(
                session,
                self.redis_client,
                self.test_brand.id,
                self.test_user.id,
                "Label Test List",
                "List for label testing"
            )
            await session.commit()

            # Add influencer
            influencer_data = self._create_test_influencer_data()
            
            from app.schemas.influencer_schemas import AddInfluencerToListRequest
            request = AddInfluencerToListRequest(**influencer_data)
            
            entry = await add_influencer_to_list(
                session,
                self.redis_client,
                test_list.id,
                self.test_user.id,
                request
            )
            await session.commit()

            # Assign labels to influencer
            label_ids = [label.id for label in self.test_labels[:2]]  # First 2 labels
            
            success, errors = await assign_labels_to_entry(
                session,
                self.redis_client,
                entry.id,
                self.test_user.id,
                label_ids
            )
            
            assert success
            assert len(errors) == 0
            await session.commit()

            # Verify labels are assigned
            detail = await get_influencer_list_detail(
                self.db_conn,
                self.redis_client,
                test_list.id
            )
            
            influencer = detail["influencers"][0]
            # Note: This would require updating the service to return label information
            # For now, we're testing the assignment logic

    # ──────────────────────────────────────────────────────────────────────────────
    # PERFORMANCE TESTS
    # ──────────────────────────────────────────────────────────────────────────────

    @pytest.mark.asyncio
    async def test_large_list_performance(self):
        \"\"\"Test performance with large influencer lists\"\"\"
        # Create test list
        async with self.db_conn.get_db() as session:
            test_list = await create_influencer_list(
                session,
                self.redis_client,
                self.test_brand.id,
                self.test_user.id,
                "Large List",
                "Performance testing list"
            )
            await session.commit()

            # Add many influencers
            from app.schemas.influencer_schemas import AddInfluencerToListRequest
            
            import time
            start_time = time.time()
            
            for i in range(100):  # 100 influencers
                influencer_data = self._create_test_influencer_data(f"perf_influencer_{i}")
                request = AddInfluencerToListRequest(**influencer_data)
                
                await add_influencer_to_list(
                    session,
                    self.redis_client,
                    test_list.id,
                    self.test_user.id,
                    request
                )
                
                # Commit every 20 entries to avoid large transactions
                if (i + 1) % 20 == 0:
                    await session.commit()
            
            await session.commit()
            
            creation_time = time.time() - start_time
            
            # Test retrieval performance
            start_time = time.time()
            
            detail = await get_influencer_list_detail(
                self.db_conn,
                self.redis_client,
                test_list.id
            )
            
            retrieval_time = time.time() - start_time
            
            # Verify all influencers are present
            assert detail["total_influencers"] == 100
            assert len(detail["influencers"]) == 100
            
            # Performance assertions (adjust thresholds as needed)
            assert creation_time < 30.0  # Should create 100 entries in under 30 seconds
            assert retrieval_time < 5.0   # Should retrieve in under 5 seconds

    # ──────────────────────────────────────────────────────────────────────────────
    # CACHE INVALIDATION TESTS
    # ──────────────────────────────────────────────────────────────────────────────

    @pytest.mark.asyncio
    async def test_cache_invalidation_on_list_changes(self):
        \"\"\"Test cache invalidation when lists are modified\"\"\"
        # Create test list
        async with self.db_conn.get_db() as session:
            test_list = await create_influencer_list(
                session,
                self.redis_client,
                self.test_brand.id,
                self.test_user.id,
                "Cache Test List",
                "List for cache testing"
            )
            await session.commit()

        # Get lists to populate cache
        result1 = await get_brand_influencer_lists(
            self.db_conn,
            self.redis_client,
            self.test_brand.id
        )
        assert len(result1) == 1

        # Add influencer to list (should invalidate cache)
        async with self.db_conn.get_db() as session:
            influencer_data = self._create_test_influencer_data()
            
            from app.schemas.influencer_schemas import AddInfluencerToListRequest
            request = AddInfluencerToListRequest(**influencer_data)
            
            await add_influencer_to_list(
                session,
                self.redis_client,
                test_list.id,
                self.test_user.id,
                request
            )
            await session.commit()

        # Get lists again - should reflect changes
        result2 = await get_brand_influencer_lists(
            self.db_conn,
            self.redis_client,
            self.test_brand.id
        )
        
        # The list should now show 1 influencer
        assert len(result2) == 1
        assert result2[0]["total_influencers"] == 1

    # ──────────────────────────────────────────────────────────────────────────────
    # STATUS TRANSITION TESTS
    # ──────────────────────────────────────────────────────────────────────────────

    @pytest.mark.asyncio
    async def test_influencer_status_transitions(self):
        \"\"\"Test various influencer status transitions\"\"\"
        # Create test list with influencer
        async with self.db_conn.get_db() as session:
            test_list = await create_influencer_list(
                session,
                self.redis_client,
                self.test_brand.id,
                self.test_user.id,
                "Status Test List",
                "List for status transition testing"
            )
            await session.commit()

            # Add influencer with shortlisted status
            influencer_data = self._create_test_influencer_data()
            influencer_data["status"] = InfluencerStatus.shortlisted
            
            from app.schemas.influencer_schemas import AddInfluencerToListRequest
            request = AddInfluencerToListRequest(**influencer_data)
            
            entry = await add_influencer_to_list(
                session,
                self.redis_client,
                test_list.id,
                self.test_user.id,
                request
            )
            await session.commit()

            # Test status transitions
            from app.schemas.influencer_schemas import UpdateInfluencerInListRequest
            
            # shortlisted → contacted
            update1 = UpdateInfluencerInListRequest(
                status=InfluencerStatus.contacted,
                notes="Initial contact made"
            )
            
            updated_entry = await update_influencer_in_list(
                session,
                self.redis_client,
                entry.id,
                self.test_user.id,
                update1
            )
            assert updated_entry.status == InfluencerStatus.contacted
            await session.commit()

            # contacted → in_progress
            update2 = UpdateInfluencerInListRequest(
                status=InfluencerStatus.in_progress,
                notes="Negotiating collaboration terms"
            )
            
            updated_entry = await update_influencer_in_list(
                session,
                self.redis_client,
                entry.id,
                self.test_user.id,
                update2
            )
            assert updated_entry.status == InfluencerStatus.in_progress
            await session.commit()

            # in_progress → completed
            update3 = UpdateInfluencerInListRequest(
                status=InfluencerStatus.completed,
                notes="Campaign completed successfully"
            )
            
            updated_entry = await update_influencer_in_list(
                session,
                self.redis_client,
                entry.id,
                self.test_user.id,
                update3
            )
            assert updated_entry.status == InfluencerStatus.completed
            await session.commit()

    # ──────────────────────────────────────────────────────────────────────────────
    # CONCURRENT OPERATIONS TESTS
    # ──────────────────────────────────────────────────────────────────────────────

    @pytest.mark.asyncio
    async def test_concurrent_influencer_additions(self):
        \"\"\"Test concurrent influencer additions to same list\"\"\"
        # Create test list
        async with self.db_conn.get_db() as session:
            test_list = await create_influencer_list(
                session,
                self.redis_client,
                self.test_brand.id,
                self.test_user.id,
                "Concurrent Test List",
                "List for concurrent testing"
            )
            await session.commit()

        # Define concurrent addition task
        async def add_influencer_task(task_id):
            async with self.db_conn.get_db() as session:
                influencer_data = self._create_test_influencer_data(f"concurrent_influencer_{task_id}")
                
                from app.schemas.influencer_schemas import AddInfluencerToListRequest
                request = AddInfluencerToListRequest(**influencer_data)
                
                try:
                    await add_influencer_to_list(
                        session,
                        self.redis_client,
                        test_list.id,
                        self.test_user.id,
                        request
                    )
                    await session.commit()
                    return True
                except Exception as e:
                    print(f"Task {task_id} failed: {e}")
                    return False

        # Run concurrent tasks
        tasks = [add_influencer_task(i) for i in range(10)]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Count successful additions
        successful_additions = sum(1 for r in results if r is True)
        
        # Verify final state
        detail = await get_influencer_list_detail(
            self.db_conn,
            self.redis_client,
            test_list.id
        )
        
        assert detail["total_influencers"] == successful_additions
        assert successful_additions >= 8  # Allow for some potential race conditions


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v", "--tb=short"])
