"""
Comprehensive Test Suite for Creatorverse Label Service
Tests all label management functionality including global labels, brand usage, and autocomplete.
"""
import asyncio
import pytest
import uuid
import json
from datetime import datetime, UTC
from typing import Dict, Any, List, Optional
from unittest.mock import AsyncMock, MagicMock, patch

from sqlalchemy import select, delete
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import get_database, get_locobuzz_redis
from app.models.user_models import (
    User, Organization, Brand, BrandMembership, GlobalLabel, BrandLabel,
    BrandInfluencerList, BrandInfluencerListEntry, BrandInfluencerListEntryLabel,
    BrandMembershipStatus, InfluencerStatus
)
from app.services.label_service import (
    create_global_label,
    get_global_labels,
    update_global_label,
    delete_global_label,
    get_brand_labels,
    get_label_autocomplete,
    assign_labels_to_entry,
    remove_labels_from_entry,
    get_label_usage_statistics
)
from app.schemas.label_schemas import (
    CreateLabelRequest,
    UpdateLabelRequest,
    LabelFilters,
    BrandLabelFilters
)


class TestLabelServiceComprehensive:
    """Comprehensive test suite for label service functionality"""

    @pytest.fixture(autouse=True)
    async def setup_test_environment(self):
        """Setup test environment with clean database and Redis"""
        self.db_conn = get_database()
        self.redis_client = get_locobuzz_redis()
        
        await self.db_conn.initialize()
        await self.redis_client.initialize()
        
        # Clean up test data
        await self._cleanup_test_data()
        
        # Setup test data
        await self._setup_test_data()
        
        yield
        
        # Cleanup after test
        await self._cleanup_test_data()
        await self.redis_client.close()
        await self.db_conn.shutdown()

    async def _cleanup_test_data(self):
        """Clean up test data from database and Redis"""
        try:
            async with self.db_conn.get_db() as session:
                # Delete test data (cascade will handle related records)
                await session.execute(
                    delete(User).where(User.email.like('%test%'))
                )
                await session.execute(
                    delete(Organization).where(Organization.domain.like('%test%'))
                )
                await session.execute(
                    delete(GlobalLabel).where(GlobalLabel.name.like('%test%'))
                )
                await session.commit()
            
            # Clear Redis test keys
            keys = await self.redis_client.keys("*test*")
            if keys:
                await self.redis_client.delete(*keys)
                
        except Exception as e:
            print(f"Cleanup error: {e}")

    async def _setup_test_data(self):
        """Setup test data for label tests"""
        async with self.db_conn.get_db() as session:
            # Create test organization
            self.test_org = Organization(
                domain="testcompany.com",
                name="Test Company",
                contact_email="<EMAIL>"
            )
            session.add(self.test_org)
            await session.flush()

            # Create test user
            self.test_user = User(
                email="<EMAIL>",
                name="Test User",
                status="active",
                is_email_verified=True
            )
            session.add(self.test_user)
            await session.flush()

            # Create test brand
            self.test_brand = Brand(
                organization_id=self.test_org.id,
                name="Test Brand",
                description="A test brand",
                created_by=self.test_user.id
            )
            session.add(self.test_brand)
            await session.flush()

            # Create brand membership
            self.brand_membership = BrandMembership(
                brand_id=self.test_brand.id,
                user_id=self.test_user.id,
                role="brand_admin",
                status=BrandMembershipStatus.active
            )
            session.add(self.brand_membership)
            
            await session.commit()

    def _create_test_label_data(self, name: str = None) -> Dict[str, Any]:
        """Create test label data"""
        if not name:
            name = f"test_label_{uuid.uuid4().hex[:8]}"
        
        return {
            "name": name,
            "description": f"Test description for {name}",
            "color": "#FF5733"
        }

    # ──────────────────────────────────────────────────────────────────────────────
    # GLOBAL LABEL MANAGEMENT TESTS
    # ──────────────────────────────────────────────────────────────────────────────

    @pytest.mark.asyncio
    async def test_create_global_label_success(self):
        """Test successful global label creation"""
        label_data = self._create_test_label_data("Entertainment")
        
        async with self.db_conn.get_db() as session:
            from app.schemas.label_schemas import CreateLabelRequest
            request = CreateLabelRequest(**label_data)
            
            result = await create_global_label(
                session,
                self.redis_client,
                request
            )
            
            assert result is not None
            assert result.name == "Entertainment"
            assert result.description == label_data["description"]
            assert result.color == "#FF5733"
            assert result.usage_count == 0
            
            await session.commit()

    @pytest.mark.asyncio
    async def test_create_global_label_duplicate_name(self):
        """Test global label creation with duplicate name"""
        label_data = self._create_test_label_data("Duplicate")
        
        async with self.db_conn.get_db() as session:
            from app.schemas.label_schemas import CreateLabelRequest
            request = CreateLabelRequest(**label_data)
            
            # Create first label
            await create_global_label(
                session,
                self.redis_client,
                request
            )
            await session.commit()

            # Try to create second label with same name
            with pytest.raises(Exception):  # Should raise IntegrityError
                await create_global_label(
                    session,
                    self.redis_client,
                    request
                )

    @pytest.mark.asyncio
    async def test_get_global_labels_cache_aside(self):
        """Test global labels retrieval with cache-aside pattern"""
        # Create test labels
        label_names = ["Entertainment", "Music", "Technology", "Fashion", "Sports"]
        created_labels = []
        
        async with self.db_conn.get_db() as session:
            from app.schemas.label_schemas import CreateLabelRequest
            
            for name in label_names:
                label_data = self._create_test_label_data(name)
                request = CreateLabelRequest(**label_data)
                
                label = await create_global_label(
                    session,
                    self.redis_client,
                    request
                )
                created_labels.append(label)
            
            await session.commit()

        # First call - cache miss, database hit
        result1 = await get_global_labels(
            self.db_conn,
            self.redis_client
        )
        
        assert len(result1) == 5
        label_names_found = [label["name"] for label in result1]
        assert all(name in label_names_found for name in label_names)

        # Second call - should hit cache
        with patch.object(self.db_conn, 'get_db') as mock_db:
            result2 = await get_global_labels(
                self.db_conn,
                self.redis_client
            )
            
            # Database should not be called (cache hit)
            mock_db.assert_not_called()
            assert result2 == result1

    @pytest.mark.asyncio
    async def test_get_global_labels_with_filters(self):
        """Test global labels retrieval with various filters"""
        # Create labels with different properties
        async with self.db_conn.get_db() as session:
            from app.schemas.label_schemas import CreateLabelRequest
            
            label_configs = [
                {"name": "Popular Entertainment", "color": "#FF0000", "description": "Entertainment content"},
                {"name": "Tech Reviews", "color": "#00FF00", "description": "Technology reviews"},
                {"name": "Music Performance", "color": "#0000FF", "description": "Live music shows"},
                {"name": "Fashion Style", "color": "#FF0000", "description": "Fashion and style tips"},
            ]
            
            for config in label_configs:
                request = CreateLabelRequest(**config)
                label = await create_global_label(
                    session,
                    self.redis_client,
                    request
                )
                # Simulate some usage
                label.usage_count = len(config["name"])  # Simple usage simulation
            
            await session.commit()

        # Test search filter
        from app.schemas.label_schemas import LabelFilters
        
        search_filter = LabelFilters(search="Tech")
        search_results = await get_global_labels(
            self.db_conn,
            self.redis_client,
            search_filter
        )
        
        assert len(search_results) == 1
        assert search_results[0]["name"] == "Tech Reviews"

        # Test color filter
        color_filter = LabelFilters(color="#FF0000")
        color_results = await get_global_labels(
            self.db_conn,
            self.redis_client,
            color_filter
        )
        
        assert len(color_results) == 2
        red_labels = [label["name"] for label in color_results]
        assert "Popular Entertainment" in red_labels
        assert "Fashion Style" in red_labels

        # Test usage count filter
        usage_filter = LabelFilters(min_usage=15)
        usage_results = await get_global_labels(
            self.db_conn,
            self.redis_client,
            usage_filter
        )
        
        # Only labels with names longer than 15 characters
        assert all(len(label["name"]) >= 15 for label in usage_results)

    @pytest.mark.asyncio
    async def test_update_global_label(self):
        """Test global label updates"""
        # Create test label
        label_data = self._create_test_label_data("Updatable")
        
        async with self.db_conn.get_db() as session:
            from app.schemas.label_schemas import CreateLabelRequest
            request = CreateLabelRequest(**label_data)
            
            created_label = await create_global_label(
                session,
                self.redis_client,
                request
            )
            await session.commit()

            # Update the label
            from app.schemas.label_schemas import UpdateLabelRequest
            
            update_request = UpdateLabelRequest(
                name="Updated Label",
                description="Updated description",
                color="#00FFFF"
            )
            
            updated_label = await update_global_label(
                session,
                self.redis_client,
                created_label.id,
                update_request
            )
            
            assert updated_label is not None
            assert updated_label.name == "Updated Label"
            assert updated_label.description == "Updated description"
            assert updated_label.color == "#00FFFF"
            
            await session.commit()

    @pytest.mark.asyncio
    async def test_delete_global_label(self):
        """Test global label deletion"""
        # Create test label
        label_data = self._create_test_label_data("Deletable")
        
        async with self.db_conn.get_db() as session:
            from app.schemas.label_schemas import CreateLabelRequest
            request = CreateLabelRequest(**label_data)
            
            created_label = await create_global_label(
                session,
                self.redis_client,
                request
            )
            await session.commit()

            # Delete the label
            success = await delete_global_label(
                session,
                self.redis_client,
                created_label.id
            )
            
            assert success
            await session.commit()

            # Verify label is deleted
            labels = await get_global_labels(
                self.db_conn,
                self.redis_client
            )
            
            label_names = [label["name"] for label in labels]
            assert "Deletable" not in label_names

    # ──────────────────────────────────────────────────────────────────────────────
    # BRAND LABEL USAGE TESTS
    # ──────────────────────────────────────────────────────────────────────────────

    @pytest.mark.asyncio
    async def test_get_brand_labels_cache_aside(self):
        """Test brand labels retrieval with cache-aside pattern"""
        # Create global labels
        async with self.db_conn.get_db() as session:
            from app.schemas.label_schemas import CreateLabelRequest
            
            global_labels = []
            for name in ["Brand Entertainment", "Brand Music", "Brand Tech"]:
                label_data = self._create_test_label_data(name)
                request = CreateLabelRequest(**label_data)
                
                label = await create_global_label(
                    session,
                    self.redis_client,
                    request
                )
                global_labels.append(label)
            
            # Create brand labels (simulate usage)
            for i, global_label in enumerate(global_labels):
                brand_label = BrandLabel(
                    brand_id=self.test_brand.id,
                    global_label_id=global_label.id,
                    usage_count=i + 1,
                    created_by=self.test_user.id
                )
                session.add(brand_label)
            
            await session.commit()

        # First call - cache miss, database hit
        result1 = await get_brand_labels(
            self.db_conn,
            self.redis_client,
            self.test_brand.id
        )
        
        assert len(result1) == 3
        usage_counts = [label["usage_count"] for label in result1]
        assert 1 in usage_counts
        assert 2 in usage_counts
        assert 3 in usage_counts

        # Second call - should hit cache
        with patch.object(self.db_conn, 'get_db') as mock_db:
            result2 = await get_brand_labels(
                self.db_conn,
                self.redis_client,
                self.test_brand.id
            )
            
            # Database should not be called (cache hit)
            mock_db.assert_not_called()
            assert result2 == result1

    @pytest.mark.asyncio
    async def test_get_label_autocomplete_prioritization(self):
        """Test label autocomplete with proper prioritization"""
        # Create global labels
        async with self.db_conn.get_db() as session:
            from app.schemas.label_schemas import CreateLabelRequest
            
            # Create labels with "music" in name
            music_labels = []
            label_configs = [
                {"name": "Music Performance", "usage": 10},
                {"name": "Live Music", "usage": 5},
                {"name": "Music Reviews", "usage": 15},
                {"name": "Classical Music", "usage": 2}
            ]
            
            for config in label_configs:
                label_data = self._create_test_label_data(config["name"])
                request = CreateLabelRequest(**label_data)
                
                label = await create_global_label(
                    session,
                    self.redis_client,
                    request
                )
                label.usage_count = config["usage"]
                music_labels.append(label)
            
            # Create brand usage for some labels (higher priority)
            brand_usage_labels = music_labels[:2]  # First 2 labels
            for i, label in enumerate(brand_usage_labels):
                brand_label = BrandLabel(
                    brand_id=self.test_brand.id,
                    global_label_id=label.id,
                    usage_count=10 + i,
                    created_by=self.test_user.id
                )
                session.add(brand_label)
            
            await session.commit()

        # Test autocomplete with "music" query
        results = await get_label_autocomplete(
            self.db_conn,
            self.redis_client,
            self.test_brand.id,
            "music",
            limit=10
        )
        
        assert len(results) == 4
        
        # Brand-used labels should come first
        brand_used_names = [results[0]["name"], results[1]["name"]]
        assert "Music Performance" in brand_used_names
        assert "Live Music" in brand_used_names

    @pytest.mark.asyncio
    async def test_get_label_autocomplete_caching(self):
        """Test label autocomplete caching behavior"""
        # Create some labels
        async with self.db_conn.get_db() as session:
            from app.schemas.label_schemas import CreateLabelRequest
            
            for name in ["Auto Entertainment", "Auto Sports", "Auto Tech"]:
                label_data = self._create_test_label_data(name)
                request = CreateLabelRequest(**label_data)
                
                await create_global_label(
                    session,
                    self.redis_client,
                    request
                )
            
            await session.commit()

        # First call - cache miss
        result1 = await get_label_autocomplete(
            self.db_conn,
            self.redis_client,
            self.test_brand.id,
            "auto",
            limit=10
        )
        
        assert len(result1) == 3

        # Second call with same query - should hit cache
        with patch.object(self.db_conn, 'get_db') as mock_db:
            result2 = await get_label_autocomplete(
                self.db_conn,
                self.redis_client,
                self.test_brand.id,
                "auto",
                limit=10
            )
            
            # Database should not be called (cache hit)
            mock_db.assert_not_called()
            assert result2 == result1

        # Different query - should miss cache
        result3 = await get_label_autocomplete(
            self.db_conn,
            self.redis_client,
            self.test_brand.id,
            "tech",
            limit=10
        )
        
        assert len(result3) == 1
        assert result3[0]["name"] == "Auto Tech"

    # ──────────────────────────────────────────────────────────────────────────────
    # LABEL ASSIGNMENT TESTS
    # ──────────────────────────────────────────────────────────────────────────────

    @pytest.mark.asyncio
    async def test_assign_labels_to_entry_success(self):
        """Test successful label assignment to influencer entry"""
        # Create test setup with influencer list and entry
        async with self.db_conn.get_db() as session:
            # Create influencer list
            test_list = BrandInfluencerList(
                brand_id=self.test_brand.id,
                name="Label Test List",
                created_by=self.test_user.id
            )
            session.add(test_list)
            await session.flush()
            
            # Create influencer entry
            test_entry = BrandInfluencerListEntry(
                list_id=test_list.id,
                influencer_id="test_influencer_123",
                influencer_name="Test Influencer",
                added_by=self.test_user.id,
                status=InfluencerStatus.shortlisted
            )
            session.add(test_entry)
            await session.flush()
            
            # Create global labels
            from app.schemas.label_schemas import CreateLabelRequest
            
            labels = []
            for name in ["Assignment Entertainment", "Assignment Music"]:
                label_data = self._create_test_label_data(name)
                request = CreateLabelRequest(**label_data)
                
                label = await create_global_label(
                    session,
                    self.redis_client,
                    request
                )
                labels.append(label)
            
            await session.commit()

            # Assign labels to entry
            label_ids = [label.id for label in labels]
            
            success, errors = await assign_labels_to_entry(
                session,
                self.redis_client,
                test_entry.id,
                self.test_user.id,
                label_ids
            )
            
            assert success
            assert len(errors) == 0
            
            await session.commit()

            # Verify assignments were created
            assignments_query = select(BrandInfluencerListEntryLabel).where(
                BrandInfluencerListEntryLabel.entry_id == test_entry.id
            )
            assignments_result = await session.execute(assignments_query)
            assignments = assignments_result.scalars().all()
            
            assert len(assignments) == 2
            assigned_label_ids = [str(a.global_label_id) for a in assignments]
            assert str(labels[0].id) in assigned_label_ids
            assert str(labels[1].id) in assigned_label_ids

    @pytest.mark.asyncio
    async def test_assign_labels_to_entry_invalid_labels(self):
        """Test label assignment with invalid label IDs"""
        # Create test setup
        async with self.db_conn.get_db() as session:
            test_list = BrandInfluencerList(
                brand_id=self.test_brand.id,
                name="Invalid Label Test List",
                created_by=self.test_user.id
            )
            session.add(test_list)
            await session.flush()
            
            test_entry = BrandInfluencerListEntry(
                list_id=test_list.id,
                influencer_id="test_influencer_456",
                influencer_name="Test Influencer 2",
                added_by=self.test_user.id,
                status=InfluencerStatus.shortlisted
            )
            session.add(test_entry)
            await session.commit()

            # Try to assign non-existent labels
            fake_label_ids = [uuid.uuid4(), uuid.uuid4()]
            
            success, errors = await assign_labels_to_entry(
                session,
                self.redis_client,
                test_entry.id,
                self.test_user.id,
                fake_label_ids
            )
            
            assert not success
            assert len(errors) == 2
            assert all("not found" in error for error in errors)

    @pytest.mark.asyncio
    async def test_assign_labels_unauthorized_user(self):
        """Test label assignment by unauthorized user"""
        # Create unauthorized user
        async with self.db_conn.get_db() as session:
            unauthorized_user = User(
                email="<EMAIL>",
                name="Unauthorized User",
                status="active"
            )
            session.add(unauthorized_user)
            await session.flush()
            
            # Create test setup
            test_list = BrandInfluencerList(
                brand_id=self.test_brand.id,
                name="Unauthorized Test List",
                created_by=self.test_user.id
            )
            session.add(test_list)
            await session.flush()
            
            test_entry = BrandInfluencerListEntry(
                list_id=test_list.id,
                influencer_id="test_influencer_789",
                influencer_name="Test Influencer 3",
                added_by=self.test_user.id,
                status=InfluencerStatus.shortlisted
            )
            session.add(test_entry)
            await session.commit()

            # Try to assign labels as unauthorized user
            with pytest.raises(ValueError, match="not a member"):
                await assign_labels_to_entry(
                    session,
                    self.redis_client,
                    test_entry.id,
                    unauthorized_user.id,
                    []
                )

    # ──────────────────────────────────────────────────────────────────────────────
    # LABEL USAGE STATISTICS TESTS
    # ──────────────────────────────────────────────────────────────────────────────

    @pytest.mark.asyncio
    async def test_label_usage_count_updates(self):
        """Test that label usage counts are properly updated"""
        # Create test setup
        async with self.db_conn.get_db() as session:
            # Create global label
            from app.schemas.label_schemas import CreateLabelRequest
            
            label_data = self._create_test_label_data("Usage Test Label")
            request = CreateLabelRequest(**label_data)
            
            test_label = await create_global_label(
                session,
                self.redis_client,
                request
            )
            
            # Create multiple entries to assign labels to
            test_list = BrandInfluencerList(
                brand_id=self.test_brand.id,
                name="Usage Test List",
                created_by=self.test_user.id
            )
            session.add(test_list)
            await session.flush()
            
            entries = []
            for i in range(3):
                entry = BrandInfluencerListEntry(
                    list_id=test_list.id,
                    influencer_id=f"usage_influencer_{i}",
                    influencer_name=f"Usage Influencer {i}",
                    added_by=self.test_user.id,
                    status=InfluencerStatus.shortlisted
                )
                session.add(entry)
                entries.append(entry)
            
            await session.flush()
            await session.commit()

            # Assign label to all entries
            for entry in entries:
                success, errors = await assign_labels_to_entry(
                    session,
                    self.redis_client,
                    entry.id,
                    self.test_user.id,
                    [test_label.id]
                )
                assert success
                await session.commit()

            # Verify usage counts
            # Refresh label to get updated usage count
            await session.refresh(test_label)
            assert test_label.usage_count == 3

            # Check brand label usage
            brand_label_query = select(BrandLabel).where(
                BrandLabel.brand_id == self.test_brand.id,
                BrandLabel.global_label_id == test_label.id
            )
            brand_label_result = await session.execute(brand_label_query)
            brand_label = brand_label_result.scalar_one_or_none()
            
            assert brand_label is not None
            assert brand_label.usage_count == 3

    # ──────────────────────────────────────────────────────────────────────────────
    # PERFORMANCE TESTS
    # ──────────────────────────────────────────────────────────────────────────────

    @pytest.mark.asyncio
    async def test_large_scale_label_operations(self):
        """Test performance with large numbers of labels"""
        # Create many global labels
        async with self.db_conn.get_db() as session:
            from app.schemas.label_schemas import CreateLabelRequest
            
            import time
            start_time = time.time()
            
            created_labels = []
            for i in range(100):
                label_data = self._create_test_label_data(f"PerformanceLabel_{i}")
                request = CreateLabelRequest(**label_data)
                
                label = await create_global_label(
                    session,
                    self.redis_client,
                    request
                )
                created_labels.append(label)
                
                # Commit every 20 to avoid large transactions
                if (i + 1) % 20 == 0:
                    await session.commit()
            
            await session.commit()
            
            creation_time = time.time() - start_time

            # Test retrieval performance
            start_time = time.time()
            
            all_labels = await get_global_labels(
                self.db_conn,
                self.redis_client
            )
            
            retrieval_time = time.time() - start_time

            # Test autocomplete performance
            start_time = time.time()
            
            autocomplete_results = await get_label_autocomplete(
                self.db_conn,
                self.redis_client,
                self.test_brand.id,
                "Performance",
                limit=50
            )
            
            autocomplete_time = time.time() - start_time

            # Verify results
            assert len(all_labels) == 100
            assert len(autocomplete_results) == 50  # Limited by limit parameter
            
            # Performance assertions (adjust thresholds as needed)
            assert creation_time < 30.0      # Should create 100 labels in under 30 seconds
            assert retrieval_time < 5.0      # Should retrieve in under 5 seconds
            assert autocomplete_time < 2.0   # Should autocomplete in under 2 seconds

    # ──────────────────────────────────────────────────────────────────────────────
    # CACHE INVALIDATION TESTS
    # ──────────────────────────────────────────────────────────────────────────────

    @pytest.mark.asyncio
    async def test_cache_invalidation_on_label_changes(self):
        """Test cache invalidation when labels are modified"""
        # Create initial labels
        async with self.db_conn.get_db() as session:
            from app.schemas.label_schemas import CreateLabelRequest
            
            label_data = self._create_test_label_data("Cache Test Label")
            request = CreateLabelRequest(**label_data)
            
            created_label = await create_global_label(
                session,
                self.redis_client,
                request
            )
            await session.commit()

        # Get labels to populate cache
        result1 = await get_global_labels(
            self.db_conn,
            self.redis_client
        )
        assert len(result1) == 1
        assert result1[0]["name"] == "Cache Test Label"

        # Update label (should invalidate cache)
        async with self.db_conn.get_db() as session:
            from app.schemas.label_schemas import UpdateLabelRequest
            
            update_request = UpdateLabelRequest(name="Updated Cache Test Label")
            
            await update_global_label(
                session,
                self.redis_client,
                created_label.id,
                update_request
            )
            await session.commit()

        # Get labels again - should reflect changes
        result2 = await get_global_labels(
            self.db_conn,
            self.redis_client
        )
        
        assert len(result2) == 1
        assert result2[0]["name"] == "Updated Cache Test Label"

    # ──────────────────────────────────────────────────────────────────────────────
    # CONCURRENT OPERATIONS TESTS
    # ──────────────────────────────────────────────────────────────────────────────

    @pytest.mark.asyncio
    async def test_concurrent_label_creation(self):
        """Test concurrent label creation scenarios"""
        async def create_label_task(task_id):
            async with self.db_conn.get_db() as session:
                from app.schemas.label_schemas import CreateLabelRequest
                
                label_data = self._create_test_label_data(f"ConcurrentLabel_{task_id}")
                request = CreateLabelRequest(**label_data)
                
                try:
                    label = await create_global_label(
                        session,
                        self.redis_client,
                        request
                    )
                    await session.commit()
                    return True
                except Exception as e:
                    print(f"Task {task_id} failed: {e}")
                    return False

        # Run concurrent tasks
        tasks = [create_label_task(i) for i in range(10)]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Count successful creations
        successful_creations = sum(1 for r in results if r is True)
        
        # Verify final state
        all_labels = await get_global_labels(
            self.db_conn,
            self.redis_client
        )
        
        concurrent_labels = [
            label for label in all_labels 
            if label["name"].startswith("ConcurrentLabel_")
        ]
        
        assert len(concurrent_labels) == successful_creations
        assert successful_creations >= 8  # Allow for some potential race conditions

    @pytest.mark.asyncio
    async def test_concurrent_label_assignment(self):
        """Test concurrent label assignments to same entry"""
        # Create test setup
        async with self.db_conn.get_db() as session:
            # Create labels
            from app.schemas.label_schemas import CreateLabelRequest
            
            labels = []
            for i in range(5):
                label_data = self._create_test_label_data(f"ConcurrentAssign_{i}")
                request = CreateLabelRequest(**label_data)
                
                label = await create_global_label(
                    session,
                    self.redis_client,
                    request
                )
                labels.append(label)
            
            # Create test list and entry
            test_list = BrandInfluencerList(
                brand_id=self.test_brand.id,
                name="Concurrent Assignment List",
                created_by=self.test_user.id
            )
            session.add(test_list)
            await session.flush()
            
            test_entry = BrandInfluencerListEntry(
                list_id=test_list.id,
                influencer_id="concurrent_test_influencer",
                influencer_name="Concurrent Test Influencer",
                added_by=self.test_user.id,
                status=InfluencerStatus.shortlisted
            )
            session.add(test_entry)
            await session.commit()

        # Define concurrent assignment task
        async def assign_label_task(label_id):
            async with self.db_conn.get_db() as session:
                try:
                    success, errors = await assign_labels_to_entry(
                        session,
                        self.redis_client,
                        test_entry.id,
                        self.test_user.id,
                        [label_id]
                    )
                    await session.commit()
                    return success and len(errors) == 0
                except Exception as e:
                    print(f"Assignment failed for label {label_id}: {e}")
                    return False

        # Run concurrent assignments
        tasks = [assign_label_task(label.id) for label in labels]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Count successful assignments
        successful_assignments = sum(1 for r in results if r is True)
        
        # Verify final state
        async with self.db_conn.get_db() as session:
            assignments_query = select(BrandInfluencerListEntryLabel).where(
                BrandInfluencerListEntryLabel.entry_id == test_entry.id
            )
            assignments_result = await session.execute(assignments_query)
            assignments = assignments_result.scalars().all()
            
            assert len(assignments) == successful_assignments
            assert successful_assignments >= 3  # Allow for some potential race conditions


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v", "--tb=short"])
