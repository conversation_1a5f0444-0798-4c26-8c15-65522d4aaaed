"""
Fix script for PhylloAPIService to skip authentication for testing.
"""
import asyncio
from app.services.phyllo_service import PhylloAPIService
from app.core.config import APP_CONFIG

async def main():
    """Test the updated _get_access_token method."""
    print("Creating PhylloAPIService instance...")
    service = PhylloAPIService()
    
    print("\nAPI Configuration:")
    print(f"URL: {service.base_url}")
    print(f"Mock APIs enabled: {service.use_mock_apis}")
    print(f"Mock Data enabled: {service.mock_data_enabled}")
    print(f"SSL verification disabled: {service.disable_ssl_verification}")
    
    print("\nTesting _get_access_token...")
    try:
        token = await service._get_access_token()
        print(f"Successfully got token: {token[:10]}...")
        
        print("\nInitializing _make_api_request...")
        await service._make_api_request("/test-endpoint")
        print("API request successful")
        
    except Exception as e:
        print(f"ERROR: {type(e).__name__}: {str(e)}")
    
    print("\nDone!")

if __name__ == "__main__":
    asyncio.run(main())
