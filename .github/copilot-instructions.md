## Project Instructions for CreatorVerse Microservice

### Core Requirements
1.  **Scope**: Microservice handles **user-related operations only** for CreatorVerse
2.  **Python Environment**:
    - Python 3.11
    - UV package manager
3.  **Dependencies**:
    - Use custom packages for:
        - Logger (async_logger from `app.utilities.async_logger`)
        - DB operations (SQLAlchemy) (database from `app.utilities.database`)
        - Redis (redis from `app.utilities.redis_code`)
4.  **Migrations**: No migration tools (Alembic, etc.)
5.  **Typing**: All code must pass mypy type checking
6.  **Static Analysis**: Use Ruff exclusively
7.  **Redis Key Pattern**: `CreatorVerse:feature:key`
8.  **Edit Mode**:
    - Request full filename
    - Provide only required changes
9. **Redis Client**:
    ```
    from app.utilities.redis_code import get_locobuzz_redis
    redis_client = get_locobuzz_redis(decoded_responses=True)
    ```
10. **Agent Mode**:
    - Avoid indentation/syntax errors
    - Use `python -m py_compile` for error resolution


### Business Logic

11. **Code Quality**:
    - Write reusable functions
    - Use proper naming conventions

### Implementation Rules
12. **Configuration**:
    - Use `APP_CONFIG` from `app/core/config.py` (never `.env`)

13. **Cache Pattern**: Prefer cache-aside pattern
14. **Function Creation**:
    - Check for existing implementations
    - Apply cache-aside pattern when missing
15. **Parameter Initialization**:
    ```
    redis_client = get_locobuzz_redis()
    db_conn = get_database()
    ```
16. **Session Management**:
    - Avoid detached objects in transactions
    - Ensure session boundaries are respected

### Technical Standards
17. **Imports**: Use top-level imports only
18. **Logging**:
    - Add logging to API endpoints
    - Use async logger's context trace ID
19. **Trace IDs**: Use context variables (not request headers)
20. **Redis Keys**:
    - Define keys in `redis_keys.py`
    - Reference defined keys

21. **Redis SETEX Pattern**:
    ```
    await redis_client.setex(key, ttl, json.dumps(payload))
    ```
22. **Multi-Key Deletion**:
    ```
    await redis_client.delete_keys(
        RedisKeys.key1(),
        RedisKeys.key2()
    )
    ```
23. **MCP Tools**: Use available MCP tools where applicable
