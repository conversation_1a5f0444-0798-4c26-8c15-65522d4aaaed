#!/usr/bin/env python3
"""
Test script for Filter Catalog API endpoints
"""

import asyncio
import json
from app.services.filter_catalog_service import filter_catalog_service
from app.models.filter_models import PlatformEnum, OptionForTypeEnum


async def test_filter_catalog_service():
    """Test the filter catalog service directly"""
    print("🧪 Testing Filter Catalog Service...")
    
    try:
        # Test Instagram creator filters
        print("\n📸 Testing Instagram Creator Filters:")
        instagram_creator_filters = await filter_catalog_service.get_filters_for_frontend(
            channel=PlatformEnum.instagram,
            option_for=OptionForTypeEnum.creator,
            use_cache=False
        )
        
        print(f"✅ Found {len(instagram_creator_filters)} filter groups")
        for group in instagram_creator_filters:
            print(f"  📁 {group['optionName']}: {len(group['filters'])} filters")
            for filter_def in group['filters'][:2]:  # Show first 2 filters
                print(f"    🔧 {filter_def['name']} ({filter_def['type']})")
        
        # Test YouTube creator filters
        print("\n📺 Testing YouTube Creator Filters:")
        youtube_creator_filters = await filter_catalog_service.get_filters_for_frontend(
            channel=PlatformEnum.youtube,
            option_for=OptionForTypeEnum.creator,
            use_cache=False
        )
        
        print(f"✅ Found {len(youtube_creator_filters)} filter groups")
        for group in youtube_creator_filters:
            print(f"  📁 {group['optionName']}: {len(group['filters'])} filters")
        
        # Test audience filters
        print("\n👥 Testing Instagram Audience Filters:")
        instagram_audience_filters = await filter_catalog_service.get_filters_for_frontend(
            channel=PlatformEnum.instagram,
            option_for=OptionForTypeEnum.audience,
            use_cache=False
        )
        
        print(f"✅ Found {len(instagram_audience_filters)} filter groups")
        for group in instagram_audience_filters:
            print(f"  📁 {group['optionName']}: {len(group['filters'])} filters")
        
        # Test specific filter structure
        print("\n🔍 Testing Filter Structure:")
        if instagram_creator_filters:
            sample_group = instagram_creator_filters[0]
            print(f"Sample Group: {sample_group['optionName']}")
            print(f"  - optionFor: {sample_group['optionFor']}")
            print(f"  - channel: {sample_group['channel']}")
            print(f"  - filters count: {len(sample_group['filters'])}")
            
            if sample_group['filters']:
                sample_filter = sample_group['filters'][0]
                print(f"  Sample Filter: {sample_filter['name']}")
                print(f"    - type: {sample_filter['type']}")
                print(f"    - icon: {sample_filter.get('icon')}")
                print(f"    - minmax: {sample_filter.get('minmax')}")
                print(f"    - enterValue: {sample_filter.get('enterValue')}")
                print(f"    - options count: {len(sample_filter.get('options', []))}")
        
        # Test location hierarchy
        print("\n📍 Testing Location Hierarchy:")
        location_filters = [f for group in instagram_creator_filters for f in group['filters'] if f['name'].lower() == 'location']
        if location_filters:
            location_filter = location_filters[0]
            print(f"Location filter type: {location_filter['type']}")
            print(f"Location options count: {len(location_filter.get('options', []))}")
            if location_filter.get('options'):
                for option in location_filter['options'][:2]:  # Show first 2 options
                    print(f"  📍 {option.get('subOptionName', 'N/A')}: {len(option.get('subOptions', []))} cities")
        
        # Test statistics
        print("\n📊 Testing Statistics:")
        stats = await filter_catalog_service.get_filter_statistics()
        print(f"Total groups: {stats['total_groups']}")
        print(f"Total definitions: {stats['total_definitions']}")
        print(f"By platform: {stats['by_platform']}")
        print(f"By option_for: {stats['by_option_for']}")
        
        print("\n✅ All tests completed successfully!")
        
        # Save sample output for verification
        sample_output = {
            "instagram_creator": instagram_creator_filters,
            "youtube_creator": youtube_creator_filters,
            "instagram_audience": instagram_audience_filters,
            "statistics": stats
        }
        
        with open("filter_catalog_test_output.json", "w") as f:
            json.dump(sample_output, f, indent=2, default=str)
        
        print("📄 Sample output saved to filter_catalog_test_output.json")
        
    except Exception as e:
        print(f"❌ Error during testing: {str(e)}")
        import traceback
        traceback.print_exc()


async def test_frontend_format_compliance():
    """Test compliance with frontend format requirements"""
    print("\n🎯 Testing Frontend Format Compliance...")
    
    try:
        # Get Instagram creator filters
        filters = await filter_catalog_service.get_filters_for_frontend(
            channel=PlatformEnum.instagram,
            option_for=OptionForTypeEnum.creator,
            use_cache=False
        )
        
        # Validate structure
        required_group_fields = ["optionName", "optionFor", "channel", "filters"]
        required_filter_fields = ["name", "type", "icon", "minmax", "enterValue"]
        
        compliance_issues = []
        
        for group in filters:
            # Check group structure
            for field in required_group_fields:
                if field not in group:
                    compliance_issues.append(f"Missing group field: {field}")
            
            # Check filter structure
            for filter_def in group.get("filters", []):
                for field in required_filter_fields:
                    if field not in filter_def:
                        compliance_issues.append(f"Missing filter field: {field} in {filter_def.get('name', 'unknown')}")
                
                # Check filter type format (should use hyphens)
                filter_type = filter_def.get("type", "")
                if "_" in filter_type:
                    compliance_issues.append(f"Filter type uses underscore instead of hyphen: {filter_type}")
                
                # Check multilevel structure
                if filter_type == "multilevel-checkbox":
                    options = filter_def.get("options", [])
                    for option in options:
                        required_multilevel_fields = ["subOptionName", "subOptionType", "subOptions"]
                        for field in required_multilevel_fields:
                            if field not in option:
                                compliance_issues.append(f"Missing multilevel field: {field}")
        
        if compliance_issues:
            print("❌ Frontend format compliance issues found:")
            for issue in compliance_issues:
                print(f"  - {issue}")
        else:
            print("✅ Frontend format compliance: PASSED")
        
        return len(compliance_issues) == 0
        
    except Exception as e:
        print(f"❌ Error during compliance testing: {str(e)}")
        return False


async def main():
    """Main test function"""
    print("🚀 Starting Filter Catalog API Tests")
    print("=" * 50)
    
    # Test service functionality
    await test_filter_catalog_service()
    
    # Test frontend format compliance
    compliance_passed = await test_frontend_format_compliance()
    
    print("\n" + "=" * 50)
    if compliance_passed:
        print("🎉 All tests PASSED! Filter catalog is ready for frontend integration.")
    else:
        print("⚠️  Some tests FAILED. Please review the issues above.")


if __name__ == "__main__":
    asyncio.run(main())
