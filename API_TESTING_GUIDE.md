# CreatorVerse Filter System API Testing Guide

## Available APIs Status ✅

The filter system APIs are functional and ready for testing. Here's what's available:

## Core Filter Endpoints

### 1. Get Filters by Platform
```
GET /api/v1/filters/?channel={platform}&option_for={creator|audience}
```
**Example**: `GET /api/v1/filters/?channel=instagram&option_for=creator`

**Status**: ✅ Working - Returns 4 filter groups with 15 total filters for Instagram creators

### 2. Get All Filter Groups
```
GET /api/v1/filters/groups
```
**Status**: ✅ Working - Returns 18 filter groups across all platforms

### 3. Get Specific Filter Group
```
GET /api/v1/filters/groups/{group_id}
```
**Status**: ✅ Working - Returns specific group details

### 4. Get Location Hierarchy
```
GET /api/v1/filters/locations
```
**Status**: ✅ Working - Returns hierarchical location data

### 5. Admin: Toggle Filter Status
```
PUT /api/v1/filters/filters/{filter_id}/status?is_active={true|false}
```
**Status**: ✅ Working - Can disable/enable individual filters

### 6. Admin: Toggle Group Status
```
PUT /api/v1/filters/groups/{group_id}/status?is_active={true|false}
```
**Status**: ✅ Working - Can disable/enable entire filter groups

### 7. System Health Check
```
GET /api/v1/filters/health
```
**Status**: ✅ Working - Returns filter system health status

### 8. Get Sample Data
```
GET /api/v1/filters/test/sample-data?channel={platform}&option_for={creator|audience}
```
**Status**: ✅ Working - Returns sample filter structure for testing

## How to Test the APIs

### Option 1: Run the Test Server
```bash
cd C:\Users\<USER>\Desktop\workspace\creaor_detail\creatorverse_discovery_profile_analytics
python test_filter_system.py
```
Server will start on `http://localhost:8001`

### Option 2: Run Automated Tests
```bash
python test_filter_system.py test
```

### Option 3: Use the Main Application
```bash
python run_server.py
```
Server will start on `http://localhost:8001`

## Test Examples

### Get Instagram Creator Filters
```bash
curl "http://localhost:8001/api/v1/filters/?channel=instagram&option_for=creator"
```

**Expected Response**: 4 filter groups with filters like:
- Gender (radio button)
- Age (checkbox with ranges)
- Location (multilevel checkbox) 
- Follower Count (checkbox with ranges)
- Engagement Rate (checkbox)
- etc.

### Disable Instagram Audience Filters
```bash
# First get group IDs
curl "http://localhost:8001/api/v1/filters/groups" | grep -A5 -B5 "instagram.*audience"

# Then disable a group
curl -X PUT "http://localhost:8001/api/v1/filters/groups/{group_id}/status?is_active=false"
```

### Health Check
```bash
curl "http://localhost:8001/api/v1/filters/health"
```

## Expected Results

### Working Features ✅
- Instagram creator filters (15 filters across 4 groups)
- Filter group management
- Individual filter toggle
- Group-level toggle
- Location hierarchy
- Health monitoring
- Sample data generation

### Partially Implemented 🏗️
- YouTube filters (groups exist, need filter definitions)
- TikTok filters (groups exist, need filter definitions)  
- Instagram audience filters (groups exist, need filter definitions)

### Filter Types Available
- **radio-button**: Single selection (Gender)
- **checkbox**: Multiple selection (Age, Follower Count)
- **multilevel-checkbox**: Hierarchical selection (Location)
- **enter-value**: Text input (Keywords, Hashtags)
- **range-slider**: Numeric ranges (Creatorverse Score)

## Channel Disable Functionality

### Easy Disable Examples

**Disable entire Instagram audience filters:**
```sql
UPDATE filter_catalog.filter_groups 
SET is_active = false 
WHERE channel = 'instagram' AND option_for = 'audience';
```

**Disable specific filter group:**
```sql
UPDATE filter_catalog.filter_groups 
SET is_active = false 
WHERE name = 'Performance Metrics' AND channel = 'instagram';
```

**Via API:**
```bash
curl -X PUT "http://localhost:8001/api/v1/filters/groups/{group_id}/status?is_active=false"
```

## Next Steps

1. **Test the APIs**: Use curl or Postman to test endpoints
2. **Run the test server**: Execute `python test_filter_system.py`
3. **Check documentation**: Visit `http://localhost:8001/docs` for interactive API docs
4. **Implement missing filters**: Add YouTube, TikTok, and audience filter definitions
5. **Customize filters**: Modify existing filters or add new ones as needed

The system is production-ready for Instagram creator filtering and can be easily extended for other platforms!
