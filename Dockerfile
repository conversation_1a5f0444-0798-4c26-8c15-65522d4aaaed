# CreatorVerse Filter Discovery System Dockerfile
FROM python:3.11-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH=/app

# Set work directory
WORKDIR /app

# Install system dependencies (minimal for filter discovery)
RUN apt-get update \
    && apt-get install -y --no-install-recommends \
        build-essential \
        curl \
        libpq-dev \
        pkg-config \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir --upgrade pip \
    && pip install --no-cache-dir -r requirements.txt

# Copy project
COPY . .

# Create logs directory
RUN mkdir -p logs

# Create non-root user
RUN groupadd -r filteruser && useradd -r -g filteruser filteruser
RUN chown -R filteruser:filteruser /app
USER filteruser

# Expose port
EXPOSE 8000

# Health check for filter discovery system
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/api/v1/filters/health || exit 1

# Command to run the filter discovery application
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
