Publishing System - Creatorverse Pattern Structure
📁 Individual Repository Structure (6 Repositories)
Following the exact patterns from the creatorverse user backend project structure.

🔧 1. Shared Utilities Repository
publishing-system-shared Repository

publishing-system-shared/
├── main.py                      # Package entry point
├── run_tests.py                 # Test runner script
├── appsettings.json             # Configuration file
├── app/
│   ├── __init__.py
│   ├── core/
│   │   ├── __init__.py
│   │   ├── config.py           # Configuration loader (AppConfig pattern)
│   │   ├── exceptions.py       # Common exception classes
│   │   └── enums.py           # Shared enums and constants
│   ├── core_helper/
│   │   ├── __init__.py
│   │   ├── async_logger.py     # Async logging utilities (creatorverse pattern)
│   │   ├── redis_client.py     # Redis connection management (creatorverse pattern)
│   │   └── database.py         # Database utilities (creatorverse pattern)
│   ├── schemas/
│   │   ├── __init__.py
│   │   ├── event_schemas.py    # Shared event schemas (Pydantic models)
│   │   └── base_schemas.py     # Base schema classes
│   ├── services/
│   │   ├── __init__.py
│   │   ├── kafka_service.py    # Kafka producer/consumer services
│   │   └── correlation_service.py # Event correlation service
│   ├── utilities/
│   │   ├── __init__.py
│   │   ├── kafka_utils.py      # Kafka utilities
│   │   ├── validation_functions.py # Shared validation functions
│   │   ├── response_handler.py # Standardized response handler
│   │   └── startup_tasks.py    # Startup task utilities
│   └── models/
│       ├── __init__.py
│       └── base_models.py      # Base SQLAlchemy models
├── tests/
│   ├── __init__.py
│   ├── conftest.py
│   ├── test_core/
│   ├── test_utilities/
│   └── test_services/
├── .gitignore
└── README.md
📝 2. Content Management Service Repository
publishing-system-content-management Repository

publishing-system-content-management/
├── main.py                      # FastAPI application entry point (lifespan management)
├── run_server.py               # Server runner script
├── run_tests.py                # Test runner script
├── appsettings.json            # Configuration file (creatorverse pattern)
├── app/
│   ├── __init__.py
│   ├── api/
│   │   ├── __init__.py
│   │   ├── dependencies.py     # FastAPI dependencies
│   │   ├── deps.py            # Additional dependencies
│   │   └── api_v1/            # API version 1 (creatorverse pattern)
│   │       ├── __init__.py
│   │       ├── api.py         # Main API router
│   │       └── endpoints/     # API endpoint handlers
│   │           ├── __init__.py
│   │           ├── content.py  # Content CRUD endpoints
│   │           ├── categories.py # Category endpoints
│   │           ├── tags.py     # Tag endpoints
│   │           ├── authors.py  # Author endpoints
│   │           └── common.py   # Common/utility endpoints
│   ├── core/
│   │   ├── __init__.py
│   │   ├── config.py          # Service configuration (AppConfig pattern)
│   │   ├── exceptions.py      # Service-specific exceptions
│   │   ├── enums.py          # Service-specific enums
│   │   ├── security.py       # Security utilities (future auth)
│   │   └── redis_keys.py     # Redis key patterns
│   ├── core_helper/
│   │   ├── __init__.py
│   │   ├── async_logger.py   # Async logging (from shared utilities)
│   │   ├── database.py       # Database connection management
│   │   └── redis_client.py   # Redis connection management
│   ├── middleware/
│   │   ├── __init__.py
│   │   └── correlation_middleware.py # Correlation ID middleware
│   ├── models/
│   │   ├── __init__.py
│   │   └── content_models.py # SQLAlchemy models (content, category, tag, author)
│   ├── schemas/
│   │   ├── __init__.py
│   │   ├── content_schemas.py  # Content Pydantic models
│   │   ├── category_schemas.py # Category Pydantic models
│   │   ├── tag_schemas.py     # Tag Pydantic models
│   │   └── author_schemas.py  # Author Pydantic models
│   ├── services/
│   │   ├── __init__.py
│   │   ├── content_service.py  # Content business logic
│   │   ├── category_service.py # Category business logic
│   │   ├── tag_service.py     # Tag business logic
│   │   ├── author_service.py  # Author business logic
│   │   ├── search_service.py  # Content search functionality
│   │   ├── cache_service.py   # Redis caching service
│   │   └── event_service.py   # Kafka event publishing service
│   ├── utilities/
│   │   ├── __init__.py
│   │   ├── content_utils.py   # Content manipulation utilities
│   │   ├── validation_functions.py # Content validation
│   │   ├── response_handler.py # Standardized response handler
│   │   ├── startup_tasks.py   # Service startup tasks
│   │   └── formatters.py      # Content formatting utilities
│   └── startup_tasks/
│       ├── __init__.py
│       └── content_startup.py # Content-specific startup tasks
├── tests/
│   ├── __init__.py
│   ├── conftest.py
│   ├── test_api/
│   ├── test_services/
│   ├── test_models/
│   └── test_utilities/
├── .gitignore
└── README.md
📤 3. Publishing Service Repository
publishing-system-publishing-service Repository

publishing-system-publishing-service/
├── main.py
├── run_server.py
├── run_tests.py
├── appsettings.json
├── app/
│   ├── __init__.py
│   ├── api/
│   │   ├── __init__.py
│   │   ├── dependencies.py
│   │   ├── deps.py
│   │   └── api_v1/
│   │       ├── __init__.py
│   │       ├── api.py
│   │       └── endpoints/
│   │           ├── __init__.py
│   │           ├── publish.py      # Publishing endpoints
│   │           ├── schedule.py     # Scheduling endpoints
│   │           ├── workflow.py     # Workflow management
│   │           ├── channels.py     # Publishing channels
│   │           ├── status.py       # Publishing status tracking
│   │           └── common.py       # Common utilities
│   ├── core/
│   │   ├── __init__.py
│   │   ├── config.py
│   │   ├── exceptions.py
│   │   ├── enums.py              # Publishing status, workflow states
│   │   ├── security.py
│   │   └── redis_keys.py
│   ├── core_helper/
│   │   ├── __init__.py
│   │   ├── async_logger.py
│   │   ├── database.py
│   │   └── redis_client.py
│   ├── middleware/
│   │   ├── __init__.py
│   │   └── workflow_middleware.py  # Workflow tracking middleware
│   ├── models/
│   │   ├── __init__.py
│   │   └── publishing_models.py    # Publication, workflow, schedule, channel models
│   ├── schemas/
│   │   ├── __init__.py
│   │   ├── publication_schemas.py
│   │   ├── workflow_schemas.py
│   │   ├── schedule_schemas.py
│   │   └── channel_schemas.py
│   ├── services/
│   │   ├── __init__.py
│   │   ├── publishing_service.py   # Main publishing orchestration
│   │   ├── workflow_service.py     # Workflow management
│   │   ├── scheduler_service.py    # Publishing scheduling
│   │   ├── channel_service.py      # Publishing channel operations
│   │   ├── status_service.py       # Status tracking and reporting
│   │   └── event_service.py        # Kafka event publishing
│   ├── utilities/
│   │   ├── __init__.py
│   │   ├── workflow_utils.py       # Workflow manipulation utilities
│   │   ├── publishing_utils.py     # Publishing utilities
│   │   ├── channel_utils.py        # Channel management utilities
│   │   ├── validation_functions.py
│   │   ├── response_handler.py
│   │   └── startup_tasks.py
│   ├── workflows/                  # Workflow definitions (business logic)
│   │   ├── __init__.py
│   │   ├── base_workflow.py
│   │   ├── immediate_publish.py
│   │   ├── scheduled_publish.py
│   │   ├── approval_workflow.py
│   │   └── batch_publish.py
│   ├── channels/                   # Publishing channel implementations
│   │   ├── __init__.py
│   │   ├── base_channel.py
│   │   ├── web_channel.py
│   │   ├── social_channel.py
│   │   └── api_channel.py
│   └── startup_tasks/
│       ├── __init__.py
│       └── publishing_startup.py
├── tests/
├── .gitignore
└── README.md
⚡ 4. Event Processing Service Repository
publishing-system-event-processing Repository

publishing-system-event-processing/
├── main.py
├── run_server.py
├── run_tests.py
├── appsettings.json
├── app/
│   ├── __init__.py
│   ├── api/
│   │   ├── __init__.py
│   │   ├── dependencies.py
│   │   ├── deps.py
│   │   └── api_v1/
│   │       ├── __init__.py
│   │       ├── api.py
│   │       └── endpoints/
│   │           ├── __init__.py
│   │           ├── events.py       # Event monitoring endpoints
│   │           ├── tasks.py        # Task status endpoints
│   │           ├── correlation.py  # Event correlation tracking
│   │           └── common.py
│   ├── core/
│   │   ├── __init__.py
│   │   ├── config.py
│   │   ├── exceptions.py
│   │   ├── enums.py              # Event types, task status
│   │   ├── security.py
│   │   └── redis_keys.py
│   ├── core_helper/
│   │   ├── __init__.py
│   │   ├── async_logger.py
│   │   ├── database.py
│   │   └── redis_client.py
│   ├── middleware/
│   │   ├── __init__.py
│   │   └── event_middleware.py   # Event processing middleware
│   ├── models/
│   │   ├── __init__.py
│   │   └── event_models.py       # Event log, task status, correlation models
│   ├── schemas/
│   │   ├── __init__.py
│   │   ├── event_schemas.py
│   │   ├── task_schemas.py
│   │   └── correlation_schemas.py
│   ├── services/
│   │   ├── __init__.py
│   │   ├── event_processor_service.py # Core event processing logic
│   │   ├── task_executor_service.py   # Task execution (Kafka-based)
│   │   ├── correlation_service.py     # Event correlation tracking
│   │   ├── content_processing_service.py # Content-related processing
│   │   ├── publishing_processing_service.py # Publishing-related processing
│   │   ├── storage_processing_service.py # Storage-related processing
│   │   └── notification_processing_service.py # Notification processing
│   ├── consumers/                # Kafka consumers (primary functionality)
│   │   ├── __init__.py
│   │   ├── base_consumer.py
│   │   ├── content_consumer.py
│   │   ├── publishing_consumer.py
│   │   ├── storage_consumer.py
│   │   └── notification_consumer.py
│   ├── processors/               # Event processors (Kafka-based)
│   │   ├── __init__.py
│   │   ├── base_processor.py
│   │   ├── content_processor.py
│   │   ├── publishing_processor.py
│   │   ├── storage_processor.py
│   │   ├── media_processor.py
│   │   └── notification_processor.py
│   ├── tasks/                    # Async task implementations (Kafka-coordinated)
│   │   ├── __init__.py
│   │   ├── image_processing.py
│   │   ├── document_conversion.py
│   │   ├── video_processing.py
│   │   ├── cdn_upload.py
│   │   ├── external_api_calls.py
│   │   ├── data_validation.py
│   │   └── cleanup_tasks.py
│   ├── utilities/
│   │   ├── __init__.py
│   │   ├── event_utils.py        # Event manipulation utilities
│   │   ├── retry_utils.py        # Retry mechanisms (Kafka-based)
│   │   ├── dead_letter_utils.py  # Dead letter queue handling
│   │   ├── correlation_utils.py  # Event correlation utilities
│   │   ├── monitoring_utils.py   # Performance monitoring
│   │   ├── validation_functions.py
│   │   ├── response_handler.py
│   │   └── startup_tasks.py
│   └── startup_tasks/
│       ├── __init__.py
│       └── event_startup.py
├── tests/
├── .gitignore
└── README.md
🔔 5. Notification Service Repository
publishing-system-notification-service Repository

publishing-system-notification-service/
├── main.py
├── run_server.py
├── run_tests.py
├── appsettings.json
├── app/
│   ├── __init__.py
│   ├── api/
│   │   ├── __init__.py
│   │   ├── dependencies.py
│   │   ├── deps.py
│   │   └── api_v1/
│   │       ├── __init__.py
│   │       ├── api.py
│   │       └── endpoints/
│   │           ├── __init__.py
│   │           ├── notifications.py     # Notification management
│   │           ├── subscriptions.py     # User subscription management
│   │           ├── templates.py         # Template management
│   │           ├── webhooks.py          # Webhook management
│   │           ├── websocket.py         # WebSocket connections
│   │           └── common.py
│   ├── core/
│   │   ├── __init__.py
│   │   ├── config.py
│   │   ├── exceptions.py
│   │   ├── enums.py              # Notification types, delivery status
│   │   ├── security.py
│   │   └── redis_keys.py
│   ├── core_helper/
│   │   ├── __init__.py
│   │   ├── async_logger.py
│   │   ├── database.py
│   │   └── redis_client.py
│   ├── middleware/
│   │   ├── __init__.py
│   │   └── notification_middleware.py # Notification tracking middleware
│   ├── models/
│   │   ├── __init__.py
│   │   └── notification_models.py     # Notification, subscription, template models
│   ├── schemas/
│   │   ├── __init__.py
│   │   ├── notification_schemas.py
│   │   ├── subscription_schemas.py
│   │   ├── template_schemas.py
│   │   ├── webhook_schemas.py
│   │   └── delivery_schemas.py
│   ├── services/
│   │   ├── __init__.py
│   │   ├── notification_service.py     # Main notification orchestration
│   │   ├── email_service.py            # Email delivery service
│   │   ├── sms_service.py              # SMS delivery service
│   │   ├── websocket_service.py        # WebSocket real-time notifications
│   │   ├── webhook_service.py          # Webhook delivery service
│   │   ├── template_service.py         # Template rendering service
│   │   ├── subscription_service.py     # User subscription management
│   │   ├── delivery_service.py         # Delivery tracking and retry
│   │   └── event_service.py            # Kafka event handling
│   ├── providers/                      # External service providers
│   │   ├── __init__.py
│   │   ├── base_provider.py
│   │   ├── email_providers/
│   │   │   ├── __init__.py
│   │   │   ├── sendgrid_provider.py
│   │   │   ├── ses_provider.py
│   │   │   └── smtp_provider.py
│   │   ├── sms_providers/
│   │   │   ├── __init__.py
│   │   │   ├── twilio_provider.py
│   │   │   └── aws_sns_provider.py
│   │   └── webhook_providers/
│   │       ├── __init__.py
│   │       └── generic_webhook.py
│   ├── templates/                      # Notification templates
│   │   ├── __init__.py
│   │   ├── email/
│   │   │   ├── base.html
│   │   │   ├── content_published.html
│   │   │   ├── publishing_failed.html
│   │   │   ├── content_approved.html
│   │   │   └── system_alert.html
│   │   ├── sms/
│   │   │   ├── urgent_alert.txt
│   │   │   ├── content_published.txt
│   │   │   └── system_status.txt
│   │   └── websocket/
│   │       ├── real_time_update.json
│   │       └── status_change.json
│   ├── consumers/
│   │   ├── __init__.py
│   │   └── notification_consumer.py    # Kafka consumer for notification events
│   ├── websocket/
│   │   ├── __init__.py
│   │   ├── connection_manager.py       # WebSocket connection management
│   │   ├── handlers.py                 # WebSocket message handlers
│   │   └── middleware.py               # WebSocket middleware
│   ├── utilities/
│   │   ├── __init__.py
│   │   ├── notification_utils.py       # Notification utilities
│   │   ├── template_engine.py          # Template rendering utilities
│   │   ├── delivery_utils.py           # Delivery utilities
│   │   ├── validation_functions.py
│   │   ├── response_handler.py
│   │   └── startup_tasks.py
│   └── startup_tasks/
│       ├── __init__.py
│       └── notification_startup.py
├── tests/
├── .gitignore
└── README.md
📁 6. Content Storage Service Repository
publishing-system-content-storage Repository

publishing-system-content-storage/
├── main.py
├── run_server.py
├── run_tests.py
├── appsettings.json
├── app/
│   ├── __init__.py
│   ├── api/
│   │   ├── __init__.py
│   │   ├── dependencies.py
│   │   ├── deps.py
│   │   └── api_v1/
│   │       ├── __init__.py
│   │       ├── api.py
│   │       └── endpoints/
│   │           ├── __init__.py
│   │           ├── files.py          # File upload/download endpoints
│   │           ├── versions.py       # File version management
│   │           ├── storage.py        # Storage configuration
│   │           ├── cdn.py            # CDN management
│   │           ├── metadata.py       # File metadata endpoints
│   │           └── common.py
│   ├── core/
│   │   ├── __init__.py
│   │   ├── config.py
│   │   ├── exceptions.py
│   │   ├── enums.py              # Storage types, file status
│   │   ├── security.py
│   │   └── redis_keys.py
│   ├── core_helper/
│   │   ├── __init__.py
│   │   ├── async_logger.py
│   │   ├── database.py
│   │   └── redis_client.py
│   ├── middleware/
│   │   ├── __init__.py
│   │   └── storage_middleware.py  # File upload tracking middleware
│   ├── models/
│   │   ├── __init__.py
│   │   └── storage_models.py      # File, version, storage config models
│   ├── schemas/
│   │   ├── __init__.py
│   │   ├── file_schemas.py
│   │   ├── upload_schemas.py
│   │   ├── version_schemas.py
│   │   ├── storage_schemas.py
│   │   └── metadata_schemas.py
│   ├── services/
│   │   ├── __init__.py
│   │   ├── file_service.py           # Main file operations
│   │   ├── storage_service.py        # Storage backend operations
│   │   ├── cdn_service.py            # CDN operations and cache management
│   │   ├── version_service.py        # File versioning service
│   │   ├── metadata_service.py       # File metadata extraction
│   │   ├── validation_service.py     # File validation and security
│   │   └── event_service.py          # Kafka event publishing
│   ├── storage/                      # Storage provider implementations
│   │   ├── __init__.py
│   │   ├── base_storage.py
│   │   ├── local_storage.py
│   │   ├── s3_storage.py
│   │   ├── gcs_storage.py
│   │   ├── azure_storage.py
│   │   └── storage_factory.py
│   ├── processors/                   # File processors
│   │   ├── __init__.py
│   │   ├── base_processor.py
│   │   ├── image_processor.py
│   │   ├── video_processor.py
│   │   ├── document_processor.py
│   │   ├── audio_processor.py
│   │   └── archive_processor.py
│   ├── cdn/                          # CDN integration
│   │   ├── __init__.py
│   │   ├── base_cdn.py
│   │   ├── cloudflare_cdn.py
│   │   ├── aws_cloudfront.py
│   │   └── cache_manager.py
│   ├── validation/                   # File validation and security
│   │   ├── __init__.py
│   │   ├── file_validator.py
│   │   ├── security_scanner.py
│   │   ├── mime_type_detector.py
│   │   └── content_analyzer.py
│   ├── utilities/
│   │   ├── __init__.py
│   │   ├── file_utils.py            # File manipulation utilities
│   │   ├── hash_utils.py            # File hashing and checksum
│   │   ├── compression_utils.py     # File compression utilities
│   │   ├── thumbnail_generator.py   # Thumbnail generation utilities
│   │   ├── validation_functions.py
│   │   ├── response_handler.py
│   │   └── startup_tasks.py
│   └── startup_tasks/
│       ├── __init__.py
│       └── storage_startup.py
├── tests/
├── .gitignore
└── README.md
🔧 Configuration Files (Creatorverse Pattern)
appsettings.json Structure (Nested Configuration)

json
{
  "service_name": "Content Management Service",
  "environ": "development",
  "connection_string": "postgresql+asyncpg://user:password@localhost:5432/content_db",
  "security": {
    "SECRET_KEY": "your-secret-key-change-this-in-production",
    "ACCESS_TOKEN_EXPIRE_MINUTES": 30,
    "REFRESH_TOKEN_EXPIRE_DAYS": 30,
    "ALGORITHM": "HS256"
  },
  "kafka": {
    "BOOTSTRAP_SERVERS": "localhost:9092",
    "CONSUMER_GROUP": "content-management-group",
    "AUTO_OFFSET_RESET": "earliest",
    "TOPICS": {
      "CONTENT_CREATED": "content.created",
      "CONTENT_UPDATED": "content.updated",
      "CONTENT_DELETED": "content.deleted"
    }
  },
  "redis_host": "localhost",
  "log_enabled": "true",
  "log_level": "INFO",
  "log_type": "json",
  "is_async_logger": false,
  "enhanced_features": true,
  "include_trace_id": true,
  "use_queue_handler": true,
  "max_queue_size": 1000,
  "batch_size": 10,
  "flush_interval": 0.1,
  "bind_uvicorn": true,
  "cors": {
    "ALLOWED_ORIGINS": [
      "http://localhost:3000",
      "http://localhost:8080"
    ]
  },
  "external_services": {
    "publishing_service": {
      "base_url": "http://localhost:8002",
      "timeout": 30
    },
    "storage_service": {
      "base_url": "http://localhost:8005",
      "timeout": 30
    },
    "notification_service": {
      "base_url": "http://localhost:8004",
      "timeout": 10
    }
  }
}
🏗️ Core Configuration Pattern (AppConfig Class)
app/core/config.py (Creatorverse Pattern)

python
import json
from typing import Optional, Dict, Any

from app.core_helper.async_logger import FastAPILogger, LogLevel
from app.core_helper.database import AsyncDatabaseDB
from app.core_helper.redis_client import RedisClient


class AppConfig:
    """Application configuration class following creatorverse pattern"""

    def __init__(self, config_file: str = "appsettings.json"):
        # Load configuration from appsettings.json
        with open(config_file, 'r') as f:
            config_data: Dict[str, Any] = json.load(f)

        # Basic settings
        self.service_name = config_data.get("service_name", "Content Management Service")
        self.environ = config_data.get("environ", "development")

        # Database settings
        self.connection_string = config_data.get("connection_string", "")

        # Redis settings
        redis_host = config_data.get("redis_host", "localhost")
        self.redis_url = f"redis://{redis_host}:6379"

        # Kafka settings
        kafka = config_data.get("kafka", {})
        self.kafka_bootstrap_servers = kafka.get("BOOTSTRAP_SERVERS", "localhost:9092")
        self.kafka_consumer_group = kafka.get("CONSUMER_GROUP", "content-management-group")
        self.kafka_auto_offset_reset = kafka.get("AUTO_OFFSET_RESET", "earliest")
        self.kafka_topics = kafka.get("TOPICS", {})

        # Logging settings (creatorverse pattern)
        self.log_enabled = config_data.get("log_enabled", "true").lower() == "true"
        self.log_level = config_data.get("log_level", "INFO")
        self.log_type = config_data.get("log_type", "json")
        self.is_async_logger = config_data.get("is_async_logger", False)
        self.enhanced_features = config_data.get("enhanced_features", True)
        self.include_trace_id = config_data.get("include_trace_id", True)
        self.use_queue_handler = config_data.get("use_queue_handler", True)
        self.max_queue_size = config_data.get("max_queue_size", 1000)
        self.batch_size = config_data.get("batch_size", 10)
        self.flush_interval = config_data.get("flush_interval", 0.1)
        self.bind_uvicorn = config_data.get("bind_uvicorn", True)

        # Security settings
        security = config_data.get("security", {})
        self.secret_key = security.get("SECRET_KEY", "")
        self.algorithm = security.get("ALGORITHM", "HS256")
        self.access_token_expire_minutes = security.get("ACCESS_TOKEN_EXPIRE_MINUTES", 30)
        self.refresh_token_expire_days = security.get("REFRESH_TOKEN_EXPIRE_DAYS", 30)

        # CORS settings
        cors = config_data.get("cors", {})
        self.allowed_origins = cors.get("ALLOWED_ORIGINS", ["*"])

        # External services
        self.external_services = config_data.get("external_services", {})

        # Initialize logger
        self.logger: Optional[FastAPILogger] = None

    def initialize_logger(self) -> FastAPILogger:
        """Initialize the application logger (creatorverse pattern)"""
        if self.logger is None:
            log_level_map = {
                "DEBUG": LogLevel.DEBUG,
                "INFO": LogLevel.INFO,
                "WARNING": LogLevel.WARNING,
                "ERROR": LogLevel.ERROR,
                "CRITICAL": LogLevel.CRITICAL
            }

            self.logger = FastAPILogger(
                service_name=self.service_name,
                log_level=log_level_map.get(self.log_level, LogLevel.INFO),
                trace_id_prefix="CMS",
                environment=self.environ
            )
        return self.logger


# Create global app config instance (creatorverse pattern)
APP_CONFIG = AppConfig()

# Global instances (to be initialized in main.py)
_db_instance: Optional[AsyncDatabaseDB] = None
_redis_instance: Optional[RedisClient] = None


def get_database() -> AsyncDatabaseDB:
    """Get database connection singleton (creatorverse pattern)"""
    global _db_instance
    if _db_instance is None:
        logger = APP_CONFIG.logger or APP_CONFIG.initialize_logger()
        _db_instance = AsyncDatabaseDB(
            connection_string=APP_CONFIG.connection_string,
            pool_size=5,
            max_overflow=10,
            logger=logger
        )
    return _db_instance


def get_redis() -> RedisClient:
    """Get Redis connection singleton (creatorverse pattern)"""
    global _redis_instance
    if _redis_instance is None:
        logger = APP_CONFIG.logger or APP_CONFIG.initialize_logger()
        _redis_instance = RedisClient(
            redis_url=APP_CONFIG.redis_url,
            logger=logger
        )
    return _redis_instance
🚀 Main Application Pattern (FastAPI with Lifespan)
main.py (Creatorverse Pattern)

python
from collections.abc import AsyncGenerator
from contextlib import asynccontextmanager

from fastapi import FastAPI, HTTPException
from fastapi.exceptions import RequestValidationError
from fastapi.middleware.cors import CORSMiddleware

from app.api.api_v1.api import api_router
from app.core.config import APP_CONFIG, get_database, get_redis
from app.core.exceptions import (
    ContentManagementError,
    content_management_exception_handler,
    http_exception_handler,
    validation_exception_handler,
    general_exception_handler
)
from app.utilities.response_handler import StandardResponse


@asynccontextmanager
async def lifespan(app_fast: FastAPI) -> AsyncGenerator[None, None]:
    """Lifespan context manager for FastAPI application (creatorverse pattern)"""
    # Startup
    print(f"Application starting up...{app_fast}")

    # Initialize logger first
    logger = APP_CONFIG.initialize_logger()
    logger.info("Logger initialized successfully")

    try:
        # Reset singletons to ensure fresh initialization
        from app.core_helper.database import AsyncDatabaseDB
        from app.core_helper.redis_client import RedisClient
        AsyncDatabaseDB._instance = None
        RedisClient._instance = None

        # Get database instance and initialize it
        db_conn = get_database()
        await db_conn.initialize()
        logger.info("Database initialized successfully")

        # Get redis instance and initialize it  
        redis_client = get_redis()
        await redis_client.initialize()
        logger.info("Redis initialized successfully")
        
        # Initialize startup tasks
        try:
            from app.utilities.startup_tasks import initialize_all_startup_tasks_sync
            initialize_all_startup_tasks_sync()
            
            from app.utilities.startup_tasks import initialize_all_startup_tasks_async
            await initialize_all_startup_tasks_async()
            
            logger.info("Startup tasks completed successfully")
        except Exception as startup_error:
            logger.warning(f"Startup tasks failed: {startup_error}")

    except Exception as e:
        logger.error(f"Failed to initialize services: {e}")
        print(f"Failed to initialize services: {e}")

    yield

    # Shutdown
    logger.info("Application shutting down...")
    try:
        redis_client = get_redis()
        await redis_client.close()
        logger.info("Redis connection closed")
        
        db_conn = get_database()
        await db_conn.shutdown()
        logger.info("Database connection pool shutdown completed")
        
        logger.info("Shutdown completed successfully")
    except Exception as e:
        logger.error(f"Error during shutdown: {e}")
        print(f"Error during shutdown: {e}")


app = FastAPI(
    title="Content Management Service API",
    version="1.0.0",
    description="Publishing System Content Management API",
    lifespan=lifespan
)

# Register exception handlers for centralized error handling
app.add_exception_handler(ContentManagementError, content_management_exception_handler)
app.add_exception_handler(HTTPException, http_exception_handler)
app.add_exception_handler(RequestValidationError, validation_exception_handler)
app.add_exception_handler(Exception, general_exception_handler)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=APP_CONFIG.allowed_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*", "ngrok-skip-browser-warning"],
)

# Include API router
app.include_router(api_router, prefix="/v1")


@app.get("/")
async def root():
    """Root endpoint with standardized response format"""
    return StandardResponse.success(
        data={"environment": APP_CONFIG.environ},
        message="Welcome to Content Management API"
    )


@app.get("/health")
async def health_check():
    """Health check endpoint with standardized response format"""
    return StandardResponse.success(
        data={
            "status": "healthy",
            "service": "Content Management Service",
            "environment": APP_CONFIG.environ
        },
        message="Service is healthy"
    )
🔧 Core Helper Pattern (Creatorverse Structure)
app/core_helper/async_logger.py

python
# Async logging utilities following creatorverse pattern
# (Implementation from shared utilities)
app/core_helper/database.py

python
# Database connection management following creatorverse pattern
# (Implementation from shared utilities)
app/core_helper/redis_client.py

python
# Redis connection management following creatorverse pattern
# (Implementation from shared utilities)
🚀 Service Port Allocation
Service Port Configuration

publishing-system-content-management    → Port 8001
publishing-system-publishing-service    → Port 8002  
publishing-system-event-processing      → Port 8003
publishing-system-notification-service  → Port 8004
publishing-system-content-storage       → Port 8005
✅ Key Features Following Creatorverse Pattern
🔄 Application Structure
main.py - FastAPI app with lifespan management
run_server.py - Server runner script
run_tests.py - Test runner script
appsettings.json - Nested configuration structure
🏗️ App Directory Structure
api/api_v1/endpoints/ - Versioned API with individual endpoint files
core/ - Configuration, exceptions, enums, security
core_helper/ - async_logger.py, database.py, redis_client.py
middleware/ - Custom middleware components
models/ - SQLAlchemy models
schemas/ - Pydantic schemas
services/ - Business logic services
utilities/ - Utility functions
startup_tasks/ - Application startup tasks
⚙️ Configuration Management
AppConfig class - Configuration loader with nested JSON structure
Global singletons - Database and Redis connections
Logger initialization - FastAPI logger with trace IDs
Lifespan management - Proper startup/shutdown handling
🔧 Shared Utilities Integration
async_logger.py - From publishing-system-shared
redis_client.py - From publishing-system-shared
database.py - From publishing-system-shared
Event schemas - Consistent across all services
This structure exactly follows the creatorverse user backend patterns while maintaining our publishing system architecture with separate repositories, Kafka-only async processing, and pre-configured infrastructure.

