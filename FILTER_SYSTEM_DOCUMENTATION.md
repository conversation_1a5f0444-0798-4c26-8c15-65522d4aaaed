# CreatorVerse Filter System - Complete Documentation

## 🎯 Overview

The CreatorVerse Filter System is a comprehensive, scalable filtering solution for creator discovery and audience analytics. It supports multiple platforms (Instagram, YouTube, TikTok) with hierarchical filter organization, real-time caching, and extensive analytics capabilities.

## 📋 Table of Contents

1. [System Architecture](#system-architecture)
2. [Database Schema](#database-schema)
3. [API Endpoints](#api-endpoints)
4. [Management Tools](#management-tools)
5. [Testing Framework](#testing-framework)
6. [Adding New Channels](#adding-new-channels)
7. [Filter Management](#filter-management)
8. [Usage Examples](#usage-examples)
9. [Troubleshooting](#troubleshooting)

## 🏗️ System Architecture

### Core Components

```
┌─────────────────────────────────────────────────────────────┐
│                   CreatorVerse Filter System                │
├─────────────────────────────────────────────────────────────┤
│  Frontend UI  ←→  API Endpoints  ←→  Filter Service         │
│                        ↓                   ↓                │
│                   PostgreSQL         Redis Cache            │
│                        ↓                                    │
│                Location Hierarchy & Filter Definitions      │
│                        ↓                                    │
│              External APIs (Phyllo, Modash, etc.)          │
└─────────────────────────────────────────────────────────────┘
```

### Technology Stack

- **Backend**: Python 3.11+ with FastAPI
- **Database**: PostgreSQL with advanced JSONB support
- **Cache**: Redis for filter metadata and results
- **ORM**: SQLAlchemy 2.0 with async support
- **Validation**: Pydantic v2 schemas
- **Testing**: Pytest with async fixtures

## 🗄️ Database Schema

### Core Tables

#### `filter_catalog.filter_groups`
Organizes filters into logical categories (e.g., "Demographics", "Performance Metrics").

```sql
CREATE TABLE filter_catalog.filter_groups (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    option_for option_for_type NOT NULL DEFAULT 'creator',
    channel platform_type NOT NULL DEFAULT 'instagram',
    sort_order SMALLINT DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);
```

#### `filter_catalog.filter_definitions`
Individual filter configurations with UI and API mappings.

```sql
CREATE TABLE filter_catalog.filter_definitions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    group_id UUID NOT NULL REFERENCES filter_groups(id),
    name VARCHAR(100) NOT NULL,
    filter_type filter_type NOT NULL DEFAULT 'checkbox',
    icon VARCHAR(50),
    has_minmax BOOLEAN DEFAULT false,
    has_enter_value BOOLEAN DEFAULT false,
    has_search_box BOOLEAN DEFAULT false,
    placeholder VARCHAR(200),
    options JSONB DEFAULT '[]'::jsonb,
    db_field VARCHAR(100),
    api_field VARCHAR(100),
    sort_order SMALLINT DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);
```

#### `filter_catalog.location_hierarchy`
Hierarchical location data with tier-based classification.

```sql
CREATE TABLE filter_catalog.location_hierarchy (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    code VARCHAR(20),
    level INTEGER DEFAULT 0,
    parent_id UUID REFERENCES location_hierarchy(id),
    population INTEGER,
    tier VARCHAR(10),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);
```

#### `filter_catalog.saved_filter_sets`
User-created filter combinations for reuse and sharing.

```sql
CREATE TABLE filter_catalog.saved_filter_sets (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    channel platform_type NOT NULL DEFAULT 'instagram',
    option_for option_for_type NOT NULL DEFAULT 'creator',
    filter_values JSONB NOT NULL,
    user_id UUID,
    is_shared BOOLEAN DEFAULT false,
    share_code VARCHAR(50) UNIQUE,
    usage_count INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);
```

### Enums

```sql
-- Platform types
CREATE TYPE filter_catalog.platform_type AS ENUM (
    'instagram', 'youtube', 'tiktok'
);

-- Filter UI types
CREATE TYPE filter_catalog.filter_type AS ENUM (
    'radio-button', 'checkbox', 'multilevel-checkbox', 'enter-value'
);

-- Target types
CREATE TYPE filter_catalog.option_for_type AS ENUM (
    'creator', 'audience'
);
```

## 🌐 API Endpoints

### Base URL: `/api/v1/filters`

#### Get Filters
```http
GET /api/v1/filters?channel=instagram&option_for=creator&include_inactive=false
```

**Response:**
```json
[
  {
    "id": "uuid",
    "optionName": "Demography & Identity",
    "optionFor": "creator",
    "channel": "instagram",
    "sort_order": 1,
    "filters": [
      {
        "id": "uuid",
        "name": "Gender",
        "type": "radio_button",
        "icon": "gender-icon",
        "minmax": false,
        "enterValue": false,
        "searchBox": false,
        "placeholder": "Select Gender",
        "options": [
          {"label": "Male", "value": "male", "description": ""},
          {"label": "Female", "value": "female", "description": ""}
        ],
        "db_field": "gender",
        "api_field": "creator_gender",
        "sort_order": 1
      }
    ]
  }
]
```

#### Apply Filters
```http
POST /api/v1/filters/apply
Content-Type: application/json

{
  "channel": "instagram",
  "option_for": "creator",
  "filters": {
    "gender": "male",
    "age": ["20-35", "36-55"],
    "location": ["mumbai", "delhi"]
  },
  "page": 1,
  "page_size": 20,
  "sort_by": "follower_count",
  "sort_order": "desc"
}
```

#### Save Filter Set
```http
POST /api/v1/filters/saved-sets
Content-Type: application/json

{
  "name": "Tech Male Influencers",
  "channel": "instagram",
  "option_for": "creator",
  "filter_values": {
    "gender": "male",
    "category": ["technology"],
    "follower_count": ["10k-50k"]
  },
  "is_shared": true
}
```

#### Get System Metadata
```http
GET /api/v1/filters/metadata
```

**Response:**
```json
{
  "total_groups": 12,
  "total_filters": 45,
  "active_groups": 10,
  "active_filters": 42,
  "platform_breakdown": {
    "instagram": 6,
    "youtube": 4,
    "tiktok": 2
  },
  "last_updated": "2024-01-15T10:30:00Z"
}
```

## 🛠️ Management Tools

### Filter Manager CLI

The `filter_manager.py` script provides comprehensive management capabilities:

#### Add New Channel
```bash
python scripts/filter_manager.py add-channel --name snapchat --display "Snapchat"
```

#### Add Filter Group
```bash
python scripts/filter_manager.py add-filter-group \
  --name "Brand Affinity" \
  --channel instagram \
  --option-for creator \
  --sort-order 5
```

#### Add Filter Definition
```bash
python scripts/filter_manager.py add-filter \
  --group-id "uuid-here" \
  --name "Brand Mentions" \
  --type checkbox \
  --icon "brand-icon" \
  --options '[{"label": "Nike", "value": "nike", "description": "Nike brand"}]' \
  --db-field "brand_mentions" \
  --api-field "brand_affinities"
```

#### Channel Management
```bash
# List all channels
python scripts/filter_manager.py list-channels

# Disable a channel
python scripts/filter_manager.py disable-channel --channel tiktok

# Enable a channel
python scripts/filter_manager.py enable-channel --channel tiktok
```

#### System Health Check
```bash
python scripts/filter_manager.py health-check
```

#### Seed Sample Data
```bash
python scripts/filter_manager.py seed-data
```

### Filter Demo Script

The `filter_demo.py` script provides comprehensive testing and demonstration:

```bash
# Run complete demo
python scripts/filter_demo.py --demo

# Quick validation tests
python scripts/filter_demo.py --quick-test

# Setup sample data and test
python scripts/filter_demo.py --setup

# Run everything
python scripts/filter_demo.py --all
```

## 🧪 Testing Framework

### Test Categories

#### Model Tests (`TestFilterModels`)
- Filter group creation and validation
- Filter definition relationships
- Location hierarchy functionality
- Saved filter set operations

#### Service Tests (`TestFilterService`)
- Filter retrieval with caching
- Location hierarchy queries
- Saved filter set CRUD operations
- Filter status management

#### Integration Tests (`TestFilterIntegration`)
- Complete workflows from creation to usage
- Cross-component interactions
- Real-world usage scenarios

#### Performance Tests (`TestFilterPerformance`)
- Large dataset handling
- Query optimization validation
- Stress testing with high load

### Running Tests

```bash
# Run all filter tests
pytest tests/test_filter_system.py -v

# Run specific test categories
pytest tests/test_filter_system.py::TestFilterModels -v
pytest tests/test_filter_system.py::TestFilterService -v

# Run with coverage
pytest tests/test_filter_system.py --cov=app.models.filter_models --cov=app.services.filter_service

# Run performance tests
pytest tests/test_filter_system.py -m slow
```

## ➕ Adding New Channels

### Step 1: Database Enum Update
```sql
ALTER TYPE filter_catalog.platform_type ADD VALUE 'snapchat';
```

### Step 2: Python Enum Update
Update `PlatformEnum` in:
- `app/models/filter_models.py`
- `app/schemas/filter_schemas.py`

```python
class PlatformEnum(str, Enum):
    instagram = "instagram"
    youtube = "youtube"
    tiktok = "tiktok"
    snapchat = "snapchat"  # Add new channel
```

### Step 3: Create Filter Groups
```bash
python scripts/filter_manager.py add-filter-group \
  --name "Demography & Identity" \
  --channel snapchat \
  --option-for creator

python scripts/filter_manager.py add-filter-group \
  --name "Performance Metrics" \
  --channel snapchat \
  --option-for creator
```

### Step 4: Add Platform-Specific Filters
```bash
python scripts/filter_manager.py add-filter \
  --group-id "group-uuid" \
  --name "Snap Score" \
  --type checkbox \
  --options '[{"label": "High", "value": "high"}, {"label": "Medium", "value": "medium"}]'
```

### Step 5: Restart Application
```bash
# Restart to load new enum values
docker-compose restart app
```

## 🎛️ Filter Management

### Enable/Disable Filters

#### Individual Filter
```bash
# Via API
PUT /api/v1/filters/filters/{filter_id}/status
{"is_active": false}

# Via service
await filter_service.toggle_filter_status(filter_id, False)
```

#### Filter Group
```bash
# Via API
PUT /api/v1/filters/groups/{group_id}/status
{"is_active": false}

# Via service
await filter_service.toggle_group_status(group_id, False)
```

#### Entire Channel
```bash
python scripts/filter_manager.py disable-channel --channel tiktok
```

### Filter Status Hierarchy

When you disable:
- **Channel**: All groups and filters for that channel are disabled
- **Group**: All filters in that group are disabled
- **Filter**: Only that specific filter is disabled

### Cache Management

The system automatically clears relevant cache entries when filters are modified:

```python
async def _clear_filter_cache(self) -> None:
    """Clear filter-related cache entries."""
    patterns = [
        "filter_groups:*",
        "location_hierarchy:*", 
        "filter_metadata"
    ]
    
    for pattern in patterns:
        keys = await self.redis.keys(pattern)
        if keys:
            await self.redis.delete(*keys)
```

## 📝 Usage Examples

### Frontend Integration

#### Get Filter Configuration
```javascript
// Fetch filter configuration
const response = await fetch('/api/v1/filters?channel=instagram&option_for=creator');
const filterGroups = await response.json();

// Render filter UI
filterGroups.forEach(group => {
    group.filters.forEach(filter => {
        if (filter.type === 'radio_button') {
            renderRadioFilter(filter);
        } else if (filter.type === 'checkbox') {
            renderCheckboxFilter(filter);
        }
    });
});
```

#### Apply Filters
```javascript
// Collect filter values from UI
const filterValues = {
    gender: getSelectedRadioValue('gender'),
    age: getSelectedCheckboxValues('age'),
    location: getSelectedLocationValues('location')
};

// Apply filters
const response = await fetch('/api/v1/filters/apply', {
    method: 'POST',
    headers: {'Content-Type': 'application/json'},
    body: JSON.stringify({
        channel: 'instagram',
        option_for: 'creator',
        filters: filterValues,
        page: 1,
        page_size: 20
    })
});

const results = await response.json();
```

### Backend Service Usage

#### Retrieve Filters
```python
from app.services.filter_service import filter_service

# Get Instagram creator filters
filters = await filter_service.get_filter_groups(
    channel=PlatformEnum.instagram,
    option_for=OptionForTypeEnum.creator
)

# Process filters
for group in filters:
    print(f"Group: {group.optionName}")
    for filter_def in group.filters:
        print(f"  Filter: {filter_def.name} ({filter_def.filter_type})")
```

#### Save Filter Set
```python
from app.schemas.filter_schemas import SaveFilterSetRequest

# Create save request
request = SaveFilterSetRequest(
    name="My Custom Filters",
    channel=PlatformEnum.instagram,
    option_for=OptionForTypeEnum.creator,
    filter_values={
        "gender": "male",
        "age": ["20-35"],
        "location": ["tier1_cities"]
    },
    is_shared=True
)

# Save filter set
saved_set = await filter_service.save_filter_set(request, user_id)
```

## 🔧 Troubleshooting

### Common Issues

#### 1. Filter Not Appearing in UI
**Symptoms**: Filter defined but not showing in frontend
**Causes**: 
- Filter is inactive (`is_active = false`)
- Parent group is inactive
- Cache issue

**Solutions**:
```bash
# Check filter status
python scripts/filter_manager.py list-filters --group-id "uuid"

# Clear cache
redis-cli FLUSHDB

# Restart application
docker-compose restart app
```

#### 2. Enum Mismatch Errors
**Symptoms**: `ValueError: 'new_channel' is not a valid PlatformEnum`
**Causes**: Database enum updated but Python enum not updated

**Solutions**:
1. Update Python enums in models and schemas
2. Restart application
3. Run health check to verify consistency

```bash
python scripts/filter_manager.py health-check
```

#### 3. Location Hierarchy Issues
**Symptoms**: Locations not appearing or incorrect hierarchy
**Causes**: Missing parent relationships or incorrect levels

**Solutions**:
```python
# Check location data
locations = await filter_service.get_location_hierarchy()

# Verify parent-child relationships
for loc in locations:
    if loc.parent_id:
        print(f"{loc.name} -> Parent: {loc.parent_id}")
```

#### 4. Performance Issues
**Symptoms**: Slow filter loading
**Causes**: Missing cache, unoptimized queries, large option sets

**Solutions**:
- Enable Redis caching
- Use pagination for large option sets
- Add database indexes
- Monitor query performance

### Health Check Diagnostics

```bash
python scripts/filter_manager.py health-check
```

**Output Analysis**:
- ✅ **Database**: Connected - Database is accessible
- ✅ **Metadata**: Healthy - Filter counts are normal
- ⚠️ **Enums**: Mismatch - Database and Python enums differ
- ❌ **Cache**: Error - Redis connection issues
- ✅ **Data Integrity**: No orphaned records

### Debug Mode

Enable debug logging for detailed troubleshooting:

```python
import logging
logging.getLogger('app.services.filter_service').setLevel(logging.DEBUG)
```

### Performance Monitoring

Monitor filter system performance:

```python
# Get system metadata
metadata = await filter_service.get_filter_metadata()

# Check cache hit rates
cache_info = await redis.info('stats')

# Monitor query execution times
await filter_service.log_filter_usage(
    filter_definition_id=filter_id,
    execution_time_ms=execution_time
)
```

## 🚀 Production Deployment

### Environment Variables
```bash
# Database
DATABASE_URL=********************************/db
REDIS_URL=redis://host:6379/0

# Filter system specific
FILTER_METADATA_TTL=3600  # 1 hour cache TTL
ENABLE_FILTER_ANALYTICS=true
```

### Monitoring
- Set up alerts for filter system health endpoints
- Monitor cache hit rates and database query performance
- Track filter usage analytics for insights

### Scaling Considerations
- Use Redis Cluster for high-availability caching
- Consider read replicas for filter metadata queries
- Implement rate limiting for filter API endpoints

---

## 📞 Support

For questions or issues with the filter system:

1. **Check this documentation** for common scenarios
2. **Run health check** to identify system issues
3. **Review logs** for error details
4. **Use demo script** to validate functionality

**Remember**: The filter system is designed to be extensible and maintainable. Always test changes in a development environment before applying to production.
