# OAuth Flow Issue Resolution - Scope Attribute Fix

## 🚨 New Issue Identified

After implementing the initial OAuth flow fixes, a new issue emerged:

```
AttributeError: 'OAuthProviderTokens' object has no attribute 'scope'
```

## 🔍 Root Cause Analysis

The error occurred because:

1. **Schema Mismatch**: The `OAuthProviderTokens` Pydantic model didn't include a `scope` field
2. **Database Model Expectation**: The `OAuthAccount` database model has a `scope` field that expects to be populated
3. **Token Handler Gap**: OAuth handlers weren't capturing scope information from provider responses

## 🔧 Implemented Fixes

### 1. Updated OAuth Schemas (`app/schemas/oauth_schemas.py`)

**Added `scope` field to `OAuthProviderTokens`:**
```python
class OAuthProviderTokens(BaseModel):
    """Provider tokens from OAuth flow"""
    access_token: str
    refresh_token: Optional[str] = None
    expires_in: Optional[int] = None
    token_type: Optional[str] = None
    scope: Optional[str] = None  # ✅ ADDED
```

### 2. Updated OAuth Handlers (`app/oauth/oauth_handlers.py`)

**Google OAuth Handler:**
```python
return OAuthProviderTokens(
    access_token=tokens_data["access_token"],
    refresh_token=tokens_data.get("refresh_token"),
    expires_in=tokens_data.get("expires_in"),
    token_type=tokens_data.get("token_type"),
    scope=tokens_data.get("scope")  # ✅ ADDED
)
```

**Facebook & Instagram OAuth Handlers:**
```python
return OAuthProviderTokens(
    access_token=tokens_data["access_token"],
    expires_in=tokens_data.get("expires_in"),
    token_type="bearer",
    scope=None  # ✅ ADDED (providers don't return scope)
)
```

### 3. Updated OAuth Utility Functions (`app/utilities/oauth_utils.py`)

**Safe Scope Attribute Access:**
```python
# For new OAuth accounts
oauth_account = OAuthAccount(
    # ... other fields
    scope=getattr(tokens, 'scope', None),  # ✅ Safe attribute access
    # ... other fields
)

# For updating existing OAuth accounts
oauth_account.scope = getattr(tokens, 'scope', None) or oauth_account.scope  # ✅ Safe update
```

### 4. Fixed YouTube Profile Creation

**Updated to match actual SocialProfile model:**
```python
# Before (INCORRECT model fields)
profile = SocialProfile(
    user_id=user_id,
    platform="youtube",      # ❌ Wrong field name
    platform_id=channel_id, # ❌ Wrong field name
    # ... other incorrect fields
)

# After (CORRECT model fields)
profile = SocialProfile(
    oauth_account_id=oauth_account.id,  # ✅ Correct field
    service="youtube",                  # ✅ Correct field
    external_id=channel_id,            # ✅ Correct field
    # ... other correct fields
)
```

## 📊 Analysis of Error Flow

### Original Error Sequence:
1. ✅ OAuth initiation successful
2. ✅ Token exchange successful
3. ✅ User profile retrieval successful
4. ✅ User role assignment successful
5. ❌ **OAuth account creation failed** - scope attribute error
6. ❌ Transaction rollback
7. ❌ HTTP 400 error returned

### Fixed Flow Sequence:
1. ✅ OAuth initiation successful
2. ✅ Token exchange successful (now includes scope)
3. ✅ User profile retrieval successful
4. ✅ User role assignment successful
5. ✅ **OAuth account creation successful** - scope handled properly
6. ✅ User auth method creation successful
7. ✅ YouTube profile creation successful (if applicable)
8. ✅ Transaction commit successful
9. ✅ Session creation successful
10. ✅ HTTP redirect with tokens

## 🧪 Additional Improvements Made

### Enhanced Error Handling
- Safe attribute access using `getattr()`
- Proper handling of optional scope values
- Better error messages and logging

### Model Compatibility
- Fixed SocialProfile field mapping to match actual database model
- Proper OAuth account linkage for social profiles
- Correct foreign key relationships

### Provider-Specific Handling
- Google: Captures actual scope from token response
- Facebook/Instagram: Explicitly sets scope to None (not provided by these providers)

## 🎯 Key Technical Insights

### Schema vs Model Alignment
The issue highlighted the importance of keeping Pydantic schemas in sync with SQLAlchemy models, especially for optional fields that might be provider-specific.

### Provider Differences
Different OAuth providers have varying token response formats:
- **Google**: Returns scope in token response
- **Facebook/Instagram**: Don't typically include scope in token response

### Safe Attribute Access Pattern
Using `getattr(tokens, 'scope', None)` instead of direct attribute access prevents AttributeError when the Pydantic model evolution is in progress.

## 🚀 Deployment Notes

### Testing Checklist
- [x] Google OAuth flow with scope capture
- [x] Facebook OAuth flow with null scope handling
- [x] Instagram OAuth flow with null scope handling
- [x] YouTube profile creation with correct model fields
- [x] Error handling for missing OAuth accounts

### Monitoring Points
- OAuth success rates by provider
- Scope capture rates (should be 100% for Google, 0% for Facebook/Instagram)
- Social profile creation success rates
- Database integrity (no more foreign key violations)

## 🔮 Future Considerations

### Scope Utilization
The captured scope information can be used for:
- Validating available API permissions
- Refreshing tokens with appropriate scopes
- User consent management
- API rate limiting strategies

### Model Evolution
Consider adding validation to ensure schema-model field compatibility during development to prevent similar issues.

---

**Status**: ✅ **RESOLVED**  
**Impact**: 🎯 **HIGH** - Critical OAuth flow now functional  
**Effort**: ⚡ **MEDIUM** - Schema updates and safe access patterns
