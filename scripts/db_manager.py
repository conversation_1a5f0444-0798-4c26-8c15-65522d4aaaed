#!/usr/bin/env python3
"""
Database Management Utility for CreatorVerse Profile Analytics
"""

import os
import sys
import asyncio
import argparse
from pathlib import Path
from datetime import datetime, timedelta
import json

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy import text, select, func, and_, desc
from models.database import (
    Base, CreatorProfile, ProfileAnalytics, CreatorAuthToken, 
    DataCollectionJob, PlatformType, DataProvider
)
from config.settings import get_settings

class DatabaseManager:
    """Database management utility"""
    
    def __init__(self):
        self.settings = get_settings()
        self.engine = create_async_engine(
            self.settings.DATABASE_URL,
            echo=False
        )
        self.SessionLocal = async_sessionmaker(
            self.engine,
            class_=AsyncSession,
            expire_on_commit=False
        )
    
    async def create_tables(self):
        """Create all database tables"""
        print("Creating database tables...")
        
        async with self.engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        
        print("✓ Database tables created successfully")
    
    async def drop_tables(self):
        """Drop all database tables"""
        print("Dropping database tables...")
        
        async with self.engine.begin() as conn:
            await conn.run_sync(Base.metadata.drop_all)
        
        print("✓ Database tables dropped successfully")
    
    async def get_stats(self):
        """Get database statistics"""
        print("Gathering database statistics...")
        
        async with self.SessionLocal() as session:
            stats = {}
            
            # Creator profiles count
            result = await session.execute(select(func.count(CreatorProfile.id)))
            stats['creator_profiles'] = result.scalar()
            
            # Analytics records count
            result = await session.execute(select(func.count(ProfileAnalytics.id)))
            stats['analytics_records'] = result.scalar()
            
            # Active tokens count
            result = await session.execute(
                select(func.count(CreatorAuthToken.id)).where(
                    CreatorAuthToken.status == 'active'
                )
            )
            stats['active_tokens'] = result.scalar()
            
            # Collection jobs count by status
            result = await session.execute(
                select(DataCollectionJob.status, func.count(DataCollectionJob.id))
                .group_by(DataCollectionJob.status)
            )
            stats['collection_jobs'] = {row[0].value: row[1] for row in result}
            
            # Platform distribution
            result = await session.execute(
                select(CreatorProfile.platform, func.count(CreatorProfile.id))
                .group_by(CreatorProfile.platform)
            )
            stats['platforms'] = {row[0].value: row[1] for row in result}
            
            return stats
    
    async def cleanup_old_data(self, days: int = 90):
        """Clean up old data"""
        print(f"Cleaning up data older than {days} days...")
        
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        
        async with self.SessionLocal() as session:
            # Delete old analytics records
            result = await session.execute(
                text("DELETE FROM profile_analytics WHERE collected_at < :cutoff"),
                {"cutoff": cutoff_date}
            )
            analytics_deleted = result.rowcount
            
            # Delete old system metrics
            result = await session.execute(
                text("DELETE FROM system_metrics WHERE recorded_at < :cutoff"),
                {"cutoff": cutoff_date}
            )
            metrics_deleted = result.rowcount
            
            # Delete old completed/failed jobs
            result = await session.execute(
                text("""
                DELETE FROM data_collection_jobs 
                WHERE completed_at < :cutoff 
                AND status IN ('completed', 'failed')
                """),
                {"cutoff": cutoff_date}
            )
            jobs_deleted = result.rowcount
            
            await session.commit()
            
            print(f"✓ Cleaned up {analytics_deleted} analytics records")
            print(f"✓ Cleaned up {metrics_deleted} system metrics")
            print(f"✓ Cleaned up {jobs_deleted} old jobs")
    
    async def backup_data(self, output_file: str):
        """Backup data to JSON file"""
        print(f"Backing up data to {output_file}...")
        
        backup_data = {
            "timestamp": datetime.utcnow().isoformat(),
            "creator_profiles": [],
            "analytics_summary": []
        }
        
        async with self.SessionLocal() as session:
            # Backup creator profiles
            result = await session.execute(select(CreatorProfile))
            profiles = result.scalars().all()
            
            for profile in profiles:
                backup_data["creator_profiles"].append({
                    "creator_id": profile.creator_id,
                    "platform": profile.platform.value,
                    "username": profile.username,
                    "followers_count": profile.followers_count,
                    "engagement_rate": profile.engagement_rate,
                    "last_updated_at": profile.last_updated_at.isoformat()
                })
            
            # Backup recent analytics summary
            thirty_days_ago = datetime.utcnow() - timedelta(days=30)
            result = await session.execute(
                select(ProfileAnalytics)
                .where(ProfileAnalytics.collected_at >= thirty_days_ago)
                .order_by(desc(ProfileAnalytics.collected_at))
                .limit(1000)
            )
            analytics = result.scalars().all()
            
            for record in analytics:
                backup_data["analytics_summary"].append({
                    "creator_id": record.creator_id,
                    "platform": record.platform.value,
                    "followers": record.followers,
                    "engagement_rate": record.engagement_rate,
                    "collected_at": record.collected_at.isoformat()
                })
        
        # Write to file
        with open(output_file, 'w') as f:
            json.dump(backup_data, f, indent=2, default=str)
        
        print(f"✓ Backup completed: {len(backup_data['creator_profiles'])} profiles, {len(backup_data['analytics_summary'])} analytics records")
    
    async def create_sample_data(self):
        """Create sample data for testing"""
        print("Creating sample data...")
        
        sample_profiles = [
            {
                "creator_id": "sample_creator_1",
                "platform": PlatformType.INSTAGRAM,
                "username": "fashion_influencer",
                "display_name": "Fashion Influencer",
                "followers_count": 50000,
                "engagement_rate": 4.2
            },
            {
                "creator_id": "sample_creator_2", 
                "platform": PlatformType.YOUTUBE,
                "username": "tech_reviewer",
                "display_name": "Tech Reviewer",
                "followers_count": 120000,
                "engagement_rate": 6.8
            },
            {
                "creator_id": "sample_creator_3",
                "platform": PlatformType.TIKTOK,
                "username": "dance_creator",
                "display_name": "Dance Creator",
                "followers_count": 85000,
                "engagement_rate": 8.1
            }
        ]
        
        async with self.SessionLocal() as session:
            from uuid import uuid4
            
            for profile_data in sample_profiles:
                # Create profile
                profile = CreatorProfile(
                    id=str(uuid4()),
                    creator_id=profile_data["creator_id"],
                    platform=profile_data["platform"],
                    username=profile_data["username"],
                    display_name=profile_data["display_name"],
                    followers_count=profile_data["followers_count"],
                    engagement_rate=profile_data["engagement_rate"],
                    first_seen_at=datetime.utcnow(),
                    last_updated_at=datetime.utcnow(),
                    is_active=True
                )
                session.add(profile)
                
                # Create sample analytics
                for i in range(7):  # 7 days of data
                    analytics = ProfileAnalytics(
                        id=str(uuid4()),
                        creator_id=profile_data["creator_id"],
                        platform=profile_data["platform"],
                        analytics_data={
                            "followers": profile_data["followers_count"] + (i * 10),
                            "likes": 500 + (i * 50),
                            "comments": 50 + (i * 5),
                            "reach": profile_data["followers_count"] * 0.8,
                            "impressions": profile_data["followers_count"] * 1.2
                        },
                        followers=profile_data["followers_count"] + (i * 10),
                        engagement_rate=profile_data["engagement_rate"],
                        reach=int(profile_data["followers_count"] * 0.8),
                        impressions=int(profile_data["followers_count"] * 1.2),
                        data_source=DataProvider.PHYLLO_V1,
                        collected_at=datetime.utcnow() - timedelta(days=i),
                        is_complete=True,
                        quality_score=0.95
                    )
                    session.add(analytics)
            
            await session.commit()
        
        print("✓ Sample data created successfully")
    
    async def analyze_performance(self):
        """Analyze database performance"""
        print("Analyzing database performance...")
        
        async with self.engine.begin() as conn:
            # Check table sizes
            result = await conn.execute(text("""
                SELECT 
                    schemaname,
                    tablename,
                    attname,
                    n_distinct,
                    correlation
                FROM pg_stats 
                WHERE schemaname = 'public'
                ORDER BY tablename, attname;
            """))
            
            print("\nTable Statistics:")
            for row in result:
                print(f"  {row.tablename}.{row.attname}: distinct={row.n_distinct}, correlation={row.correlation}")
            
            # Check index usage
            result = await conn.execute(text("""
                SELECT 
                    indexrelname,
                    idx_tup_read,
                    idx_tup_fetch
                FROM pg_stat_user_indexes 
                ORDER BY idx_tup_read DESC;
            """))
            
            print("\nIndex Usage:")
            for row in result:
                print(f"  {row.indexrelname}: reads={row.idx_tup_read}, fetches={row.idx_tup_fetch}")
    
    async def vacuum_analyze(self):
        """Run VACUUM ANALYZE on all tables"""
        print("Running VACUUM ANALYZE...")
        
        async with self.engine.begin() as conn:
            await conn.execute(text("VACUUM ANALYZE;"))
        
        print("✓ VACUUM ANALYZE completed")
    
    async def close(self):
        """Close database connections"""
        await self.engine.dispose()

async def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description="Database Management Utility")
    parser.add_argument(
        "command",
        choices=[
            "create", "drop", "stats", "cleanup", "backup", 
            "sample", "analyze", "vacuum", "migrate"
        ],
        help="Command to execute"
    )
    parser.add_argument("--days", type=int, default=90, help="Days for cleanup command")
    parser.add_argument("--output", help="Output file for backup command")
    parser.add_argument("--force", action="store_true", help="Force dangerous operations")
    
    args = parser.parse_args()
    
    db_manager = DatabaseManager()
    
    try:
        if args.command == "create":
            await db_manager.create_tables()
        
        elif args.command == "drop":
            if not args.force:
                response = input("⚠️  This will drop all tables. Are you sure? (yes/no): ")
                if response.lower() != "yes":
                    print("Operation cancelled")
                    return
            await db_manager.drop_tables()
        
        elif args.command == "stats":
            stats = await db_manager.get_stats()
            print("\n📊 Database Statistics:")
            print(f"  Creator Profiles: {stats['creator_profiles']}")
            print(f"  Analytics Records: {stats['analytics_records']}")
            print(f"  Active Tokens: {stats['active_tokens']}")
            print(f"  Collection Jobs: {stats['collection_jobs']}")
            print(f"  Platform Distribution: {stats['platforms']}")
        
        elif args.command == "cleanup":
            if not args.force:
                response = input(f"⚠️  This will delete data older than {args.days} days. Continue? (yes/no): ")
                if response.lower() != "yes":
                    print("Operation cancelled")
                    return
            await db_manager.cleanup_old_data(args.days)
        
        elif args.command == "backup":
            output_file = args.output or f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            await db_manager.backup_data(output_file)
        
        elif args.command == "sample":
            await db_manager.create_sample_data()
        
        elif args.command == "analyze":
            await db_manager.analyze_performance()
        
        elif args.command == "vacuum":
            await db_manager.vacuum_analyze()
        
        elif args.command == "migrate":
            print("Running database migrations...")
            import subprocess
            subprocess.run(["alembic", "upgrade", "head"], check=True)
            print("✓ Migrations completed")
    
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)
    
    finally:
        await db_manager.close()

if __name__ == "__main__":
    asyncio.run(main())
