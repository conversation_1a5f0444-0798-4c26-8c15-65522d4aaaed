#!/usr/bin/env python3
"""
System Monitoring and Maintenance Utility
Real-time monitoring and maintenance tasks for CreatorVerse Profile Analytics
"""

import asyncio
import aiohttp
import psutil
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List
import json
import argparse
from pathlib import Path
import sys

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class SystemMonitor:
    """System monitoring and maintenance utility"""
    
    def __init__(self, api_url: str = "http://localhost:8000"):
        self.api_url = api_url.rstrip("/")
        self.session = None
        self.monitoring_data = []
    
    async def __aenter__(self):
        """Async context manager entry"""
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()
    
    async def check_api_health(self) -> Dict[str, Any]:
        """Check API health status"""
        try:
            start_time = time.time()
            async with self.session.get(f"{self.api_url}/api/v1/health", timeout=10) as response:
                response_time = time.time() - start_time
                
                if response.status == 200:
                    data = await response.json()
                    return {
                        "status": "healthy",
                        "response_time": response_time,
                        "details": data
                    }
                else:
                    return {
                        "status": "unhealthy",
                        "response_time": response_time,
                        "error": f"HTTP {response.status}"
                    }
        except Exception as e:
            return {
                "status": "error",
                "response_time": 0,
                "error": str(e)
            }
    
    async def check_system_resources(self) -> Dict[str, Any]:
        """Check system resource usage"""
        try:
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_count = psutil.cpu_count()
            load_avg = psutil.getloadavg() if hasattr(psutil, 'getloadavg') else (0, 0, 0)
            
            # Memory usage
            memory = psutil.virtual_memory()
            swap = psutil.swap_memory()
            
            # Disk usage
            disk = psutil.disk_usage('/')
            
            # Network stats
            network = psutil.net_io_counters()
            
            return {
                "cpu": {
                    "usage_percent": cpu_percent,
                    "count": cpu_count,
                    "load_avg": load_avg
                },
                "memory": {
                    "total_gb": round(memory.total / (1024**3), 2),
                    "available_gb": round(memory.available / (1024**3), 2),
                    "used_gb": round((memory.total - memory.available) / (1024**3), 2),
                    "usage_percent": memory.percent
                },
                "swap": {
                    "total_gb": round(swap.total / (1024**3), 2),
                    "used_gb": round(swap.used / (1024**3), 2),
                    "usage_percent": swap.percent
                },
                "disk": {
                    "total_gb": round(disk.total / (1024**3), 2),
                    "used_gb": round(disk.used / (1024**3), 2),
                    "free_gb": round(disk.free / (1024**3), 2),
                    "usage_percent": round((disk.used / disk.total) * 100, 2)
                },
                "network": {
                    "bytes_sent": network.bytes_sent,
                    "bytes_recv": network.bytes_recv,
                    "packets_sent": network.packets_sent,
                    "packets_recv": network.packets_recv
                }
            }
        except Exception as e:
            return {"error": str(e)}
    
    async def check_docker_services(self) -> Dict[str, Any]:
        """Check Docker services status"""
        try:
            import subprocess
            
            # Get docker-compose services
            result = subprocess.run(
                ["docker-compose", "ps", "--format", "json"],
                capture_output=True,
                text=True,
                check=True
            )
            
            services = []
            for line in result.stdout.strip().split('\n'):
                if line:
                    service_data = json.loads(line)
                    services.append({
                        "name": service_data.get("Service"),
                        "status": service_data.get("State"),
                        "health": service_data.get("Health"),
                        "ports": service_data.get("Publishers", [])
                    })
            
            return {"services": services, "status": "available"}
            
        except subprocess.CalledProcessError as e:
            return {"status": "error", "error": f"Docker command failed: {e}"}
        except FileNotFoundError:
            return {"status": "not_available", "error": "Docker not found"}
        except Exception as e:
            return {"status": "error", "error": str(e)}
    
    async def check_database_connections(self) -> Dict[str, Any]:
        """Check database connection status"""
        try:
            # Get database stats from API
            async with self.session.get(f"{self.api_url}/api/v1/monitoring/stats") as response:
                if response.status == 200:
                    data = await response.json()
                    return {
                        "status": "connected",
                        "stats": data
                    }
                else:
                    return {
                        "status": "error",
                        "error": f"API returned {response.status}"
                    }
        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            }
    
    async def collect_monitoring_data(self) -> Dict[str, Any]:
        """Collect comprehensive monitoring data"""
        timestamp = datetime.utcnow()
        
        # Collect all monitoring data concurrently
        api_health, system_resources, docker_status, db_status = await asyncio.gather(
            self.check_api_health(),
            self.check_system_resources(),
            self.check_docker_services(),
            self.check_database_connections(),
            return_exceptions=True
        )
        
        monitoring_data = {
            "timestamp": timestamp.isoformat(),
            "api_health": api_health if not isinstance(api_health, Exception) else {"error": str(api_health)},
            "system_resources": system_resources if not isinstance(system_resources, Exception) else {"error": str(system_resources)},
            "docker_services": docker_status if not isinstance(docker_status, Exception) else {"error": str(docker_status)},
            "database": db_status if not isinstance(db_status, Exception) else {"error": str(db_status)}
        }
        
        self.monitoring_data.append(monitoring_data)
        return monitoring_data
    
    def analyze_trends(self, hours: int = 24) -> Dict[str, Any]:
        """Analyze monitoring trends"""
        if len(self.monitoring_data) < 2:
            return {"error": "Insufficient data for trend analysis"}
        
        # Filter data by time window
        cutoff_time = datetime.utcnow() - timedelta(hours=hours)
        recent_data = [
            d for d in self.monitoring_data 
            if datetime.fromisoformat(d["timestamp"]) >= cutoff_time
        ]
        
        if len(recent_data) < 2:
            return {"error": f"Insufficient data for {hours}h trend analysis"}
        
        # CPU trend
        cpu_values = [
            d["system_resources"].get("cpu", {}).get("usage_percent", 0)
            for d in recent_data
            if "system_resources" in d and "error" not in d["system_resources"]
        ]
        
        # Memory trend
        memory_values = [
            d["system_resources"].get("memory", {}).get("usage_percent", 0)
            for d in recent_data
            if "system_resources" in d and "error" not in d["system_resources"]
        ]
        
        # API response time trend
        api_response_times = [
            d["api_health"].get("response_time", 0)
            for d in recent_data
            if "api_health" in d and d["api_health"].get("status") == "healthy"
        ]
        
        trends = {
            "period_hours": hours,
            "data_points": len(recent_data),
            "cpu": {
                "avg": sum(cpu_values) / len(cpu_values) if cpu_values else 0,
                "min": min(cpu_values) if cpu_values else 0,
                "max": max(cpu_values) if cpu_values else 0,
                "trend": "increasing" if len(cpu_values) > 1 and cpu_values[-1] > cpu_values[0] else "decreasing"
            },
            "memory": {
                "avg": sum(memory_values) / len(memory_values) if memory_values else 0,
                "min": min(memory_values) if memory_values else 0,
                "max": max(memory_values) if memory_values else 0,
                "trend": "increasing" if len(memory_values) > 1 and memory_values[-1] > memory_values[0] else "decreasing"
            },
            "api_response_time": {
                "avg": sum(api_response_times) / len(api_response_times) if api_response_times else 0,
                "min": min(api_response_times) if api_response_times else 0,
                "max": max(api_response_times) if api_response_times else 0,
                "trend": "increasing" if len(api_response_times) > 1 and api_response_times[-1] > api_response_times[0] else "decreasing"
            }
        }
        
        return trends
    
    def check_alerts(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Check for alert conditions"""
        alerts = []
        
        # System resource alerts
        if "system_resources" in data and "error" not in data["system_resources"]:
            resources = data["system_resources"]
            
            # CPU alert
            cpu_usage = resources.get("cpu", {}).get("usage_percent", 0)
            if cpu_usage > 80:
                alerts.append({
                    "type": "high_cpu_usage",
                    "severity": "critical" if cpu_usage > 95 else "warning",
                    "message": f"CPU usage is {cpu_usage:.1f}%",
                    "value": cpu_usage,
                    "threshold": 80
                })
            
            # Memory alert
            memory_usage = resources.get("memory", {}).get("usage_percent", 0)
            if memory_usage > 80:
                alerts.append({
                    "type": "high_memory_usage",
                    "severity": "critical" if memory_usage > 95 else "warning",
                    "message": f"Memory usage is {memory_usage:.1f}%",
                    "value": memory_usage,
                    "threshold": 80
                })
            
            # Disk alert
            disk_usage = resources.get("disk", {}).get("usage_percent", 0)
            if disk_usage > 85:
                alerts.append({
                    "type": "high_disk_usage",
                    "severity": "critical" if disk_usage > 95 else "warning",
                    "message": f"Disk usage is {disk_usage:.1f}%",
                    "value": disk_usage,
                    "threshold": 85
                })
        
        # API health alerts
        if "api_health" in data:
            api_health = data["api_health"]
            
            if api_health.get("status") != "healthy":
                alerts.append({
                    "type": "api_unhealthy",
                    "severity": "critical",
                    "message": f"API is {api_health.get('status')}: {api_health.get('error')}",
                    "value": api_health.get("status")
                })
            
            response_time = api_health.get("response_time", 0)
            if response_time > 2.0:  # 2 second threshold
                alerts.append({
                    "type": "slow_api_response",
                    "severity": "warning",
                    "message": f"API response time is {response_time:.3f}s",
                    "value": response_time,
                    "threshold": 2.0
                })
        
        return alerts
    
    def print_status_dashboard(self, data: Dict[str, Any]):
        """Print real-time status dashboard"""
        print("\033[2J\033[H")  # Clear screen and move cursor to top
        
        timestamp = data.get("timestamp", "Unknown")
        print(f"🖥️  CreatorVerse System Monitor - {timestamp}")
        print("=" * 80)
        
        # API Health
        api_health = data.get("api_health", {})
        status_icon = "🟢" if api_health.get("status") == "healthy" else "🔴"
        response_time = api_health.get("response_time", 0)
        print(f"{status_icon} API Health: {api_health.get('status', 'unknown')} ({response_time:.3f}s)")
        
        # System Resources
        if "system_resources" in data and "error" not in data["system_resources"]:
            resources = data["system_resources"]
            
            cpu = resources.get("cpu", {})
            memory = resources.get("memory", {})
            disk = resources.get("disk", {})
            
            print(f"\n💻 System Resources:")
            print(f"  CPU: {cpu.get('usage_percent', 0):.1f}% (Load: {cpu.get('load_avg', [0])[0]:.2f})")
            print(f"  Memory: {memory.get('usage_percent', 0):.1f}% ({memory.get('used_gb', 0):.1f}GB / {memory.get('total_gb', 0):.1f}GB)")
            print(f"  Disk: {disk.get('usage_percent', 0):.1f}% ({disk.get('used_gb', 0):.1f}GB / {disk.get('total_gb', 0):.1f}GB)")
        
        # Docker Services
        if "docker_services" in data:
            docker_data = data["docker_services"]
            if docker_data.get("status") == "available":
                services = docker_data.get("services", [])
                print(f"\n🐳 Docker Services ({len(services)}):")
                for service in services:
                    status_icon = "🟢" if service.get("status") == "running" else "🔴"
                    print(f"  {status_icon} {service.get('name', 'unknown')}: {service.get('status', 'unknown')}")
            else:
                print(f"\n🐳 Docker: {docker_data.get('status', 'unknown')}")
        
        # Database
        db_data = data.get("database", {})
        db_status_icon = "🟢" if db_data.get("status") == "connected" else "🔴"
        print(f"\n🗄️  Database: {db_status_icon} {db_data.get('status', 'unknown')}")
        
        # Alerts
        alerts = self.check_alerts(data)
        if alerts:
            print(f"\n⚠️  Alerts ({len(alerts)}):")
            for alert in alerts:
                severity_icon = "🚨" if alert["severity"] == "critical" else "⚠️"
                print(f"  {severity_icon} {alert['message']}")
        else:
            print(f"\n✅ No alerts")
        
        print("\n" + "=" * 80)
        print("Press Ctrl+C to stop monitoring")
    
    async def continuous_monitoring(self, interval: int = 30):
        """Continuous system monitoring"""
        print("Starting continuous monitoring...")
        
        try:
            while True:
                data = await self.collect_monitoring_data()
                self.print_status_dashboard(data)
                await asyncio.sleep(interval)
        
        except KeyboardInterrupt:
            print("\nMonitoring stopped by user")
        except Exception as e:
            print(f"\nMonitoring error: {e}")
    
    def save_report(self, filename: str):
        """Save monitoring report to file"""
        if not self.monitoring_data:
            print("No monitoring data to save")
            return
        
        # Generate comprehensive report
        latest_data = self.monitoring_data[-1]
        trends = self.analyze_trends()
        alerts = self.check_alerts(latest_data)
        
        report = {
            "generated_at": datetime.utcnow().isoformat(),
            "monitoring_period": {
                "start": self.monitoring_data[0]["timestamp"] if self.monitoring_data else None,
                "end": latest_data["timestamp"],
                "data_points": len(self.monitoring_data)
            },
            "current_status": latest_data,
            "trends": trends,
            "active_alerts": alerts,
            "historical_data": self.monitoring_data
        }
        
        with open(filename, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        print(f"📄 Report saved to {filename}")

async def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description="System Monitoring Utility")
    parser.add_argument("--url", default="http://localhost:8000", help="API base URL")
    parser.add_argument("--interval", type=int, default=30, help="Monitoring interval in seconds")
    parser.add_argument("--once", action="store_true", help="Run monitoring once and exit")
    parser.add_argument("--report", help="Save report to file")
    parser.add_argument("--trends", type=int, help="Analyze trends for specified hours")
    
    args = parser.parse_args()
    
    async with SystemMonitor(args.url) as monitor:
        if args.once:
            # Single monitoring run
            data = await monitor.collect_monitoring_data()
            monitor.print_status_dashboard(data)
            
            alerts = monitor.check_alerts(data)
            if alerts:
                print(f"\n⚠️  Found {len(alerts)} alerts:")
                for alert in alerts:
                    print(f"  - {alert['message']}")
        
        elif args.trends:
            # Run monitoring for specified time and analyze trends
            print(f"Collecting data for {args.trends} hours of trend analysis...")
            
            # Collect some initial data
            for i in range(min(10, args.trends * 2)):  # Collect up to 10 samples
                data = await monitor.collect_monitoring_data()
                print(f"Collected sample {i + 1}...")
                if i < 9:  # Don't sleep on last iteration
                    await asyncio.sleep(30)
            
            trends = monitor.analyze_trends(args.trends)
            print("\n📈 Trend Analysis:")
            print(json.dumps(trends, indent=2))
        
        else:
            # Continuous monitoring
            await monitor.continuous_monitoring(args.interval)
        
        # Save report if requested
        if args.report:
            monitor.save_report(args.report)

if __name__ == "__main__":
    asyncio.run(main())
