#!/usr/bin/env python3
"""
API Testing Utility for CreatorVerse Profile Analytics
Comprehensive API endpoint testing and validation
"""

import asyncio
import aiohttp
import json
import time
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
import argparse
from pathlib import Path

@dataclass
class TestResult:
    """Test result data structure"""
    endpoint: str
    method: str
    status_code: int
    response_time: float
    success: bool
    error_message: Optional[str] = None
    response_data: Optional[Dict[str, Any]] = None

class APITester:
    """Comprehensive API testing utility"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url.rstrip("/")
        self.session = None
        self.auth_token = None
        self.test_results: List[TestResult] = []
    
    async def __aenter__(self):
        """Async context manager entry"""
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()
    
    async def make_request(
        self, 
        method: str, 
        endpoint: str, 
        headers: Optional[Dict[str, str]] = None,
        data: Optional[Dict[str, Any]] = None,
        params: Optional[Dict[str, str]] = None
    ) -> TestResult:
        """Make HTTP request and return test result"""
        
        url = f"{self.base_url}{endpoint}"
        start_time = time.time()
        
        # Default headers
        request_headers = {"Content-Type": "application/json"}
        if headers:
            request_headers.update(headers)
        
        # Add auth token if available
        if self.auth_token and "Authorization" not in request_headers:
            request_headers["Authorization"] = f"Bearer {self.auth_token}"
        
        try:
            async with self.session.request(
                method=method,
                url=url,
                headers=request_headers,
                json=data,
                params=params,
                timeout=30
            ) as response:
                
                response_time = time.time() - start_time
                
                try:
                    response_data = await response.json()
                except:
                    response_data = await response.text()
                
                result = TestResult(
                    endpoint=endpoint,
                    method=method,
                    status_code=response.status,
                    response_time=response_time,
                    success=200 <= response.status < 300,
                    response_data=response_data
                )
                
                if not result.success:
                    result.error_message = f"HTTP {response.status}: {response_data}"
                
                return result
        
        except Exception as e:
            response_time = time.time() - start_time
            return TestResult(
                endpoint=endpoint,
                method=method,
                status_code=0,
                response_time=response_time,
                success=False,
                error_message=str(e)
            )
    
    async def authenticate(self) -> bool:
        """Authenticate and get JWT token"""
        print("🔐 Authenticating...")
        
        login_data = {
            "email": "<EMAIL>",
            "password": "admin123"
        }
        
        result = await self.make_request(
            "POST",
            "/api/v1/auth/jwt/login",
            data=login_data
        )
        
        if result.success and isinstance(result.response_data, dict):
            self.auth_token = result.response_data.get("access_token")
            print(f"✓ Authentication successful")
            return True
        else:
            print(f"✗ Authentication failed: {result.error_message}")
            return False
    
    async def test_health_endpoints(self):
        """Test health and basic endpoints"""
        print("🏥 Testing health endpoints...")
        
        tests = [
            ("GET", "/", "Root endpoint"),
            ("GET", "/api/v1/health", "Health check"),
            ("GET", "/docs", "API documentation"),
        ]
        
        for method, endpoint, description in tests:
            result = await self.make_request(method, endpoint)
            self.test_results.append(result)
            
            status = "✓" if result.success else "✗"
            print(f"  {status} {description}: {result.status_code} ({result.response_time:.3f}s)")
    
    async def test_authentication_endpoints(self):
        """Test authentication endpoints"""
        print("🔑 Testing authentication endpoints...")
        
        # Test login
        login_data = {
            "email": "<EMAIL>", 
            "password": "admin123"
        }
        result = await self.make_request("POST", "/api/v1/auth/jwt/login", data=login_data)
        self.test_results.append(result)
        
        status = "✓" if result.success else "✗"
        print(f"  {status} JWT Login: {result.status_code}")
        
        if result.success and isinstance(result.response_data, dict):
            token = result.response_data.get("access_token")
            
            # Test token refresh
            if "refresh_token" in result.response_data:
                refresh_result = await self.make_request(
                    "POST", 
                    "/api/v1/auth/jwt/refresh",
                    data={"refresh_token": result.response_data["refresh_token"]}
                )
                self.test_results.append(refresh_result)
                
                status = "✓" if refresh_result.success else "✗"
                print(f"  {status} Token Refresh: {refresh_result.status_code}")
        
        # Test invalid credentials
        invalid_login = {"email": "<EMAIL>", "password": "wrong"}
        result = await self.make_request("POST", "/api/v1/auth/jwt/login", data=invalid_login)
        self.test_results.append(result)
        
        status = "✓" if result.status_code == 401 else "✗"
        print(f"  {status} Invalid Login (should be 401): {result.status_code}")
    
    async def test_profile_endpoints(self):
        """Test creator profile endpoints"""
        print("👤 Testing profile endpoints...")
        
        # Test getting non-existent profile
        result = await self.make_request(
            "GET", 
            "/api/v1/profiles/test_creator?platform=instagram"
        )
        self.test_results.append(result)
        
        status = "✓" if result.status_code == 404 else "✗"
        print(f"  {status} Get Non-existent Profile (should be 404): {result.status_code}")
        
        # Test invalid platform
        result = await self.make_request(
            "GET",
            "/api/v1/profiles/test_creator?platform=invalid"
        )
        self.test_results.append(result)
        
        status = "✓" if result.status_code == 400 else "✗"
        print(f"  {status} Invalid Platform (should be 400): {result.status_code}")
    
    async def test_analytics_endpoints(self):
        """Test analytics endpoints"""
        print("📊 Testing analytics endpoints...")
        
        # Test analytics metrics
        result = await self.make_request(
            "GET",
            "/api/v1/analytics/metrics/test_creator?platform=instagram"
        )
        self.test_results.append(result)
        
        status = "✓" if result.status_code in [200, 404] else "✗"
        print(f"  {status} Get Analytics Metrics: {result.status_code}")
        
        # Test analytics summary
        result = await self.make_request(
            "GET",
            "/api/v1/analytics/summary/test_creator?platform=instagram&period=30d"
        )
        self.test_results.append(result)
        
        status = "✓" if result.status_code in [200, 404] else "✗"
        print(f"  {status} Get Analytics Summary: {result.status_code}")
    
    async def test_monitoring_endpoints(self):
        """Test monitoring endpoints"""
        print("🔍 Testing monitoring endpoints...")
        
        # Test system health
        result = await self.make_request("GET", "/api/v1/monitoring/health")
        self.test_results.append(result)
        
        status = "✓" if result.success else "✗"
        print(f"  {status} System Health: {result.status_code}")
        
        # Test system stats (requires authentication)
        result = await self.make_request("GET", "/api/v1/monitoring/stats")
        self.test_results.append(result)
        
        status = "✓" if result.status_code in [200, 401] else "✗"
        print(f"  {status} System Stats: {result.status_code}")
    
    async def test_data_collection_endpoints(self):
        """Test data collection endpoints"""
        print("🔄 Testing data collection endpoints...")
        
        collection_request = {
            "platform": "instagram",
            "job_type": "analytics",
            "data_provider": "phyllo_v1",
            "priority": 5
        }
        
        result = await self.make_request(
            "POST",
            "/api/v1/profiles/test_creator/collect",
            data=collection_request
        )
        self.test_results.append(result)
        
        status = "✓" if result.status_code in [200, 401, 404] else "✗"
        print(f"  {status} Start Data Collection: {result.status_code}")
        
        # Test getting collection jobs
        result = await self.make_request(
            "GET",
            "/api/v1/profiles/test_creator/jobs"
        )
        self.test_results.append(result)
        
        status = "✓" if result.status_code in [200, 401, 404] else "✗"
        print(f"  {status} Get Collection Jobs: {result.status_code}")
    
    async def test_error_handling(self):
        """Test error handling"""
        print("❌ Testing error handling...")
        
        # Test malformed JSON
        result = await self.make_request(
            "POST",
            "/api/v1/auth/jwt/login",
            headers={"Content-Type": "application/json"},
            data="invalid json"
        )
        self.test_results.append(result)
        
        status = "✓" if result.status_code == 422 else "✗"
        print(f"  {status} Malformed JSON (should be 422): {result.status_code}")
        
        # Test missing required fields
        result = await self.make_request(
            "POST",
            "/api/v1/profiles/test_creator/collect",
            data={"platform": "instagram"}  # Missing required fields
        )
        self.test_results.append(result)
        
        status = "✓" if result.status_code == 422 else "✗"
        print(f"  {status} Missing Required Fields (should be 422): {result.status_code}")
    
    async def test_rate_limiting(self):
        """Test rate limiting (if enabled)"""
        print("🚦 Testing rate limiting...")
        
        # Make multiple requests quickly
        tasks = []
        for i in range(20):
            task = self.make_request("GET", "/api/v1/health")
            tasks.append(task)
        
        results = await asyncio.gather(*tasks)
        
        # Check if any requests were rate limited
        rate_limited = sum(1 for r in results if r.status_code == 429)
        total_requests = len(results)
        
        print(f"  Made {total_requests} requests, {rate_limited} were rate limited")
        
        # Add to results
        self.test_results.extend(results)
    
    async def performance_test(self, endpoint: str = "/api/v1/health", requests: int = 100):
        """Performance test for an endpoint"""
        print(f"⚡ Performance testing {endpoint} with {requests} requests...")
        
        start_time = time.time()
        
        # Make concurrent requests
        tasks = []
        for _ in range(requests):
            task = self.make_request("GET", endpoint)
            tasks.append(task)
        
        results = await asyncio.gather(*tasks)
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # Calculate statistics
        successful_requests = sum(1 for r in results if r.success)
        failed_requests = requests - successful_requests
        avg_response_time = sum(r.response_time for r in results) / len(results)
        requests_per_second = requests / total_time
        
        print(f"  Total Time: {total_time:.2f}s")
        print(f"  Successful: {successful_requests}/{requests}")
        print(f"  Failed: {failed_requests}")
        print(f"  Avg Response Time: {avg_response_time:.3f}s")
        print(f"  Requests/Second: {requests_per_second:.2f}")
        
        # Add to results
        self.test_results.extend(results)
        
        return {
            "total_time": total_time,
            "successful_requests": successful_requests,
            "failed_requests": failed_requests,
            "avg_response_time": avg_response_time,
            "requests_per_second": requests_per_second
        }
    
    def generate_report(self) -> Dict[str, Any]:
        """Generate comprehensive test report"""
        total_tests = len(self.test_results)
        successful_tests = sum(1 for r in self.test_results if r.success)
        failed_tests = total_tests - successful_tests
        
        avg_response_time = sum(r.response_time for r in self.test_results) / max(1, total_tests)
        
        # Group by status code
        status_codes = {}
        for result in self.test_results:
            code = result.status_code
            status_codes[code] = status_codes.get(code, 0) + 1
        
        # Find slowest endpoints
        slowest = sorted(self.test_results, key=lambda r: r.response_time, reverse=True)[:5]
        
        report = {
            "summary": {
                "total_tests": total_tests,
                "successful": successful_tests,
                "failed": failed_tests,
                "success_rate": (successful_tests / max(1, total_tests)) * 100,
                "avg_response_time": avg_response_time
            },
            "status_codes": status_codes,
            "slowest_endpoints": [
                {
                    "endpoint": r.endpoint,
                    "method": r.method,
                    "response_time": r.response_time,
                    "status_code": r.status_code
                }
                for r in slowest
            ],
            "failed_tests": [
                {
                    "endpoint": r.endpoint,
                    "method": r.method,
                    "status_code": r.status_code,
                    "error": r.error_message
                }
                for r in self.test_results if not r.success
            ]
        }
        
        return report
    
    def print_report(self):
        """Print formatted test report"""
        report = self.generate_report()
        
        print("\n" + "="*60)
        print("📋 API TEST REPORT")
        print("="*60)
        
        summary = report["summary"]
        print(f"Total Tests: {summary['total_tests']}")
        print(f"Successful: {summary['successful']} ({summary['success_rate']:.1f}%)")
        print(f"Failed: {summary['failed']}")
        print(f"Average Response Time: {summary['avg_response_time']:.3f}s")
        
        print(f"\n📊 Status Code Distribution:")
        for code, count in sorted(report["status_codes"].items()):
            print(f"  {code}: {count}")
        
        if report["slowest_endpoints"]:
            print(f"\n🐌 Slowest Endpoints:")
            for endpoint in report["slowest_endpoints"]:
                print(f"  {endpoint['method']} {endpoint['endpoint']}: {endpoint['response_time']:.3f}s")
        
        if report["failed_tests"]:
            print(f"\n❌ Failed Tests:")
            for test in report["failed_tests"]:
                print(f"  {test['method']} {test['endpoint']}: {test['status_code']} - {test['error']}")
        
        print("="*60)

async def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description="API Testing Utility")
    parser.add_argument("--url", default="http://localhost:8000", help="Base URL for API")
    parser.add_argument("--auth", action="store_true", help="Test with authentication")
    parser.add_argument("--performance", action="store_true", help="Run performance tests")
    parser.add_argument("--requests", type=int, default=100, help="Number of requests for performance test")
    parser.add_argument("--endpoint", help="Specific endpoint to test")
    parser.add_argument("--report", help="Save report to JSON file")
    
    args = parser.parse_args()
    
    async with APITester(args.url) as tester:
        print(f"🧪 Testing API at {args.url}")
        print("-" * 40)
        
        # Authenticate if requested
        if args.auth:
            await tester.authenticate()
        
        # Run specific endpoint test
        if args.endpoint:
            if args.performance:
                await tester.performance_test(args.endpoint, args.requests)
            else:
                result = await tester.make_request("GET", args.endpoint)
                tester.test_results.append(result)
                status = "✓" if result.success else "✗"
                print(f"{status} {args.endpoint}: {result.status_code} ({result.response_time:.3f}s)")
        
        else:
            # Run comprehensive tests
            await tester.test_health_endpoints()
            await tester.test_authentication_endpoints()
            await tester.test_profile_endpoints()
            await tester.test_analytics_endpoints()
            await tester.test_monitoring_endpoints()
            await tester.test_data_collection_endpoints()
            await tester.test_error_handling()
            await tester.test_rate_limiting()
            
            if args.performance:
                await tester.performance_test("/api/v1/health", args.requests)
        
        # Generate and print report
        tester.print_report()
        
        # Save report if requested
        if args.report:
            report = tester.generate_report()
            with open(args.report, 'w') as f:
                json.dump(report, f, indent=2, default=str)
            print(f"\n📄 Report saved to {args.report}")

if __name__ == "__main__":
    asyncio.run(main())
