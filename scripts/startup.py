#!/usr/bin/env python3
"""
CreatorVerse Profile Analytics System Startup Script
Comprehensive setup and deployment utility
"""

import os
import sys
import subprocess
import time
import json
import argparse
from pathlib import Path
from typing import List, Dict, Any
import asyncio
import aiohttp
import asyncpg
import redis.asyncio as aioredis

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class Colors:
    """ANSI color codes for console output"""
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    RED = '\033[91m'
    BLUE = '\033[94m'
    PURPLE = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'
    END = '\033[0m'

class CreatorVerseSetup:
    """Setup and deployment manager for CreatorVerse Profile Analytics"""
    
    def __init__(self):
        self.project_root = project_root
        self.env_file = self.project_root / ".env"
        
    def print_banner(self):
        """Print application banner"""
        banner = f"""
{Colors.CYAN}{Colors.BOLD}
╔══════════════════════════════════════════════════════════════════╗
║                                                                  ║
║    🚀 CreatorVerse Profile Analytics System                      ║
║    📊 Production-Ready Analytics Pipeline                        ║
║                                                                  ║
║    Version: 1.0.0                                               ║
║    Author: Advanced AI Development Team                         ║
║                                                                  ║
╚══════════════════════════════════════════════════════════════════╝
{Colors.END}
        """
        print(banner)
    
    def log(self, message: str, level: str = "INFO"):
        """Log message with color coding"""
        color = {
            "INFO": Colors.GREEN,
            "WARN": Colors.YELLOW,
            "ERROR": Colors.RED,
            "DEBUG": Colors.BLUE
        }.get(level, Colors.WHITE)
        
        print(f"{color}[{level}]{Colors.END} {message}")
    
    def run_command(self, command: List[str], capture_output: bool = False) -> subprocess.CompletedProcess:
        """Run shell command with logging"""
        self.log(f"Running: {' '.join(command)}", "DEBUG")
        
        try:
            result = subprocess.run(
                command,
                capture_output=capture_output,
                text=True,
                check=True
            )
            return result
        except subprocess.CalledProcessError as e:
            self.log(f"Command failed: {e}", "ERROR")
            if capture_output:
                self.log(f"STDOUT: {e.stdout}", "ERROR")
                self.log(f"STDERR: {e.stderr}", "ERROR")
            raise
    
    def check_dependencies(self) -> bool:
        """Check if required dependencies are installed"""
        self.log("Checking dependencies...", "INFO")
        
        dependencies = {
            "python": ["python", "--version"],
            "docker": ["docker", "--version"],
            "docker-compose": ["docker-compose", "--version"],
            "git": ["git", "--version"]
        }
        
        missing = []
        
        for name, command in dependencies.items():
            try:
                self.run_command(command, capture_output=True)
                self.log(f"✓ {name} is installed", "INFO")
            except (subprocess.CalledProcessError, FileNotFoundError):
                self.log(f"✗ {name} is not installed", "ERROR")
                missing.append(name)
        
        if missing:
            self.log(f"Missing dependencies: {', '.join(missing)}", "ERROR")
            return False
        
        return True
    
    def setup_environment(self):
        """Setup environment configuration"""
        self.log("Setting up environment configuration...", "INFO")
        
        if not self.env_file.exists():
            example_env = self.project_root / ".env.example"
            if example_env.exists():
                # Copy example to .env
                import shutil
                shutil.copy(example_env, self.env_file)
                self.log("Created .env file from .env.example", "INFO")
                self.log("⚠️  Please edit .env file with your actual credentials", "WARN")
            else:
                self.log("No .env.example file found", "ERROR")
                return False
        else:
            self.log("Environment file already exists", "INFO")
        
        return True
    
    def generate_security_keys(self):
        """Generate secure keys for the application"""
        self.log("Generating security keys...", "INFO")
        
        import secrets
        from cryptography.fernet import Fernet
        
        # Generate keys
        secret_key = secrets.token_urlsafe(32)
        jwt_secret = secrets.token_urlsafe(32)
        encryption_key = Fernet.generate_key().decode()
        
        keys = {
            "SECRET_KEY": secret_key,
            "JWT_SECRET_KEY": jwt_secret,
            "ENCRYPTION_KEY": encryption_key
        }
        
        # Update .env file
        if self.env_file.exists():
            content = self.env_file.read_text()
            
            for key, value in keys.items():
                # Replace placeholder values
                content = content.replace(
                    f'{key}="your_{key.lower()}_must_be_at_least_32_characters_long_and_secure"',
                    f'{key}="{value}"'
                )
                content = content.replace(
                    f'{key}="your_{key.lower()}_must_be_32_url_safe_base64_encoded_bytes"',
                    f'{key}="{value}"'
                )
            
            self.env_file.write_text(content)
            self.log("Security keys generated and updated in .env", "INFO")
        
        return keys
    
    def setup_database(self):
        """Setup database with migrations"""
        self.log("Setting up database...", "INFO")
        
        try:
            # Install Python dependencies first
            self.log("Installing Python dependencies...", "INFO")
            self.run_command([
                sys.executable, "-m", "pip", "install", "-r", 
                str(self.project_root / "requirements.txt")
            ])
            
            # Run database migrations
            self.log("Running database migrations...", "INFO")
            os.chdir(self.project_root)
            
            # Check if alembic is initialized
            alembic_dir = self.project_root / "alembic"
            if not alembic_dir.exists():
                self.log("Initializing Alembic...", "INFO")
                self.run_command(["alembic", "init", "alembic"])
            
            # Run migrations
            self.run_command(["alembic", "upgrade", "head"])
            self.log("Database migrations completed", "INFO")
            
            return True
            
        except Exception as e:
            self.log(f"Database setup failed: {e}", "ERROR")
            return False
    
    def start_services_docker(self):
        """Start services using Docker Compose"""
        self.log("Starting services with Docker Compose...", "INFO")
        
        try:
            # Pull latest images
            self.log("Pulling Docker images...", "INFO")
            self.run_command(["docker-compose", "pull"])
            
            # Start services
            self.log("Starting Docker services...", "INFO")
            self.run_command(["docker-compose", "up", "-d"])
            
            # Wait for services to be ready
            self.log("Waiting for services to be ready...", "INFO")
            time.sleep(10)
            
            return True
            
        except Exception as e:
            self.log(f"Docker startup failed: {e}", "ERROR")
            return False
    
    def start_services_local(self):
        """Start services locally (development)"""
        self.log("Starting services locally...", "INFO")
        
        try:
            # Start the FastAPI application
            self.log("Starting FastAPI application...", "INFO")
            os.chdir(self.project_root)
            
            # Run with uvicorn
            self.run_command([
                sys.executable, "-m", "uvicorn", 
                "main:app", 
                "--reload", 
                "--host", "0.0.0.0", 
                "--port", "8000"
            ])
            
        except KeyboardInterrupt:
            self.log("Application stopped by user", "INFO")
        except Exception as e:
            self.log(f"Local startup failed: {e}", "ERROR")
            return False
    
    async def verify_services(self) -> bool:
        """Verify that all services are running correctly"""
        self.log("Verifying services...", "INFO")
        
        checks = [
            ("API Health", "http://localhost:8000/api/v1/health"),
            ("API Docs", "http://localhost:8000/docs"),
        ]
        
        async with aiohttp.ClientSession() as session:
            for name, url in checks:
                try:
                    async with session.get(url, timeout=10) as response:
                        if response.status == 200:
                            self.log(f"✓ {name} is responding", "INFO")
                        else:
                            self.log(f"✗ {name} returned status {response.status}", "ERROR")
                            return False
                except Exception as e:
                    self.log(f"✗ {name} check failed: {e}", "ERROR")
                    return False
        
        return True
    
    async def test_database_connection(self) -> bool:
        """Test database connection"""
        self.log("Testing database connection...", "INFO")
        
        try:
            # Load environment variables
            from dotenv import load_dotenv
            load_dotenv(self.env_file)
            
            db_url = os.getenv("DATABASE_URL")
            if not db_url:
                self.log("DATABASE_URL not found in environment", "ERROR")
                return False
            
            # Convert async URL to sync for testing
            sync_url = db_url.replace("postgresql+asyncpg://", "postgresql://")
            
            # Parse connection parameters
            import urllib.parse
            parsed = urllib.parse.urlparse(sync_url)
            
            # Test connection
            conn = await asyncpg.connect(
                host=parsed.hostname,
                port=parsed.port or 5432,
                user=parsed.username,
                password=parsed.password,
                database=parsed.path[1:] if parsed.path else None
            )
            
            # Test query
            result = await conn.fetchval("SELECT 1")
            await conn.close()
            
            if result == 1:
                self.log("✓ Database connection successful", "INFO")
                return True
            else:
                self.log("✗ Database connection test failed", "ERROR")
                return False
                
        except Exception as e:
            self.log(f"✗ Database connection failed: {e}", "ERROR")
            return False
    
    async def test_redis_connection(self) -> bool:
        """Test Redis connection"""
        self.log("Testing Redis connection...", "INFO")
        
        try:
            from dotenv import load_dotenv
            load_dotenv(self.env_file)
            
            redis_host = os.getenv("REDIS_HOST", "localhost")
            redis_port = int(os.getenv("REDIS_PORT", "6379"))
            redis_password = os.getenv("REDIS_PASSWORD")
            
            redis_client = aioredis.from_url(
                f"redis://{redis_host}:{redis_port}",
                password=redis_password,
                decode_responses=True
            )
            
            # Test connection
            await redis_client.ping()
            await redis_client.close()
            
            self.log("✓ Redis connection successful", "INFO")
            return True
            
        except Exception as e:
            self.log(f"✗ Redis connection failed: {e}", "ERROR")
            return False
    
    def show_success_message(self):
        """Show success message with access information"""
        success_msg = f"""
{Colors.GREEN}{Colors.BOLD}
🎉 CreatorVerse Profile Analytics System Successfully Deployed!
{Colors.END}

{Colors.CYAN}📊 API Endpoints:{Colors.END}
  • Main API: http://localhost:8000/api/v1/
  • Interactive Docs: http://localhost:8000/docs
  • ReDoc: http://localhost:8000/redoc
  • Health Check: http://localhost:8000/api/v1/health

{Colors.CYAN}🔧 Management:{Colors.END}
  • View Logs: docker-compose logs -f app
  • Monitor Services: docker-compose ps
  • Stop Services: docker-compose down

{Colors.CYAN}🔐 Default Admin Credentials:{Colors.END}
  • Email: <EMAIL>
  • Password: admin123

{Colors.YELLOW}⚠️  Next Steps:{Colors.END}
  1. Update .env file with your actual API credentials
  2. Change default admin password
  3. Configure external API integrations
  4. Set up monitoring and alerting

{Colors.GREEN}🚀 Your analytics platform is ready to process creator data!{Colors.END}
        """
        print(success_msg)
    
    def show_help(self):
        """Show help information"""
        help_msg = f"""
{Colors.BLUE}{Colors.BOLD}CreatorVerse Profile Analytics - Setup Help{Colors.END}

{Colors.CYAN}Available Commands:{Colors.END}
  setup     - Complete system setup and deployment
  local     - Start services locally (development)
  docker    - Start services with Docker
  check     - Check system dependencies
  env       - Setup environment configuration
  keys      - Generate security keys
  db        - Setup database and run migrations
  verify    - Verify running services
  help      - Show this help message

{Colors.CYAN}Examples:{Colors.END}
  python scripts/startup.py setup     # Complete setup
  python scripts/startup.py local     # Development mode
  python scripts/startup.py docker    # Production mode
  python scripts/startup.py verify    # Check services

{Colors.CYAN}Configuration:{Colors.END}
  • Edit .env file for configuration
  • Use docker-compose.yml for service orchestration
  • Check README.md for detailed documentation
        """
        print(help_msg)

async def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description="CreatorVerse Profile Analytics Setup")
    parser.add_argument(
        "command",
        choices=["setup", "local", "docker", "check", "env", "keys", "db", "verify", "help"],
        help="Command to execute"
    )
    parser.add_argument("--skip-deps", action="store_true", help="Skip dependency check")
    parser.add_argument("--skip-verify", action="store_true", help="Skip service verification")
    
    args = parser.parse_args()
    
    setup = CreatorVerseSetup()
    setup.print_banner()
    
    try:
        if args.command == "help":
            setup.show_help()
            return
        
        if args.command == "check":
            if setup.check_dependencies():
                setup.log("All dependencies are installed ✓", "INFO")
            else:
                setup.log("Some dependencies are missing ✗", "ERROR")
                sys.exit(1)
            return
        
        if args.command == "env":
            if setup.setup_environment():
                setup.log("Environment setup completed ✓", "INFO")
            else:
                setup.log("Environment setup failed ✗", "ERROR")
                sys.exit(1)
            return
        
        if args.command == "keys":
            keys = setup.generate_security_keys()
            setup.log("Security keys generated ✓", "INFO")
            return
        
        if args.command == "db":
            if setup.setup_database():
                setup.log("Database setup completed ✓", "INFO")
            else:
                setup.log("Database setup failed ✗", "ERROR")
                sys.exit(1)
            return
        
        if args.command == "verify":
            if await setup.verify_services():
                setup.log("All services verified ✓", "INFO")
            else:
                setup.log("Service verification failed ✗", "ERROR")
                sys.exit(1)
            return
        
        # Full setup or service start commands
        if not args.skip_deps and not setup.check_dependencies():
            setup.log("Dependencies check failed", "ERROR")
            sys.exit(1)
        
        if args.command in ["setup", "docker"]:
            # Setup environment
            if not setup.setup_environment():
                setup.log("Environment setup failed", "ERROR")
                sys.exit(1)
            
            # Generate keys if needed
            setup.generate_security_keys()
            
            # Start with Docker
            if not setup.start_services_docker():
                setup.log("Docker startup failed", "ERROR")
                sys.exit(1)
            
            # Wait a bit for services to stabilize
            setup.log("Waiting for services to stabilize...", "INFO")
            time.sleep(15)
            
            # Verify services
            if not args.skip_verify:
                if await setup.verify_services():
                    setup.show_success_message()
                else:
                    setup.log("Service verification failed", "WARN")
                    setup.log("Services may still be starting up. Check with: docker-compose ps", "INFO")
        
        elif args.command == "local":
            # Setup environment
            if not setup.setup_environment():
                setup.log("Environment setup failed", "ERROR")
                sys.exit(1)
            
            # Setup database
            if not setup.setup_database():
                setup.log("Database setup failed", "ERROR")
                sys.exit(1)
            
            # Start locally
            setup.start_services_local()
        
    except KeyboardInterrupt:
        setup.log("Setup interrupted by user", "INFO")
    except Exception as e:
        setup.log(f"Setup failed: {e}", "ERROR")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
