"""
Test script to verify that the PhylloAPIService module can be imported without errors.
"""
import sys
import os
import importlib
from pathlib import Path

def main():
    """Main entry point to verify the PhylloAPIService module"""
    print("Testing PhylloAPIService module...")
    
    try:
        # Try importing the module
        print("Importing app.services.phyllo_service...")
        
        # Force reload the module in case it's cached with errors
        if 'app.services.phyllo_service' in sys.modules:
            print("Module already loaded, reloading...")
            del sys.modules['app.services.phyllo_service']
        
        import app.services.phyllo_service
        print("✅ Successfully imported phyllo_service module")
          # Check if phyllo_service is an instance
        print("Checking phyllo_service object...")
        service = app.services.phyllo_service
        
        # Print module attributes
        print(f"Module attributes: {dir(service)}")
        print(f"Module type: {type(service)}")
        print("✅ phyllo_service module imported successfully")
        
        print("\nThe module is now successfully fixed and can be imported.")
        return True
    except Exception as e:
        print(f"❌ Error: {type(e).__name__}: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    # Return success or failure code
    sys.exit(0 if success else 1)
