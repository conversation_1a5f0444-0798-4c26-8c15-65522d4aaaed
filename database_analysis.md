"""
Database Schema Analysis for CreatorVerse Discovery & Profile Analytics
=======================================================================

Based on analysis of actual database structure and Phyllo response format
"""

# Database Schema Analysis

## 1. CORE PROFILE TABLE
**Table: profile_analytics.profile**
```sql
Columns (24):
- id (uuid, PK)
- work_platform_id (uuid) 
- external_id (text) - from phyllo
- platform_username (text)
- url (text)
- image_url (text) 
- full_name (text)
- introduction (text)
- content_count (integer)
- is_verified (boolean)
- platform_account_type (text) - BUSINESS/PERSONAL
- gender (text) - MALE/FEMALE/OTHER
- age_group (text)
- language (character) - ISO code 
- follower_count (integer)
- subscriber_count (integer)
- average_likes (integer)
- average_comments (integer)
- average_views (integer)
- average_reels_views (integer)
- engagement_rate (numeric)
- credibility_score (numeric)
- created_at (timestamptz)
- updated_at (timestamptz)
```

**Supports Filters:**
✅ follower_count (range)
✅ engagement_rate (range) 
✅ average_likes/comments/views (range)
✅ gender (categorical)
✅ is_verified (boolean)
✅ language (categorical)
✅ credibility_score (range)

**Missing Columns:**
❌ platform (instagram/youtube/tiktok) - THIS IS CRITICAL
❌ user_id (to distinguish internal vs external)
❌ country/state/city (location data)

## 2. AUDIENCE DEMOGRAPHICS TABLES

**Table: audience_gender_age**
```sql
- profile_id (uuid, FK)
- gender (text) - MALE/FEMALE  
- age_range (text) - 13-17, 18-24, 25-34, etc.
- value (numeric) - percentage
```

**Supports Filters:**
✅ audience_gender (percentage ranges)
✅ audience_age (percentage ranges)

**Table: audience_location** 
```sql
- profile_id (uuid, FK)
- geo_id (uuid, FK) - references location hierarchy
- value (numeric) - percentage
```

**Supports Filters:**
✅ audience_location (percentage ranges)

## 3. CONTENT & BRAND TABLES

**Table: profile_interest**
```sql
- profile_id (uuid, FK)
- interest_id (uuid, FK)
- weight (numeric)
```

**Table: profile_brand_affinity**
```sql  
- profile_id (uuid, FK)
- brand_id (uuid, FK)
- weight (numeric)
```

**Supports Filters:**
✅ creator_interests (via weight)
✅ brand_affinity (via weight)

## 4. PERFORMANCE STATS TABLE

**Table: profile_stats_current**
```sql
- profile_id (uuid, FK)
- follower_count (integer)
- following_count (integer) 
- average_likes (integer)
- average_views (integer)
- average_comments (integer)
- engagement_rate (numeric)
- updated_at (timestamptz)
```

**Note:** This duplicates some data from main profile table but allows for historical tracking.

## 5. FILTER CATALOG SCHEMA

**Purpose:** Defines the UI filters and their mappings

**Tables:**
- filter_groups - Filter categories (Demographics, Performance, etc.)
- filter_definitions - Individual filters with UI metadata
- location_hierarchy - Geographic hierarchy (country/state/city)

## SCHEMA ALIGNMENT ANALYSIS

### ✅ WELL SUPPORTED FILTERS:
1. **Performance Metrics:**
   - follower_count, engagement_rate, average_likes/comments/views
   - credibility_score
   
2. **Demographics:**
   - gender, age_group, language, is_verified
   
3. **Audience Demographics:**
   - audience_gender, audience_age, audience_location percentages
   
4. **Content/Interests:**
   - creator_interests, brand_affinity (via junction tables)

### ❌ MISSING CRITICAL COMPONENTS:

1. **Platform Field Missing:**
   ```sql
   ALTER TABLE profile_analytics.profile 
   ADD COLUMN platform VARCHAR(20) DEFAULT 'instagram';
   ```

2. **User Association Missing:**
   ```sql  
   ALTER TABLE profile_analytics.profile
   ADD COLUMN user_id UUID REFERENCES users.users(id);
   ```

3. **Geographic Data:**
   ```sql
   ALTER TABLE profile_analytics.profile 
   ADD COLUMN country VARCHAR(5),
   ADD COLUMN state VARCHAR(100), 
   ADD COLUMN city VARCHAR(100);
   ```

### 🔧 REQUIRED FIXES:

1. **Update ActualProfile Model:**
   - Remove extra columns not in database
   - Add proper table joins for related data
   - Add methods to fetch audience/interest data

2. **Fix Discovery Service:**
   - Build queries across multiple tables
   - Join profile with audience/interest tables
   - Handle platform filtering once column added

3. **Phyllo_dummy Integration:**
   - Determine correct endpoints
   - Map phyllo response to our normalized schema
   - Handle data insertion across multiple tables

## PHYLLO_DUMMY API ENDPOINTS

Based on the project structure, likely endpoints:
```
GET  /api/v1/profiles/search
POST /api/v1/profiles/search  
GET  /api/v1/profiles/{id}
GET  /api/v1/profiles/{id}/audience
GET  /api/v1/profiles/{id}/interests
```

Need to test these endpoints to confirm structure.
