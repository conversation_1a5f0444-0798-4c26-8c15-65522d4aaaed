#!/usr/bin/env python3
"""
Real Database Filter System Test
Tests the actual filter system with real database connectivity
"""

import asyncio
import asyncpg
import json
from datetime import datetime
import httpx
from typing import Dict, List, Any, Optional
import sys
import os

# Database configuration
DATABASE_CONFIG = {
    "host": "************",
    "port": 5432,
    "database": "postgres",
    "user": "postgres",
    "password": "s81JKkaoe42Tm5W"
}

class DatabaseFilterTester:
    """Test filter system using real database"""
    
    def __init__(self):
        self.conn = None
    
    async def connect(self):
        """Connect to database"""
        self.conn = await asyncpg.connect(**DATABASE_CONFIG)
        print("✅ Connected to database")
    
    async def disconnect(self):
        """Disconnect from database"""
        if self.conn:
            await self.conn.close()
            print("✅ Disconnected from database")
    
    async def test_database_schema(self):
        """Test database schema and data"""
        print("\n🔍 Testing Database Schema...")
        print("-" * 40)
        
        # Test schema existence
        schema_exists = await self.conn.fetchval("""
            SELECT schema_name FROM information_schema.schemata 
            WHERE schema_name = 'filter_catalog'
        """)
        
        if not schema_exists:
            raise Exception("filter_catalog schema not found")
        print("✅ filter_catalog schema exists")
        
        # Test enums
        enums = await self.conn.fetch("""
            SELECT typname FROM pg_type t 
            JOIN pg_namespace n ON t.typnamespace = n.oid 
            WHERE nspname = 'filter_catalog' AND typtype = 'e'
            ORDER BY typname
        """)
        print(f"✅ Found {len(enums)} enum types: {[e['typname'] for e in enums]}")
        
        # Test tables
        tables = await self.conn.fetch("""
            SELECT table_name FROM information_schema.tables 
            WHERE table_schema = 'filter_catalog'
            ORDER BY table_name
        """)
        print(f"✅ Found {len(tables)} tables: {[t['table_name'] for t in tables]}")
        
        return True
    
    async def test_filter_data(self):
        """Test actual filter data"""
        print("\n📊 Testing Filter Data...")
        print("-" * 40)
        
        # Get filter groups summary
        groups_summary = await self.conn.fetch("""
            SELECT 
                fg.channel,
                fg.option_for,
                COUNT(fg.id) as group_count,
                COUNT(fd.id) as filter_count,
                COUNT(CASE WHEN fd.is_active THEN 1 END) as active_filters
            FROM filter_catalog.filter_groups fg
            LEFT JOIN filter_catalog.filter_definitions fd ON fg.id = fd.group_id
            WHERE fg.is_active = true
            GROUP BY fg.channel, fg.option_for
            ORDER BY fg.channel, fg.option_for
        """)
        
        print("📋 Filter Groups Summary:")
        for row in groups_summary:
            print(f"   • {row['channel']} {row['option_for']}: {row['group_count']} groups, {row['active_filters']} active filters")
        
        # Get Instagram creator filters (most complete)
        instagram_filters = await self.conn.fetch("""
            SELECT 
                fg.name as group_name,
                fd.name as filter_name,
                fd.filter_type,
                fd.has_minmax,
                fd.has_enter_value,
                fd.has_search_box,
                fd.options,
                fd.is_active
            FROM filter_catalog.filter_definitions fd
            JOIN filter_catalog.filter_groups fg ON fd.group_id = fg.id
            WHERE fg.channel = 'instagram' AND fg.option_for = 'creator' 
            AND fg.is_active = true
            ORDER BY fg.sort_order, fd.sort_order
        """)
        
        print(f"\n📱 Instagram Creator Filters ({len(instagram_filters)} total):")
        current_group = None
        for filter_def in instagram_filters:
            if filter_def['group_name'] != current_group:
                current_group = filter_def['group_name']
                print(f"\n   📂 {current_group}:")
            
            options_count = len(filter_def['options']) if filter_def['options'] else 0
            status = "✅" if filter_def['is_active'] else "❌"
            print(f"      {status} {filter_def['filter_name']} ({filter_def['filter_type']}) - {options_count} options")
        
        return instagram_filters
    
    async def test_filter_queries(self):
        """Test specific filter queries"""
        print("\n🔍 Testing Filter Queries...")
        print("-" * 40)
        
        # Test getting filters for Instagram creators
        instagram_creator_query = """
            SELECT 
                fg.id as group_id,
                fg.name as group_name,
                fg.sort_order as group_sort,
                jsonb_agg(
                    jsonb_build_object(
                        'id', fd.id,
                        'name', fd.name,
                        'type', fd.filter_type,
                        'icon', fd.icon,
                        'minmax', fd.has_minmax,
                        'enterValue', fd.has_enter_value,
                        'searchBox', fd.has_search_box,
                        'placeholder', fd.placeholder,
                        'options', fd.options,
                        'sort_order', fd.sort_order
                    ) ORDER BY fd.sort_order, fd.name
                ) as filters
            FROM filter_catalog.filter_groups fg
            LEFT JOIN filter_catalog.filter_definitions fd ON fg.id = fd.group_id AND fd.is_active = true
            WHERE fg.channel = 'instagram' AND fg.option_for = 'creator' AND fg.is_active = true
            GROUP BY fg.id, fg.name, fg.sort_order
            ORDER BY fg.sort_order
        """
        
        result = await self.conn.fetch(instagram_creator_query)
        
        print("✅ Instagram Creator Filter Query Result:")
        for group in result:
            filters = group['filters']
            filter_count = len([f for f in filters if f['id'] is not None]) if filters else 0
            print(f"   • {group['group_name']}: {filter_count} filters")
        
        return result
    
    async def test_location_hierarchy(self):
        """Test location hierarchy"""
        print("\n🌍 Testing Location Hierarchy...")
        print("-" * 40)
        
        locations = await self.conn.fetch("""
            SELECT name, code, level, tier, population
            FROM filter_catalog.location_hierarchy
            WHERE is_active = true
            ORDER BY level, name
            LIMIT 10
        """)
        
        print(f"✅ Found {len(locations)} sample locations:")
        for loc in locations:
            print(f"   • {loc['name']} (Level {loc['level']}, {loc['tier'] or 'No tier'})")
        
        return locations
    
    async def test_channel_disable_functionality(self):
        """Test channel disable functionality"""
        print("\n🔄 Testing Channel Disable Functionality...")
        print("-" * 40)
        
        # Get initial state of Instagram audience filters
        initial_state = await self.conn.fetch("""
            SELECT id, name, is_active 
            FROM filter_catalog.filter_groups 
            WHERE channel = 'instagram' AND option_for = 'audience'
        """)
        
        print("📋 Initial Instagram Audience Groups:")
        for group in initial_state:
            status = "✅ Active" if group['is_active'] else "❌ Inactive"
            print(f"   • {group['name']}: {status}")
        
        if initial_state:
            # Test disable
            await self.conn.execute("""
                UPDATE filter_catalog.filter_groups 
                SET is_active = false, updated_at = CURRENT_TIMESTAMP
                WHERE channel = 'instagram' AND option_for = 'audience'
            """)
            
            print("🚫 Disabled Instagram audience filters")
            
            # Verify disable
            disabled_state = await self.conn.fetch("""
                SELECT id, name, is_active 
                FROM filter_catalog.filter_groups 
                WHERE channel = 'instagram' AND option_for = 'audience'
            """)
            
            print("📋 After Disable:")
            for group in disabled_state:
                status = "✅ Active" if group['is_active'] else "❌ Inactive"
                print(f"   • {group['name']}: {status}")
            
            # Test re-enable
            await self.conn.execute("""
                UPDATE filter_catalog.filter_groups 
                SET is_active = true, updated_at = CURRENT_TIMESTAMP
                WHERE channel = 'instagram' AND option_for = 'audience'
            """)
            
            print("✅ Re-enabled Instagram audience filters")
            
            # Verify re-enable
            final_state = await self.conn.fetch("""
                SELECT id, name, is_active 
                FROM filter_catalog.filter_groups 
                WHERE channel = 'instagram' AND option_for = 'audience'
            """)
            
            print("📋 After Re-enable:")
            for group in final_state:
                status = "✅ Active" if group['is_active'] else "❌ Inactive"
                print(f"   • {group['name']}: {status}")
            
            print("✅ Channel disable/enable functionality working correctly!")
        else:
            print("⚠️ No Instagram audience groups found to test")
        
        return True
    
    async def generate_api_response_format(self):
        """Generate the expected API response format"""
        print("\n📄 Generating API Response Format...")
        print("-" * 40)
        
        # Get Instagram creator filters in API format
        groups = await self.conn.fetch("""
            SELECT 
                fg.id as group_id,
                fg.name as group_name,
                fg.option_for,
                fg.channel,
                fg.sort_order as group_sort
            FROM filter_catalog.filter_groups fg
            WHERE fg.channel = 'instagram' AND fg.option_for = 'creator' AND fg.is_active = true
            ORDER BY fg.sort_order
        """)
        
        api_response = []
        
        for group in groups:
            # Get filters for this group
            filters = await self.conn.fetch("""
                SELECT 
                    id, name, filter_type, icon, has_minmax, has_enter_value,
                    has_search_box, placeholder, options, db_field, api_field, sort_order
                FROM filter_catalog.filter_definitions
                WHERE group_id = $1 AND is_active = true
                ORDER BY sort_order, name
            """, group['group_id'])
            
            filter_list = []
            for filter_def in filters:
                filter_obj = {
                    'id': str(filter_def['id']),
                    'name': filter_def['name'],
                    'type': filter_def['filter_type'],
                    'icon': filter_def['icon'],
                    'minmax': filter_def['has_minmax'],
                    'enterValue': filter_def['has_enter_value'],
                    'searchBox': filter_def['has_search_box'],
                    'placeholder': filter_def['placeholder'],
                    'options': filter_def['options'] or [],
                    'db_field': filter_def['db_field'],
                    'api_field': filter_def['api_field'],
                    'sort_order': filter_def['sort_order']
                }
                filter_list.append(filter_obj)
            
            group_obj = {
                'id': str(group['group_id']),
                'optionName': group['group_name'],
                'optionFor': group['option_for'],
                'channel': group['channel'],
                'sort_order': group['group_sort'],
                'filters': filter_list
            }
            api_response.append(group_obj)
        
        print(f"✅ Generated API response with {len(api_response)} filter groups")
        
        # Save to file for reference
        with open('instagram_creator_filters_response.json', 'w') as f:
            json.dump(api_response, f, indent=2, default=str)
        
        print("💾 Saved response to instagram_creator_filters_response.json")
        
        return api_response
    
    async def run_all_tests(self):
        """Run all database tests"""
        print("🧪 Running Comprehensive Database Filter Tests...")
        print("=" * 60)
        
        try:
            await self.connect()
            
            # Run all tests
            await self.test_database_schema()
            await self.test_filter_data()
            await self.test_filter_queries()
            await self.test_location_hierarchy()
            await self.test_channel_disable_functionality()
            api_response = await self.generate_api_response_format()
            
            print("\n" + "=" * 60)
            print("🎉 All Database Tests Completed Successfully!")
            print("=" * 60)
            
            print("📋 Summary:")
            print("   ✅ Database schema validation passed")
            print("   ✅ Filter data validation passed")
            print("   ✅ Query functionality verified")
            print("   ✅ Location hierarchy working")
            print("   ✅ Channel disable/enable functionality working")
            print("   ✅ API response format generated")
            
            return True
            
        except Exception as e:
            print(f"❌ Database test failed: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
        
        finally:
            await self.disconnect()

async def test_simple_filter_server():
    """Test the simple filter server"""
    print("\n🌐 Testing Simple Filter Server...")
    print("=" * 60)
    
    base_url = "http://localhost:8001"
    
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            # Test basic endpoints
            tests = [
                ("Root", "/"),
                ("Health", "/health"),
                ("Filter Health", "/api/v1/filters/health"),
                ("Instagram Creator", "/api/v1/filters/?channel=instagram&option_for=creator"),
                ("All Groups", "/api/v1/filters/groups"),
                ("Locations", "/api/v1/filters/locations"),
                ("Sample Data", "/api/v1/filters/test/sample-data")
            ]
            
            results = {"passed": 0, "failed": 0}
            
            for test_name, endpoint in tests:
                try:
                    response = await client.get(f"{base_url}{endpoint}")
                    response.raise_for_status()
                    data = response.json()
                    
                    print(f"✅ {test_name}: {response.status_code}")
                    if isinstance(data, list):
                        print(f"   📊 Returned {len(data)} items")
                    elif isinstance(data, dict) and 'status' in data:
                        print(f"   📊 Status: {data['status']}")
                    
                    results["passed"] += 1
                    
                except Exception as e:
                    print(f"❌ {test_name}: {str(e)}")
                    results["failed"] += 1
            
            total = results["passed"] + results["failed"]
            print(f"\n📊 Server Test Results: {results['passed']}/{total} passed")
            
            return results["passed"] == total
    
    except Exception as e:
        print(f"❌ Cannot connect to server: {str(e)}")
        print("💡 Make sure to start the server first with: python simple_filter_test.py")
        return False

async def main():
    """Main test runner"""
    print("🧪 CreatorVerse Filter System Comprehensive Testing")
    print("=" * 80)
    
    # Run database tests
    db_tester = DatabaseFilterTester()
    db_success = await db_tester.run_all_tests()
    
    # Run server tests (if server is running)
    print(f"\n{'='*80}")
    server_success = await test_simple_filter_server()
    
    # Final summary
    print(f"\n{'='*80}")
    print("🎯 FINAL TEST SUMMARY")
    print("=" * 80)
    
    print(f"📊 Database Tests: {'✅ PASSED' if db_success else '❌ FAILED'}")
    print(f"📊 Server Tests: {'✅ PASSED' if server_success else '❌ FAILED'}")
    
    if db_success and server_success:
        print(f"\n🎉 ALL TESTS PASSED! Filter system is fully functional.")
    elif db_success:
        print(f"\n✅ Database is working. Start the server with: python simple_filter_test.py")
    else:
        print(f"\n❌ Some tests failed. Check the errors above.")
    
    print("\n📋 Next Steps:")
    if db_success:
        print("   1. ✅ Database is ready")
        print("   2. 🚀 Start server: python simple_filter_test.py")
        print("   3. 🧪 Run server tests: python real_filter_test.py")
        print("   4. 📖 Check API docs: http://localhost:8001/docs")
    else:
        print("   1. ❌ Fix database connectivity issues")
        print("   2. 🔧 Check database configuration")
        print("   3. 🔄 Run tests again")

if __name__ == "__main__":
    asyncio.run(main())
