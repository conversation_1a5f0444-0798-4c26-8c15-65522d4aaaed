"""
Fix script for PGEnum and UUID JSON serialization issues
"""

import re

def fix_pgenum_warnings(file_path):
    """Remove the redundant native_enum=False parameter from PGEnum declarations"""
    with open(file_path, 'r', encoding='utf-8') as file:
        content = file.read()
    
    # Remove native_enum=False from PGEnum declarations
    pattern = r'(PGEnum\(\s*[^,]+,[^,]+,[^,]+,)\s*native_enum=False,'
    content = re.sub(pattern, r'\1', content)
    
    with open(file_path, 'w', encoding='utf-8') as file:
        file.write(content)
    
    print(f"Updated {file_path} - Removed redundant native_enum=False parameters")

def main():
    # Fix PGEnum warnings in filter_models.py
    fix_pgenum_warnings('app/models/filter_models.py')

if __name__ == "__main__":
    main()
