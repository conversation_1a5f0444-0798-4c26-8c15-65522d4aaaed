#!/usr/bin/env python3
"""
Test script for CreatorVerse Filter System
Tests all filter endpoints and functionality
"""

import asyncio
import httpx
import json
import sys
import uvicorn
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager

# Add the app directory to Python path
sys.path.append("./app")

# Import filter endpoints
from app.api.filter_endpoints import router as filter_router

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Simple lifespan for testing"""
    print("🚀 Starting Filter System Test Server...")
    yield
    print("🛑 Stopping Filter System Test Server...")

# Create minimal FastAPI app for testing
app = FastAPI(
    title="CreatorVerse Filter System Test",
    description="Test server for filter system endpoints",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include filter router
app.include_router(filter_router)

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "CreatorVerse Filter System Test Server",
        "status": "running",
        "endpoints": {
            "filters": "/api/v1/filters/",
            "health": "/api/v1/filters/health",
            "sample_data": "/api/v1/filters/test/sample-data",
            "docs": "/docs"
        }
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "filter-system-test"}

class FilterTester:
    """Test class for filter system functionality"""
    
    def __init__(self, base_url: str = "http://localhost:8001"):
        self.base_url = base_url
        self.client = httpx.AsyncClient(timeout=30.0)
    
    async def test_all_endpoints(self):
        """Test all filter endpoints"""
        print("\n🧪 Testing CreatorVerse Filter System...")
        print("=" * 60)
        
        tests = [
            ("Root Endpoint", self.test_root),
            ("Health Check", self.test_health),
            ("Sample Data", self.test_sample_data),
            ("Instagram Creator Filters", self.test_instagram_creator_filters),
            ("Instagram Audience Filters", self.test_instagram_audience_filters),
            ("YouTube Creator Filters", self.test_youtube_creator_filters),
            ("TikTok Creator Filters", self.test_tiktok_creator_filters),
            ("Filter Groups", self.test_filter_groups),
            ("Locations", self.test_locations),
            ("Toggle Filter Status", self.test_toggle_filter_status),
            ("Toggle Group Status", self.test_toggle_group_status),
            ("Filter Health Check", self.test_filter_health),
        ]
        
        results = {}
        for test_name, test_func in tests:
            try:
                print(f"\n📋 Testing: {test_name}")
                print("-" * 40)
                result = await test_func()
                results[test_name] = {"status": "✅ PASSED", "data": result}
                print(f"✅ {test_name}: PASSED")
            except Exception as e:
                results[test_name] = {"status": "❌ FAILED", "error": str(e)}
                print(f"❌ {test_name}: FAILED - {str(e)}")
        
        # Print summary
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY")
        print("=" * 60)
        
        passed = sum(1 for r in results.values() if "PASSED" in r["status"])
        total = len(results)
        
        for test_name, result in results.items():
            print(f"{result['status']} {test_name}")
        
        print(f"\n🎯 Results: {passed}/{total} tests passed")
        
        if passed == total:
            print("🎉 All tests passed! Filter system is working correctly.")
        else:
            print(f"⚠️  {total - passed} tests failed. Check the errors above.")
        
        await self.client.aclose()
        return results
    
    async def test_root(self):
        """Test root endpoint"""
        response = await self.client.get(f"{self.base_url}/")
        response.raise_for_status()
        data = response.json()
        print(f"📌 Service: {data.get('message')}")
        return data
    
    async def test_health(self):
        """Test health endpoint"""
        response = await self.client.get(f"{self.base_url}/health")
        response.raise_for_status()
        data = response.json()
        print(f"💚 Health: {data.get('status')}")
        return data
    
    async def test_sample_data(self):
        """Test sample data endpoint"""
        response = await self.client.get(f"{self.base_url}/api/v1/filters/test/sample-data")
        response.raise_for_status()
        data = response.json()
        print(f"📄 Sample data source: {data.get('source')}")
        print(f"📊 Channel: {data.get('channel')}, Option for: {data.get('option_for')}")
        if 'filter_groups' in data:
            print(f"🔍 Filter groups: {len(data['filter_groups'])}")
        return data
    
    async def test_instagram_creator_filters(self):
        """Test Instagram creator filters"""
        response = await self.client.get(
            f"{self.base_url}/api/v1/filters/",
            params={"channel": "instagram", "option_for": "creator"}
        )
        response.raise_for_status()
        data = response.json()
        print(f"📱 Instagram Creator Filter Groups: {len(data)}")
        for group in data:
            print(f"   • {group.get('optionName')}: {len(group.get('filters', []))} filters")
        return data
    
    async def test_instagram_audience_filters(self):
        """Test Instagram audience filters"""
        response = await self.client.get(
            f"{self.base_url}/api/v1/filters/",
            params={"channel": "instagram", "option_for": "audience"}
        )
        response.raise_for_status()
        data = response.json()
        print(f"👥 Instagram Audience Filter Groups: {len(data)}")
        for group in data:
            print(f"   • {group.get('optionName')}: {len(group.get('filters', []))} filters")
        return data
    
    async def test_youtube_creator_filters(self):
        """Test YouTube creator filters"""
        response = await self.client.get(
            f"{self.base_url}/api/v1/filters/",
            params={"channel": "youtube", "option_for": "creator"}
        )
        response.raise_for_status()
        data = response.json()
        print(f"🎥 YouTube Creator Filter Groups: {len(data)}")
        for group in data:
            print(f"   • {group.get('optionName')}: {len(group.get('filters', []))} filters")
        return data
    
    async def test_tiktok_creator_filters(self):
        """Test TikTok creator filters"""
        response = await self.client.get(
            f"{self.base_url}/api/v1/filters/",
            params={"channel": "tiktok", "option_for": "creator"}
        )
        response.raise_for_status()
        data = response.json()
        print(f"🎵 TikTok Creator Filter Groups: {len(data)}")
        for group in data:
            print(f"   • {group.get('optionName')}: {len(group.get('filters', []))} filters")
        return data
    
    async def test_filter_groups(self):
        """Test filter groups endpoint"""
        response = await self.client.get(f"{self.base_url}/api/v1/filters/groups")
        response.raise_for_status()
        data = response.json()
        print(f"📂 Total Filter Groups: {len(data)}")
        
        # Group by channel
        channels = {}
        for group in data:
            channel = group.get('channel')
            if channel not in channels:
                channels[channel] = []
            channels[channel].append(group)
        
        for channel, groups in channels.items():
            print(f"   • {channel}: {len(groups)} groups")
        return data
    
    async def test_locations(self):
        """Test locations endpoint"""
        response = await self.client.get(f"{self.base_url}/api/v1/filters/locations")
        response.raise_for_status()
        data = response.json()
        print(f"🌍 Total Locations: {len(data)}")
        
        # Group by level
        levels = {}
        for location in data:
            level = location.get('level', 0)
            if level not in levels:
                levels[level] = []
            levels[level].append(location)
        
        for level, locs in sorted(levels.items()):
            print(f"   • Level {level}: {len(locs)} locations")
        return data
    
    async def test_toggle_filter_status(self):
        """Test toggling filter status"""
        # First get a filter ID
        response = await self.client.get(
            f"{self.base_url}/api/v1/filters/",
            params={"channel": "instagram", "option_for": "creator"}
        )
        response.raise_for_status()
        data = response.json()
        
        if data and data[0].get('filters'):
            filter_id = data[0]['filters'][0]['id']
            
            # Toggle filter status
            response = await self.client.put(
                f"{self.base_url}/api/v1/filters/filters/{filter_id}/status",
                params={"is_active": False}
            )
            response.raise_for_status()
            result = response.json()
            print(f"🔄 Toggled filter status: {result.get('message')}")
            
            # Toggle back
            response = await self.client.put(
                f"{self.base_url}/api/v1/filters/filters/{filter_id}/status",
                params={"is_active": True}
            )
            response.raise_for_status()
            result = response.json()
            print(f"🔄 Restored filter status: {result.get('message')}")
            return result
        else:
            print("⚠️ No filters found to test toggle")
            return {"message": "No filters available"}
    
    async def test_toggle_group_status(self):
        """Test toggling group status"""
        # First get a group ID
        response = await self.client.get(f"{self.base_url}/api/v1/filters/groups")
        response.raise_for_status()
        data = response.json()
        
        if data:
            group_id = data[0]['id']
            
            # Toggle group status
            response = await self.client.put(
                f"{self.base_url}/api/v1/filters/groups/{group_id}/status",
                params={"is_active": False}
            )
            response.raise_for_status()
            result = response.json()
            print(f"🔄 Toggled group status: {result.get('message')}")
            
            # Toggle back
            response = await self.client.put(
                f"{self.base_url}/api/v1/filters/groups/{group_id}/status",
                params={"is_active": True}
            )
            response.raise_for_status()
            result = response.json()
            print(f"🔄 Restored group status: {result.get('message')}")
            return result
        else:
            print("⚠️ No groups found to test toggle")
            return {"message": "No groups available"}
    
    async def test_filter_health(self):
        """Test filter system health check"""
        response = await self.client.get(f"{self.base_url}/api/v1/filters/health")
        response.raise_for_status()
        data = response.json()
        print(f"💊 Filter System Health: {data.get('status')}")
        if 'components' in data:
            for component, status in data['components'].items():
                print(f"   • {component}: {status.get('status')}")
        return data

async def run_tests():
    """Run the test suite"""
    print("🧪 Starting CreatorVerse Filter System Tests...")
    
    # Wait a moment for server to start
    await asyncio.sleep(2)
    
    tester = FilterTester()
    results = await tester.test_all_endpoints()
    
    return results

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "test":
        # Run tests only
        asyncio.run(run_tests())
    else:
        # Run server
        print("🚀 Starting CreatorVerse Filter System Test Server...")
        print("📊 Server will be available at: http://localhost:8001")
        print("📖 API Documentation: http://localhost:8001/docs")
        print("🧪 To run tests: python test_filter_system.py test")
        
        uvicorn.run(
            "test_filter_system:app",
            host="0.0.0.0",
            port=8001,
            reload=False,
            log_level="info"
        )
