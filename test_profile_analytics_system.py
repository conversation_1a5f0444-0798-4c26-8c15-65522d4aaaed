"""
Comprehensive test suite for Profile Analytics API system
"""
import asyncio
import httpx
import json
import uuid
from datetime import datetime
from typing import Dict, Any

# Test configuration
BASE_URL = "http://localhost:8000"
PHYLLO_URL = "http://127.0.0.1:8001"
TEST_PROFILE_ID = None  # Will be set during testing


async def test_phyllo_dummy_connection():
    """Test connection to Phyllo dummy service"""
    print("🔗 Testing Phyllo dummy service connection...")
    
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(f"{PHYLLO_URL}/health")
            if response.status_code == 200:
                print("✅ Phyllo dummy service is running")
                return True
            else:
                print(f"❌ Phyllo dummy service returned {response.status_code}")
                return False
    except Exception as e:
        print(f"❌ Failed to connect to Phyllo dummy service: {str(e)}")
        return False


async def test_main_service_connection():
    """Test connection to main CreatorVerse service"""
    print("🔗 Testing main service connection...")

    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(f"{BASE_URL}/v1/health/")
            if response.status_code == 200:
                print("✅ Main service is running")
                return True
            else:
                print(f"❌ Main service returned {response.status_code}")
                return False
    except Exception as e:
        print(f"❌ Failed to connect to main service: {str(e)}")
        return False


async def get_test_profile_id():
    """Get a test profile ID from the database"""
    print("🔍 Getting test profile ID...")
    
    try:
        async with httpx.AsyncClient() as client:
            # Try to get profiles from discovery endpoint
            response = await client.post(
                f"{BASE_URL}/v1/discovery/search",
                json={
                    "filters": {
                        "performance": {
                            "follower_count": {"min_value": 1000, "max_value": 10000000}
                        }
                    },
                    "view_mode": "quick_view",
                    "limit": 1
                }
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("data") and len(data["data"]) > 0:
                    profile_id = data["data"][0].get("id")
                    print(f"✅ Found test profile ID: {profile_id}")
                    return profile_id
            
            print("⚠️ No profiles found, will use dummy ID")
            return str(uuid.uuid4())
            
    except Exception as e:
        print(f"❌ Error getting test profile ID: {str(e)}")
        return str(uuid.uuid4())


async def test_basic_profile_analytics(profile_id: str):
    """Test basic profile analytics endpoint"""
    print(f"📊 Testing basic profile analytics for {profile_id}...")
    
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.get(
                f"{BASE_URL}/v1/profile-analytics/{profile_id}",
                params={
                    "include_audience": True,
                    "include_content_analysis": True,
                    "refresh_external": True
                }
            )
            
            print(f"Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print("✅ Basic analytics endpoint working")
                
                # Validate response structure
                if "data" in data:
                    analytics_data = data["data"]
                    required_fields = [
                        "profile_id", "platform", "platform_username",
                        "performance_metrics", "last_updated"
                    ]
                    
                    missing_fields = [field for field in required_fields if field not in analytics_data]
                    if missing_fields:
                        print(f"⚠️ Missing fields in response: {missing_fields}")
                    else:
                        print("✅ Response structure is valid")
                    
                    # Check performance metrics
                    if "performance_metrics" in analytics_data:
                        metrics = analytics_data["performance_metrics"]
                        print(f"📈 Followers: {metrics.get('followers', 'N/A')}")
                        print(f"📈 Engagement Rate: {metrics.get('engagement_rate', 'N/A')}%")
                        print(f"📈 Avg Likes: {metrics.get('average_likes', 'N/A')}")
                
                return True
            else:
                print(f"❌ Basic analytics failed: {response.text}")
                return False
                
    except Exception as e:
        print(f"❌ Error testing basic analytics: {str(e)}")
        return False


async def test_audience_demographics(profile_id: str):
    """Test audience demographics endpoint"""
    print(f"👥 Testing audience demographics for {profile_id}...")
    
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.get(
                f"{BASE_URL}/v1/profile-analytics/{profile_id}/audience"
            )
            
            print(f"Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print("✅ Audience demographics endpoint working")
                
                if "data" in data and "demographics" in data["data"]:
                    demographics = data["data"]["demographics"]
                    
                    # Check for key demographic data
                    if "countries" in demographics:
                        print(f"🌍 Countries data available: {len(demographics['countries'])} countries")
                    if "age_groups" in demographics:
                        print(f"👶 Age groups data available: {len(demographics['age_groups'])} groups")
                    if "gender_distribution" in demographics:
                        print(f"⚧ Gender distribution available")
                
                return True
            else:
                print(f"❌ Audience demographics failed: {response.text}")
                return False
                
    except Exception as e:
        print(f"❌ Error testing audience demographics: {str(e)}")
        return False


async def test_audience_insights(profile_id: str):
    """Test audience insights endpoint"""
    print(f"🧠 Testing audience insights for {profile_id}...")
    
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.get(
                f"{BASE_URL}/v1/profile-analytics/{profile_id}/audience-insights"
            )
            
            print(f"Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print("✅ Audience insights endpoint working")
                
                if "data" in data and "insights" in data["data"]:
                    insights = data["data"]["insights"]
                    
                    if "interests" in insights:
                        print(f"🎯 Interests data available: {len(insights['interests'])} interests")
                    if "brand_affinity" in insights:
                        print(f"🏷️ Brand affinity data available: {len(insights['brand_affinity'])} brands")
                
                return True
            else:
                print(f"❌ Audience insights failed: {response.text}")
                return False
                
    except Exception as e:
        print(f"❌ Error testing audience insights: {str(e)}")
        return False


async def test_sponsored_content(profile_id: str):
    """Test sponsored content analysis endpoint"""
    print(f"💰 Testing sponsored content analysis for {profile_id}...")
    
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.get(
                f"{BASE_URL}/v1/profile-analytics/{profile_id}/sponsored-content"
            )
            
            print(f"Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print("✅ Sponsored content endpoint working")
                
                if "data" in data and "analysis" in data["data"]:
                    analysis = data["data"]["analysis"]
                    
                    if "sponsored_contents" in analysis:
                        print(f"📺 Sponsored content available: {len(analysis['sponsored_contents'])} posts")
                    if "sponsored_posts_performance" in analysis:
                        print(f"📊 Performance score: {analysis['sponsored_posts_performance']}")
                    if "estimated_pricing" in analysis:
                        print(f"💵 Pricing data available")
                
                return True
            else:
                print(f"❌ Sponsored content failed: {response.text}")
                return False
                
    except Exception as e:
        print(f"❌ Error testing sponsored content: {str(e)}")
        return False


async def test_similar_creators(profile_id: str):
    """Test similar creators endpoint"""
    print(f"👯 Testing similar creators for {profile_id}...")
    
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.get(
                f"{BASE_URL}/v1/profile-analytics/{profile_id}/similar-creators",
                params={"limit": 5}
            )
            
            print(f"Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print("✅ Similar creators endpoint working")
                
                if "data" in data:
                    similar_data = data["data"]
                    creators_count = len(similar_data.get("similar_creators", []))
                    print(f"👥 Found {creators_count} similar creators")
                    
                    if creators_count > 0:
                        first_creator = similar_data["similar_creators"][0]
                        print(f"🎯 Top match: {first_creator.get('platform_username', 'N/A')} "
                              f"(similarity: {first_creator.get('similarity_score', 'N/A')})")
                
                return True
            else:
                print(f"❌ Similar creators failed: {response.text}")
                return False
                
    except Exception as e:
        print(f"❌ Error testing similar creators: {str(e)}")
        return False


async def test_caching_behavior(profile_id: str):
    """Test caching behavior"""
    print(f"🗄️ Testing caching behavior for {profile_id}...")
    
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            # First request (should hit external API)
            start_time = datetime.now()
            response1 = await client.get(
                f"{BASE_URL}/v1/profile-analytics/{profile_id}",
                params={"refresh_external": True}
            )
            first_request_time = (datetime.now() - start_time).total_seconds()
            
            # Second request (should hit cache)
            start_time = datetime.now()
            response2 = await client.get(
                f"{BASE_URL}/v1/profile-analytics/{profile_id}",
                params={"refresh_external": False}
            )
            second_request_time = (datetime.now() - start_time).total_seconds()
            
            if response1.status_code == 200 and response2.status_code == 200:
                print(f"✅ Caching test completed")
                print(f"⏱️ First request (external): {first_request_time:.2f}s")
                print(f"⏱️ Second request (cached): {second_request_time:.2f}s")
                
                if second_request_time < first_request_time:
                    print("✅ Cache appears to be working (faster second request)")
                else:
                    print("⚠️ Cache may not be working (second request not faster)")
                
                return True
            else:
                print(f"❌ Caching test failed")
                return False
                
    except Exception as e:
        print(f"❌ Error testing caching: {str(e)}")
        return False


async def run_comprehensive_test():
    """Run comprehensive test suite"""
    print("🚀 Starting Profile Analytics API System Test")
    print("=" * 60)
    
    # Test service connections
    phyllo_ok = await test_phyllo_dummy_connection()
    main_ok = await test_main_service_connection()
    
    if not phyllo_ok or not main_ok:
        print("❌ Service connection tests failed. Aborting.")
        return
    
    # Get test profile ID
    global TEST_PROFILE_ID
    TEST_PROFILE_ID = await get_test_profile_id()
    
    print("\n" + "=" * 60)
    print("🧪 Running API Endpoint Tests")
    print("=" * 60)
    
    # Test all endpoints
    tests = [
        ("Basic Analytics", test_basic_profile_analytics),
        ("Audience Demographics", test_audience_demographics),
        ("Audience Insights", test_audience_insights),
        ("Sponsored Content", test_sponsored_content),
        ("Similar Creators", test_similar_creators),
        ("Caching Behavior", test_caching_behavior)
    ]
    
    results = {}
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            results[test_name] = await test_func(TEST_PROFILE_ID)
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {str(e)}")
            results[test_name] = False
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 Test Results Summary")
    print("=" * 60)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Profile Analytics API system is working correctly.")
    else:
        print("⚠️ Some tests failed. Please check the logs above for details.")


if __name__ == "__main__":
    asyncio.run(run_comprehensive_test())
