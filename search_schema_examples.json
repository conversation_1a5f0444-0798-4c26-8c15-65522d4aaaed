{"basic_search_example": {"work_platform_id": "instagram", "sort_by": {"field": "FOLLOWER_COUNT", "order": "DESCENDING"}, "limit": 10, "offset": 0}, "advanced_search_example": {"work_platform_id": "instagram", "sort_by": {"field": "AVERAGE_LIKES", "order": "DESCENDING"}, "follower_count": {"min": 10000, "max": 1000000}, "subscriber_count": {"min": 5000, "max": 500000}, "content_count": {"min": 50, "max": 1000}, "audience_gender": {"type": "FEMALE", "operator": "GT", "percentage_value": 60}, "creator_gender": "FEMALE", "audience_age": {"min": 18, "max": 35, "percentage_value": 70}, "creator_age": {"min": 20, "max": 40}, "description_keywords": "fashion beauty lifestyle", "is_verified": true, "has_contact_details": true, "specific_contact_details": [{"type": "EMAIL", "preference": "SHOULD_HAVE"}, {"type": "INSTAGRAM", "preference": "MUST_HAVE"}], "last_post_timestamp": "2024-01-01T00:00:00Z", "audience_language": [{"code": "en", "percentage_value": "80"}, {"code": "es", "percentage_value": "15"}], "creator_language": {"code": "en"}, "audience_interests": ["fashion", "beauty", "lifestyle"], "audience_interest_affinities": [{"value": "fashion", "operation": "GT", "percentage_value": "25"}], "creator_interests": ["fashion", "photography"], "audience_brand_affinities": ["nike", "adidas", "zara"], "creator_brand_affinities": ["chanel", "dior"], "average_likes": {"min": 1000, "max": 50000}, "average_views": {"min": 10000, "max": 500000}, "engagement_rate": {"percentage_value": "3.5"}, "has_sponsored_posts": true, "brand_sponsors": ["nike", "adidas"], "instagram_options": {"reel_views": {"min": 50000, "max": 1000000}}, "audience_locations": [{"location_id": "US", "percentage_value": 45.5, "operator": "GT"}, {"location_id": "UK", "percentage_value": 20.0, "operator": "GT"}], "creator_locations": ["US", "UK", "CA"], "follower_growth": {"interval": 3, "interval_unit": "MONTH", "operator": "GT", "percentage_value": 15}, "subscriber_growth": {"interval": 6, "interval_unit": "MONTH", "operator": "GT", "percentage_value": 10}, "bio_phrase": "content creator", "hashtags": [{"name": "fashion"}, {"name": "style"}], "mentions": [{"name": "fashionbrand"}], "topic_relevance": {"name": ["fashion", "beauty"], "weight": 0.7, "threshold": 0.6}, "audience_lookalikes": "fashioninfluencer", "platform_account_type": "CREATOR", "creator_account_type": ["CREATOR", "BUSINESS"], "creator_lookalikes": "@fashionista", "audience_location": [{"name": "United States", "percentage_value": 45, "operator": "GT"}], "limit": 20, "offset": 0, "audience_source": "FOLLOWERS", "total_engagements": {"min": 5000, "max": 100000}, "audience_credibility_category": ["HIGH", "EXCELLENT"], "audience_credibility_score": 0.8, "is_official_artist": false, "has_audience_info": true, "share_count": {"min": 100, "max": 5000}, "save_count": {"min": 200, "max": 10000}, "exclude_private_profiles": true, "creator_age_bracket": "OVER_18", "livestream_options": {"recent_activity": "gaming", "category": "GAMING", "categories_streamed": "2-5", "hours_watched": {"min": 1000, "max": 50000}, "average_concurrent_viewers": {"min": 100, "max": 5000}, "absolute_follower_growth": {"min": 500, "max": 10000}, "peak_viewers": {"min": 1000, "max": 20000}, "vtubers": {"enabled": true}}}, "valid_enum_values": {"sort_by_fields": ["AVERAGE_LIKES", "FOLLOWER_COUNT", "ENGAGEMENT_RATE", "DESCRIPTION", "AVERAGE_VIEWS", "CONTENT_COUNT", "REELS_VIEWS", "FOLLOWER_GROWTH", "TOTAL_VIEWS_GROWTH", "TOTAL_LIKES_GROWTH", "AUDIENCE_LOCATIONS", "AUDIENCE_LANGUAGE", "AUDIENCE_BRAND_AFFINITIES", "AUDIENCE_INTERESTS", "AUDIENCE_AGE", "CREATOR_LOOKALIKES", "AUDIENCE_LOOKALIKES", "AVERAGE_LIKE", "AUDIENCE_LOCATION", "TOPIC_RELEVANCE", "PEAK_VIEWERS", "AVG_CONCURRENT_VIEWERS", "AIRTIME", "HOURS_WATCHED"], "sort_orders": ["DESCENDING", "ASCENDING"], "platforms": ["instagram", "youtube", "tiktok", "twitter", "twitch"], "genders": ["ANY", "FEMALE", "GENDER_NEUTRAL", "MALE", "ORGANIZATION"], "operators": ["GT", "LT", "EQ", "GTE", "LTE"], "contact_types": ["BBM", "EMAIL", "FACEBOOK", "INSTAGRAM", "ITUNES", "KAKAO", "KIK", "LINEID", "LINKTREE", "PHONE", "PINTEREST", "SARAHAH", "SAYAT", "SKYPE", "SNAPCHAT", "TELEGRAM", "TIKTOK", "TUMBLR", "TWITCHTV", "TWITTER", "VIBER", "VK", "WECHAT", "WEIBO", "WHATSAPP", "YOUTUBE"], "contact_preferences": ["MUST_HAVE", "SHOULD_HAVE"], "audience_sources": ["ANY", "LIKERS", "FOLLOWERS", "COMMENTERS"], "credibility_categories": ["BAD", "LOW", "NORMAL", "HIGH", "EXCELLENT"], "account_types": ["ANY", "BUSINESS", "CREATOR", "PERSONAL", "PARTNERS", "AFFILIATES", "NULL"], "age_brackets": ["OVER_18", "OVER_21"], "twitch_categories": ["IRL", "GAMING", "ESPORTS", "SPORTS", "MUSIC", "SLOTS", "CHESS"], "categories_streamed_options": ["1", "2-5", "6-10", "11-25", "25-1000"], "interval_units": ["MONTH", "DAY", "WEEK", "YEAR"]}}