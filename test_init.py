"""Test script to verify initialization of services"""
import asyncio
import sys
from sqlalchemy.sql import text
from app.core.config import APP_CONFIG
from app.core_helper.database import AsyncDatabaseDB
from app.core_helper.redis_client import RedisClient

async def verify_database_connection(db: AsyncDatabaseDB, logger) -> bool:
    """Verify database connection is working"""
    try:
        if not hasattr(db, "initialized") or not db.initialized:
            await db.initialize()
            logger.info("Database initialized")
        
        # Check if engine was created successfully
        if not db.engine:
            logger.error("Database engine not initialized")
            return False
            
        # Test connection directly with the engine
        try:
            async with db.engine.connect() as conn:
                result = await conn.execute(text("SELECT 1"))
                row = result.fetchone()
                if row and row[0] == 1:
                    logger.info("Database connection verified successfully")
                    return True
                else:
                    logger.error("Database verification failed - unexpected result")
                    return False
        except Exception as conn_err:
            logger.error(f"Database connection test failed: {conn_err}", exc_info=True)
            return False
                
    except Exception as e:
        logger.error(f"Database connection verification failed: {e}", exc_info=True)
        return False

async def verify_redis_connection(redis: RedisClient, logger) -> bool:
    """Verify Redis connection is working"""
    try:
        if not redis.initialized:
            await redis.initialize()
        
        if not redis.redis_client:
            logger.error("Redis client not initialized")
            return False
            
        # Test Redis connection
        await redis.redis_client.ping()
        logger.info("Redis connection verified successfully")
        return True
    except Exception as e:
        logger.error(f"Redis connection verification failed: {e}")
        return False

async def test_initialization():
    """Test initialization of core services"""
    try:
        print("Starting service initialization tests...")
        
        # 1. Initialize Logger
        logger = APP_CONFIG.initialize_logger()
        logger.info("Logger initialized successfully")
        
        # Print configuration for debugging
        print(f"Connection string: {APP_CONFIG.connection_string}")
        print(f"Redis host from config: {getattr(APP_CONFIG, 'redis_host', 'not found')}")        # 2. Test Database Connection
        # Reset the singleton to avoid stale instance
        AsyncDatabaseDB._instance = None
        
        print(f"Creating DB instance with connection string: {APP_CONFIG.connection_string}")
        db = AsyncDatabaseDB(
            connection_string=APP_CONFIG.connection_string,
            pool_size=5,
            max_overflow=10,
            logger=logger
        )
        
        # Check if connection string was set properly
        print(f"DB connection string after init: {db.connection_string}")
        
        # Explicitly initialize the database
        print("Initializing database...")
        await db.initialize()
        
        if not await verify_database_connection(db, logger):
            raise Exception("Database verification failed")        # 3. Test Redis Connection
        # Reset the singleton to avoid stale instance
        RedisClient._instance = None
        
        # Try to get the actual redis_host from config (fallback to IP from appsettings.json)
        redis_host = getattr(APP_CONFIG, 'redis_host')  # From appsettings.json
        redis_url = f"redis://{redis_host}:6379"
        print(f"Using Redis URL: {redis_url}")
        
        redis = RedisClient(
            redis_url=redis_url,
            logger=logger
        )
        
        if not await verify_redis_connection(redis, logger):
            raise Exception("Redis verification failed")
        
        logger.info("All services initialized and verified successfully")
        return True
        
    except Exception as e:
        print(f"Initialization failed: {str(e)}")
        if hasattr(logger, 'error'):
            logger.error(f"Initialization failed: {str(e)}", exc_info=True)
        return False

if __name__ == "__main__":
    success = asyncio.run(test_initialization())
    sys.exit(0 if success else 1)
