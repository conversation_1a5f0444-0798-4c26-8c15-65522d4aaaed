"""
Performance Test Suite for Creatorverse User Backend
Tests system performance under various load conditions, database queries, and cache operations.
"""
import asyncio
import pytest
import time
import statistics
import uuid
from datetime import datetime, UTC
from typing import Dict, Any, List, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed
import psutil
import memory_profiler

from sqlalchemy import select, func
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import get_database, get_locobuzz_redis
from app.models.user_models import (
    User, Organization, Brand, BrandInfluencerList, BrandInfluencerListEntry,
    GlobalLabel, BrandLabel, UserSession
)
from app.services.user_service import (
    create_user_cache_aside,
    get_user_by_email_cache_aside,
    update_user_cache_aside
)
from app.services.influencer_list_service import (
    create_influencer_list,
    get_brand_influencer_lists,
    add_influencer_to_list
)
from app.services.label_service import (
    create_global_label,
    get_global_labels,
    assign_labels_to_entry
)


class PerformanceTestResults:
    """Class to collect and analyze performance test results"""
    
    def __init__(self):
        self.results = []
        self.memory_usage = []
        self.cpu_usage = []
    
    def add_result(self, operation: str, duration: float, success: bool = True, **kwargs):
        """Add a performance test result"""
        self.results.append({
            "operation": operation,
            "duration": duration,
            "success": success,
            "timestamp": datetime.now(UTC),
            **kwargs
        })
    
    def add_memory_usage(self, usage_mb: float):
        """Add memory usage measurement"""
        self.memory_usage.append(usage_mb)
    
    def add_cpu_usage(self, usage_percent: float):
        """Add CPU usage measurement"""
        self.cpu_usage.append(usage_percent)
    
    def get_statistics(self, operation: str = None) -> Dict[str, Any]:
        """Get performance statistics for operations"""
        if operation:
            filtered_results = [r for r in self.results if r["operation"] == operation]
        else:
            filtered_results = self.results
        
        durations = [r["duration"] for r in filtered_results if r["success"]]
        
        if not durations:
            return {"count": 0}
        
        return {
            "count": len(durations),
            "min": min(durations),
            "max": max(durations),
            "mean": statistics.mean(durations),
            "median": statistics.median(durations),
            "p95": statistics.quantiles(durations, n=20)[18] if len(durations) > 20 else max(durations),
            "p99": statistics.quantiles(durations, n=100)[98] if len(durations) > 100 else max(durations),
            "success_rate": len([r for r in filtered_results if r["success"]]) / len(filtered_results)
        }
    
    def print_summary(self):
        """Print performance test summary"""
        print("\n" + "="*80)
        print("PERFORMANCE TEST SUMMARY")
        print("="*80)
        
        operations = list(set(r["operation"] for r in self.results))
        
        for operation in operations:
            stats = self.get_statistics(operation)
            print(f"\n{operation.upper()}:")
            print(f"  Count: {stats['count']}")
            if stats['count'] > 0:
                print(f"  Success Rate: {stats['success_rate']:.2%}")
                print(f"  Min: {stats['min']:.4f}s")
                print(f"  Max: {stats['max']:.4f}s")
                print(f"  Mean: {stats['mean']:.4f}s")
                print(f"  Median: {stats['median']:.4f}s")
                print(f"  P95: {stats['p95']:.4f}s")
                if 'p99' in stats:
                    print(f"  P99: {stats['p99']:.4f}s")
        
        if self.memory_usage:
            print(f"\nMEMORY USAGE:")
            print(f"  Min: {min(self.memory_usage):.2f} MB")
            print(f"  Max: {max(self.memory_usage):.2f} MB")
            print(f"  Mean: {statistics.mean(self.memory_usage):.2f} MB")
        
        if self.cpu_usage:
            print(f"\nCPU USAGE:")
            print(f"  Min: {min(self.cpu_usage):.2f}%")
            print(f"  Max: {max(self.cpu_usage):.2f}%")
            print(f"  Mean: {statistics.mean(self.cpu_usage):.2f}%")


class TestPerformanceComprehensive:
    """Comprehensive performance test suite"""

    @pytest.fixture(autouse=True)
    async def setup_test_environment(self):
        """Setup test environment for performance testing"""
        self.db_conn = get_database()
        self.redis_client = get_locobuzz_redis()
        self.results = PerformanceTestResults()
        
        await self.db_conn.initialize()
        await self.redis_client.initialize()
        
        # Setup test data
        await self._setup_performance_test_data()
        
        yield
        
        # Print results and cleanup
        self.results.print_summary()
        await self._cleanup_test_data()
        await self.redis_client.close()
        await self.db_conn.shutdown()

    async def _setup_performance_test_data(self):
        """Setup test data for performance tests"""
        async with self.db_conn.get_db() as session:
            # Create test organization
            self.test_org = Organization(
                domain="perftest.com",
                name="Performance Test Company",
                contact_email="<EMAIL>"
            )
            session.add(self.test_org)
            await session.flush()

            # Create test brand
            self.test_brand = Brand(
                organization_id=self.test_org.id,
                name="Performance Test Brand",
                description="Brand for performance testing"
            )
            session.add(self.test_brand)
            await session.flush()

            # Create test user
            self.test_user = User(
                email="<EMAIL>",
                name="Performance Test User",
                status="active",
                is_email_verified=True
            )
            session.add(self.test_user)
            await session.commit()

    async def _cleanup_test_data(self):
        """Clean up performance test data"""
        try:
            async with self.db_conn.get_db() as session:
                from sqlalchemy import delete
                
                # Delete test data
                await session.execute(
                    delete(User).where(User.email.like('%perf%'))
                )
                await session.execute(
                    delete(Organization).where(Organization.domain.like('%perf%'))
                )
                await session.commit()
            
            # Clear Redis
            keys = await self.redis_client.keys("*perf*")
            if keys:
                await self.redis_client.delete(*keys)
                
        except Exception as e:
            print(f"Cleanup error: {e}")

    def _measure_memory(self):
        """Measure current memory usage"""
        process = psutil.Process()
        memory_mb = process.memory_info().rss / 1024 / 1024
        self.results.add_memory_usage(memory_mb)
        return memory_mb

    def _measure_cpu(self):
        """Measure current CPU usage"""
        cpu_percent = psutil.cpu_percent(interval=0.1)
        self.results.add_cpu_usage(cpu_percent)
        return cpu_percent

    # ──────────────────────────────────────────────────────────────────────────────
    # USER OPERATION PERFORMANCE TESTS
    # ──────────────────────────────────────────────────────────────────────────────

    @pytest.mark.asyncio
    async def test_user_creation_performance(self):
        """Test user creation performance under load"""
        print("\n\nTesting user creation performance...")
        
        async def create_user_task(user_id: int):
            start_time = time.time()
            memory_before = self._measure_memory()
            
            user_data = {
                "email": f"perf.user.{user_id}@perftest.com",
                "name": f"Performance User {user_id}",
                "status": "active"
            }
            
            try:
                success, created_user, message = await create_user_cache_aside(
                    self.db_conn, self.redis_client, user_data
                )
                
                duration = time.time() - start_time
                memory_after = self._measure_memory()
                
                self.results.add_result(
                    "user_creation",
                    duration,
                    success,
                    user_id=user_id,
                    memory_delta=memory_after - memory_before
                )
                
                return success
            except Exception as e:
                duration = time.time() - start_time
                self.results.add_result("user_creation", duration, False, error=str(e))
                return False

        # Create users concurrently
        tasks = [create_user_task(i) for i in range(100)]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        success_count = sum(1 for r in results if r is True)
        print(f"Created {success_count}/100 users successfully")
        
        # Verify performance metrics
        stats = self.results.get_statistics("user_creation")
        assert stats["success_rate"] > 0.95  # 95% success rate
        assert stats["p95"] < 1.0  # 95th percentile under 1 second

    @pytest.mark.asyncio
    async def test_user_retrieval_cache_performance(self):
        """Test user retrieval performance with cache"""
        print("\n\nTesting user retrieval cache performance...")
        
        # Create test users first
        test_emails = []
        for i in range(50):
            email = f"perf.retrieval.{i}@perftest.com"
            user_data = {"email": email, "name": f"Retrieval User {i}"}
            
            await create_user_cache_aside(self.db_conn, self.redis_client, user_data)
            test_emails.append(email)

        async def retrieve_user_task(email: str, iteration: int):
            start_time = time.time()
            
            try:
                user = await get_user_by_email_cache_aside(
                    self.db_conn, self.redis_client, email
                )
                
                duration = time.time() - start_time
                cache_hit = iteration > 0  # First call is cache miss, subsequent are hits
                
                self.results.add_result(
                    "user_retrieval_cache_hit" if cache_hit else "user_retrieval_cache_miss",
                    duration,
                    user is not None,
                    email=email,
                    iteration=iteration
                )
                
                return user is not None
            except Exception as e:
                duration = time.time() - start_time
                self.results.add_result("user_retrieval", duration, False, error=str(e))
                return False

        # Test cache miss performance (first retrieval)
        tasks = [retrieve_user_task(email, 0) for email in test_emails]
        await asyncio.gather(*tasks)
        
        # Test cache hit performance (subsequent retrievals)
        for iteration in range(1, 4):
            tasks = [retrieve_user_task(email, iteration) for email in test_emails]
            await asyncio.gather(*tasks)

        # Verify cache hit performance is significantly better
        miss_stats = self.results.get_statistics("user_retrieval_cache_miss")
        hit_stats = self.results.get_statistics("user_retrieval_cache_hit")
        
        print(f"Cache miss average: {miss_stats['mean']:.4f}s")
        print(f"Cache hit average: {hit_stats['mean']:.4f}s")
        print(f"Performance improvement: {miss_stats['mean'] / hit_stats['mean']:.2f}x")
        
        assert hit_stats["mean"] < miss_stats["mean"] / 2  # Cache hits should be at least 2x faster

    @pytest.mark.asyncio
    async def test_bulk_user_update_performance(self):
        """Test bulk user update performance"""
        print("\n\nTesting bulk user update performance...")
        
        # Create users to update
        user_ids = []
        for i in range(100):
            user_data = {
                "email": f"perf.update.{i}@perftest.com",
                "name": f"Update User {i}"
            }
            success, created_user, _ = await create_user_cache_aside(
                self.db_conn, self.redis_client, user_data
            )
            if success:
                user_ids.append(uuid.UUID(created_user["id"]))

        async def update_user_task(user_id: uuid.UUID, update_iteration: int):
            start_time = time.time()
            
            update_data = {
                "name": f"Updated User {update_iteration}",
                "phone_number": f"+123456{update_iteration:04d}"
            }
            
            try:
                success, updated_user, message = await update_user_cache_aside(
                    self.db_conn, self.redis_client, user_id, update_data
                )
                
                duration = time.time() - start_time
                self.results.add_result(
                    "user_update",
                    duration,
                    success,
                    user_id=str(user_id),
                    iteration=update_iteration
                )
                
                return success
            except Exception as e:
                duration = time.time() - start_time
                self.results.add_result("user_update", duration, False, error=str(e))
                return False

        # Update all users concurrently
        tasks = [update_user_task(user_id, 1) for user_id in user_ids]
        results = await asyncio.gather(*tasks)
        
        success_count = sum(1 for r in results if r is True)
        print(f"Updated {success_count}/{len(user_ids)} users successfully")
        
        stats = self.results.get_statistics("user_update")
        assert stats["success_rate"] > 0.95
        assert stats["p95"] < 0.5  # Updates should be fast

    # ──────────────────────────────────────────────────────────────────────────────
    # INFLUENCER LIST PERFORMANCE TESTS
    # ──────────────────────────────────────────────────────────────────────────────

    @pytest.mark.asyncio
    async def test_large_influencer_list_performance(self):
        """Test performance with large influencer lists"""
        print("\n\nTesting large influencer list performance...")
        
        async with self.db_conn.get_db() as session:
            # Create test list
            start_time = time.time()
            test_list = await create_influencer_list(
                session,
                self.redis_client,
                self.test_brand.id,
                self.test_user.id,
                "Performance Test List",
                "Large list for performance testing"
            )
            await session.commit()
            
            list_creation_time = time.time() - start_time
            self.results.add_result("list_creation", list_creation_time, True)

            # Add many influencers
            async def add_influencer_task(influencer_index: int):
                start_time = time.time()
                
                from app.schemas.influencer_schemas import AddInfluencerToListRequest
                
                influencer_data = AddInfluencerToListRequest(
                    influencer_id=f"perf_influencer_{influencer_index}",
                    influencer_name=f"Performance Influencer {influencer_index}",
                    influencer_username=f"@perfinfluencer{influencer_index}",
                    audience_size=10000 + (influencer_index * 100),
                    engagement_rate=300 + (influencer_index % 100),
                    channels=["instagram", "youtube"],
                    campaign="Performance Test Campaign"
                )
                
                try:
                    async with self.db_conn.get_db() as inner_session:
                        await add_influencer_to_list(
                            inner_session,
                            self.redis_client,
                            test_list.id,
                            self.test_user.id,
                            influencer_data
                        )
                        await inner_session.commit()
                    
                    duration = time.time() - start_time
                    self.results.add_result(
                        "influencer_addition",
                        duration,
                        True,
                        influencer_index=influencer_index
                    )
                    return True
                except Exception as e:
                    duration = time.time() - start_time
                    self.results.add_result("influencer_addition", duration, False, error=str(e))
                    return False

            # Add influencers in batches to avoid overwhelming the system
            batch_size = 50
            total_influencers = 500
            
            for batch_start in range(0, total_influencers, batch_size):
                batch_end = min(batch_start + batch_size, total_influencers)
                batch_tasks = [
                    add_influencer_task(i) 
                    for i in range(batch_start, batch_end)
                ]
                
                batch_results = await asyncio.gather(*batch_tasks)
                success_count = sum(1 for r in batch_results if r is True)
                print(f"Batch {batch_start//batch_size + 1}: Added {success_count}/{len(batch_tasks)} influencers")
                
                # Small delay between batches
                await asyncio.sleep(0.1)

            # Test list retrieval performance
            start_time = time.time()
            list_detail = await get_brand_influencer_lists(
                self.db_conn,
                self.redis_client,
                self.test_brand.id
            )
            retrieval_time = time.time() - start_time
            
            self.results.add_result("large_list_retrieval", retrieval_time, True)
            
            print(f"List retrieval time: {retrieval_time:.4f}s")
            
            # Verify performance
            addition_stats = self.results.get_statistics("influencer_addition")
            assert addition_stats["success_rate"] > 0.90
            assert addition_stats["p95"] < 1.0

    # ──────────────────────────────────────────────────────────────────────────────
    # DATABASE QUERY PERFORMANCE TESTS
    # ──────────────────────────────────────────────────────────────────────────────

    @pytest.mark.asyncio
    async def test_complex_query_performance(self):
        """Test performance of complex database queries"""
        print("\n\nTesting complex query performance...")
        
        # Create test data for complex queries
        async with self.db_conn.get_db() as session:
            # Create multiple brands and lists
            brands = []
            for i in range(10):
                brand = Brand(
                    organization_id=self.test_org.id,
                    name=f"Query Test Brand {i}",
                    description=f"Brand {i} for query testing"
                )
                session.add(brand)
                brands.append(brand)
            
            await session.flush()
            
            # Create lists and entries for each brand
            for brand in brands:
                for list_idx in range(5):
                    test_list = BrandInfluencerList(
                        brand_id=brand.id,
                        name=f"Query List {list_idx}",
                        created_by=self.test_user.id
                    )
                    session.add(test_list)
                    await session.flush()
                    
                    # Add entries to each list
                    for entry_idx in range(20):
                        entry = BrandInfluencerListEntry(
                            list_id=test_list.id,
                            influencer_id=f"query_inf_{brand.id}_{list_idx}_{entry_idx}",
                            influencer_name=f"Query Influencer {entry_idx}",
                            added_by=self.test_user.id,
                            audience_size=10000 + (entry_idx * 1000),
                            engagement_rate=250 + (entry_idx * 10)
                        )
                        session.add(entry)
            
            await session.commit()

        async def complex_query_task(query_type: str):
            start_time = time.time()
            
            try:
                async with self.db_conn.get_db() as session:
                    if query_type == "aggregation":
                        # Complex aggregation query
                        query = select(
                            Brand.name,
                            func.count(BrandInfluencerListEntry.id).label("total_influencers"),
                            func.avg(BrandInfluencerListEntry.audience_size).label("avg_audience"),
                            func.max(BrandInfluencerListEntry.engagement_rate).label("max_engagement")
                        ).select_from(
                            Brand
                        ).join(
                            BrandInfluencerList, Brand.id == BrandInfluencerList.brand_id
                        ).join(
                            BrandInfluencerListEntry, BrandInfluencerList.id == BrandInfluencerListEntry.list_id
                        ).group_by(
                            Brand.id, Brand.name
                        ).order_by(
                            func.count(BrandInfluencerListEntry.id).desc()
                        )
                        
                        result = await session.execute(query)
                        rows = result.all()
                        
                    elif query_type == "join":
                        # Complex join query
                        query = select(
                            User.name.label("user_name"),
                            Brand.name.label("brand_name"),
                            BrandInfluencerList.name.label("list_name"),
                            func.count(BrandInfluencerListEntry.id).label("entry_count")
                        ).select_from(
                            User
                        ).join(
                            BrandInfluencerList, User.id == BrandInfluencerList.created_by
                        ).join(
                            Brand, BrandInfluencerList.brand_id == Brand.id
                        ).join(
                            BrandInfluencerListEntry, BrandInfluencerList.id == BrandInfluencerListEntry.list_id
                        ).group_by(
                            User.id, User.name, Brand.id, Brand.name, BrandInfluencerList.id, BrandInfluencerList.name
                        )
                        
                        result = await session.execute(query)
                        rows = result.all()
                        
                    elif query_type == "filtering":
                        # Complex filtering query
                        query = select(
                            BrandInfluencerListEntry
                        ).join(
                            BrandInfluencerList, BrandInfluencerListEntry.list_id == BrandInfluencerList.id
                        ).join(
                            Brand, BrandInfluencerList.brand_id == Brand.id
                        ).where(
                            BrandInfluencerListEntry.audience_size > 15000,
                            BrandInfluencerListEntry.engagement_rate > 300,
                            Brand.name.like("%Query Test%")
                        ).order_by(
                            BrandInfluencerListEntry.audience_size.desc()
                        ).limit(50)
                        
                        result = await session.execute(query)
                        rows = result.scalars().all()
                
                duration = time.time() - start_time
                self.results.add_result(
                    f"complex_query_{query_type}",
                    duration,
                    True,
                    row_count=len(rows)
                )
                return True
                
            except Exception as e:
                duration = time.time() - start_time
                self.results.add_result(f"complex_query_{query_type}", duration, False, error=str(e))
                return False

        # Test different types of complex queries
        query_types = ["aggregation", "join", "filtering"]
        
        for query_type in query_types:
            # Run each query type multiple times
            tasks = [complex_query_task(query_type) for _ in range(10)]
            results = await asyncio.gather(*tasks)
            
            success_count = sum(1 for r in results if r is True)
            stats = self.results.get_statistics(f"complex_query_{query_type}")
            
            print(f"{query_type.title()} queries: {success_count}/10 successful, avg time: {stats['mean']:.4f}s")
            
            assert stats["success_rate"] > 0.90
            assert stats["p95"] < 2.0  # Complex queries should complete within 2 seconds

    # ──────────────────────────────────────────────────────────────────────────────
    # REDIS CACHE PERFORMANCE TESTS
    # ──────────────────────────────────────────────────────────────────────────────

    @pytest.mark.asyncio
    async def test_redis_cache_performance(self):
        """Test Redis cache performance under load"""
        print("\n\nTesting Redis cache performance...")
        
        async def redis_operation_task(operation_type: str, key_index: int):
            start_time = time.time()
            key = f"perf_test_key_{key_index}"
            
            try:
                if operation_type == "set":
                    value = f"test_value_{key_index}_{'x' * 100}"  # ~100 bytes
                    await self.redis_client.set(key, value, ex=300)  # 5 min expiry
                    
                elif operation_type == "get":
                    value = await self.redis_client.get(key)
                    
                elif operation_type == "delete":
                    await self.redis_client.delete(key)
                    
                elif operation_type == "hash_set":
                    hash_data = {
                        f"field_{i}": f"value_{i}_{key_index}" 
                        for i in range(10)
                    }
                    await self.redis_client.hset(f"hash_{key}", mapping=hash_data)
                    
                elif operation_type == "hash_get":
                    hash_data = await self.redis_client.hgetall(f"hash_{key}")
                
                duration = time.time() - start_time
                self.results.add_result(
                    f"redis_{operation_type}",
                    duration,
                    True,
                    key_index=key_index
                )
                return True
                
            except Exception as e:
                duration = time.time() - start_time
                self.results.add_result(f"redis_{operation_type}", duration, False, error=str(e))
                return False

        # Test different Redis operations
        operations = ["set", "get", "hash_set", "hash_get", "delete"]
        
        for operation in operations:
            if operation == "get" or operation == "hash_get" or operation == "delete":
                # First populate data for read/delete operations
                if operation == "get" or operation == "delete":
                    for i in range(100):
                        await self.redis_client.set(f"perf_test_key_{i}", f"test_value_{i}")
                elif operation == "hash_get":
                    for i in range(100):
                        hash_data = {f"field_{j}": f"value_{j}_{i}" for j in range(10)}
                        await self.redis_client.hset(f"hash_perf_test_key_{i}", mapping=hash_data)
            
            # Run operations concurrently
            tasks = [redis_operation_task(operation, i) for i in range(100)]
            results = await asyncio.gather(*tasks)
            
            success_count = sum(1 for r in results if r is True)
            stats = self.results.get_statistics(f"redis_{operation}")
            
            print(f"Redis {operation}: {success_count}/100 successful, avg time: {stats['mean']:.6f}s")
            
            assert stats["success_rate"] > 0.98  # Redis should be very reliable
            assert stats["p95"] < 0.01  # Redis operations should be very fast (< 10ms)

    # ──────────────────────────────────────────────────────────────────────────────
    # MEMORY USAGE TESTS
    # ──────────────────────────────────────────────────────────────────────────────

    @pytest.mark.asyncio
    async def test_memory_usage_under_load(self):
        """Test memory usage under sustained load"""
        print("\n\nTesting memory usage under load...")
        
        initial_memory = self._measure_memory()
        print(f"Initial memory usage: {initial_memory:.2f} MB")
        
        # Create sustained load
        async def sustained_load_task(duration_seconds: int):
            end_time = time.time() + duration_seconds
            operation_count = 0
            
            while time.time() < end_time:
                # Mix of operations
                if operation_count % 4 == 0:
                    # User creation
                    user_data = {
                        "email": f"memory.test.{operation_count}@perftest.com",
                        "name": f"Memory Test User {operation_count}"
                    }
                    await create_user_cache_aside(self.db_conn, self.redis_client, user_data)
                    
                elif operation_count % 4 == 1:
                    # User retrieval
                    await get_user_by_email_cache_aside(
                        self.db_conn, self.redis_client, "<EMAIL>"
                    )
                    
                elif operation_count % 4 == 2:
                    # Redis operations
                    await self.redis_client.set(f"memory_test_{operation_count}", f"data_{operation_count}")
                    await self.redis_client.get(f"memory_test_{operation_count}")
                    
                else:
                    # Label operations
                    from app.schemas.label_schemas import CreateLabelRequest
                    async with self.db_conn.get_db() as session:
                        try:
                            label_data = CreateLabelRequest(
                                name=f"memory_label_{operation_count}",
                                description=f"Memory test label {operation_count}"
                            )
                            await create_global_label(session, self.redis_client, label_data)
                            await session.commit()
                        except:
                            pass  # Ignore duplicate errors
                
                operation_count += 1
                
                # Measure memory every 100 operations
                if operation_count % 100 == 0:
                    current_memory = self._measure_memory()
                    cpu_usage = self._measure_cpu()
                    
                    print(f"Operations: {operation_count}, Memory: {current_memory:.2f} MB, CPU: {cpu_usage:.1f}%")
                
                # Small delay to avoid overwhelming
                await asyncio.sleep(0.01)
            
            return operation_count

        # Run sustained load for 30 seconds
        total_operations = await sustained_load_task(30)
        
        final_memory = self._measure_memory()
        memory_increase = final_memory - initial_memory
        
        print(f"Final memory usage: {final_memory:.2f} MB")
        print(f"Memory increase: {memory_increase:.2f} MB")
        print(f"Total operations: {total_operations}")
        print(f"Operations per second: {total_operations / 30:.1f}")
        
        # Memory increase should be reasonable
        assert memory_increase < 500  # Less than 500MB increase
        assert total_operations > 1000  # At least 1000 operations in 30 seconds

    # ──────────────────────────────────────────────────────────────────────────────
    # STRESS TESTS
    # ──────────────────────────────────────────────────────────────────────────────

    @pytest.mark.asyncio
    async def test_system_stress_concurrent_users(self):
        """Test system stress with many concurrent users"""
        print("\n\nTesting system stress with concurrent users...")
        
        async def simulate_user_session(user_id: int):
            """Simulate a complete user session"""
            session_start = time.time()
            operations_completed = 0
            
            try:
                # 1. User registration
                user_data = {
                    "email": f"stress.user.{user_id}@perftest.com",
                    "name": f"Stress User {user_id}"
                }
                success, created_user, _ = await create_user_cache_aside(
                    self.db_conn, self.redis_client, user_data
                )
                if success:
                    operations_completed += 1
                
                # 2. Multiple user retrievals (cache hits)
                for _ in range(5):
                    await get_user_by_email_cache_aside(
                        self.db_conn, self.redis_client, user_data["email"]
                    )
                    operations_completed += 1
                
                # 3. User updates
                update_data = {"name": f"Updated Stress User {user_id}"}
                if success:
                    success, _, _ = await update_user_cache_aside(
                        self.db_conn, self.redis_client, 
                        uuid.UUID(created_user["id"]), update_data
                    )
                    if success:
                        operations_completed += 1
                
                # 4. Redis operations
                for i in range(10):
                    await self.redis_client.set(f"stress_{user_id}_{i}", f"data_{i}")
                    await self.redis_client.get(f"stress_{user_id}_{i}")
                    operations_completed += 2
                
                session_duration = time.time() - session_start
                self.results.add_result(
                    "stress_user_session",
                    session_duration,
                    True,
                    user_id=user_id,
                    operations_completed=operations_completed
                )
                
                return operations_completed
                
            except Exception as e:
                session_duration = time.time() - session_start
                self.results.add_result(
                    "stress_user_session",
                    session_duration,
                    False,
                    user_id=user_id,
                    error=str(e),
                    operations_completed=operations_completed
                )
                return operations_completed

        # Simulate many concurrent users
        concurrent_users = 50
        print(f"Simulating {concurrent_users} concurrent users...")
        
        start_time = time.time()
        tasks = [simulate_user_session(i) for i in range(concurrent_users)]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        total_time = time.time() - start_time
        
        successful_sessions = [r for r in results if isinstance(r, int)]
        total_operations = sum(successful_sessions)
        
        print(f"Completed {len(successful_sessions)}/{concurrent_users} user sessions")
        print(f"Total operations: {total_operations}")
        print(f"Total time: {total_time:.2f}s")
        print(f"Operations per second: {total_operations / total_time:.1f}")
        
        stats = self.results.get_statistics("stress_user_session")
        print(f"Average session time: {stats['mean']:.4f}s")
        print(f"Success rate: {stats['success_rate']:.2%}")
        
        # Stress test success criteria
        assert stats["success_rate"] > 0.80  # 80% of sessions should complete
        assert stats["p95"] < 10.0  # 95% of sessions under 10 seconds
        assert total_operations / total_time > 100  # At least 100 ops/second


if __name__ == "__main__":
    # Run performance tests
    pytest.main([__file__, "-v", "--tb=short", "-s"])
