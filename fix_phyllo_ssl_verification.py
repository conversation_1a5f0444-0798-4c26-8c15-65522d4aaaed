"""
<PERSON><PERSON><PERSON> to fix SSL verification issues in the PhylloAPIService.

This script will:
1. Verify the PhylloAPIService configuration
2. Test the API connection with SSL verification disabled
3. Report the results

Usage:
    python -m fix_phyllo_ssl_verification
"""
import json
import sys
import httpx
import asyncio
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent))

from app.core.config import APP_CONFIG


async def check_config():
    """Check the current Phyllo API configuration"""
    print("\n=== Current Configuration ===")
    print(f"Phyllo API URL: {APP_CONFIG.phyllo_api_url}")
    print(f"SSL Verification Disabled: {APP_CONFIG.disable_ssl_verification}")

    # Check appsettings.json
    with open("appsettings.json", "r") as f:
        settings = json.load(f)
    
    phyllo_api_url = settings.get("phyllo", {}).get("api_url", "")
    disable_ssl = settings.get("testing", {}).get("disable_ssl_verification", False)
    
    print(f"\nIn appsettings.json:")
    print(f"Phyllo API URL: {phyllo_api_url}")
    print(f"SSL Verification Disabled: {disable_ssl}")
    
    # Check for mismatches
    if phyllo_api_url != APP_CONFIG.phyllo_api_url:
        print("\n⚠️ Warning: Mismatch between configured phyllo_api_url and APP_CONFIG value")
    
    if disable_ssl != APP_CONFIG.disable_ssl_verification:
        print("\n⚠️ Warning: Mismatch between configured disable_ssl_verification and APP_CONFIG value")


async def test_connection():
    """Test connection to Phyllo API with SSL verification disabled"""
    print("\n=== Testing Connection to Phyllo API ===")
    url = APP_CONFIG.phyllo_api_url
    
    print(f"Connecting to: {url}")
    
    try:
        # Test with SSL verification disabled
        async with httpx.AsyncClient(verify=False) as client:
            response = await client.get(f"{url}/health", timeout=10.0)
            if response.status_code in [200, 404]:  # 404 is OK as the endpoint might not exist
                print(f"✅ Successfully connected to {url} with SSL verification disabled")
                print(f"   Status: {response.status_code}")
                return True
            else:
                print(f"❌ Connection failed with status: {response.status_code}")
                print(f"   Response: {response.text[:200]}...")
                return False
                
    except Exception as e:
        print(f"❌ Connection failed with error: {str(e)}")
        return False


async def main():
    """Main entry point"""
    print("Phyllo API SSL Verification Fix Tool")
    print("===================================")
    
    # Check configuration
    await check_config()
    
    # Test connection
    success = await test_connection()
    
    # Print recommendations
    print("\n=== Recommendations ===")
    if success:
        print("1. The connection test was successful with SSL verification disabled.")
        print("2. Ensure the PhylloAPIService class uses 'self.disable_ssl_verification' consistently.")
        print("3. Make sure 'verify=not self.disable_ssl_verification' is used in all httpx.AsyncClient calls.")
    else:
        print("1. Update the API URL in appsettings.json to ensure it's correct.")
        print("2. Check network connectivity to the API endpoint.")
        print("3. If using a local API, ensure it's running and accessible.")
        print("4. Ensure 'testing.disable_ssl_verification' is set to true in appsettings.json.")


if __name__ == "__main__":
    asyncio.run(main())
