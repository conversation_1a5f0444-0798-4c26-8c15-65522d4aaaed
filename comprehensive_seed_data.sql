-- CreatorVerse Filter System - Comprehensive Seed Data
-- Based on Excel analysis and Phyllo API mapping

-- Insert filter groups for all platform/option combinations
INSERT INTO filter_catalog.filter_groups (name, option_for, channel, sort_order, description) VALUES
-- Instagram Creator Groups
('Demography & Identity', 'creator', 'instagram', 1, 'Basic demographic and identity filters for creators'),
('Performance Metrics', 'creator', 'instagram', 2, 'Engagement, growth, and performance indicators'),
('Content & Niche', 'creator', 'instagram', 3, 'Content categories, keywords, and niche classification'),
('Credibility & Platform', 'creator', 'instagram', 4, 'Verification status and platform credibility metrics'),

-- Instagram Audience Groups  
('Demography & Identity', 'audience', 'instagram', 1, 'Audience demographic and identity characteristics'),
('Interests & Behavior', 'audience', 'instagram', 2, 'Audience interests, behaviors, and preferences'),

-- YouTube Creator Groups
('Demography & Identity', 'creator', 'youtube', 1, 'Creator demographic and identity filters'),
('Performance Metrics', 'creator', 'youtube', 2, 'Subscriber growth, view metrics, and engagement'),
('Content & Niche', 'creator', 'youtube', 3, 'Video categories, keywords, and content classification'),
('Credibility & Platform', 'creator', 'youtube', 4, 'Channel verification and credibility indicators'),

-- YouTube Audience Groups
('Demography & Identity', 'audience', 'youtube', 1, 'Viewer demographic characteristics'),
('Interests & Behavior', 'audience', 'youtube', 2, 'Viewer interests and engagement patterns'),

-- TikTok Creator Groups
('Demography & Identity', 'creator', 'tiktok', 1, 'Creator demographic and identity filters'),
('Performance Metrics', 'creator', 'tiktok', 2, 'Followers, likes, views, and engagement'),
('Content & Niche', 'creator', 'tiktok', 3, 'Content categories and trending topics'),
('Credibility & Platform', 'creator', 'tiktok', 4, 'Account verification and credibility'),

-- TikTok Audience Groups
('Demography & Identity', 'audience', 'tiktok', 1, 'Audience demographic characteristics'),
('Interests & Behavior', 'audience', 'tiktok', 2, 'Audience interests and trending behaviors');

-- Insert location hierarchy (sample data)
INSERT INTO filter_catalog.location_hierarchy (name, code, level, tier, population) VALUES
-- Countries
('India', 'IN', 0, 'Country', **********),
('United States', 'US', 0, 'Country', *********),
('United Kingdom', 'GB', 0, 'Country', ********),
('Canada', 'CA', 0, 'Country', ********),

-- India - Tier 1 cities
('Mumbai', 'MUM', 1, 'Tier1', ********),
('Delhi', 'DEL', 1, 'Tier1', ********),
('Bangalore', 'BLR', 1, 'Tier1', ********),
('Hyderabad', 'HYD', 1, 'Tier1', ********),
('Chennai', 'CHE', 1, 'Tier1', 9000000),
('Kolkata', 'KOL', 1, 'Tier1', 8000000),
('Pune', 'PUN', 1, 'Tier1', 7000000),
('Ahmedabad', 'AHM', 1, 'Tier1', 6000000),

-- India - Tier 2 cities  
('Surat', 'SUR', 1, 'Tier2', 5000000),
('Jaipur', 'JAI', 1, 'Tier2', 4500000),
('Lucknow', 'LKO', 1, 'Tier2', 4000000),
('Kanpur', 'KAN', 1, 'Tier2', 3500000),
('Nagpur', 'NAG', 1, 'Tier2', 3000000),
('Indore', 'IND', 1, 'Tier2', 2800000),
('Bhopal', 'BHO', 1, 'Tier2', 2500000),
('Visakhapatnam', 'VIS', 1, 'Tier2', 2300000);

-- Now add the filter definitions for each platform and option type
-- This will be done through a series of WITH statements to get group IDs and insert filters

-- Instagram Creator Filters
WITH demo_group AS (
    SELECT id FROM filter_catalog.filter_groups 
    WHERE name = 'Demography & Identity' AND option_for = 'creator' AND channel = 'instagram'
),
perf_group AS (
    SELECT id FROM filter_catalog.filter_groups 
    WHERE name = 'Performance Metrics' AND option_for = 'creator' AND channel = 'instagram'
),
content_group AS (
    SELECT id FROM filter_catalog.filter_groups 
    WHERE name = 'Content & Niche' AND option_for = 'creator' AND channel = 'instagram'
),
cred_group AS (
    SELECT id FROM filter_catalog.filter_groups 
    WHERE name = 'Credibility & Platform' AND option_for = 'creator' AND channel = 'instagram'
)
INSERT INTO filter_catalog.filter_definitions 
(group_id, name, filter_type, icon, has_minmax, has_enter_value, has_search_box, placeholder, options, api_field, sort_order) 
SELECT * FROM (
    -- Demography & Identity Filters
    SELECT demo_group.id, 'Gender', 'radio-button', 'gender-icon', false, false, false, 'Select Gender', 
    '[
        {"label": "Any", "value": "any", "description": "All genders"},
        {"label": "Male", "value": "male", "description": "Male creators"},
        {"label": "Female", "value": "female", "description": "Female creators"},
        {"label": "Other", "value": "other", "description": "Non-binary and other"}
    ]'::jsonb, 'creator.gender', 1
    FROM demo_group
    
    UNION ALL
    
    SELECT demo_group.id, 'Age', 'checkbox', 'age-icon', true, false, false, 'Select Age Range', 
    '[
        {"label": "Teen (13-19)", "value": "13-19", "description": "13-19 years"},
        {"label": "Young Adult (20-35)", "value": "20-35", "description": "20-35 years"},
        {"label": "Adult (36-55)", "value": "36-55", "description": "36-55 years"},
        {"label": "Senior (56+)", "value": "56+", "description": "56+ years"}
    ]'::jsonb, 'creator.age_range', 2
    FROM demo_group
    
    UNION ALL
    
    SELECT demo_group.id, 'Location', 'multilevel-checkbox', 'location-icon', false, false, true, 'Search Location', 
    '[
        {"label": "Tier 1", "value": "tier1", "description": "Metro cities"},
        {"label": "Tier 2", "value": "tier2", "description": "Secondary cities"},
        {"label": "Tier 3", "value": "tier3", "description": "Smaller cities"},
        {"label": "Rural Areas", "value": "rural", "description": "Rural areas"}
    ]'::jsonb, 'creator.location.tier', 3
    FROM demo_group
    
    UNION ALL
    
    SELECT demo_group.id, 'Language', 'checkbox', 'language-icon', false, false, true, 'Search Language', 
    '[
        {"label": "English", "value": "en", "description": "English"},
        {"label": "Hindi", "value": "hi", "description": "Hindi"},
        {"label": "Spanish", "value": "es", "description": "Spanish"},
        {"label": "French", "value": "fr", "description": "French"},
        {"label": "German", "value": "de", "description": "German"},
        {"label": "Italian", "value": "it", "description": "Italian"},
        {"label": "Japanese", "value": "ja", "description": "Japanese"},
        {"label": "Portuguese", "value": "pt", "description": "Portuguese"}
    ]'::jsonb, 'creator.language', 4
    FROM demo_group
    
    UNION ALL
    
    -- Performance Metrics Filters
    SELECT perf_group.id, 'Follower Count', 'checkbox', 'follower-icon', true, false, false, 'Select Follower Range', 
    '[
        {"label": "Nano (1K-10K)", "value": "1000-10000", "description": "1,000 to 10,000 followers"},
        {"label": "Micro (10K-100K)", "value": "10000-100000", "description": "10,000 to 100,000 followers"},
        {"label": "Mid (100K-500K)", "value": "100000-500000", "description": "100,000 to 500,000 followers"},
        {"label": "Macro (500K-1M)", "value": "500000-1000000", "description": "500,000 to 1 million followers"},
        {"label": "Mega (1M+)", "value": "1000000+", "description": "Over 1 million followers"}
    ]'::jsonb, 'metrics.follower_count', 1
    FROM perf_group
    
    UNION ALL
    
    SELECT perf_group.id, 'Engagement Rate', 'checkbox', 'engagement-icon', true, false, false, 'Select Engagement Rate', 
    '[
        {"label": "Low (<2%)", "value": "0-2", "description": "Below 2%"},
        {"label": "Average (2%-5%)", "value": "2-5", "description": "2% to 5%"},
        {"label": "High (5%-10%)", "value": "5-10", "description": "5% to 10%"},
        {"label": "Very High (10%+)", "value": "10+", "description": "Above 10%"}
    ]'::jsonb, 'metrics.engagement_rate', 2
    FROM perf_group
    
    UNION ALL
    
    SELECT perf_group.id, 'Average Likes', 'checkbox', 'likes-icon', true, false, false, 'Select Average Likes', 
    '[
        {"label": "Low (100-500)", "value": "100-500", "description": "100-500 likes"},
        {"label": "Medium (501-5K)", "value": "501-5000", "description": "501-5,000 likes"},
        {"label": "High (5K+)", "value": "5001+", "description": "Over 5,000 likes"}
    ]'::jsonb, 'metrics.avg_likes', 3
    FROM perf_group
    
    UNION ALL
    
    SELECT perf_group.id, 'Average Comments', 'checkbox', 'comments-icon', true, false, false, 'Select Average Comments', 
    '[
        {"label": "Low (10-50)", "value": "10-50", "description": "10-50 comments"},
        {"label": "Medium (51-500)", "value": "51-500", "description": "51-500 comments"},
        {"label": "High (500+)", "value": "501+", "description": "Over 500 comments"}
    ]'::jsonb, 'metrics.avg_comments', 4
    FROM perf_group
    
    UNION ALL
    
    SELECT perf_group.id, 'Reel Views', 'checkbox', 'reel-icon', true, false, false, 'Select Reel Views', 
    '[
        {"label": "Low (1K-5K)", "value": "1000-5000", "description": "1K-5K views"},
        {"label": "Medium (5K-50K)", "value": "5001-50000", "description": "5K-50K views"},
        {"label": "High (50K-500K)", "value": "50001-500000", "description": "50K-500K views"},
        {"label": "Very High (500K+)", "value": "500001+", "description": "Over 500K views"}
    ]'::jsonb, 'metrics.reel_views', 5
    FROM perf_group
    
    UNION ALL
    
    SELECT perf_group.id, 'Follower Growth', 'multilevel-checkbox', 'growth-icon', false, false, false, 'Select Growth Rate', 
    '[
        {
            "subOptionName": "Percentage",
            "subOptionType": "checkbox",
            "checkboxEnabled": false,
            "collapsed": false,
            "subOptions": [
                {"label": "Negative (<0%)", "value": "negative", "description": "Declining followers"},
                {"label": "Slow (0%-2%)", "value": "0-2", "description": "0%-2% growth"},
                {"label": "Moderate (2%-5%)", "value": "2-5", "description": "2%-5% growth"},
                {"label": "Rapid (5%+)", "value": "5+", "description": "Over 5% growth"}
            ]
        },
        {
            "subOptionName": "Time Period",
            "subOptionType": "radio-button",
            "checkboxEnabled": false,
            "collapsed": false,
            "subOptions": [
                {"label": "Last 1 Month", "value": "30", "description": "30 days"},
                {"label": "Last 3 Months", "value": "90", "description": "90 days"},
                {"label": "Last 6 Months", "value": "180", "description": "180 days"},
                {"label": "Last 12 Months", "value": "365", "description": "365 days"}
            ]
        }
    ]'::jsonb, 'metrics.follower_growth', 6
    FROM perf_group
    
    UNION ALL
    
    SELECT perf_group.id, 'Sponsored Content', 'radio-button', 'sponsored-icon', false, false, false, 'Select Content Type', 
    '[
        {"label": "Any", "value": "any", "description": "All content"},
        {"label": "Sponsored", "value": "true", "description": "Sponsored content only"},
        {"label": "Non-Sponsored", "value": "false", "description": "Organic content only"}
    ]'::jsonb, 'content.has_sponsored', 7
    FROM perf_group
    
    UNION ALL
    
    -- Content & Niche Filters
    SELECT content_group.id, 'Category', 'checkbox', 'category-icon', false, false, true, 'Search Categories', 
    '[
        {"label": "Fashion & Style", "value": "fashion", "description": "Fashion, clothing, and style"},
        {"label": "Travel & Tourism", "value": "travel", "description": "Travel, destinations, tourism"},
        {"label": "Food & Cooking", "value": "food", "description": "Food, recipes, culinary content"},
        {"label": "Fitness & Health", "value": "fitness", "description": "Fitness, wellness, health"},
        {"label": "Technology", "value": "tech", "description": "Technology, gadgets, innovation"},
        {"label": "Beauty & Cosmetics", "value": "beauty", "description": "Beauty, makeup, skincare"},
        {"label": "Entertainment", "value": "entertainment", "description": "Movies, music, shows"},
        {"label": "Education", "value": "education", "description": "Learning, tutorials, knowledge"},
        {"label": "Gaming", "value": "gaming", "description": "Video games, gaming content"},
        {"label": "Business", "value": "business", "description": "Business, entrepreneurship"}
    ]'::jsonb, 'content.category', 1
    FROM content_group
    
    UNION ALL
    
    SELECT content_group.id, 'Keywords', 'enter-value', 'keywords-icon', false, true, false, 'Enter keywords separated by commas', 
    '[]'::jsonb, 'content.keywords', 2
    FROM content_group
    
    UNION ALL
    
    SELECT content_group.id, 'Hashtags', 'enter-value', 'hashtags-icon', false, true, false, 'Enter hashtags separated by commas', 
    '[]'::jsonb, 'content.hashtags', 3
    FROM content_group
    
    UNION ALL
    
    SELECT content_group.id, 'Mentions', 'enter-value', 'mentions-icon', false, true, false, 'Enter mentions separated by commas', 
    '[]'::jsonb, 'content.mentions', 4
    FROM content_group
    
    UNION ALL
    
    -- Credibility & Platform Filters
    SELECT cred_group.id, 'Verification', 'radio-button', 'verified-icon', false, false, false, 'Select Verification Status', 
    '[
        {"label": "Any", "value": "any", "description": "All accounts"},
        {"label": "Verified", "value": "verified", "description": "Verified accounts only"},
        {"label": "Unverified", "value": "unverified", "description": "Unverified accounts only"}
    ]'::jsonb, 'creator.is_verified', 1
    FROM cred_group
    
    UNION ALL
    
    SELECT cred_group.id, 'Creatorverse Score', 'range-slider', 'score-icon', true, false, false, 'Select Score Range', 
    '[
        {"label": "Lower (0-4)", "value": "0-4", "description": "0-4 score"},
        {"label": "Medium (5-7)", "value": "5-7", "description": "5-7 score"},
        {"label": "Higher (8-10)", "value": "8-10", "description": "8-10 score"}
    ]'::jsonb, 'creator.creatorverse_score', 2
    FROM cred_group
) AS filter_data;

-- Instagram Audience Filters
WITH demo_group AS (
    SELECT id FROM filter_catalog.filter_groups 
    WHERE name = 'Demography & Identity' AND option_for = 'audience' AND channel = 'instagram'
),
interest_group AS (
    SELECT id FROM filter_catalog.filter_groups 
    WHERE name = 'Interests & Behavior' AND option_for = 'audience' AND channel = 'instagram'
)
INSERT INTO filter_catalog.filter_definitions 
(group_id, name, filter_type, icon, has_minmax, has_enter_value, has_search_box, placeholder, options, api_field, sort_order) 
SELECT * FROM (
    -- Audience Demographics
    SELECT demo_group.id, 'Gender', 'radio-button', 'gender-icon', false, false, false, 'Select Gender', 
    '[
        {"label": "Any", "value": "any", "description": "All genders"},
        {"label": "Male", "value": "male", "description": "Male audience"},
        {"label": "Female", "value": "female", "description": "Female audience"},
        {"label": "Mixed", "value": "mixed", "description": "Mixed gender audience"}
    ]'::jsonb, 'audience.demographics.gender', 1
    FROM demo_group
    
    UNION ALL
    
    SELECT demo_group.id, 'Age', 'checkbox', 'age-icon', true, false, false, 'Select Age Range', 
    '[
        {"label": "Teen (13-19)", "value": "13-19", "description": "13-19 years"},
        {"label": "Young Adult (20-35)", "value": "20-35", "description": "20-35 years"},
        {"label": "Adult (36-55)", "value": "36-55", "description": "36-55 years"},
        {"label": "Senior (56+)", "value": "56+", "description": "56+ years"}
    ]'::jsonb, 'audience.demographics.age_ranges', 2
    FROM demo_group
    
    UNION ALL
    
    SELECT demo_group.id, 'Location', 'multilevel-checkbox', 'location-icon', false, false, true, 'Search Location', 
    '[
        {"label": "Tier 1", "value": "tier1", "description": "Metro cities"},
        {"label": "Tier 2", "value": "tier2", "description": "Secondary cities"},
        {"label": "Tier 3", "value": "tier3", "description": "Smaller cities"},
        {"label": "Rural Areas", "value": "rural", "description": "Rural areas"}
    ]'::jsonb, 'audience.demographics.locations', 3
    FROM demo_group
    
    UNION ALL
    
    SELECT demo_group.id, 'Language', 'checkbox', 'language-icon', false, false, true, 'Search Language', 
    '[
        {"label": "English", "value": "en", "description": "English"},
        {"label": "Hindi", "value": "hi", "description": "Hindi"},
        {"label": "Spanish", "value": "es", "description": "Spanish"},
        {"label": "French", "value": "fr", "description": "French"}
    ]'::jsonb, 'audience.demographics.languages', 4
    FROM demo_group
    
    UNION ALL
    
    -- Audience Interests
    SELECT interest_group.id, 'Interests', 'checkbox', 'interests-icon', false, false, true, 'Search Interests', 
    '[
        {"label": "Fashion", "value": "fashion", "description": "Fashion and style"},
        {"label": "Technology", "value": "technology", "description": "Technology and gadgets"},
        {"label": "Travel", "value": "travel", "description": "Travel and tourism"},
        {"label": "Food", "value": "food", "description": "Food and cooking"},
        {"label": "Fitness", "value": "fitness", "description": "Fitness and health"},
        {"label": "Entertainment", "value": "entertainment", "description": "Entertainment and media"}
    ]'::jsonb, 'audience.interests', 1
    FROM interest_group
    
    UNION ALL
    
    SELECT interest_group.id, 'Brand Affinity', 'checkbox', 'brand-icon', false, false, true, 'Search Brands', 
    '[]'::jsonb, 'audience.brand_affinity', 2
    FROM interest_group
) AS audience_filter_data;

-- Add similar filter definitions for YouTube and TikTok platforms
-- (This would follow the same pattern but adapted for platform-specific metrics)

SELECT 'Comprehensive filter data seeded successfully!' as status;
