{"instagram_creator": [{"optionName": "Demography & Identity", "optionFor": "creator", "channel": "instagram", "filters": [{"name": "Gender", "type": "radio-button", "icon": "gender-icon", "minmax": false, "enterValue": false, "placeholder": "Select Gender", "sort_order": 1, "options": [{"label": "Any", "value": "any", "description": "All genders"}, {"label": "Male", "value": "male", "description": "Male creators"}, {"label": "Female", "value": "female", "description": "Female creators"}, {"label": "Other", "value": "other", "description": "Non-binary and other"}]}, {"name": "Age", "type": "checkbox", "icon": "age-icon", "minmax": true, "enterValue": false, "placeholder": "Select Age Range", "sort_order": 2, "options": [{"label": "Teen (13-19)", "value": "13-19", "description": "13-19 years"}, {"label": "Young Adult (20-35)", "value": "20-35", "description": "20-35 years"}, {"label": "Adult (36-55)", "value": "36-55", "description": "36-55 years"}, {"label": "Senior (56+)", "value": "56+", "description": "56+ years"}]}, {"name": "Location", "type": "multilevel-checkbox", "icon": "location-icon", "minmax": false, "enterValue": false, "placeholder": "Search Location", "sort_order": 3, "searchBox": true, "options": [{"subOptionName": "Tier 1", "subOptionType": "checkbox", "collapsed": true, "checkboxEnabled": true, "subOptions": [{"label": "Ahmedabad", "value": "ahmedabad", "description": ""}, {"label": "Bangalore", "value": "bangalore", "description": ""}, {"label": "Chennai", "value": "chennai", "description": ""}, {"label": "Delhi", "value": "delhi", "description": ""}, {"label": "Hyderabad", "value": "hyderabad", "description": ""}, {"label": "Kolkata", "value": "kolkata", "description": ""}, {"label": "Mumbai", "value": "mumbai", "description": ""}, {"label": "Pune", "value": "pune", "description": ""}]}, {"subOptionName": "Tier 2", "subOptionType": "checkbox", "collapsed": true, "checkboxEnabled": true, "subOptions": [{"label": "Bhopal", "value": "bhopal", "description": ""}, {"label": "Coimbatore", "value": "coimbatore", "description": ""}, {"label": "Indore", "value": "indore", "description": ""}, {"label": "Jaipur", "value": "jaipur", "description": ""}, {"label": "Kanpur", "value": "kanpur", "description": ""}, {"label": "Lucknow", "value": "lucknow", "description": ""}, {"label": "Nagpur", "value": "nagpur", "description": ""}, {"label": "<PERSON><PERSON>", "value": "patna", "description": ""}, {"label": "Surat", "value": "surat", "description": ""}, {"label": "Visakhapatnam", "value": "visakhapatnam", "description": ""}]}, {"subOptionName": "Tier 3 or Rural", "subOptionType": "checkbox", "collapsed": true, "checkboxEnabled": true, "subOptions": [{"label": "Aligarh", "value": "aligarh", "description": ""}, {"label": "Bhavnagar", "value": "bhavnagar", "description": ""}, {"label": "<PERSON><PERSON>", "value": "gaya", "description": ""}, {"label": "Gorakhpur", "value": "gorakhpur", "description": ""}, {"label": "Saharanpur", "value": "saharanpur", "description": ""}]}]}, {"name": "Language", "type": "checkbox", "icon": "language-icon", "minmax": false, "enterValue": false, "placeholder": "Search Language", "sort_order": 4, "searchBox": true, "options": [{"label": "English", "value": "en", "description": "English"}, {"label": "Hindi", "value": "hi", "description": "Hindi"}, {"label": "Spanish", "value": "es", "description": "Spanish"}, {"label": "French", "value": "fr", "description": "French"}, {"label": "German", "value": "de", "description": "German"}, {"label": "Italian", "value": "it", "description": "Italian"}]}]}, {"optionName": "Performance Metrics", "optionFor": "creator", "channel": "instagram", "filters": [{"name": "Follower Count", "type": "checkbox", "icon": "follower-icon", "minmax": true, "enterValue": false, "placeholder": "Select Follower Range", "sort_order": 1, "options": [{"label": "Nano (1K-10K)", "value": "1000-10000", "description": "1,000 to 10,000 followers"}, {"label": "Micro (10K-100K)", "value": "10000-100000", "description": "10,000 to 100,000 followers"}, {"label": "Mid (100K-500K)", "value": "100000-500000", "description": "100,000 to 500,000 followers"}, {"label": "Macro (500K-1M)", "value": "500000-1000000", "description": "500,000 to 1 million followers"}, {"label": "Mega (1M+)", "value": "1000000+", "description": "Over 1 million followers"}]}, {"name": "Engagement Rate", "type": "checkbox", "icon": "engagement-icon", "minmax": true, "enterValue": false, "placeholder": "Select Engagement Rate", "sort_order": 2, "options": [{"label": "Low (<2%)", "value": "0-2", "description": "Below 2%"}, {"label": "Average (2%-5%)", "value": "2-5", "description": "2% to 5%"}, {"label": "High (5%-10%)", "value": "5-10", "description": "5% to 10%"}, {"label": "Very High (10%+)", "value": "10+", "description": "Above 10%"}]}, {"name": "Average Likes", "type": "checkbox", "icon": "likes-icon", "minmax": true, "enterValue": false, "placeholder": "Select Average Likes", "sort_order": 3, "options": [{"label": "Low (100-500)", "value": "100-500", "description": "100-500 likes"}, {"label": "Medium (501-5K)", "value": "501-5000", "description": "501-5,000 likes"}, {"label": "High (5K+)", "value": "5001+", "description": "Over 5,000 likes"}]}, {"name": "Average Comments", "type": "checkbox", "icon": "comments-icon", "minmax": true, "enterValue": false, "placeholder": "Select Average Comments", "sort_order": 4, "options": [{"label": "Low (10-50)", "value": "10-50", "description": "10-50 comments"}, {"label": "Medium (51-500)", "value": "51-500", "description": "51-500 comments"}, {"label": "High (500+)", "value": "501+", "description": "Over 500 comments"}]}, {"name": "Reel Views", "type": "checkbox", "icon": "reel-icon", "minmax": true, "enterValue": false, "placeholder": "Select Reel Views", "sort_order": 5, "options": [{"label": "Low (1K-5K)", "value": "1000-5000", "description": "1K-5K views"}, {"label": "Medium (5K-50K)", "value": "5001-50000", "description": "5K-50K views"}, {"label": "High (50K-500K)", "value": "50001-500000", "description": "50K-500K views"}, {"label": "Very High (500K+)", "value": "500001+", "description": "Over 500K views"}]}]}, {"optionName": "Content & Niche", "optionFor": "creator", "channel": "instagram", "filters": [{"name": "Category", "type": "checkbox", "icon": "category-icon", "minmax": false, "enterValue": false, "placeholder": "Search Categories", "sort_order": 1, "searchBox": true, "options": [{"label": "Fashion & Style", "value": "fashion", "description": "Fashion, clothing, and style"}, {"label": "Travel & Tourism", "value": "travel", "description": "Travel, destinations, tourism"}, {"label": "Food & Cooking", "value": "food", "description": "Food, recipes, culinary content"}, {"label": "Fitness & Health", "value": "fitness", "description": "Fitness, wellness, health"}, {"label": "Technology", "value": "tech", "description": "Technology, gadgets, innovation"}, {"label": "Beauty & Cosmetics", "value": "beauty", "description": "Beauty, makeup, skincare"}, {"label": "Entertainment", "value": "entertainment", "description": "Movies, music, shows"}, {"label": "Education", "value": "education", "description": "Learning, tutorials, knowledge"}, {"label": "Gaming", "value": "gaming", "description": "Video games, gaming content"}, {"label": "Business", "value": "business", "description": "Business, entrepreneurship"}]}, {"name": "Keywords", "type": "enter-value", "icon": "keywords-icon", "minmax": false, "enterValue": true, "placeholder": "Enter keywords separated by commas", "sort_order": 2}, {"name": "Hashtags", "type": "enter-value", "icon": "hashtags-icon", "minmax": false, "enterValue": true, "placeholder": "Enter hashtags separated by commas", "sort_order": 3}, {"name": "Mentions", "type": "enter-value", "icon": "mentions-icon", "minmax": false, "enterValue": true, "placeholder": "Enter mentions separated by commas", "sort_order": 4}]}, {"optionName": "Credibility & Platform", "optionFor": "creator", "channel": "instagram", "filters": [{"name": "Verification", "type": "radio-button", "icon": "verified-icon", "minmax": false, "enterValue": false, "placeholder": "Select Verification Status", "sort_order": 1, "options": [{"label": "Any", "value": "any", "description": "All accounts"}, {"label": "Verified", "value": "verified", "description": "Verified accounts only"}, {"label": "Unverified", "value": "unverified", "description": "Unverified accounts only"}]}, {"name": "Creatorverse Score", "type": "range-slider", "icon": "score-icon", "minmax": true, "enterValue": false, "placeholder": "Select Score Range", "sort_order": 2, "options": [{"label": "Lower (0-4)", "value": "0-4", "description": "0-4 score"}, {"label": "Medium (5-7)", "value": "5-7", "description": "5-7 score"}, {"label": "Higher (8-10)", "value": "8-10", "description": "8-10 score"}]}]}], "youtube_creator": [{"optionName": "Demography & Identity", "optionFor": "creator", "channel": "youtube", "filters": [{"name": "Gender", "type": "radio-button", "icon": "gender-icon", "minmax": false, "enterValue": false, "placeholder": "Select Gender", "sort_order": 0, "options": [{"label": "Male", "value": "male", "description": ""}, {"label": "Female", "value": "female", "description": ""}, {"label": "Other", "value": "other", "description": ""}]}, {"name": "Age", "type": "checkbox", "icon": "age-icon", "minmax": true, "enterValue": false, "placeholder": "Select Age Range", "sort_order": 1, "options": [{"label": "Teen", "value": "13-19", "description": "13-19"}, {"label": "Young Adult", "value": "20-35", "description": "20-35"}, {"label": "Adult", "value": "36-55", "description": "36-55"}, {"label": "Senior", "value": "56+", "description": "56+"}]}, {"name": "Location", "type": "multilevel-checkbox", "icon": "location-icon", "minmax": false, "enterValue": false, "placeholder": "Search Location", "sort_order": 2, "searchBox": true, "options": [{"subOptionName": "Tier 1", "subOptionType": "checkbox", "collapsed": true, "checkboxEnabled": true, "subOptions": [{"label": "Ahmedabad", "value": "ahmedabad", "description": ""}, {"label": "Bangalore", "value": "bangalore", "description": ""}, {"label": "Chennai", "value": "chennai", "description": ""}, {"label": "Delhi", "value": "delhi", "description": ""}, {"label": "Hyderabad", "value": "hyderabad", "description": ""}, {"label": "Kolkata", "value": "kolkata", "description": ""}, {"label": "Mumbai", "value": "mumbai", "description": ""}, {"label": "Pune", "value": "pune", "description": ""}]}, {"subOptionName": "Tier 2", "subOptionType": "checkbox", "collapsed": true, "checkboxEnabled": true, "subOptions": [{"label": "Bhopal", "value": "bhopal", "description": ""}, {"label": "Coimbatore", "value": "coimbatore", "description": ""}, {"label": "Indore", "value": "indore", "description": ""}, {"label": "Jaipur", "value": "jaipur", "description": ""}, {"label": "Kanpur", "value": "kanpur", "description": ""}, {"label": "Lucknow", "value": "lucknow", "description": ""}, {"label": "Nagpur", "value": "nagpur", "description": ""}, {"label": "<PERSON><PERSON>", "value": "patna", "description": ""}, {"label": "Surat", "value": "surat", "description": ""}, {"label": "Visakhapatnam", "value": "visakhapatnam", "description": ""}]}, {"subOptionName": "Tier 3 or Rural", "subOptionType": "checkbox", "collapsed": true, "checkboxEnabled": true, "subOptions": [{"label": "Aligarh", "value": "aligarh", "description": ""}, {"label": "Bhavnagar", "value": "bhavnagar", "description": ""}, {"label": "<PERSON><PERSON>", "value": "gaya", "description": ""}, {"label": "Gorakhpur", "value": "gorakhpur", "description": ""}, {"label": "Saharanpur", "value": "saharanpur", "description": ""}]}]}, {"name": "Language", "type": "checkbox", "icon": "language-icon", "minmax": false, "enterValue": false, "placeholder": "Search Language", "sort_order": 3, "searchBox": true, "options": [{"label": "English", "value": "english", "description": ""}, {"label": "Hindi", "value": "hindi", "description": ""}, {"label": "Spanish", "value": "spanish", "description": ""}, {"label": "French", "value": "french", "description": ""}]}]}, {"optionName": "Performance Metrics", "optionFor": "creator", "channel": "youtube", "filters": [{"name": "Subscriber Count", "type": "checkbox", "icon": "subscriber-icon", "minmax": true, "enterValue": false, "placeholder": "Select Subscriber Count", "sort_order": 0, "options": [{"label": "<PERSON><PERSON>", "value": "1000-10000", "description": "1k-10K"}, {"label": "Micro", "value": "10000-100000", "description": "10K-100K"}, {"label": "Mid", "value": "100000-1000000", "description": "100K-1M"}, {"label": "Macro", "value": "1000000-", "description": "1M+"}]}, {"name": "Average Views", "type": "checkbox", "icon": "views-icon", "minmax": true, "enterValue": false, "placeholder": "Select Average Views", "sort_order": 1, "options": [{"label": "Very High", "value": "500001+", "description": "500001+"}, {"label": "High", "value": "50001-500000", "description": "50001-500000"}, {"label": "Medium", "value": "5001-50000", "description": "5001-50000"}, {"label": "Low", "value": "1000-5000", "description": "1000-5000"}]}]}, {"optionName": "Content & Niche", "optionFor": "creator", "channel": "youtube", "filters": [{"name": "Category", "type": "checkbox", "icon": "category-icon", "minmax": false, "enterValue": false, "placeholder": "Select Category", "sort_order": 0, "options": [{"label": "Fashion", "value": "fashion", "description": ""}, {"label": "Travel", "value": "travel", "description": ""}, {"label": "Food", "value": "food", "description": ""}, {"label": "Fitness", "value": "fitness", "description": ""}]}]}, {"optionName": "Credibility & Platform", "optionFor": "creator", "channel": "youtube", "filters": [{"name": "Official", "type": "radio-button", "icon": "official-icon", "minmax": false, "enterValue": false, "placeholder": "", "sort_order": 0, "options": [{"label": "Any", "value": "any", "description": ""}, {"label": "Official", "value": "official", "description": ""}, {"label": "Unofficial", "value": "unofficial", "description": ""}]}]}], "instagram_audience": [{"optionName": "Interests", "optionFor": "audience", "channel": "instagram", "filters": [{"name": "Interests", "type": "enter-value", "icon": "interests-icon", "minmax": false, "enterValue": true, "placeholder": "Enter Interests", "sort_order": 0}, {"name": "Brand Affinities", "type": "enter-value", "icon": "brand-icon", "minmax": false, "enterValue": true, "placeholder": "Enter Brand Affinities", "sort_order": 1}]}, {"optionName": "Demography & Identity", "optionFor": "audience", "channel": "instagram", "filters": [{"name": "Gender", "type": "radio-button", "icon": "gender-icon", "minmax": false, "enterValue": false, "placeholder": "Select Gender", "sort_order": 0, "options": [{"label": "Male", "value": "male", "description": ""}, {"label": "Female", "value": "female", "description": ""}, {"label": "Other", "value": "other", "description": ""}]}, {"name": "Age", "type": "checkbox", "icon": "age-icon", "minmax": true, "enterValue": false, "placeholder": "Select Age", "sort_order": 1, "options": [{"label": "Teen", "value": "13-19", "description": "13-19"}, {"label": "Young Adult", "value": "20-35", "description": "20-35"}, {"label": "Adult", "value": "36-55", "description": "36-55"}, {"label": "Senior", "value": "56+", "description": "56+"}]}]}], "statistics": {"total_groups": 19, "total_definitions": 33, "by_platform": {"instagram": 7, "youtube": 6, "tiktok": 6}, "by_option_for": {"creator": 12, "audience": 7}}}