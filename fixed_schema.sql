-- CreatorVerse Filter System - Clean Schema
-- Fixed version without problematic DO blocks

-- Set client messages to reduce noise
SET client_min_messages = WARNING;

-- Drop and recreate schema
DROP SCHEMA IF EXISTS filter_catalog CASCADE;
CREATE SCHEMA filter_catalog AUTHOR<PERSON><PERSON><PERSON>ION postgres;
GRANT ALL ON SCHEMA filter_catalog TO postgres;

-- <PERSON>reate enum types
CREATE TYPE filter_catalog.data_type_e AS ENUM (
    'range', 'enum', 'multi_enum', 'boolean', 'keyword', 'location', 'date_range'
);

CREATE TYPE filter_catalog.filter_type AS ENUM (
    'radio-button', 'checkbox', 'multilevel-checkbox', 'enter-value',
    'range-slider', 'date-picker', 'search-dropdown'
);

CREATE TYPE filter_catalog.option_for_type AS ENUM (
    'creator', 'audience'
);

CREATE TYPE filter_catalog.platform_type AS ENUM (
    'instagram', 'youtube', 'tiktok'
);

CREATE TYPE filter_catalog.provider_e AS ENUM (
    'phyllo_v1', 'phyllo_v2', 'modash', 'custom'
);

CREATE TYPE filter_catalog.validation_type_e AS ENUM (
    'none', 'min_max', 'regex', 'length', 'custom'
);

-- Create filter_groups table
CREATE TABLE filter_catalog.filter_groups (
    id UUID DEFAULT gen_random_uuid() NOT NULL,
    name VARCHAR(100) NOT NULL,
    option_for filter_catalog.option_for_type DEFAULT 'creator' NOT NULL,
    channel filter_catalog.platform_type DEFAULT 'instagram' NOT NULL,
    sort_order SMALLINT DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    description TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT filter_groups_pkey PRIMARY KEY (id),
    CONSTRAINT uq_filter_group_context UNIQUE (name, option_for, channel)
);

-- Create filter_definitions table
CREATE TABLE filter_catalog.filter_definitions (
    id UUID DEFAULT gen_random_uuid() NOT NULL,
    group_id UUID NOT NULL,
    name VARCHAR(100) NOT NULL,
    filter_type filter_catalog.filter_type DEFAULT 'checkbox' NOT NULL,
    icon VARCHAR(50),
    has_minmax BOOLEAN DEFAULT false,
    has_enter_value BOOLEAN DEFAULT false,
    has_search_box BOOLEAN DEFAULT false,
    placeholder VARCHAR(200),
    options JSONB DEFAULT '[]',
    db_field VARCHAR(100),
    api_field VARCHAR(100),
    validation_rules JSONB DEFAULT '{}',
    sort_order SMALLINT DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT filter_definitions_pkey PRIMARY KEY (id),
    CONSTRAINT uq_filter_group_name UNIQUE (group_id, name),
    CONSTRAINT filter_definitions_group_id_fkey 
        FOREIGN KEY (group_id) REFERENCES filter_catalog.filter_groups(id) ON DELETE CASCADE
);

-- Create location_hierarchy table
CREATE TABLE filter_catalog.location_hierarchy (
    id UUID DEFAULT gen_random_uuid() NOT NULL,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(20),
    level INTEGER DEFAULT 0,
    parent_id UUID,
    population INTEGER,
    tier VARCHAR(10),
    coordinates JSONB,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT location_hierarchy_pkey PRIMARY KEY (id),
    CONSTRAINT uq_location_context UNIQUE (name, parent_id, level),
    CONSTRAINT location_hierarchy_parent_id_fkey 
        FOREIGN KEY (parent_id) REFERENCES filter_catalog.location_hierarchy(id) ON DELETE CASCADE
);

-- Create saved_filter_sets table
CREATE TABLE filter_catalog.saved_filter_sets (
    id UUID DEFAULT gen_random_uuid() NOT NULL,
    name VARCHAR(100) NOT NULL,
    channel filter_catalog.platform_type DEFAULT 'instagram' NOT NULL,
    option_for filter_catalog.option_for_type DEFAULT 'creator' NOT NULL,
    filter_values JSONB NOT NULL,
    user_id UUID,
    is_shared BOOLEAN DEFAULT false,
    share_code VARCHAR(50),
    usage_count INTEGER DEFAULT 0,
    tags JSONB DEFAULT '[]',
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT saved_filter_sets_pkey PRIMARY KEY (id),
    CONSTRAINT saved_filter_sets_share_code_key UNIQUE (share_code)
);

-- Create filter_usage_logs table
CREATE TABLE filter_catalog.filter_usage_logs (
    id UUID DEFAULT gen_random_uuid() NOT NULL,
    filter_definition_id UUID NOT NULL,
    user_id UUID,
    session_id VARCHAR(255),
    filter_value JSONB,
    result_count INTEGER,
    execution_time_ms INTEGER,
    success BOOLEAN DEFAULT true,
    error_message TEXT,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT filter_usage_logs_pkey PRIMARY KEY (id),
    CONSTRAINT filter_usage_logs_filter_id_fkey 
        FOREIGN KEY (filter_definition_id) REFERENCES filter_catalog.filter_definitions(id) ON DELETE CASCADE
);

-- Create indexes
CREATE INDEX idx_filter_groups_active ON filter_catalog.filter_groups(is_active, channel, option_for);
CREATE INDEX idx_filter_groups_sort ON filter_catalog.filter_groups(channel, option_for, sort_order);
CREATE INDEX idx_filter_definitions_active ON filter_catalog.filter_definitions(group_id, is_active);
CREATE INDEX idx_filter_definitions_sort ON filter_catalog.filter_definitions(group_id, sort_order);
CREATE INDEX idx_filter_definitions_type ON filter_catalog.filter_definitions(filter_type);
CREATE INDEX idx_location_hierarchy_level ON filter_catalog.location_hierarchy(level, is_active);
CREATE INDEX idx_location_hierarchy_parent ON filter_catalog.location_hierarchy(parent_id, is_active);
CREATE INDEX idx_saved_filter_sets_user ON filter_catalog.saved_filter_sets(user_id, channel);
CREATE INDEX idx_saved_filter_sets_shared ON filter_catalog.saved_filter_sets(is_shared, channel);
CREATE INDEX idx_filter_usage_logs_filter ON filter_catalog.filter_usage_logs(filter_definition_id, created_at);
CREATE INDEX idx_filter_usage_logs_user ON filter_catalog.filter_usage_logs(user_id, created_at);

-- Create update trigger function
CREATE OR REPLACE FUNCTION filter_catalog.update_updated_at()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$;

-- Create triggers for updated_at
CREATE TRIGGER trigger_filter_groups_updated_at
    BEFORE UPDATE ON filter_catalog.filter_groups
    FOR EACH ROW EXECUTE FUNCTION filter_catalog.update_updated_at();

CREATE TRIGGER trigger_filter_definitions_updated_at
    BEFORE UPDATE ON filter_catalog.filter_definitions
    FOR EACH ROW EXECUTE FUNCTION filter_catalog.update_updated_at();

CREATE TRIGGER trigger_location_hierarchy_updated_at
    BEFORE UPDATE ON filter_catalog.location_hierarchy
    FOR EACH ROW EXECUTE FUNCTION filter_catalog.update_updated_at();

CREATE TRIGGER trigger_saved_filter_sets_updated_at
    BEFORE UPDATE ON filter_catalog.saved_filter_sets
    FOR EACH ROW EXECUTE FUNCTION filter_catalog.update_updated_at();

-- Grant permissions
GRANT ALL ON SCHEMA filter_catalog TO postgres;
GRANT ALL ON ALL TABLES IN SCHEMA filter_catalog TO postgres;
GRANT ALL ON ALL SEQUENCES IN SCHEMA filter_catalog TO postgres;
GRANT ALL ON ALL FUNCTIONS IN SCHEMA filter_catalog TO postgres;

SELECT 'Schema created successfully!' as status;
