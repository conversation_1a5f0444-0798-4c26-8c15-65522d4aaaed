# Frontend Filter Transformation System

## 🔒 CRITICAL: Frontend Format is FIXED

**The frontend filter format will NEVER change.** This system is designed to accept the exact format that the frontend sends and transform it for any external API provider.

## 📋 Overview

This system provides a flexible, provider-agnostic filter transformation layer that:

1. ✅ Accepts the **FIXED** frontend filter format
2. ✅ Transforms filters to any external API provider format (Phyllo, Modash, etc.)
3. ✅ Provides standardized responses across all providers
4. ✅ Supports platform-specific field mappings (Instagram vs YouTube)
5. ✅ Includes mock data for testing and development

## 🏗️ Architecture

```
Frontend (FIXED Format) 
    ↓
FrontendFilterSelections Schema
    ↓
ExternalAPIService (Provider Manager)
    ↓
Specific Provider (Phyllo, Modash, etc.)
    ↓
Provider-Specific API Call
    ↓
Standardized CreatorProfile Response
```

## 📊 Frontend Format (FIXED)

The frontend always sends this exact structure:

```json
{
  "searchQuery": "optional text search",
  "filterSelections": {
    "channel": "instagram|youtube|tiktok",
    "optionFor": "creator|audience",
    "filters": {
      "gender": ["male", "female"],
      "follower_count": {"min": 1000, "max": 100000},
      "location": ["mumbai", "delhi"],
      "engagement_rate": {"min": 2.0, "max": 15.0},
      "category": ["lifestyle", "fashion"],
      "is_verified": true,
      "age": ["young_adult", "adult"]
    },
    "page": 1,
    "pageSize": 20,
    "sortBy": "follower_count",
    "sortOrder": "desc"
  },
  "includeExternal": true,
  "cachePreference": "prefer"
}
```

## 🔄 Filter Transformation

### Platform-Specific Mappings

Each provider has platform-specific field mappings:

#### Instagram Creator Filters
```python
{
    "gender": "creator_gender",
    "age": "creator_age", 
    "location": "creator_locations",
    "follower_count": "follower_count",
    "engagement_rate": "engagement_rate",
    "category": "creator_interests",
    "is_verified": "is_verified",
    "reel_views": "reel_views"  # Instagram-specific
}
```

#### YouTube Creator Filters
```python
{
    "gender": "creator_gender",
    "subscriber_count": "subscriber_count",  # YouTube-specific
    "average_views": "average_views",
    "category": "creator_interests"
}
```

### Value Transformation

The system handles different value types:

- **Range Values**: `{"min": 1000, "max": 100000}` → `{"min": 1000, "max": 100000}`
- **List Values**: `["male", "female"]` → `["male", "female"]`
- **Boolean Values**: `true` → `true`
- **Age Mapping**: `["young_adult"]` → `["20-35"]`

## 🚀 New API Endpoints

### 1. Frontend Search
```http
POST /api/v1/frontend/search-frontend
Content-Type: application/json

{
  "searchQuery": "lifestyle influencer",
  "filterSelections": {
    "channel": "instagram",
    "optionFor": "creator",
    "filters": {
      "gender": ["female"],
      "follower_count": {"min": 10000, "max": 500000}
    }
  }
}
```

### 2. Quick Search
```http
GET /api/v1/frontend/quick-search-frontend?query=sarah&platform=instagram&limit=5
```

### 3. Creator Analytics
```http
GET /api/v1/frontend/creator-analytics/{creator_id}?platform=instagram&provider=phyllo
```

### 4. Available Providers
```http
GET /api/v1/frontend/providers
```

## 🔌 Adding New API Providers

To add a new provider (e.g., Modash):

### 1. Create Provider Config
```python
class ModashConfig(APIProviderConfig):
    @property
    def provider_type(self) -> APIProviderType:
        return APIProviderType.MODASH
    
    def get_filter_mapping(self, platform, option_for):
        return {
            "gender": "creator_gender_modash",
            "follower_count": "followers"
            # Modash-specific mappings
        }
```

### 2. Implement Provider
```python
class ModashProvider(ExternalAPIProvider):
    def transform_filters(self, filter_selections):
        # Convert frontend format to Modash API format
        pass
    
    async def search_creators(self, filter_selections):
        # Make Modash API calls
        pass
```

### 3. Register Provider
```python
APIProviderFactory.register_provider(APIProviderType.MODASH, ModashProvider)
```

## 🧪 Testing

Run the test suite to validate the implementation:

```bash
cd /home/<USER>/Desktop/workspace/creatorverse_services/creatorverse_discovery_and_profile_analytics
python test_frontend_filters.py
```

## 📁 File Structure

```
app/
├── schemas/external_api/
│   ├── frontend_schemas.py          # FIXED frontend format schemas
│   └── provider_interface.py       # Generic provider interface
├── services/
│   ├── external_api_service.py      # Main provider manager
│   └── external_providers/
│       └── phyllo_provider.py       # Phyllo implementation
└── api/api_v1/endpoints/
    └── frontend_discovery.py        # New frontend endpoints
```

## 🎯 Key Benefits

1. **Future-Proof**: Frontend format never changes
2. **Provider Agnostic**: Easy to add new API providers
3. **Platform Aware**: Handles Instagram vs YouTube differences
4. **Standardized**: Consistent response format
5. **Testable**: Mock data support for development
6. **Maintainable**: Clear separation of concerns

## 🔍 Example Usage

### Search with Filters
```python
# Frontend sends this exact format
frontend_request = {
    "filterSelections": {
        "channel": "instagram",
        "optionFor": "creator", 
        "filters": {
            "gender": ["female"],
            "follower_count": {"min": 10000, "max": 500000},
            "location": ["mumbai", "delhi"]
        }
    }
}

# System automatically transforms to Phyllo format:
phyllo_request = {
    "platform": "instagram",
    "creator_gender": ["female"],
    "follower_count": {"min": 10000, "max": 500000},
    "creator_locations": ["mumbai", "delhi"]
}
```

### Response Format
```json
{
  "success": true,
  "data": {
    "profiles": [
      {
        "id": "creator_123",
        "platform_username": "sarah_lifestyle",
        "full_name": "Sarah Johnson",
        "follower_count": 25000,
        "engagement_rate": 4.2,
        "data_source": "phyllo"
      }
    ],
    "metadata": {
      "total_count": 150,
      "page": 1,
      "execution_time_ms": 245,
      "data_sources": ["phyllo"]
    }
  }
}
```

## ⚠️ Important Notes

1. **Frontend Format**: Never modify the frontend filter structure
2. **Provider Independence**: Each provider should work independently
3. **Error Handling**: Graceful fallbacks when providers fail
4. **Caching**: Results are cached for performance
5. **Rate Limiting**: Each provider has its own rate limits

This system ensures that the CreatorVerse platform can easily integrate with any external API provider while maintaining a consistent interface for the frontend.
