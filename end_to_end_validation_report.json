{"database_validation": {"passed": true, "checks": {"has_filter_groups": true, "has_filter_definitions": true, "has_instagram_filters": true, "has_youtube_filters": true, "has_creator_filters": true, "has_audience_filters": true, "adequate_filter_count": true}, "statistics": {"total_groups": 19, "total_definitions": 33, "by_platform": {"instagram": 7, "youtube": 6, "tiktok": 6}, "by_option_for": {"creator": 12, "audience": 7}}}, "api_validation": {"passed": true, "results": {"instagram_creator": {"success": true, "filter_groups": 4, "total_filters": 15, "response_time_ms": 230.15}, "instagram_audience": {"success": true, "filter_groups": 2, "total_filters": 4, "response_time_ms": 80.57}, "youtube_creator": {"success": true, "filter_groups": 4, "total_filters": 8, "response_time_ms": 122.58}, "tiktok_creator": {"success": true, "filter_groups": 3, "total_filters": 6, "response_time_ms": 136.47}}, "success_rate": "4/4"}, "frontend_format_validation": {"passed": true, "checks": {"has_option_groups": true, "correct_group_structure": true, "correct_filter_structure": true, "proper_filter_types": true, "multilevel_structure_valid": true, "location_hierarchy_working": true}}, "performance_validation": {"passed": false, "cold_cache_ms": 84.00487899780273, "warm_cache_ms": 93.76192092895508, "checks": {"cold_cache_under_500ms": true, "warm_cache_under_50ms": false, "cache_improvement": false}}, "phyllo_integration_validation": {"passed": true, "checks": {"has_demographic_filters": true, "has_performance_filters": true, "has_content_filters": true, "has_api_field_mappings": true, "supports_multiple_platforms": true}}, "overall_status": "MOSTLY_PASSED"}