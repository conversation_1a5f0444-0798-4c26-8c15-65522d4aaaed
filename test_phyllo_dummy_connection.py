#!/usr/bin/env python3
"""
Test script to verify phyllo_dummy service endpoints are working correctly
"""
import asyncio
import aiohttp
import json
from datetime import datetime

async def test_phyllo_dummy_endpoints():
    """Test various endpoints on phyllo_dummy service"""
    base_url = "http://127.0.0.1:8001"
    
    # Test endpoints to try
    endpoints = [
        "/api/creators/search",
        "/search", 
        "/api/v1/creators/search",
        "/api/v1/profiles/search",
        "/creators/search",
        "/creators",
        "/api/quick-search",
        "/health",
        "/",
        "/docs"
    ]
    
    # Test data for POST requests
    test_data = {
        "platform": "instagram",
        "filters": {},
        "limit": 20,
        "offset": 0
    }
    
    quick_search_data = {
        "query": "test",
        "platform": "instagram", 
        "limit": 10
    }
    
    async with aiohttp.ClientSession() as session:
        print(f"Testing phyllo_dummy service at {base_url}")
        print("=" * 60)
        
        for endpoint in endpoints:
            url = f"{base_url}{endpoint}"
            
            # Try GET first
            try:
                async with session.get(url, timeout=10) as response:
                    print(f"GET {endpoint:30} -> {response.status} {response.reason}")
                    if response.status == 200:
                        try:
                            data = await response.json()
                            print(f"  ✓ JSON Response: {len(str(data))} chars")
                        except:
                            text = await response.text()
                            print(f"  ✓ Text Response: {len(text)} chars")
            except Exception as e:
                print(f"GET {endpoint:30} -> ERROR: {str(e)[:50]}")
            
            # Try POST for search endpoints
            if "search" in endpoint:
                try:
                    data_to_send = quick_search_data if "quick" in endpoint else test_data
                    async with session.post(url, json=data_to_send, timeout=10) as response:
                        print(f"POST {endpoint:29} -> {response.status} {response.reason}")
                        if response.status == 200:
                            try:
                                data = await response.json()
                                print(f"  ✓ JSON Response: {len(str(data))} chars")
                                if "data" in data:
                                    print(f"  ✓ Data array length: {len(data['data'])}")
                            except:
                                text = await response.text()
                                print(f"  ✓ Text Response: {len(text)} chars")
                except Exception as e:
                    print(f"POST {endpoint:29} -> ERROR: {str(e)[:50]}")
            
            print()  # Empty line for readability

if __name__ == "__main__":
    asyncio.run(test_phyllo_dummy_endpoints())
