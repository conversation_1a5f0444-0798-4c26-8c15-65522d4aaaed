"""
Test script for Frontend Filter Transformation System
This script validates the new frontend filter format handling and API provider integration.
"""
import asyncio
import json
from datetime import datetime
from typing import Dict, Any

# Test data matching EXACT frontend format
FRONTEND_TEST_DATA = {
    "searchQuery": "lifestyle influencer",
    "filterSelections": {
        "channel": "instagram",
        "optionFor": "creator",
        "filters": {
            "gender": ["female"],
            "follower_count": {"min": 10000, "max": 500000},
            "location": ["mumbai", "delhi", "bangalore"],
            "engagement_rate": {"min": 2.0, "max": 15.0},
            "category": ["lifestyle", "fashion"],
            "is_verified": True,
            "age": ["young_adult", "adult"]
        },
        "page": 1,
        "pageSize": 20,
        "sortBy": "follower_count",
        "sortOrder": "desc"
    },
    "includeExternal": True,
    "cachePreference": "prefer"
}

QUICK_SEARCH_TEST = {
    "query": "sarah",
    "platform": "instagram",
    "limit": 5
}


async def test_frontend_schemas():
    """Test frontend schema validation"""
    print("🧪 Testing Frontend Schemas...")
    
    try:
        from app.schemas.external_api.frontend_schemas import FrontendSearchRequest
        
        # Test schema validation
        request = FrontendSearchRequest(**FRONTEND_TEST_DATA)
        print(f"✅ Frontend schema validation passed")
        print(f"   Platform: {request.filterSelections.channel}")
        print(f"   Option for: {request.filterSelections.optionFor}")
        print(f"   Filters count: {len(request.filterSelections.filters)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Frontend schema validation failed: {str(e)}")
        return False


async def test_phyllo_provider():
    """Test Phyllo provider with frontend format"""
    print("\n🧪 Testing Phyllo Provider...")
    
    try:
        from app.services.external_providers.phyllo_provider import PhylloProvider, PhylloConfig
        from app.schemas.external_api.frontend_schemas import FrontendFilterSelections
        
        # Create provider
        config = PhylloConfig()
        provider = PhylloProvider(config)
        
        # Create filter selections
        filter_selections = FrontendFilterSelections(**FRONTEND_TEST_DATA["filterSelections"])
        
        # Test filter transformation
        phyllo_filters = provider.transform_filters(filter_selections)
        print(f"✅ Filter transformation successful")
        print(f"   Original filters: {len(filter_selections.filters)}")
        print(f"   Phyllo filters: {len(phyllo_filters)}")
        print(f"   Sample mapping: gender -> {phyllo_filters.get('creator_gender', 'not mapped')}")
        
        # Test mock search (since we're in testing mode)
        if config.use_mock:
            creators, metadata = await provider.search_creators(filter_selections)
            print(f"✅ Mock search successful: {len(creators)} creators found")
            print(f"   Execution time: {metadata.get('execution_time_ms', 0)}ms")
        
        return True
        
    except Exception as e:
        print(f"❌ Phyllo provider test failed: {str(e)}")
        return False


async def test_external_api_service():
    """Test external API service"""
    print("\n🧪 Testing External API Service...")
    
    try:
        from app.services.external_api_service import external_api_service
        from app.schemas.external_api.frontend_schemas import FrontendSearchRequest
        
        # Test service initialization
        providers = external_api_service.get_available_providers()
        print(f"✅ Service initialization successful")
        print(f"   Available providers: {[p.value for p in providers]}")
        
        # Test search capability
        request = FrontendSearchRequest(**FRONTEND_TEST_DATA)
        
        # Test mock search
        response = await external_api_service.search_creators(request)
        print(f"✅ Search test successful")
        print(f"   Profiles found: {len(response.profiles)}")
        print(f"   Total count: {response.metadata.total_count}")
        print(f"   Data sources: {response.metadata.data_sources}")
        
        return True
        
    except Exception as e:
        print(f"❌ External API service test failed: {str(e)}")
        return False


async def test_filter_conversion():
    """Test frontend to internal filter conversion"""
    print("\n🧪 Testing Filter Conversion...")
    
    try:
        from app.api.api_v1.endpoints.frontend_discovery import _convert_frontend_to_discovery_filters
        from app.schemas.external_api.frontend_schemas import FrontendFilterSelections
        
        # Create filter selections
        filter_selections = FrontendFilterSelections(**FRONTEND_TEST_DATA["filterSelections"])
        
        # Convert to internal format
        discovery_filters = _convert_frontend_to_discovery_filters(filter_selections)
        
        print(f"✅ Filter conversion successful")
        print(f"   Has demographic filters: {discovery_filters.demographic is not None}")
        print(f"   Has performance filters: {discovery_filters.performance is not None}")
        print(f"   Has platform filters: {discovery_filters.platform is not None}")
        
        if discovery_filters.demographic:
            print(f"   Gender: {discovery_filters.demographic.gender}")
            print(f"   Cities: {discovery_filters.demographic.city}")
        
        if discovery_filters.performance:
            print(f"   Follower count range: {discovery_filters.performance.follower_count}")
            print(f"   Engagement rate range: {discovery_filters.performance.engagement_rate}")
        
        return True
        
    except Exception as e:
        print(f"❌ Filter conversion test failed: {str(e)}")
        return False


async def test_api_provider_factory():
    """Test API provider factory"""
    print("\n🧪 Testing API Provider Factory...")
    
    try:
        from app.schemas.external_api.provider_interface import APIProviderFactory, APIProviderType
        from app.services.external_providers.phyllo_provider import PhylloConfig
        
        # Test factory
        available = APIProviderFactory.get_available_providers()
        print(f"✅ Factory test successful")
        print(f"   Registered providers: {[p.value for p in available]}")
        
        # Test provider creation
        if APIProviderType.PHYLLO in available:
            config = PhylloConfig()
            provider = APIProviderFactory.create_provider(APIProviderType.PHYLLO, config)
            print(f"✅ Provider creation successful: {provider.get_provider_type()}")
        
        return True
        
    except Exception as e:
        print(f"❌ API provider factory test failed: {str(e)}")
        return False


def validate_frontend_format():
    """Validate that test data matches expected frontend format"""
    print("🔍 Validating Frontend Format Compliance...")
    
    required_fields = [
        "filterSelections.channel",
        "filterSelections.optionFor", 
        "filterSelections.filters"
    ]
    
    all_valid = True
    for field_path in required_fields:
        parts = field_path.split(".")
        value = FRONTEND_TEST_DATA
        
        try:
            for part in parts:
                value = value[part]
            print(f"✅ {field_path}: {value}")
        except KeyError:
            print(f"❌ Missing required field: {field_path}")
            all_valid = False
    
    # Validate filter structure
    filters = FRONTEND_TEST_DATA["filterSelections"]["filters"]
    print(f"\n📋 Filter Analysis:")
    print(f"   Total filters: {len(filters)}")
    
    for filter_name, filter_value in filters.items():
        value_type = type(filter_value).__name__
        print(f"   {filter_name}: {value_type} = {filter_value}")
    
    return all_valid


async def run_all_tests():
    """Run comprehensive test suite"""
    print("🚀 Starting Frontend Filter Transformation System Tests")
    print("=" * 60)
    
    # Validate frontend format
    format_valid = validate_frontend_format()
    if not format_valid:
        print("❌ Frontend format validation failed - stopping tests")
        return
    
    # Run tests
    tests = [
        ("Frontend Schemas", test_frontend_schemas),
        ("Phyllo Provider", test_phyllo_provider),
        ("External API Service", test_external_api_service),
        ("Filter Conversion", test_filter_conversion),
        ("API Provider Factory", test_api_provider_factory)
    ]
    
    results = {}
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ {test_name} test crashed: {str(e)}")
            results[test_name] = False
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Test Results Summary:")
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed ({(passed/total)*100:.1f}%)")
    
    if passed == total:
        print("🎉 All tests passed! Frontend filter transformation system is ready.")
    else:
        print("⚠️  Some tests failed. Please check the implementation.")


def generate_api_examples():
    """Generate example API requests for documentation"""
    print("\n📚 Generating API Examples...")
    
    examples = {
        "frontend_search": {
            "url": "POST /api/v1/frontend/search-frontend",
            "payload": FRONTEND_TEST_DATA,
            "description": "Search creators using FIXED frontend format"
        },
        "quick_search": {
            "url": "GET /api/v1/frontend/quick-search-frontend",
            "params": QUICK_SEARCH_TEST,
            "description": "Quick search for creators by name/username"
        },
        "creator_analytics": {
            "url": "GET /api/v1/frontend/creator-analytics/{creator_id}",
            "params": {
                "platform": "instagram",
                "include_audience": True,
                "provider": "phyllo"
            },
            "description": "Get detailed creator analytics"
        },
        "providers": {
            "url": "GET /api/v1/frontend/providers",
            "description": "Get available external API providers"
        }
    }
    
    print("📋 API Endpoint Examples:")
    for name, example in examples.items():
        print(f"\n🔗 {name.upper()}:")
        print(f"   URL: {example['url']}")
        print(f"   Description: {example['description']}")
        
        if 'payload' in example:
            print(f"   Payload: {json.dumps(example['payload'], indent=2)}")
        elif 'params' in example:
            print(f"   Params: {json.dumps(example['params'], indent=2)}")


def print_transformation_flow():
    """Print the filter transformation flow"""
    print("\n🔄 Filter Transformation Flow:")
    print("=" * 60)
    
    flow_steps = [
        "1. Frontend sends FIXED format filters",
        "2. FrontendFilterSelections schema validates input", 
        "3. ExternalAPIService routes to appropriate provider",
        "4. Provider transforms filters using platform-specific mappings",
        "5. Provider makes API call to external service (Phyllo, etc.)",
        "6. Response is parsed to standardized CreatorProfile format",
        "7. Results are returned in FrontendSearchResponse format"
    ]
    
    for step in flow_steps:
        print(f"   {step}")
    
    print("\n📋 Key Benefits:")
    benefits = [
        "✅ Frontend format is FIXED and will never change",
        "✅ API provider agnostic - can add Modash, custom APIs, etc.",
        "✅ Automatic filter mapping per platform (Instagram vs YouTube)",
        "✅ Standardized response format across all providers",
        "✅ Graceful error handling and fallbacks",
        "✅ Mock data support for testing and development"
    ]
    
    for benefit in benefits:
        print(f"   {benefit}")


if __name__ == "__main__":
    """Run the test suite"""
    print("🧪 Frontend Filter Transformation System - Test Suite")
    print("🔒 IMPORTANT: Frontend format is FIXED and will NEVER change")
    print()
    
    # Generate documentation
    generate_api_examples()
    print_transformation_flow()
    
    # Run tests
    try:
        asyncio.run(run_all_tests())
    except KeyboardInterrupt:
        print("\n⏹️  Tests interrupted by user")
    except Exception as e:
        print(f"\n💥 Test suite crashed: {str(e)}")
