import json
import psycopg2
import uuid
from datetime import datetime

# Database connection settings
DB_URI = "postgresql+psycopg2://postgres:s81JKkaoe42Tm5W@172.16.4.173:5432/postgres"

# Remove sqlalchemy prefix for psycopg2
DB_URI = DB_URI.replace("postgresql+psycopg2://", "postgresql://")

# Read the first 1000 profiles from the generated file
with open("generated_profile_analytics.json", "r") as f:
    profiles = json.load(f)[:1000]

conn = psycopg2.connect(DB_URI)
cur = conn.cursor()

for profile in profiles:
    try:
        p = profile["profile"]
        profile_id = str(uuid.uuid4())
        # Insert into analytics_profille2.profile
        cur.execute('''
            INSERT INTO analytics_profille2.profile (
                id, work_platform_id, external_id, platform_username, url, image_url, full_name, introduction, content_count, is_verified, platform_account_type, gender, age_group, language, follower_count, subscriber_count, average_likes, average_comments, average_views, average_reels_views, engagement_rate, credibility_score, created_at, updated_at
            ) VALUES (
                %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
            ) ON CONFLICT DO NOTHING
        ''', (
            profile_id,
            profile["work_platform"]["id"],
            p["external_id"],
            p["platform_username"],
            p.get("url"),
            p.get("image_url"),
            p.get("full_name"),
            p.get("introduction"),
            p.get("content_count"),
            p.get("is_verified", False),
            p.get("platform_account_type"),
            p.get("gender"),
            p.get("age_group"),
            p.get("language"),
            p.get("follower_count"),
            p.get("subscriber_count"),
            p.get("average_likes"),
            p.get("average_comments"),
            p.get("average_views"),
            p.get("average_reels_views"),
            p.get("engagement_rate"),
            None,  # credibility_score
            datetime.now(),
            datetime.now()
        ))
    except Exception as e:
        print(f"Error inserting profile: {e}\nProfile data: {p}")
    # Add more inserts for related tables as needed

conn.commit()
cur.close()
conn.close()
print("Inserted 1000 profiles into analytics_profille2.profile.")
