#!/usr/bin/env python3
"""
Sample API calls demonstrating how to use the enhanced Phyllo API proxy.
"""

import requests
import json

BASE_URL = "http://localhost:8000/v1/social/creator"

def pretty_print(data):
    """Pretty print JSON data"""
    print(json.dumps(data, indent=2))

def test_profile_analytics():
    """Test profile analytics endpoint"""
    print("=" * 60)
    print("PROFILE ANALYTICS ENDPOINT")
    print("=" * 60)
    
    url = f"{BASE_URL}/profile/analytics"
    payload = {
        "profile_id": "cfa27c2b-6451-4433-99d7-98230dd1a1d6",
        "include_audience": True,
        "include_content": True,
        "include_pricing": True
    }
    
    print("Request:")
    print(f"POST {url}")
    pretty_print(payload)
    
    response = requests.post(url, json=payload)
    
    print(f"\nResponse Status: {response.status_code}")
    print("Response Body:")
    pretty_print(response.json())

def test_quick_search():
    """Test quick search endpoint with EXACT Phyllo API structure"""
    print("\n" + "=" * 60)
    print("QUICK SEARCH ENDPOINT (EXACT Phyllo API Compliance)")
    print("=" * 60)

    url = f"{BASE_URL}/profile/quick-search"

    # Test 1: Basic search with required parameters only
    print("\n1. Basic Search (Required Parameters Only):")
    payload = {
        "work_platform_id": "instagram",
        "sort_by": {
            "field": "FOLLOWER_COUNT",
            "order": "DESCENDING"
        }
    }

    print("Request:")
    print(f"POST {url}")
    pretty_print(payload)

    response = requests.post(url, json=payload)
    print(f"\nResponse Status: {response.status_code}")
    print("Response Body:")
    pretty_print(response.json())

    # Test 2: Advanced search with multiple filters
    print("\n2. Advanced Search with Multiple Filters:")
    payload = {
        "work_platform_id": "instagram",
        "follower_count": {
            "min": 1000,
            "max": 1000000
        },
        "subscriber_count": {
            "min": 500
        },
        "content_count": {
            "min": 10,
            "max": 1000
        },
        "audience_gender": {
            "type": "FEMALE",
            "operator": "GT",
            "percentage_value": 60
        },
        "creator_gender": "FEMALE",
        "audience_age": {
            "min": 18,
            "max": 35,
            "percentage_value": 50
        },
        "creator_age": {
            "min": 20,
            "max": 40
        },
        "description_keywords": "fashion beauty lifestyle",
        "is_verified": True,
        "has_contact_details": True,
        "engagement_rate": {
            "percentage_value": "2.5"
        },
        "has_sponsored_posts": True,
        "audience_interests": ["fashion", "beauty", "lifestyle"],
        "creator_interests": ["fashion", "beauty"],
        "average_likes": {
            "min": 1000,
            "max": 50000
        },
        "average_views": {
            "min": 5000
        },
        "platform_account_type": "CREATOR",
        "creator_account_type": ["CREATOR", "BUSINESS"],
        "sort_by": {
            "field": "ENGAGEMENT_RATE",
            "order": "DESCENDING"
        },
        "limit": 10,
        "offset": 0,
        "audience_source": "FOLLOWERS",
        "is_official_artist": False,
        "has_audience_info": True,
        "exclude_private_profiles": False
    }

    print("Request:")
    print(f"POST {url}")
    pretty_print(payload)

    response = requests.post(url, json=payload)
    print(f"\nResponse Status: {response.status_code}")
    print("Response Body:")
    pretty_print(response.json())

def test_advanced_search():
    """Test advanced search endpoint with full Phyllo API compliance and enhanced response format"""
    print("\n" + "=" * 60)
    print("ADVANCED SEARCH ENDPOINT (Enhanced Response Format)")
    print("=" * 60)

    url = f"{BASE_URL}/profile/search"

    # Test 1: Basic search with contact details
    print("\n1. Search with Contact Details:")
    payload = {
        "work_platform_id": "instagram",
        "follower_count": {
            "min": 1000,
            "max": 1000000
        },
        "creator_gender": "FEMALE",
        "has_contact_details": True,
        "description_keywords": "fashion beauty",
        "sort_by": {
            "field": "FOLLOWER_COUNT",
            "order": "DESCENDING"
        },
        "limit": 5,
        "offset": 0
    }

    print("Request:")
    print(f"POST {url}")
    pretty_print(payload)

    response = requests.post(url, json=payload)
    print(f"\nResponse Status: {response.status_code}")
    print("Response Body:")
    pretty_print(response.json())

    # Test 2: Advanced search with multiple filters
    print("\n2. Advanced Search with Multiple Filters:")
    payload = {
        "work_platform_id": "youtube",
        "follower_count": {
            "min": 10000,
            "max": 500000
        },
        "subscriber_count": {
            "min": 1000
        },
        "content_count": {
            "min": 50
        },
        "creator_gender": "MALE",
        "creator_age": {
            "min": 25,
            "max": 40
        },
        "engagement_rate": {
            "percentage_value": "3.0"
        },
        "description_keywords": "gaming technology review",
        "is_verified": True,
        "has_contact_details": True,
        "audience_interests": ["gaming", "technology"],
        "creator_interests": ["gaming"],
        "average_likes": {
            "min": 1000,
            "max": 50000
        },
        "average_views": {
            "min": 10000
        },
        "platform_account_type": "CREATOR",
        "sort_by": {
            "field": "ENGAGEMENT_RATE",
            "order": "DESCENDING"
        },
        "limit": 10,
        "offset": 0
    }

    print("Request:")
    print(f"POST {url}")
    pretty_print(payload)

    response = requests.post(url, json=payload)
    print(f"\nResponse Status: {response.status_code}")
    print("Response Body:")
    pretty_print(response.json())

    # Test 3: Test 500 result limit
    print("\n3. Test 500 Result Limit:")
    payload = {
        "work_platform_id": "instagram",
        "sort_by": {
            "field": "FOLLOWER_COUNT",
            "order": "DESCENDING"
        },
        "limit": 100,
        "offset": 450  # This should work (450 + 100 = 550 > 500, should fail)
    }

    print("Request:")
    print(f"POST {url}")
    pretty_print(payload)

    response = requests.post(url, json=payload)
    print(f"\nResponse Status: {response.status_code}")
    print("Response Body:")
    pretty_print(response.json())

def test_validation_errors():
    """Test validation error responses with Phyllo-compliant structure"""
    print("\n" + "=" * 60)
    print("VALIDATION ERROR EXAMPLES (Phyllo-Compliant)")
    print("=" * 60)

    # Test 1: Invalid work platform ID
    print("\n1. Invalid Work Platform ID Test:")
    url = f"{BASE_URL}/profile/quick-search"
    payload = {
        "work_platform_id": "invalid_platform",
        "sort_by": {
            "field": "FOLLOWER_COUNT",
            "order": "DESCENDING"
        }
    }

    print("Request:")
    print(f"POST {url}")
    pretty_print(payload)

    response = requests.post(url, json=payload)
    print(f"\nResponse Status: {response.status_code}")
    print("Response Body:")
    pretty_print(response.json())

    # Test 2: Invalid follower range
    print("\n2. Invalid Follower Range Test:")
    payload = {
        "work_platform_id": "instagram",
        "follower_count": {
            "min": 100000,
            "max": 1000  # Max less than min
        },
        "sort_by": {
            "field": "FOLLOWER_COUNT",
            "order": "DESCENDING"
        }
    }

    print("Request:")
    print(f"POST {url}")
    pretty_print(payload)

    response = requests.post(url, json=payload)
    print(f"\nResponse Status: {response.status_code}")
    print("Response Body:")
    pretty_print(response.json())

    # Test 3: Missing required sort_by field
    print("\n3. Missing Required sort_by Field Test:")
    payload = {
        "work_platform_id": "instagram",
        "follower_count": {
            "min": 1000
        }
        # Missing required sort_by field
    }

    print("Request:")
    print(f"POST {url}")
    pretty_print(payload)

    response = requests.post(url, json=payload)
    print(f"\nResponse Status: {response.status_code}")
    print("Response Body:")
    pretty_print(response.json())

    # Test 4: Invalid creator gender
    print("\n4. Invalid Creator Gender Test:")
    url = f"{BASE_URL}/profile/search"
    payload = {
        "work_platform_id": "instagram",
        "creator_gender": "INVALID_GENDER",
        "sort_by": {
            "field": "FOLLOWER_COUNT",
            "order": "DESCENDING"
        }
    }

    print("Request:")
    print(f"POST {url}")
    pretty_print(payload)

    response = requests.post(url, json=payload)
    print(f"\nResponse Status: {response.status_code}")
    print("Response Body:")
    pretty_print(response.json())

def test_security_features():
    """Test security features with Phyllo-compliant structure"""
    print("\n" + "=" * 60)
    print("SECURITY FEATURES TEST (Phyllo-Compliant)")
    print("=" * 60)

    # Test XSS prevention in description keywords
    print("\n1. XSS Prevention Test:")
    url = f"{BASE_URL}/profile/search"
    payload = {
        "work_platform_id": "instagram",
        "description_keywords": "<script>alert('xss')</script>",
        "sort_by": {
            "field": "FOLLOWER_COUNT",
            "order": "DESCENDING"
        }
    }

    print("Request:")
    print(f"POST {url}")
    pretty_print(payload)

    response = requests.post(url, json=payload)
    print(f"\nResponse Status: {response.status_code}")
    print("Response Body:")
    pretty_print(response.json())

    # Test SQL injection prevention
    print("\n2. SQL Injection Prevention Test:")
    payload = {
        "work_platform_id": "instagram",
        "description_keywords": "'; DROP TABLE profiles; --",
        "sort_by": {
            "field": "FOLLOWER_COUNT",
            "order": "DESCENDING"
        }
    }

    print("Request:")
    print(f"POST {url}")
    pretty_print(payload)

    response = requests.post(url, json=payload)
    print(f"\nResponse Status: {response.status_code}")
    print("Response Body:")
    pretty_print(response.json())

def main():
    """Run all sample API calls"""
    print("🚀 Phyllo API Proxy - Sample API Calls")
    print("📋 This demonstrates the enhanced validation and security features")
    
    try:
        # Test basic functionality
        test_profile_analytics()
        test_quick_search()
        test_advanced_search()
        
        # Test validation
        test_validation_errors()
        
        # Test security
        test_security_features()
        
        print("\n" + "=" * 60)
        print("✅ ALL TESTS COMPLETED")
        print("=" * 60)
        print("\n📖 See API_USAGE_GUIDE.md for more detailed examples")
        print("🔧 See test_enhanced_api.py for comprehensive test suite")
        
    except requests.exceptions.ConnectionError:
        print("❌ Error: Could not connect to the API server.")
        print("Please make sure the server is running on http://localhost:8000")
        print("Run: python -m uvicorn app.main:app --reload")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

if __name__ == "__main__":
    main()
