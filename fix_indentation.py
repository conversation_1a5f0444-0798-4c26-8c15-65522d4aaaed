"""
Quick fix for indentation issues in the PhylloAPIService class.
"""
import re

def fix_indentation(line):
    """Fix indentation in a line"""
    # Replace any tab characters with spaces
    line = line.replace('\t', '    ')
    
    # Remove extra spaces at the beginning of the line
    line = re.sub(r'^[ ]+', lambda m: ' ' * (len(m.group(0)) // 4 * 4), line)
    
    return line

def fix_file():
    """Fix indentation issues in the file"""
    filename = "app/services/phyllo_service.py"
    
    with open(filename, 'r') as file:
        lines = file.readlines()
    
    # Fix indentation in each line
    fixed_lines = [fix_indentation(line) for line in lines]
    
    # Write the fixed content back to the file
    with open(filename, 'w') as file:
        file.writelines(fixed_lines)
    
    print(f"Fixed indentation in {filename}")

if __name__ == "__main__":
    fix_file()
