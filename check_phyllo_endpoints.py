#!/usr/bin/env python3
"""
Simple script to check phyllo_dummy service endpoints
"""
import asyncio
import aiohttp
import json

async def check_phyllo_dummy():
    """Check what endpoints are available on phyllo_dummy"""
    base_url = "http://127.0.0.1:8001"
    
    async with aiohttp.ClientSession() as session:
        print(f"🔍 Checking phyllo_dummy service at {base_url}")
        print("=" * 60)
        
        # Test basic connectivity
        try:
            async with session.get(base_url, timeout=5) as response:
                print(f"✅ Service is running - Status: {response.status}")
                if response.status == 200:
                    text = await response.text()
                    print(f"   Response preview: {text[:200]}...")
                print()
        except Exception as e:
            print(f"❌ Service not accessible: {e}")
            return
        
        # Test common API endpoints
        endpoints = [
            "/",
            "/docs", 
            "/openapi.json",
            "/health",
            "/api/search",
            "/api/creators",
            "/api/profiles", 
            "/creators",
            "/profiles",
            "/search",
            "/api/v1/search",
            "/v1/search",
            "/api/v1/creators",
            "/api/creators/search",
            "/api/profiles/search"
        ]
        
        working_endpoints = []
        
        for endpoint in endpoints:
            try:
                async with session.get(f"{base_url}{endpoint}", timeout=5) as response:
                    status_emoji = "✅" if response.status == 200 else "⚠️" if response.status < 500 else "❌"
                    print(f"{status_emoji} GET  {endpoint:25} -> {response.status} {response.reason}")
                    
                    if response.status == 200:
                        working_endpoints.append(endpoint)
                        
                        # Try to get content preview
                        try:
                            if 'json' in response.headers.get('content-type', ''):
                                data = await response.json()
                                print(f"     📄 JSON response: {str(data)[:100]}...")
                            else:
                                text = await response.text()
                                print(f"     📄 Text response: {text[:100]}...")
                        except:
                            pass
                    
            except Exception as e:
                print(f"❌ GET  {endpoint:25} -> ERROR: {str(e)[:50]}")
        
        print(f"\n🎯 Working endpoints found: {len(working_endpoints)}")
        for ep in working_endpoints:
            print(f"   • {ep}")
        
        # Test POST requests on potential search endpoints
        search_endpoints = [ep for ep in working_endpoints if 'search' in ep or ep in ['/api/creators', '/creators', '/api/profiles', '/profiles']]
        
        if search_endpoints:
            print(f"\n🔄 Testing POST requests on search endpoints...")
            
            test_data = {
                "platform": "instagram",
                "filters": {},
                "limit": 20,
                "offset": 0
            }
            
            for endpoint in search_endpoints:
                try:
                    async with session.post(
                        f"{base_url}{endpoint}", 
                        json=test_data,
                        timeout=5
                    ) as response:
                        status_emoji = "✅" if response.status == 200 else "⚠️" if response.status < 500 else "❌"
                        print(f"{status_emoji} POST {endpoint:25} -> {response.status} {response.reason}")
                        
                        if response.status == 200:
                            try:
                                data = await response.json()
                                print(f"     📊 Success! Response keys: {list(data.keys()) if isinstance(data, dict) else type(data)}")
                            except:
                                pass
                                
                except Exception as e:
                    print(f"❌ POST {endpoint:25} -> ERROR: {str(e)[:50]}")

if __name__ == "__main__":
    asyncio.run(check_phyllo_dummy())
