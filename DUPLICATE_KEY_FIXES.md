# Duplicate Key Constraint Violation Fixes

## Problem Description

The application was encountering **duplicate key constraint violations** when users attempted multiple OTP verifications or when re-registering. The specific error was:

```
duplicate key value violates unique constraint "user_roles_pkey"
DETAIL: Key (user_id, role_id)=(7a573360-ec2e-4130-a3ac-3f32c3913752, 11239913-1b15-4724-b176-270dff840ef7) already exists.
```

This occurred because the code was trying to insert user roles and authentication methods without checking if they already existed.

## Root Cause Analysis

### Primary Issues:
1. **Role Assignment**: Code attempted to insert `UserRoleModel` records without checking for existing roles
2. **Auth Method Assignment**: Code attempted to insert `UserAuthMethod` records without checking for existing methods
3. **Missing Idempotency**: OTP verification endpoints were not idempotent, causing failures on repeated attempts

### Affected Endpoints:
- `/v1/auth/influencer/verify-otp`
- `/v1/auth/brands/verify-otp`
- OAuth flows in `oauth_utils.py`

## Solutions Implemented

### 1. Enhanced Influencer OTP Verification (`auth_influencer.py`)

#### Before:
```python
# 7) Assign influencer role
session.add(
    UserRoleModel(user_id=session_user.id, role_id=data.role_uuid)
)

# 8) Record the auth selection
auth_method = UserAuthMethod(...)
session.add(auth_method)
```

#### After:
```python
# 7) Assign influencer role with duplicate check
existing_role_stmt = select(UserRoleModel).where(
    UserRoleModel.user_id == session_user.id,
    UserRoleModel.role_id == data.role_uuid
)
existing_role_result = await session.execute(existing_role_stmt)
existing_role = existing_role_result.scalar_one_or_none()

if not existing_role:
    session.add(UserRoleModel(user_id=session_user.id, role_id=data.role_uuid))
    logger.info("Assigned influencer role to user", ...)
else:
    logger.info("User already has influencer role assigned", ...)

# 8) Record auth selection with duplicate check
existing_auth_stmt = select(UserAuthMethod).where(
    UserAuthMethod.user_id == session_user.id,
    UserAuthMethod.auth_method_id == auth_method_id
)
existing_auth_result = await session.execute(existing_auth_stmt)
existing_auth = existing_auth_result.scalar_one_or_none()

if not existing_auth:
    auth_method = UserAuthMethod(...)
    session.add(auth_method)
    logger.debug("Added auth method for user", ...)
else:
    # Update existing auth method to ensure it's enabled
    existing_auth.is_enabled = True
    existing_auth.enabled_at = datetime.now(UTC)
    logger.debug("Updated existing auth method for user", ...)
```

### 2. Enhanced Brand OTP Verification (`auth_brands.py`)

Applied identical fixes to brand OTP verification endpoint:
- Added duplicate role check before insertion
- Added duplicate auth method check with update logic
- Enhanced logging for better debugging

### 3. Enhanced OAuth Utilities (`oauth_utils.py`)

#### Influencer OAuth Flow:
```python
# Enhanced role assignment check
if not user_role:
    user_role = UserRoleModel(...)
    session.add(user_role)
    logger.info("Assigned influencer role", ...)
else:
    logger.info("User already has influencer role", ...)
```

#### Brand OAuth Flow:
```python
# Enhanced role assignment check
if not user_role:
    user_role = UserRoleModel(...)
    session.add(user_role)
    logger.info("Assigned brand role", ...)
else:
    logger.info("User already has brand role", ...)
```

## Benefits of the Fixes

### 1. **Idempotency**
- OTP verification endpoints are now idempotent
- Users can retry verification without encountering database errors
- System gracefully handles duplicate attempts

### 2. **Improved Error Handling**
- No more `500 Internal Server Error` on duplicate role assignments
- Clear logging distinguishes between new assignments and existing ones
- Better user experience during registration/verification

### 3. **Data Consistency**
- Prevents duplicate roles in the database
- Ensures auth methods are properly enabled/updated
- Maintains referential integrity

### 4. **Enhanced Logging**
- Added specific log messages for existing vs. new role assignments
- Debug-level logging for auth method operations
- Better observability for troubleshooting

## Implementation Details

### Query Pattern Used:
```python
# Check for existing record
existing_stmt = select(Model).where(
    Model.user_id == user_id,
    Model.other_id == other_id
)
result = await session.execute(existing_stmt)
existing_record = result.scalar_one_or_none()

if not existing_record:
    # Create new record
    new_record = Model(...)
    session.add(new_record)
    logger.info("Created new record", ...)
else:
    # Handle existing record (update if needed)
    logger.info("Record already exists", ...)
```

### Authentication Method Update Logic:
For existing auth methods, the code now:
1. Sets `is_enabled = True` to ensure the method is active
2. Updates `enabled_at` timestamp to current time
3. Logs the update operation for audit purposes

## Testing Scenarios Covered

### 1. **First-time Registration**
- ✅ Creates new user role
- ✅ Creates new auth method
- ✅ Logs new assignments

### 2. **Duplicate OTP Verification**
- ✅ Detects existing user role
- ✅ Updates existing auth method
- ✅ Logs existing assignments
- ✅ Completes successfully without errors

### 3. **OAuth Flows**
- ✅ Handles duplicate role assignments gracefully
- ✅ Maintains consistency across different authentication methods

## Monitoring and Observability

### New Log Messages Added:
- `"Assigned [influencer/brand] role to user"` - New role assignment
- `"User already has [influencer/brand] role assigned"` - Existing role detected
- `"Added auth method for user"` - New auth method created
- `"Updated existing auth method for user"` - Existing auth method updated

### Metrics to Monitor:
- Frequency of duplicate role detection (indicates retry patterns)
- Auth method update vs. creation ratio
- Error rate reduction in OTP verification endpoints

## Conclusion

These fixes resolve the duplicate key constraint violations while maintaining data integrity and improving the user experience. The implementation follows best practices for:

- **Idempotent Operations**: Safe to retry without side effects
- **Defensive Programming**: Checks for existing data before insertion
- **Comprehensive Logging**: Clear audit trail for all operations
- **Graceful Error Handling**: No user-facing errors for duplicate attempts

The system now handles edge cases gracefully and provides a smoother authentication experience for both influencers and brands.
