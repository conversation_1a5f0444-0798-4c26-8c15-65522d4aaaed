// This kind of response we need to send to the frontend for filter options
const filterOptions = [
    {
        optionName: "Demography & Identity",
        optionFor: "creator",
        channel: "instagram",
        filters: [
            {
                name: "gender",
                type: "radio-button",
                icon: "gender-icon",
                minmax: false,
                enterValue: false,
                placeholder: "Select Gender", //only needed for enterValue
                options: [
                    { label: "Male", value: "male", description: ""
                    },
                    { label: "Female", value: "female", description: ""
                    },
                    { label: "Other", value: "other", description: ""
                    }
                ]
            },
            {
                name: "Location",
                type: "multilevel-checkbox",
                icon: "location-icon",
                searchBox: true,
                minmax: false,
                enterValue: false,
                placeholder: "Search Location",
                options: [
                    {
                        subOptionName: "Tier 1",
                        subOptionType: "checkbox",
                        collapsed: true,
                        checkboxEnabled: true,
                        subOptions: [
                            { label: "Mumbai", value: "mumbai", description: ""
                            },
                            { label: "Delhi", value: "delhi", description: ""
                            },
                            { label: "Bangalore", value: "bangalore", description: ""
                            },
                            { label: "Hyderabad", value: "hyderabad", description: ""
                            },
                            { label: "Chennai", value: "chennai", description: ""
                            },
                            { label: "Kolkata", value: "kolkata", description: ""
                            },
                            { label: "Pune", value: "pune", description: ""
                            },
                            { label: "Ahmedabad", value: "ahmedabad", description: ""
                            }
                        ]
                    },
                    {
                        subOptionName: "Tier 2",
                        subOptionType: "checkbox",
                        checkboxEnabled: true,
                        collapsed: true,
                        subOptions: [
                            { label: "Surat", value: "surat", description: ""
                            },
                            { label: "Jaipur", value: "jaipur", description: ""
                            },
                            { label: "Lucknow", value: "lucknow", description: ""
                            },
                            { label: "Kanpur", value: "kanpur", description: ""
                            },
                            { label: "Nagpur", value: "nagpur", description: ""
                            },
                            { label: "Indore", value: "indore", description: ""
                            },
                            { label: "Bhopal", value: "bhopal", description: ""
                            },
                            { label: "Coimbatore", value: "coimbatore", description: ""
                            },
                            { label: "Visakhapatnam", value: "visakhapatnam", description: ""
                            },
                            { label: "Patna", value: "patna", description: ""
                            },
                            { label: "Vadodara", value: "vadodara", description: ""
                            },
                            { label: "Ludhiana", value: "ludhiana", description: ""
                            },
                            { label: "Agra", value: "agra", description: ""
                            },
                            { label: "Nashik", value: "nashik", description: ""
                            },
                            { label: "Faridabad", value: "faridabad", description: ""
                            },
                            { label: "Meerut", value: "meerut", description: ""
                            },
                            { label: "Rajkot", value: "rajkot", description: ""
                            },
                            { label: "Varanasi", value: "varanasi", description: ""
                            },
                            { label: "Amritsar", value: "amritsar", description: ""
                            },
                            { label: "Ranchi", value: "ranchi", description: ""
                            },
                            { label: "Raipur", value: "raipur", description: ""
                            },
                            { label: "Jodhpur", value: "jodhpur", description: ""
                            },
                            { label: "Madurai", value: "madurai", description: ""
                            },
                            { label: "Guwahati", value: "guwahati", description: ""
                            },
                            { label: "Chandigarh", value: "chandigarh", description: ""
                            },
                            { label: "Mysore", value: "mysore", description: ""
                            },
                            { label: "Hubli", value: "hubli", description: ""
                            },
                            { label: "Trivandrum", value: "trivandrum", description: ""
                            }
                        ]
                    },
                    {
                        subOptionName: "Tier 3 or Rural",
                        subOptionType: "checkbox",
                        collapsed: true,
                        checkboxEnabled: true,
                        subOptions: [
                            { label: "Aligarh", value: "aligarh", description: ""
                            },
                            { label: "Gorakhpur", value: "gorakhpur", description: ""
                            },
                            { label: "Gaya", value: "gaya", description: ""
                            },
                            { label: "Saharanpur", value: "saharanpur", description: ""
                            },
                            { label: "Bhavnagar", value: "bhavnagar", description: ""
                            },
                            { label: "Guntur", value: "guntur", description: ""
                            },
                            { label: "Bilaspur", value: "bilaspur", description: ""
                            },
                            { label: "Rewa", value: "rewa", description: ""
                            },
                            { label: "Hapur", value: "hapur", description: ""
                            },
                            { label: "Jamnagar", value: "jamnagar", description: ""
                            },
                            { label: "Jhansi", value: "jhansi", description: ""
                            },
                            { label: "Ujjain", value: "ujjain", description: ""
                            },
                            { label: "Tirunelveli", value: "tirunelveli", description: ""
                            },
                            { label: "Kakinada", value: "kakinada", description: ""
                            },
                            { label: "Nanded", value: "nanded", description: ""
                            },
                            { label: "Shimla", value: "shimla", description: ""
                            },
                            { label: "Muzaffarpur", value: "muzaffarpur", description: ""
                            },
                            { label: "Cuttack", value: "cuttack", description: ""
                            },
                            { label: "Jalandhar", value: "jalandhar", description: ""
                            },
                            { label: "Belgaum", value: "belgaum", description: ""
                            },
                            { label: "Kottayam", value: "kottayam", description: ""
                            },
                            { label: "Hoshiarpur", value: "hoshiarpur", description: ""
                            },
                            { label: "Rohtak", value: "rohtak", description: ""
                            }
                        ]
                    }
                ]
            },
            {
                name: "age",
                type: "checkbox",
                minmax: true,
                icon: "age-icon",
                enterValue: false,
                placeholder: "Select Age", //only needed for enterValue
                options: [
                    { label: "Teen", value: "13-19", description: "13-19"
                    },
                    { label: "Young Adult", value: "20-35", description: "20-35"
                    },
                    { label: "Adult", value: "36-55", description: "36-55"
                    },
                    { label: "Senior", value: "56+", description: "56+"
                    }
                ]
            },
            {
                name: "language",
                type: "checkbox",
                minmax: false,
                icon: "language-icon",
                searchBox: true,
                enterValue: false,
                placeholder: "Search Language",
                options: [
                    { label: "English", value: "english", description: ""
                    },
                    { label: "Hindi", value: "hindi", description: ""
                    },
                    { label: "Spanish", value: "spanish", description: ""
                    },
                    { label: "French", value: "french", description: ""
                    }
                ]
            }
        ]
    },
    {
        optionName: "Performance Metrics",
        optionFor: "creator",
        channel: "instagram",
        filters: [
            {
                name: "follower count",
                type: "checkbox",
                minmax: true,
                icon: "follower-icon",
                enterValue: false,
                placeholder: "Select Follower Count", //only needed for enterValue
                options: [
                    { label: "Nano", value: "1000-10000", description: "1k-10K"
                    },
                    { label: "Micro", value: "10000-50000", description: "10K-50K"
                    },
                    { label: "Mid", value: "50000-500000", description: "50K-500K"
                    },
                    { label: "Macro", value: "500000-1000000", description: "500K-1M"
                    },
                    { label: "Mega", value: "1000000-", description: "1M+"
                    }
                ]
            },
            {
                name: "Average Likes",
                type: "checkbox",
                minmax: true,
                icon: "likes-icon",
                enterValue: false,
                placeholder: "Select Average Likes", //only needed for enterValue
                options: [
                    { label: "High", value: "5000+", description: "5000+"
                    },
                    { label: "Medium", value: "501-5000", description: "501-5000"
                    },
                    { label: "Low", value: "0-500", description: "0-500"
                    },
                ]
            },
            {
                name: "Average Comments",
                type: "checkbox",
                minmax: true,
                icon: "comments-icon",
                enterValue: false,
                placeholder: "Select Average Comments", //only needed for enterValue
                options: [
                    { label: "High", value: "500+", description: "500+"
                    },
                    { label: "Medium", value: "51-500", description: "51-500"
                    },
                    { label: "Low", value: "10-50", description: "10-50"
                    },
                ]
            },
            {
                name: "Engagement Rate",
                type: "checkbox",
                minmax: false,
                icon: "engagement-icon",
                enterValue: false,
                placeholder: "Select Engagement Rate", //only needed for enterValue
                options: [
                    { label: "Very High", value: "10%+", description: "10%+"
                    },
                    { label: "High", value: "5%-10%", description: "5%-10%"
                    },
                    { label: "Medium", value: "2%-5%", description: "2%-5%"
                    },
                    { label: "Low", value: "<2%", description: "<2%"
                    },
                ]
            },
            {
                name: "Reel Views",
                type: "checkbox",
                minmax: true,
                icon: "reel-icon",
                enterValue: false,
                placeholder: "Select Reel Views", //only needed for enterValue
                options: [
                    { label: "Very High", value: "500001+", description: "500001+"
                    },
                    { label: "High", value: "50001-500000+", description: "50001-500000+"
                    },
                    { label: "Medium", value: "5001-50000", description: "5001-50000"
                    },
                    { label: "Low", value: "1000-5000", description: "1000-5000"
                    },
                ]
            },
            {
                name: "Follower Growth",
                type: "multilevel-checkbox",
                minmax: false,
                searchBox: false,
                icon: "growth-icon",
                enterValue: false,
                placeholder: "Select Follower Growth",
                options: [
                    {
                        subOptionName: "Percentage",
                        subOptionType: "checkbox",
                        checkboxEnabled: false,
                        collapsed: false,
                        subOptions: [
                            { label: "Rapid", value: "5%+", description: "5%+"
                            },
                            { label: "Moderate", value: "2%-5%", description: "2%-5%"
                            },
                            { label: "Slow", value: "0%-2%", description: "0-2%"
                            },
                            { label: "Negative", value: "<0%", description: "<0%"
                            },
                        ]
                    },
                    {
                        subOptionName: "Time Interval",
                        subOptionType: "radio-button",
                        checkboxEnabled: false,
                        collapsed: false,
                        subOptions: [
                            { label: "Last 1 Month", value: "-30", description: ""
                            },
                            { label: "Last 3 Month", value: "-90", description: ""
                            },
                            { label: "Last 6 Month", value: "-180", description: ""
                            },
                        ]
                    }
                ]
            },
            {
                name: "Sponsored Brand Posts",
                type: "radio-button",
                minmax: false,
                icon: "sponsored-icon",
                enterValue: false,
                placeholder: "Select Sponsored Brand Posts", //only needed for enterValue
                options: [
                    { label: "Any", value: "any", description: ""
                    },
                    { label: "Sponsored", value: "true", description: ""
                    },
                    { label: "Non-Sponsored", value: "false", description: ""
                    }
                ]
            }
        ]
    },
    {
        optionName: "Content & Niche",
        optionFor: "creator",
        channel: "instagram",
        filters: [
            {
                name: "Category",
                type: "checkbox",
                minmax: false,
                icon: "category-icon",
                enterValue: false,
                searchBox: true,
                placeholder: "Select Category", //only needed for enterValue
                options: [
                    { label: "Camera & Photography", value: "camera_photography", description: ""
                    },
                    { label: "Friends, Family & Relationships", value: "friends_family_relationships", description: ""
                    },
                    { label: "Clothes, Handbags & Accessories", value: "clothes_shoes_handbags_accessories", description: ""
                    },
                    { label: "Travel, Tourism & Aviation", value: "travel_tourism_aviation", description: ""
                    },
                    { label: "Restaurants, Food & Grocery", value: "restaurants_food_grocery", description: ""
                    },
                    { label: "Art & Design", value: "art_design", description: ""
                    },
                    { label: "Television & Film", value: "television_film", description: ""
                    },
                    { label: "Cars & Motorbikes", value: "cars_motorbikes", description: ""
                    },
                    { label: "Sports", value: "sports", description: ""
                    },
                    { label: "Toys, Children & Baby", value: "toys_children_baby", description: ""
                    },
                    { label: "Beauty & Cosmetics", value: "beauty_cosmetics", description: ""
                    },
                    { label: "Wedding", value: "wedding", description: ""
                    }
                ]
            },
            {
                name: "Keywords",
                type: "enter-value",
                minmax: false,
                icon: "keywords-icon",
                enterValue: true,
                placeholder: "Enter Keywords desctiption",
            },
            {
                name: "Hashtags",
                type: "enter-value",
                minmax: false,
                icon: "hashtags-icon",
                enterValue: true,
                placeholder: "Enter Hashtags used",
            },
            {
                name: "Mentions",
                type: "enter-value",
                minmax: false,
                icon: "mentions-icon",
                enterValue: true,
                placeholder: "Enter Mentions used",
            }
        ]
    },
    {
        optionName: "Credibility & Platform",
        optionFor: "creator",
        channel: "instagram",
        filters: [
            {
                name: "Verification",
                type: "radio-button",
                minmax: false,
                icon: "verification-icon",
                enterValue: false,
                placeholder: "Select Verification Status", //only needed for enterValue
                options: [
                    { label: "Any", value: "any", description: ""
                    },
                    { label: "Verified", value: "verified", description: ""
                    },
                    { label: "Unverified", value: "unverified", description: ""
                    },
                ]
            },
            {
                name: "Creatorverse Score",
                type: "checkbox",
                minmax: true,
                icon: "creatorverse-icon",
                enterValue: false,
                placeholder: "Enter Creatorverse Score",
                options: [
                    { label: "Excellent", value: "9-10", description: "9-10"
                    },
                    { label: "Good", value: "6-8", description: "6-8"
                    },
                    { label: "Average", value: "3-5", description: "3-5"
                    },
                    { label: "Poor", value: "0-2", description: "0-2"
                    },
                ]
            }
        ]
    },
    {
        optionName: "Demography & Identity",
        optionFor: "audience",
        channel: "instagram",
        filters: [
            {
                name: "gender",
                type: "radio-button",
                icon: "gender-icon",
                minmax: false,
                enterValue: false,
                searchBox: false,
                placeholder: "Select Gender", //only needed for enterValue
                options: [
                    { label: "Male", value: "male", description: ""
                    },
                    { label: "Female", value: "female", description: ""
                    },
                    { label: "Other", value: "other", description: ""
                    }
                ]
            },
            {
                name: "age",
                type: "checkbox",
                minmax: true,
                icon: "age-icon",
                enterValue: false,
                placeholder: "Select Age", //only needed for enterValue
                options: [
                    { label: "Teen", value: "13-19", description: "13-19"
                    },
                    { label: "Young Adult", value: "20-35", description: "20-35"
                    },
                    { label: "Adult", value: "36-55", description: "36-55"
                    },
                    { label: "Senior", value: "56+", description: "56+"
                    }
                ]
            },
            {
                name: "Location",
                type: "multilevel-checkbox",
                icon: "location-icon",
                searchBox: true,
                minmax: false,
                enterValue: false,
                placeholder: "Search Location",
                options: [
                    {
                        subOptionName: "Tier 1",
                        subOptionType: "checkbox",
                        collapsed: true,
                        checkboxEnabled: true,
                        subOptions: [
                            { label: "Mumbai", value: "mumbai", description: ""
                            },
                            { label: "Delhi", value: "delhi", description: ""
                            },
                            { label: "Bangalore", value: "bangalore", description: ""
                            },
                            { label: "Hyderabad", value: "hyderabad", description: ""
                            },
                            { label: "Chennai", value: "chennai", description: ""
                            },
                            { label: "Kolkata", value: "kolkata", description: ""
                            },
                            { label: "Pune", value: "pune", description: ""
                            },
                            { label: "Ahmedabad", value: "ahmedabad", description: ""
                            }
                        ]
                    },
                    {
                        subOptionName: "Tier 2",
                        subOptionType: "checkbox",
                        collapsed: true,
                        checkboxEnabled: true,
                        subOptions: [
                            { label: "Surat", value: "surat", description: ""
                            },
                            { label: "Jaipur", value: "jaipur", description: ""
                            },
                            { label: "Lucknow", value: "lucknow", description: ""
                            },
                            { label: "Kanpur", value: "kanpur", description: ""
                            },
                            { label: "Nagpur", value: "nagpur", description: ""
                            },
                            { label: "Indore", value: "indore", description: ""
                            },
                            { label: "Bhopal", value: "bhopal", description: ""
                            },
                            { label: "Coimbatore", value: "coimbatore", description: ""
                            },
                            { label: "Visakhapatnam", value: "visakhapatnam", description: ""
                            },
                            { label: "Patna", value: "patna", description: ""
                            },
                            { label: "Vadodara", value: "vadodara", description: ""
                            },
                            { label: "Ludhiana", value: "ludhiana", description: ""
                            },
                            { label: "Agra", value: "agra", description: ""
                            },
                            { label: "Nashik", value: "nashik", description: ""
                            },
                            { label: "Faridabad", value: "faridabad", description: ""
                            },
                            { label: "Meerut", value: "meerut", description: ""
                            },
                            { label: "Rajkot", value: "rajkot", description: ""
                            },
                            { label: "Varanasi", value: "varanasi", description: ""
                            },
                            { label: "Amritsar", value: "amritsar", description: ""
                            },
                            { label: "Ranchi", value: "ranchi", description: ""
                            },
                            { label: "Raipur", value: "raipur", description: ""
                            },
                            { label: "Jodhpur", value: "jodhpur", description: ""
                            },
                            { label: "Madurai", value: "madurai", description: ""
                            },
                            { label: "Guwahati", value: "guwahati", description: ""
                            },
                            { label: "Chandigarh", value: "chandigarh", description: ""
                            },
                            { label: "Mysore", value: "mysore", description: ""
                            },
                            { label: "Hubli", value: "hubli", description: ""
                            },
                            { label: "Trivandrum", value: "trivandrum", description: ""
                            }
                        ]
                    },
                    {
                        subOptionName: "Tier 3 or Rural",
                        subOptionType: "checkbox",
                        collapsed: true,
                        checkboxEnabled: true,
                        subOptions: [
                            { label: "Aligarh", value: "aligarh", description: ""
                            },
                            { label: "Gorakhpur", value: "gorakhpur", description: ""
                            },
                            { label: "Gaya", value: "gaya", description: ""
                            },
                            { label: "Saharanpur", value: "saharanpur", description: ""
                            },
                            { label: "Bhavnagar", value: "bhavnagar", description: ""
                            },
                            { label: "Guntur", value: "guntur", description: ""
                            },
                            { label: "Bilaspur", value: "bilaspur", description: ""
                            },
                            { label: "Rewa", value: "rewa", description: ""
                            },
                            { label: "Hapur", value: "hapur", description: ""
                            },
                            { label: "Jamnagar", value: "jamnagar", description: ""
                            },
                            { label: "Jhansi", value: "jhansi", description: ""
                            },
                            { label: "Ujjain", value: "ujjain", description: ""
                            },
                            { label: "Tirunelveli", value: "tirunelveli", description: ""
                            },
                            { label: "Kakinada", value: "kakinada", description: ""
                            },
                            { label: "Nanded", value: "nanded", description: ""
                            },
                            { label: "Shimla", value: "shimla", description: ""
                            },
                            { label: "Muzaffarpur", value: "muzaffarpur", description: ""
                            },
                            { label: "Cuttack", value: "cuttack", description: ""
                            },
                            { label: "Jalandhar", value: "jalandhar", description: ""
                            },
                            { label: "Belgaum", value: "belgaum", description: ""
                            },
                            { label: "Kottayam", value: "kottayam", description: ""
                            },
                            { label: "Hoshiarpur", value: "hoshiarpur", description: ""
                            },
                            { label: "Rohtak", value: "rohtak", description: ""
                            }
                        ]
                    }
                ]
            },
            {
                name: "language",
                type: "checkbox",
                minmax: false,
                icon: "language-icon",
                enterValue: false,
                searchBox: true,
                placeholder: "Search Language",
                options: [
                    { label: "English", value: "english", description: ""
                    },
                    { label: "Hindi", value: "hindi", description: ""
                    },
                    { label: "Spanish", value: "spanish", description: ""
                    },
                    { label: "French", value: "french", description: ""
                    }
                ]
            }
        ]
    },
    {
        optionName: "Interests",
        optionFor: "audience",
        channel: "instagram",
        filters: [
            {
                name: "Interests",
                type: "enter-value",
                minmax: false,
                icon: "interests-icon",
                enterValue: true,
                placeholder: "Enter Interests",
            },
            {
                name: "Brand Affinities",
                type: "enter-value",
                minmax: false,
                icon: "brand-icon",
                enterValue: true,
                placeholder: "Enter Brand Affinities",
            }
        ]
    },
    {
        optionName: "Demography & Identity",
        optionFor: "creator",
        channel: "youtube",
        filters: [
            {
                name: "gender",
                type: "radio-button",
                icon: "gender-icon",
                minmax: false,
                enterValue: false,
                placeholder: "Select Gender", //only needed for enterValue
                options: [
                    { label: "Male", value: "male", description: ""
                    },
                    { label: "Female", value: "female", description: ""
                    },
                    { label: "Other", value: "other", description: ""
                    }
                ]
            },
            {
                name: "age",
                type: "checkbox",
                minmax: true,
                icon: "age-icon",
                enterValue: false,
                placeholder: "Select Age", //only needed for enterValue
                options: [
                    { label: "Teen", value: "13-19", description: "13-19"
                    },
                    { label: "Young Adult", value: "20-35", description: "20-35"
                    },
                    { label: "Adult", value: "36-55", description: "36-55"
                    },
                    { label: "Senior", value: "56+", description: "56+"
                    }
                ]
            },
            {
                name: "Location",
                type: "multilevel-checkbox",
                icon: "location-icon",
                searchBox: true,
                minmax: false,
                enterValue: false,
                placeholder: "Search Location",
                options: [
                    {
                        subOptionName: "Tier 1",
                        subOptionType: "checkbox",
                        collapsed: true,
                        checkboxEnabled: true,
                        subOptions: [
                            { label: "Mumbai", value: "mumbai", description: ""
                            },
                            { label: "Delhi", value: "delhi", description: ""
                            },
                            { label: "Bangalore", value: "bangalore", description: ""
                            },
                            { label: "Hyderabad", value: "hyderabad", description: ""
                            },
                            { label: "Chennai", value: "chennai", description: ""
                            },
                            { label: "Kolkata", value: "kolkata", description: ""
                            },
                            { label: "Pune", value: "pune", description: ""
                            },
                            { label: "Ahmedabad", value: "ahmedabad", description: ""
                            }
                        ]
                    },
                    {
                        subOptionName: "Tier 2",
                        subOptionType: "checkbox",
                        collapsed: true,
                        checkboxEnabled: true,
                        subOptions: [
                            { label: "Surat", value: "surat", description: ""
                            },
                            { label: "Jaipur", value: "jaipur", description: ""
                            },
                            { label: "Lucknow", value: "lucknow", description: ""
                            },
                            { label: "Kanpur", value: "kanpur", description: ""
                            },
                            { label: "Nagpur", value: "nagpur", description: ""
                            },
                            { label: "Indore", value: "indore", description: ""
                            },
                            { label: "Bhopal", value: "bhopal", description: ""
                            },
                            { label: "Coimbatore", value: "coimbatore", description: ""
                            },
                            { label: "Visakhapatnam", value: "visakhapatnam", description: ""
                            },
                            { label: "Patna", value: "patna", description: ""
                            },
                            { label: "Vadodara", value: "vadodara", description: ""
                            },
                            { label: "Ludhiana", value: "ludhiana", description: ""
                            },
                            { label: "Agra", value: "agra", description: ""
                            },
                            { label: "Nashik", value: "nashik", description: ""
                            },
                            { label: "Faridabad", value: "faridabad", description: ""
                            },
                            { label: "Meerut", value: "meerut", description: ""
                            },
                            { label: "Rajkot", value: "rajkot", description: ""
                            },
                            { label: "Varanasi", value: "varanasi", description: ""
                            },
                            { label: "Amritsar", value: "amritsar", description: ""
                            },
                            { label: "Ranchi", value: "ranchi", description: ""
                            },
                            { label: "Raipur", value: "raipur", description: ""
                            },
                            { label: "Jodhpur", value: "jodhpur", description: ""
                            },
                            { label: "Madurai", value: "madurai", description: ""
                            },
                            { label: "Guwahati", value: "guwahati", description: ""
                            },
                            { label: "Chandigarh", value: "chandigarh", description: ""
                            },
                            { label: "Mysore", value: "mysore", description: ""
                            },
                            { label: "Hubli", value: "hubli", description: ""
                            },
                            { label: "Trivandrum", value: "trivandrum", description: ""
                            }
                        ]
                    },
                    {
                        subOptionName: "Tier 3 or Rural",
                        subOptionType: "checkbox",
                        collapsed: true,
                        checkboxEnabled: true,
                        subOptions: [
                            { label: "Aligarh", value: "aligarh", description: ""
                            },
                            { label: "Gorakhpur", value: "gorakhpur", description: ""
                            },
                            { label: "Gaya", value: "gaya", description: ""
                            },
                            { label: "Saharanpur", value: "saharanpur", description: ""
                            },
                            { label: "Bhavnagar", value: "bhavnagar", description: ""
                            },
                            { label: "Guntur", value: "guntur", description: ""
                            },
                            { label: "Bilaspur", value: "bilaspur", description: ""
                            },
                            { label: "Rewa", value: "rewa", description: ""
                            },
                            { label: "Hapur", value: "hapur", description: ""
                            },
                            { label: "Jamnagar", value: "jamnagar", description: ""
                            },
                            { label: "Jhansi", value: "jhansi", description: ""
                            },
                            { label: "Ujjain", value: "ujjain", description: ""
                            },
                            { label: "Tirunelveli", value: "tirunelveli", description: ""
                            },
                            { label: "Kakinada", value: "kakinada", description: ""
                            },
                            { label: "Nanded", value: "nanded", description: ""
                            },
                            { label: "Shimla", value: "shimla", description: ""
                            },
                            { label: "Muzaffarpur", value: "muzaffarpur", description: ""
                            },
                            { label: "Cuttack", value: "cuttack", description: ""
                            },
                            { label: "Jalandhar", value: "jalandhar", description: ""
                            },
                            { label: "Belgaum", value: "belgaum", description: ""
                            },
                            { label: "Kottayam", value: "kottayam", description: ""
                            },
                            { label: "Hoshiarpur", value: "hoshiarpur", description: ""
                            },
                            { label: "Rohtak", value: "rohtak", description: ""
                            }
                        ]
                    }
                ]
            },
            {
                name: "language",
                type: "checkbox",
                minmax: false,
                icon: "language-icon",
                enterValue: false,
                searchBox: true,
                placeholder: "Search Language",
                options: [
                    { label: "English", value: "english", description: ""
                    },
                    { label: "Hindi", value: "hindi", description: ""
                    },
                    { label: "Spanish", value: "spanish", description: ""
                    },
                    { label: "French", value: "french", description: ""
                    }
                ]
            }
        ]
    },
    {
        optionName: "Performance Metrics",
        optionFor: "creator",
        channel: "youtube",
        filters: [
            {
                name: "Subscribers Count",
                type: "multilevel-checkbox",
                minmax: false,
                searchBox: false,
                icon: "growth-icon",
                enterValue: false,
                placeholder: "Select Follower Growth",
                options: [
                    {
                        subOptionName: "Percentage",
                        subOptionType: "checkbox",
                        checkboxEnabled: false,
                        collapsed: false,
                        subOptions: [
                            { label: "Rapid", value: "5%+", description: "5%+"
                            },
                            { label: "Moderate", value: "2%-5%", description: "2%-5%"
                            },
                            { label: "Slow", value: "0%-2%", description: "0-2%"
                            },
                            { label: "Negative", value: "<0%", description: "<0%"
                            },
                        ]
                    },
                    {
                        subOptionName: "Time Interval",
                        subOptionType: "radio-button",
                        checkboxEnabled: false,
                        collapsed: false,
                        subOptions: [
                            { label: "Last 1 Month", value: "-30", description: ""
                            },
                            { label: "Last 3 Month", value: "-90", description: ""
                            },
                            { label: "Last 6 Month", value: "-180", description: ""
                            },
                        ]
                    }
                ]
            },
            {
                name: "Average Likes",
                type: "checkbox",
                minmax: true,
                icon: "likes-icon",
                enterValue: false,
                placeholder: "Select Average Likes", //only needed for enterValue
                options: [
                    { label: "High", value: "5000+", description: "5000+"
                    },
                    { label: "Medium", value: "501-5000", description: "501-5000"
                    },
                    { label: "Low", value: "0-500", description: "0-500"
                    },
                ]
            },
            {
                name: "Average Views",
                type: "checkbox",
                minmax: true,
                icon: "reel-icon",
                enterValue: false,
                placeholder: "Select Average Views", //only needed for enterValue
                options: [
                    { label: "Very High", value: "500001+", description: "500001+"
                    },
                    { label: "High", value: "50001-500000+", description: "50001-500000+"
                    },
                    { label: "Medium", value: "5001-50000", description: "5001-50000"
                    },
                    { label: "Low", value: "1000-5000", description: "1000-5000"
                    },
                ]
            },
            {
                name: "Engagement Rate",
                type: "checkbox",
                minmax: false,
                icon: "engagement-icon",
                enterValue: false,
                placeholder: "Select Engagement Rate", //only needed for enterValue
                options: [
                    { label: "Very High", value: "10%+", description: "10%+"
                    },
                    { label: "High", value: "5%-10%", description: "5%-10%"
                    },
                    { label: "Medium", value: "2%-5%", description: "2%-5%"
                    },
                    { label: "Low", value: "<2%", description: "<2%"
                    },
                ]
            },
            {
                name: "Subscribers Growth",
                type: "checkbox",
                minmax: false,
                icon: "growth-icon",
                enterValue: false,
                placeholder: "Select Subscribers Growth", //only needed for enterValue
                options: [
                    { label: "Rapid", value: "5%+", description: "5%+"
                    },
                    { label: "Moderate", value: "2%-5%", description: "2%-5%"
                    },
                    { label: "Slow", value: "0%-2%", description: "0-2%"
                    },
                    { label: "Negative", value: "<0%", description: "<0%"
                    },
                ]
            }
        ]
    },
    {
        optionName: "Content & Niche",
        optionFor: "creator",
        channel: "youtube",
        filters: [
            {
                name: "Category",
                type: "checkbox",
                minmax: false,
                icon: "category-icon",
                enterValue: false,
                placeholder: "Select Category", //only needed for enterValue
                options: [
                    { label: "Fashion", value: "fashion", description: ""
                    },
                    { label: "Travel", value: "travel", description: ""
                    },
                    { label: "Food", value: "food", description: ""
                    },
                    { label: "Fitness", value: "fitness", description: ""
                    },
                ]
            },
            {
                name: "Keywords",
                type: "enter-value",
                minmax: false,
                icon: "keywords-icon",
                enterValue: true,
                placeholder: "Enter Keywords desctiption",
            }
        ]
    },
    {
        optionName: "Credibility & Platform",
        optionFor: "creator",
        channel: "youtube",
        filters: [
            {
                name: "Official",
                type: "radio-button",
                minmax: false,
                icon: "official-icon",
                enterValue: false,
                placeholder: "", //only needed for enterValue
                options: [
                    { label: "Any", value: "any", description: ""
                    },
                    { label: "Official", value: "official", description: ""
                    },
                    { label: "Unofficial", value: "unofficial", description: ""
                    },
                ]
            },
            {
                name: "Creatorverse Score",
                type: "enter-value",
                minmax: true,
                icon: "creatorverse-icon",
                enterValue: false,
                placeholder: "Enter Creatorverse Score",
            }
        ]
    },
    {
        optionName: "Demography & Identity",
        optionFor: "audience",
        channel: "youtube",
        filters: [
            {
                name: "gender",
                type: "radio-button",
                icon: "gender-icon",
                minmax: false,
                enterValue: false,
                placeholder: "Select Gender", //only needed for enterValue
                options: [
                    { label: "Male", value: "male", description: ""
                    },
                    { label: "Female", value: "female", description: ""
                    },
                    { label: "Other", value: "other", description: ""
                    }
                ]
            },
            {
                name: "age",
                type: "checkbox",
                minmax: true,
                icon: "age-icon",
                enterValue: false,
                placeholder: "Select Age", //only needed for enterValue
                options: [
                    { label: "Teen", value: "13-19", description: "13-19"
                    },
                    { label: "Young Adult", value: "20-35", description: "20-35"
                    },
                    { label: "Adult", value: "36-55", description: "36-55"
                    },
                    { label: "Senior", value: "56+", description: "56+"
                    }
                ]
            },
            {
                name: "Location",
                type: "multilevel-checkbox",
                icon: "location-icon",
                searchBox: true,
                minmax: false,
                enterValue: false,
                placeholder: "Search Location",
                options: [
                    {
                        subOptionName: "Tier 1",
                        subOptionType: "checkbox",
                        collapsed: true,
                        checkboxEnabled: true,
                        subOptions: [
                            { label: "Mumbai", value: "mumbai", description: ""
                            },
                            { label: "Delhi", value: "delhi", description: ""
                            },
                            { label: "Bangalore", value: "bangalore", description: ""
                            },
                            { label: "Hyderabad", value: "hyderabad", description: ""
                            },
                            { label: "Chennai", value: "chennai", description: ""
                            },
                            { label: "Kolkata", value: "kolkata", description: ""
                            },
                            { label: "Pune", value: "pune", description: ""
                            },
                            { label: "Ahmedabad", value: "ahmedabad", description: ""
                            }
                        ]
                    },
                    {
                        subOptionName: "Tier 2",
                        subOptionType: "checkbox",
                        collapsed: true,
                        checkboxEnabled: true,
                        subOptions: [
                            { label: "Surat", value: "surat", description: ""
                            },
                            { label: "Jaipur", value: "jaipur", description: ""
                            },
                            { label: "Lucknow", value: "lucknow", description: ""
                            },
                            { label: "Kanpur", value: "kanpur", description: ""
                            },
                            { label: "Nagpur", value: "nagpur", description: ""
                            },
                            { label: "Indore", value: "indore", description: ""
                            },
                            { label: "Bhopal", value: "bhopal", description: ""
                            },
                            { label: "Coimbatore", value: "coimbatore", description: ""
                            },
                            { label: "Visakhapatnam", value: "visakhapatnam", description: ""
                            },
                            { label: "Patna", value: "patna", description: ""
                            },
                            { label: "Vadodara", value: "vadodara", description: ""
                            },
                            { label: "Ludhiana", value: "ludhiana", description: ""
                            },
                            { label: "Agra", value: "agra", description: ""
                            },
                            { label: "Nashik", value: "nashik", description: ""
                            },
                            { label: "Faridabad", value: "faridabad", description: ""
                            },
                            { label: "Meerut", value: "meerut", description: ""
                            },
                            { label: "Rajkot", value: "rajkot", description: ""
                            },
                            { label: "Varanasi", value: "varanasi", description: ""
                            },
                            { label: "Amritsar", value: "amritsar", description: ""
                            },
                            { label: "Ranchi", value: "ranchi", description: ""
                            },
                            { label: "Raipur", value: "raipur", description: ""
                            },
                            { label: "Jodhpur", value: "jodhpur", description: ""
                            },
                            { label: "Madurai", value: "madurai", description: ""
                            },
                            { label: "Guwahati", value: "guwahati", description: ""
                            },
                            { label: "Chandigarh", value: "chandigarh", description: ""
                            },
                            { label: "Mysore", value: "mysore", description: ""
                            },
                            { label: "Hubli", value: "hubli", description: ""
                            },
                            { label: "Trivandrum", value: "trivandrum", description: ""
                            }
                        ]
                    },
                    {
                        subOptionName: "Tier 3 or Rural",
                        subOptionType: "checkbox",
                        collapsed: true,
                        checkboxEnabled: true,
                        subOptions: [
                            { label: "Aligarh", value: "aligarh", description: ""
                            },
                            { label: "Gorakhpur", value: "gorakhpur", description: ""
                            },
                            { label: "Gaya", value: "gaya", description: ""
                            },
                            { label: "Saharanpur", value: "saharanpur", description: ""
                            },
                            { label: "Bhavnagar", value: "bhavnagar", description: ""
                            },
                            { label: "Guntur", value: "guntur", description: ""
                            },
                            { label: "Bilaspur", value: "bilaspur", description: ""
                            },
                            { label: "Rewa", value: "rewa", description: ""
                            },
                            { label: "Hapur", value: "hapur", description: ""
                            },
                            { label: "Jamnagar", value: "jamnagar", description: ""
                            },
                            { label: "Jhansi", value: "jhansi", description: ""
                            },
                            { label: "Ujjain", value: "ujjain", description: ""
                            },
                            { label: "Tirunelveli", value: "tirunelveli", description: ""
                            },
                            { label: "Kakinada", value: "kakinada", description: ""
                            },
                            { label: "Nanded", value: "nanded", description: ""
                            },
                            { label: "Shimla", value: "shimla", description: ""
                            },
                            { label: "Muzaffarpur", value: "muzaffarpur", description: ""
                            },
                            { label: "Cuttack", value: "cuttack", description: ""
                            },
                            { label: "Jalandhar", value: "jalandhar", description: ""
                            },
                            { label: "Belgaum", value: "belgaum", description: ""
                            },
                            { label: "Kottayam", value: "kottayam", description: ""
                            },
                            { label: "Hoshiarpur", value: "hoshiarpur", description: ""
                            },
                            { label: "Rohtak", value: "rohtak", description: ""
                            }
                        ]
                    }
                ]
            },
            {
                name: "language",
                type: "checkbox",
                minmax: false,
                icon: "language-icon",
                enterValue: true,
                placeholder: "Enter Language",
                options: [
                    { label: "English", value: "english", description: ""
                    },
                    { label: "Hindi", value: "hindi", description: ""
                    },
                    { label: "Spanish", value: "spanish", description: ""
                    },
                    { label: "French", value: "french", description: ""
                    }
                ]
            }
        ]
    },
];