{"service_name": "CreatorVerseUser Backend", "environ": "development", "g_chat_webhook": "1221", "g_chat_error_webhook": "21", "connection_string": "postgresql+asyncpg://postgres:s81JKkaoe42Tm5W@172.16.4.173:5432/postgres", "security": {"SECRET_KEY": "your-secret-key-change-this-in-production", "ACCESS_TOKEN_EXPIRE_MINUTES": 30, "REFRESH_TOKEN_EXPIRE_DAYS": 30, "ALGORITHM": "HS256"}, "oauth": {"GOOGLE_CLIENT_ID": "710433774036-cbpuvstb545rgh91aos47t8h3k7eubt4.apps.googleusercontent.com", "GOOGLE_CLIENT_SECRET": "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", "GOOGLE_REDIRECT_URI": "http://127.0.0.1:8000/v1/oauth/callback", "INSTAGRAM_CLIENT_ID": "573438572481705", "INSTAGRAM_CLIENT_SECRET": "1dff9c717cec7668d34d2d9c80256de0", "INSTAGRAM_REDIRECT_URI": "http://localhost:8000/v1/oauth/callback"}, "email": {"MAILGUN_DOMAIN": "locobuzz.info", "MAILGUN_SECRET": "************************************", "SENDER_NAME": "CreatorVerse"}, "cors": {"ALLOWED_ORIGINS": ["http://localhost:3000", "http://localhost:8080"]}, "redis_host": "localhost", "log_enabled": "true", "log_level": "INFO", "log_type": "json", "is_async_logger": false, "enhanced_features": true, "include_trace_id": true, "use_queue_handler": true, "max_queue_size": 1000, "batch_size": 10, "flush_interval": 0.1, "bind_uvicorn": true}