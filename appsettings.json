{"service_name": "CreatorVerse Discovery & Analytics Backend", "environ": "development", "g_chat_webhook": "1221", "g_chat_error_webhook": "21", "connection_string": "postgresql+asyncpg://postgres:s81JKkaoe42Tm5W@************:5432/postgres", "security": {"SECRET_KEY": "your-secret-key-change-this-in-production", "ACCESS_TOKEN_EXPIRE_MINUTES": 30, "REFRESH_TOKEN_EXPIRE_DAYS": 30, "ALGORITHM": "HS256"}, "external_apis": {"PHYLLO_BASE_URL": "http://127.0.0.1:8002", "PHYLLO_CLIENT_ID": "dummy-client-id", "PHYLLO_CLIENT_SECRET": "dummy-client-secret", "PHYLLO_ENVIRONMENT": "sandbox", "USE_MOCK_APIS": true, "DISABLE_SSL_VERIFICATION": true}, "cors": {"ALLOWED_ORIGINS": ["http://localhost:3000", "http://localhost:8080"]}, "redis_host": "localhost", "cache": {"PROFILE_CACHE_TTL": 3600, "FILTER_CACHE_TTL": 86400, "EXTERNAL_API_CACHE_TTL": 1800}, "kafka": {"BOOTSTRAP_SERVERS": "localhost:9092", "TOPIC_PROFILE_UPDATES": "profile_updates", "TOPIC_EXTERNAL_FETCH": "external_fetch_requests", "CONSUMER_GROUP": "discovery_analytics_group"}, "discovery": {"DEFAULT_PAGE_SIZE": 20, "MAX_PAGE_SIZE": 100, "QUICK_VIEW_FIELDS": ["id", "platform_username", "full_name", "follower_count", "engagement_rate", "image_url"], "EXTERNAL_API_TIMEOUT": 30, "MAX_CONCURRENT_EXTERNAL_CALLS": 5}, "log_enabled": "true", "log_level": "INFO", "log_type": "json", "is_async_logger": false, "enhanced_features": true, "include_trace_id": true, "use_queue_handler": true, "max_queue_size": 1000, "batch_size": 10, "flush_interval": 0.1, "bind_uvicorn": true}