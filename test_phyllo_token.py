"""
Fix script for PhylloAPIService to skip authentication for testing.
This is a simpler version that just tests the _get_access_token method.
"""
import sys
from pathlib import Path
import asyncio
import json

# Add the project root to the Python path
sys.path.append(str(Path(__file__).parent))

# Import after adding to path
from app.services.phyllo_service import PhylloAPIService
from app.core.config import APP_CONFIG


async def main():
    """Test the _get_access_token method."""
    print("Testing PhylloAPIService without authentication...")
    
    try:
        # Create PhylloAPIService instance
        service = PhylloAPIService()
        print(f"Created PhylloAPIService instance")
        print(f"API URL: {service.base_url}")
        print(f"Mock APIs enabled: {service.use_mock_apis}")
        print(f"Mock Data enabled: {service.mock_data_enabled}")
        
        # Try to get access token
        token = await service._get_access_token()
        print(f"Successfully got token: {token[:10]}... (length: {len(token)})")
        
        print("All tests passed!")
        return True
    except Exception as e:
        print(f"ERROR: {type(e).__name__}: {str(e)}")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
